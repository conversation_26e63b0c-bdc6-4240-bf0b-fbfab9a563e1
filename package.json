{"name": "zhuge", "version": "1.1.1", "group": "trip", "private": true, "description": "诸葛后台(去node版)", "author": {"name": "南麓", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://gitlab.alibaba-inc.com/trip/zhuge"}, "keywords": ["clam", "zhuge"], "toolkit": "@ali/clam-toolkit-one", "commandType": "clam", "defPublishType": "assets", "isCloudBuild": true, "scripts": {"analyze": "cross-env ANALYZE=1 umi build", "build": "NODE_OPTIONS=--openssl-legacy-provider umi build", "lint": "eslint --ext .js src && npm run lint:style && npm run lint:prettier", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js", "lint:fix": "eslint --fix --ext .js src && stylelint --fix 'src/**/*.less' --syntax less", "lint:prettier": "check-prettier lint", "lint:style": "stylelint 'src/**/*.less' --syntax less", "prettier": "node ./scripts/prettier.js", "site": "umi build && npm run functions:build", "start": "NODE_OPTIONS=--openssl-legacy-provider cross-env umi dev", "tslint": "npm run tslint:fix", "tslint:fix": "tslint --fix 'src/**/*.ts*'"}, "lint-staged": {"**/*.less": "stylelint --syntax less", "**/*.{js,jsx}": "npm run lint-staged:js", "**/*.{js,ts,tsx,json,jsx,less}": ["node ./scripts/lint-prettier.js", "git add"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ali/search-form": "^2.0.0", "@ali/select-user": "^0.0.5", "@ali/simple-aem": "^0.2.8", "@alife/waterMark": "^1.2.13", "@ant-design/charts": "^1.4.2", "@ant-design/compatible": "^1.0.0", "@ant-design/icons": "4.0.0", "@ant-design/pro-components": "^2.8.7", "@antv/data-set": "^0.10.2", "@antv/g6": "3.5.11", "@monaco-editor/react": "4.4.0", "@types/react": "^17.0.2", "@types/react-dom": "^17.0.2", "@umijs/preset-react": "^1.8.32", "@xyflow/react": "^12.6.0", "antd": "^5.24.6", "bizcharts": "4.0.14", "bizcharts-plugin-slider": "^2.1.1-beta.1", "chart-render": "^0.1.9", "classnames": "^2.2.6", "dagre": "^0.8.5", "dayjs": "^1.8.35", "deepclone": "^1.0.2", "dva": "^2.4.1", "enquire-js": "^0.2.1", "express": "^4.16.4", "form-render": "^2.0.0", "gg-editor": "^2.0.2", "html-to-image": "^1.11.13", "lodash": "4.17.11", "lodash-decorators": "^6.0.1", "memoize-one": "^5.0.0", "netlify-lambda": "^1.4.3", "numeral": "^2.0.6", "nzh": "^1.0.4", "omit.js": "^1.0.0", "path-to-regexp": "^3.0.0", "prop-types": "^15.6.2", "qs": "^6.6.0", "rc-animate": "^2.6.0", "react": "^17.0.2", "react-amap": "^1.2.8", "react-codemirror2": "^6.0.0", "react-container-query": "^0.11.0", "react-copy-to-clipboard": "^5.0.1", "react-document-title": "^2.0.3", "react-dom": "^17.0.2", "react-fittext": "^1.0.0", "react-media": "^1.9.2", "react-sortable-hoc": "^1.11.0", "resize-observer-polyfill": "^1.5.1", "slash2": "^2.0.0", "solarlunar": "^2.0.7", "umi": "^3.5.41", "umi-request": "^1.0.5", "uuid": "^9.0.1"}, "devDependencies": {"@alife/umi-external-plugin": "^1.0.2", "@types/history": "^4.7.2", "@types/react": "^16.9.1", "@types/react-dom": "^16.8.5", "antd-dayjs-webpack-plugin": "^0.0.8", "antd-pro-merge-less": "^1.0.0", "antd-theme-webpack-plugin": "^1.2.0", "babel-eslint": "^10.0.1", "chalk": "^2.4.2", "check-prettier": "^1.0.1", "cross-env": "^5.2.0", "cross-port-killer": "^1.0.1", "enzyme": "^3.9.0", "eslint": "^5.13.0", "eslint-config-airbnb": "^17.1.0", "eslint-config-prettier": "^4.1.0", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-compat": "^2.6.3", "eslint-plugin-import": "^2.16.0", "eslint-plugin-jsx-a11y": "^6.2.1", "eslint-plugin-markdown": "^1.0.0", "eslint-plugin-react": "^7.12.4", "gh-pages": "^2.0.1", "husky": "^1.3.1", "jsdom-global": "^3.0.2", "less": "^3.9.0", "lint-staged": "^8.1.1", "merge-umi-mock-data": "^1.0.4", "prettier": "^1.17.0", "serverless-http": "^2.0.1", "stylelint": "^9.10.1", "stylelint-config-css-modules": "^1.3.0", "stylelint-config-prettier": "^5.0.0", "stylelint-config-rational-order": "^0.1.0", "stylelint-config-standard": "^18.2.0", "stylelint-declaration-block-no-ignored-properties": "^2.1.0", "stylelint-order": "^2.0.0", "tslint": "^5.12.1", "tslint-config-prettier": "^1.17.0", "tslint-react": "^3.6.0"}, "engines": {"node": ">=14.19.2"}, "checkFiles": ["src/**/*.js*", "src/**/*.ts*", "src/**/*.less", "config/**/*.js*", "scripts/**/*.js"], "overrides": {"lodash": {".": "$lodash"}, "@types/ms": {".": "0.7.34"}, "swr": {".": "2.0.3"}}}