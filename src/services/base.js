import get from 'lodash/get';

let API_ROOT_TEMP = '/api';
let IS_IN_ONE_TEMP = false;
const whiteList = ['fliggy.alibaba.net', 'pre-fliggy.alibaba-inc.com', 'fliggy.alibaba-inc.com'];
// 这里要做区分
const baseKey = '/zhuge'; // 此处和父应用server config里面配置保持一致

if (
  get(
    whiteList.filter(_ => window.location.href.indexOf(_) > -1),
    '[0]',
    null
  )
) {
  IS_IN_ONE_TEMP = true;
  API_ROOT_TEMP = `${baseKey}${API_ROOT_TEMP}`;
}

export const API_ROOT = API_ROOT_TEMP;
export const IS_IN_ONE = IS_IN_ONE_TEMP;
