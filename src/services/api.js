import { stringify } from 'qs';
import request from '@/utils/request';
import { API_ROOT } from '@/services/base';
import { update } from 'lodash';
// import {PAGE_ROOT} from '@/services/page';

/**
 * 标签树
 */
export async function queryBuildTree(params) {
  return request(`/category/buildTree?${stringify(params)}`, {
    method: 'POST',
  });
}

/**
 * 人群标签
 * @param {*} params 参数
 */
export async function queryLabel(params) {
  const data = params;
  if (data.status === 'ALL') {
    delete data.status;
  }
  if (data.bizOwner) {
    data.bizOwner = JSON.parse(data.bizOwner);
  }
  return request(`/label/pageQuery?pageNum=${params.pageNum}&pageSize=${params.pageSize}`, {
    method: 'POST',
    data,
  });
}

/**
 * 查询单个标签信息
 * @param {*} params {id}
 */
export async function queryLabelById(params) {
  const { id = '' } = params;
  return request(`/label/query/id?id=${id}`, {
    method: 'POST',
  });
}

/**
 * 根据名字查询
 * @param {*} params 英文名称
 */
export async function queryLabelByName(params) {
  return request(`/label/query/name?${stringify(params)}`, {
    method: 'POST',
  });
}

/**
 * 人群标签编辑
 * @param {*} data 参数
 */
export async function editLabel(data) {
  return request(`/label/${data.id?'update':'create'}`, {
    method: 'POST',
    data,
  });
}

/**
 * 用户标签管理删除
 * @param {*} params 参数
 */
export async function editDeleteSQL(params) {
  const { id = '' } = params;
  return request(`/label/delete/${id}`);
}

/**
 * 检查当前用户是否为管理员
 */
export async function CheckIsAdmin() {
  return request(`/label/check/isAdmin`);
}

/**
 * 标签对应枚举值类型
 */
export async function queryLabelEnum() {
  return request(`/label/getEnum`, {
    method: 'POST',
  });
}

/**
 * 人群标签批量操作
 * @param {*} data 参数
 */
export async function operateLabel(data) {
  return request(`/label/batchOperate`, {
    method: 'POST',
    data,
  });
}

/**
 * 查询odps已授权的表
 */
export async function queryOdpsGrantedTable(params) {
  return request(`/dataflow/query/dwsTableList?sceneType=${params.sceneType}`, {
    method: 'POST',
  });
}

/**
 * 通过名称查询已授权表信息
 * @param {*} params {tableName}
 */
export async function queryOdpsGrantedTableByTableName(params) {
  const { tableName = '' } = params;
  return request(`/dataflow/query/dwsTable/name?tableName=${tableName}`);
}

/**
 * 模糊查询odps表名
 * @param {*} params 参数
 */
export async function queryOdpsTable(params) {
  const { keyword = '', pageSize = 10 } = params;
  return request(
    `/dataflow/fuzzyQuery/tables?keyword=${keyword}&pageSize=${pageSize}`, {
      method: 'POST',
    }
  );
}

/**
 * 查询某表所有字段名
 * @param {*} params 参数
 */
export async function queryOdpsTableColumn(params) {
  return request(`/dataflow/query/table/columns?tableGuid=${params.tableGuid}`, {
    method: 'POST',
  });
}

/**
 * 查询授权表详细信息
 * @param {*} params 参数
 */
export async function queryActCalendarOdpsGrantedTableInfo(params) {
  const data = params;
  delete data.pageNum;
  delete data.pageSize;
  return request(
    `/dataflow/pageQuery/dwsTable?pageNum=${params.pageNum}&pageSize=${params.pageSize}`,
    {
      method: 'POST',
      data,
    }
  );
}

/**
 * 添加授权表
 * @param {}} params 参数
 */
export async function editGrantedTable(data) {
  return request(`/dataflow/add/dwsTable`, {
    method: 'POST',
    data,
  });
}

// 编辑枚举值对象
export async function editLabelEnumValueObj(data) {
  return request(`/labelEnum/labelEnumValueObj/${data.id ? 'update' : 'create'}`, {
    method: 'POST',
    data,
  });
}

// 编辑枚举值
export async function editLabelEnumValue(data) {
  return request(`/labelEnum/labelEnumValue/${data.id ? 'update' : 'create'}`, {
    method: 'POST',
    data,
  });
}

// 删除枚举值
export async function deleteLabelEnumValue(data) {
  return request(`/labelEnum/labelEnumValue/delete?index=${data.id}`, {
    method: 'POST',
  });
}

// 增量数据更新接口
export async function updateFromOdps(data) {
  return request(`/labelEnum/labelEnumValue/updateFromOdps?id=${data.id}`, {
    method: 'POST',
  });
}

// 枚举值对象查询
export async function queryLabelEnumValueObj(params) {
  return request(`/labelEnum/labelEnumValueObj/query`, {
    method: 'POST',
    data: params,
  });
}

// 根据code查询枚举值对象
export async function queryLabelEnumValueObjByCode(params) {
  return request(`/labelEnum/labelEnumValueObj/queryByCode?code=${params.code}`);
}

// 枚举值详情树
export async function queryLabelEnumValueBuildTree(params) {
  return request(`/labelEnum/buildTree?${stringify(params)}`, {
    method: 'POST',
  });
}

// 分页查询业务实体
export async function queryBizEntity(data) {
  return request(`/bizEntity/pageQuery?pageNum=${params.pageNum}&pageSize=${params.pageSize}`, {
    method: 'POST',
    data,
  });
}

// 编辑业务实体
export async function editBizEntity(data) {
  return request(`/bizEntity/${data.id ? 'update' : 'create'}`, {
    method: 'POST',
    data,
  });
}

// 删除业务实体
export async function deleteBizEntity(data) {
  return request(`/bizEntity/delete?bizEntityName=${encodeURIComponent(data.bizEntityName)}`, {
    method: 'POST',
  });
}

// 查询活动日历
export async function queryActCalendar(params) {
  return request(`${API_ROOT}/dataService/act/canlendar?${stringify(params)}`);
}

export async function queryActType(params) {
  return request(`${API_ROOT}/dataService/act/actType?${stringify(params)}`);
}

export async function queryActList(params) {
  return request(`${API_ROOT}/act/list?${stringify(params)}`);
}

export async function queryType(params) {
  return request(`${API_ROOT}/act/queryType?${stringify(params)}`);
}

export async function createNewAct(data) {
  return request(`${API_ROOT}/act/create`, {
    method: 'POST',
    data,
  });
}

export async function updateAct(data) {
  return request(`${API_ROOT}/act/update`, {
    method: 'POST',
    data,
  });
}

export async function deleteAct(data) {
  return request(`${API_ROOT}/act/delete`, {
    method: 'POST',
    data,
  });
}
// 查询活动日历的管理员
export async function queryActAdmin(params) {
  return request(`${API_ROOT}/act/admin?${stringify(params)}`);
}
// 查询活动日历的活动类型
export async function queryActTypeConst(params) {
  return request(`${API_ROOT}/act/actType?${stringify(params)}`);
}
// 查询已删除的活动
export async function queryDeleteAct(params) {
  return request(`${API_ROOT}/act/queryActDeleted?${stringify(params)}`);
}
// 恢复已删除的活动
export async function unDeleteAct(data) {
  return request(`${API_ROOT}/act/unDelete`, {
    method: 'POST',
    data,
  });
}
// 按月度查询活动数据
export async function queryCalendarData(params) {
  return request(`${API_ROOT}/act/queryActMonthData?${stringify(params)}`);
}

// 查询算法模型
export async function queryAlgoModel(params) {
  return request(`${API_ROOT}/crowd/algoModel/list?${stringify(params)}`);
}

/**
 * 算法模型编辑
 * @param data
 */
export async function editAlgoModel(data) {
  return request(`${API_ROOT}/crowd/algoModel/edit`, {
    method: 'POST',
    data,
  });
}

/**
 * 算法模型相关枚举
 */
export async function queryAlgoModelEnum() {
  return request(`${API_ROOT}/crowd/algoModel/enum`);
}

/**
 * 算法模型任务配置
 */
export async function queryAlgoModelTaskConfig(params) {
  return request(`${API_ROOT}/crowd/algoModelTaskConfig/list?${stringify(params)}`);
}

/**
 * 算法模型任务实例
 */
export async function queryAlgoModelTaskInstance(params) {
  return request(`${API_ROOT}/crowd/algoModelTaskInstance/list?${stringify(params)}`);
}

/**
 * 算法模型枚举
 */
export async function queryAlgoModelTaskConfigEnum() {
  return request(`${API_ROOT}/crowd/algoModelTaskConfig/enum`);
}

/**
 * 编辑算法模型任务配置
 */
export async function editAlgoModelTaskConfig(data) {
  return request(`${API_ROOT}/crowd/algoModelTaskConfig/edit`, {
    method: 'POST',
    data,
  });
}

/**
 * 执行任务
 */
export async function excuteAlgoModelTaskConfigTask(params) {
  return request(`${API_ROOT}/crowd/algoModelTaskConfig/excuteTask?${stringify(params)}`);
}

/**
 * 停止任务
 */
export async function stopAlgoModelTaskConfigTask(params) {
  return request(`${API_ROOT}/crowd/algoModelTaskConfig/stopTask?${stringify(params)}`);
}

/**
 * 标签圈人
 */
export async function editCrowdByLabel(data) {
  return request(`${API_ROOT}/crowd/label/edit`, {
    method: 'POST',
    data,
  });
}
/**
 * FBI报表管理
 */
export async function queryFbiAdmin() {
  return request(`${API_ROOT}/admin/list`);
}

export async function createFbiAdmin(data) {
  return request(`${API_ROOT}/admin/create`, {
    method: 'POST',
    data,
  });
}

/**
 * 算法圈人
 * @param {*} data
 */
export async function editCrowdByAlgo(data) {
  return request(`${API_ROOT}/crowd/algo/edit`, {
    method: 'POST',
    data,
  });
}

/**
 * 跃迁圈人
 */

export async function editCrowdTransition(data) {
  return request(`${API_ROOT}/crowd/transition/edit`, {
    method: 'POST',
    data,
  });
}

export async function deleteFbiAdmin(data) {
  return request(`${API_ROOT}/admin/destroy`, {
    method: 'POST',
    data,
  });
}

/**
 * 人群list接口
 */
export async function queryCrowd(data) {
  return request(`/crowd/pageQuery?pageNum=${data.pageNum}&pageSize=${data.pageSize}`, {
    method: 'POST',
    data,
  });
}

/**
 * 搜索人群
 */
export async function queryCrowdKeyword(params) {
  const data = params;
  return request(`/crowd/pageQueryByKeyword?pageNum=${params.pageNum || 1}&pageSize=${params.pageSize || 10}`, {
    method: 'POST',
    data,
  });
}

/**
 * 人群构建错误码查询
 */
export async function errorMsg(params) {
  return request(`${API_ROOT}/crowd/errorMsg/query?${stringify(params)}`);
}

/**
 * 根据id查询人群
 * @param {*} params id
 */
export async function queryCrowdById(params) {
  const { id = '' } = params;
  return request(`/crowd/queryById?id=${id}`, {
    method: 'POST',
  });
}

/**
 * 根据画像标签条件查询
 * @param {*} params id
 */
export async function queryPictureSearch(params) {
  const { propertyDescription = '' } = params;
  return request(`${API_ROOT}/picasso/label/pageQuery`, {
    method: 'POST',
    data: params,
  });
}

/**
 * 查询画像
 */
export async function querySearchPicture(params) {
  const { id = '', profileName = '' } = params;
  return request(`${API_ROOT}/crowd/profile/analysis/pageQuery`, {
    method: 'POST',
    data: params,
  });
}

/**
 * 提交画像
 */
export async function queryPictureSubmit(params) {
  const {
    profileIndexList = [],
    extInfo = '',
    profileName = '',
    profileDescription = '',
    profileCrowdId = '',
    operator = [],
    profileType = '',
  } = params;
  return request(`${API_ROOT}/crowd/profile/analysis/create`, {
    method: 'POST',
    data: params,
  });
}

/**
 * 更新画像
 */
export async function queryPictureUpdate(params) {
  const { id = '', deleted = '' } = params;
  return request(`${API_ROOT}/crowd/profile/analysis/update`, {
    method: 'POST',
    data: params,
  });
}

/**
 * 新建指标结果
 */
export async function queryCreatCanvas(params) {
  const { analysis_label_name = [], analysis_task_id = '', analysis_graph_type = '' } = params;
  return request(`${API_ROOT}/crowd/profile/index/create`, {
    method: 'POST',
    data: params,
  });
}

/**
 * 查询图表
 */
export async function querySearchCanvas(params) {
  const { taskId = '' } = params;
  return request(`${API_ROOT}/crowd/profile/index/query`, {
    method: 'POST',
    data: params,
  });
}

/**
 * 刷新图表
 */
export async function queryRefreshCanvas(params) {
  const {} = params;
  return request(`${API_ROOT}/crowd/profile/index/refresh`, {
    method: 'POST',
    data: params,
  });
}

/**
 * 删除图表
 */
export async function queryDeleteCanvas(params) {
  const {} = params;
  return request(`${API_ROOT}/crowd/profile/index/delete`, {
    method: 'POST',
    data: params,
  });
}

export async function queryCreatePicture(params) {
  const { id = '', profileName = '' } = params;
  return request(`${API_ROOT}/crowd/pageQuery`, {
    method: 'POST',
    data: params,
  });
}

/**
 * 查询人群数量
 * @param {*} data
 */
export async function queryCrowdCount(data) {
  return request(`/crowd/label/count`, {
    method: 'POST',
    data,
  });
}
/**
 * 通过已选择标签查询是否支持push
 * @param {*} data
 */
export async function getIsSupportPush(data) {
  return request(`/crowd/judge/support/scene`, {
    method: 'POST',
    data,
  });
}

/**
 * 人群重跑
 */
export async function getRerunCrowd(params) {
  return request(`/crowd/rerun?${stringify(params)}`);
}

/**
 * 删除人群
 */
export async function deleteCrowd(data) {
  return request(`/crowd/delete?crowdId=${data.id}`, {
    method: 'POST',
    data,
  });
}

/**
 * 人群延期
 */

export async function delayCrowd(data) {
  return request(`${API_ROOT}/crowd/delay`, {
    method: 'POST',
    data,
  });
}

/** *
 * 新人群延期
 */
export async function delayNewCrowd(data) {
  return request(`/crowd_circle/postpone?crowdId=${data.id}&expired=${data.expired}`, {
    method: 'POST',
  });
}

/**
 * 诸葛选品规则
 */
export async function queryZhugeRule() {
  return request(`${API_ROOT}/rule/chooseProductRule/list`);
}

/**
 * 子活动list
 */
export async function queryActivityByCondition(params) {
  return request(
    `${API_ROOT}/tripGalaxy/activityQueryService/queryActivityByCondition?${stringify(params)}`
  );
}

// 人群放大接口
export async function editCrowdByEnLarge(data) {
  return request(`${API_ROOT}/crowd/algo/enlarge/edit`, {
    method: 'POST',
    data,
  });
}

// 查询userId是否在人群中
export async function isUserInCrowd(data) {
  return request(`/crowd/isUserInCrowd`, {
    method: 'POST',
    data,
  });
}

// 查询设备id是否在人群中
export async function singleMatch(data) {
  return request(`/crowd/match/singleMatch`, {
    method: 'POST',
    data,
  });
}

// 人群测试输入类型枚举
export async function queryMatchIdTypes() {
  return request(`${API_ROOT}/crowd/match/queryMatchIdTypes`);
}

// odps导入圈人
export async function editCrowdByOdps(data) {
  return request(`${API_ROOT}/crowd/odps/edit`, {
    method: 'POST',
    data,
  });
}

// 文件上传圈人
export async function editCrowdByFile(data) {
  return request(`${API_ROOT}/crowd/file/edit`, {
    method: 'POST',
    data,
  });
}

// 查询是否种子人群
export async function isSeedCrowd(data) {
  return request(`/crowd/isSeedCrowd?crowdId=${data.id}`);
}

// 编辑人群导出任务
export async function editExportCrowdTask(data) {
  if (data.id) {
    delete data.exportCreator
  }
  return request(`/crowd/export/odps/${data.id ? 'update' : 'create'}`, {
    method: 'POST',
    data,
  });
}

// 查人群导出任务
export async function queryExportCrowdTask(params) {
  return request(`/crowd/export/odps/queryByCrowdId?crowdId=${params.crowdId}`);
}

// 查询群组黑白名单
export async function queryblackwhitelist(params) {
  const { groupId = 123, groupType = 'TAOBAO_USER' } = params;
  return request(`/group/blackwhitelist/query?groupId=${groupId}&groupType=${groupType}`);
}
// 新增群组黑白名单
export async function addBlackWhiteListCrowdTask(data) {
  return request(`/group/blackwhitelist/insert`, {
    method: 'POST',
    data,
  });
}
// 修改群组黑白名单
export async function updateBlackWhiteListCrowdTask(data) {
  return request(`/group/blackwhitelist/update`, {
    method: 'POST',
    data,
  });
}
// 查询群组黑白名单变更记录
export async function queryBlackWhiteListRecords(params) {
  const { groupId } = params;
  return request(`/group/blackwhitelist/records?groupId=${groupId}`);
}

// 人群逻辑运算
export async function editCrowdByOperate(data) {
  return request(`/crowd/edit`, {
    method: 'POST',
    data,
  });
}

// 查询人货匹配
export async function queryEnclosedItems(params) {
  return request(`${API_ROOT}/crowd/encloseditem/list?${stringify(params)}`);
}

/**
 * 创建人群素材匹配
 */
export async function createEnclosedItem(data) {
  return request(`/crowd/algo/enclosedItem/${data.id ? 'update' : 'create'}`, {
    method: 'POST',
    data,
  });
}

/**
 * 标签对应枚举值类型
 */
export async function queryEnclosedEnum() {
  return request(`/crowd/algo/enclosedItem/getEnum`);
}

/**
 * 素材延期
 */
export async function delayEnclosed({ id, expired }) {
  return request(`/crowd/algo/enclosedItem/postpone`, {
    method: 'GET',
    params: {
      taskId: id,
      expired,
    },
  });
}

/**
 * 素材删除
 */
export async function deleteEnclosed(data) {
  return request(`/crowd/algo/enclosedItem/delete`, {
    method: 'POST',
    data,
  });
}

/**
 * SQL圈人 模版查询接口
 */
export async function queryTemplate(data = {}) {
  return request(`/crowd/sql/template/pageQuery?pageNum=1&pageSize=10000`, {
    method: 'POST',
    data,
  });
}

/**
 * SQL圈人 创建&更新
 */
export async function editCrowdBySQL(data) {
  return request(`/crowd/${data.id ? 'update' : 'create'}`, {
    method: 'POST',
    data,
  });
}

/**
 * 人群分桶
 */
export async function crowdBucket(data) {
  return request(`/crowd/bucket`, {
    method: 'POST',
    data,
  });
}

/**
 * 标签圈人人群精准查询
 */

export async function queryGpByName(data) {
  return request(`/crowd/queryByName?crowdName=${encodeURIComponent(data.crowdName)}`, {
    method: 'POST',
    data,
  });
}

/**
 * 查询指定人群最近运行实例信息
 */

export async function crowdDagQuery(params) {
  return request(`/crowd/dag/query/recent?${stringify(params)}`);
}
/**
 * 配置人群监控
 */

export async function monitorConfig(data) {
  return request(`/crowd/monitor/config`, {
    method: 'POST',
    data,
  });
}

// 查询可授权账号列表
export async function queryAccountInfos() {
  return request(`/crowd/export/auth/queryAccountInfos`);
}

// 人群导出授权
export async function authorize(data) {
  return request(`/crowd/export/auth/authorize`, {
    method: 'POST',
    data,
  });
}

// 人群同步人群中心
export async function syncCrowdCenter(data) {
  return request(`/crowd/export/auth/syncCrowdCenter`, {
    method: 'POST',
    data,
  });
}

// 判断超级管理员
export async function isSuperAdmin(data) {
  return request(`/permission/isSuperAdmin`);
}

// 人群导出授权
export async function updateCreator(data) {
  return request(`/crowd/updateCreator`, {
    method: 'POST',
    data,
  });
}

// 公告
export async function globalNotification() {
  return request(`/globalNotification`);
}

/**
 * scrmOneid实时查询
 */

export async function uidToScrmOneId(params) {
  return request(`/crowd/mapping/onlineMapping/uidToScrmOneId?${stringify(params)}`);
}

/**
 * scrmOneid离线查询
 */

export async function offlineMapping(data) {
  return request(`/crowd/mapping/offlineMapping`, {
    method: 'POST',
    data,
  });
}

/**
 * scrmOneid查询任务状态
 */

export async function queryByCrowdId(params) {
  return request(`/crowd/mapping/queryByCrowdId?${stringify(params)}`);
}

/**
 * Device-Oneid工具查询输入id类型
 */

export async function queryMappingIdTypes() {
  return request(`/crowd/mapping/queryMappingIdTypes`);
}

/**
 * Device-Oneid查询
 */

export async function idToDeviceOneId(params) {
  return request(`/crowd/mapping/onlineMapping/idToDeviceOneId?${stringify(params)}`);
}

/**
 * 查询画像工具列表
 */

export async function queryProfileList(data) {
  return request(`/profile/physical/list`, {
    method: 'POST',
    data,
  });
}

/**
 * 画像创建
 */

export async function profilePhysicalCreate(data) {
  return request(`/profile/physical/create`, {
    method: 'POST',
    data,
  });
}

/**
 * 画像编辑
 */

export async function profilePhysicalUpdate(data) {
  return request(`/profile/physical/update`, {
    method: 'POST',
    data,
  });
}

/**
 * 画像删除
 */

export async function profilePhysicalDelete(data) {
  return request(`/profile/physical/delete?id=${data.id}`, {
    method: 'POST',
  });
}

/**
 * 判断是否展示商业化租户
 */

export async function isCommercializationManager() {
  return request(`/label/check/isCommercializationManager`);
}

/**
 * 人群审核判断
 */

export async function approvalJudge(data) {
  return request(`/crowd/approvalJudge`, {
    method: 'POST',
    data,
  });
}

/**
 * 导出审核判断
 */

export async function exportApprovalJudge(params) {
  return request(`/crowd/export/odps/approval_judge?${stringify(params)}`);
}

/**
 * 数据源
 */
export async function getHsfLabelValueSource() {
  return request(`/label/getHsfLabelValueSource`);
}

/**
 * 点赞和踩
 */
export async function userFeedback(data) {
  return request(`/feedback/userFeedback`, {
    method: 'POST',
    data,
  });
}

/**
 * odps授权校验
 */
export async function odpsCheckTaskCreate(data) {
  return request(`/tool/odpsCheckTaskCreate`, {
    method: 'POST',
    data,
  });
}

/**
 * odps表历史任务
 */
export async function odpsCheckHistoryList(data) {
  return request(`/tool/odpsCheckHistoryList`, {
    method: 'POST',
    data,
  });
}

/**
 * 标签匹配校验
 */
export async function queryLabelValue(data) {
  return request(`/tool/queryLabelValue`, {
    method: 'POST',
    data,
  });
}

/**
 * 匹配结果不符合预期
 */
export async function crowdMatch(data) {
  return request(`/tool/crowdMatch`, {
    method: 'POST',
    data,
  });
}

/**
 * 线上流量统计
 */
export async function queryOnlineStatisticVal(data) {
  return request(`/tool/queryOnlineStatisticVal`, {
    method: 'POST',
    data,
  });
}

/**
 * 时间类型枚举接口
 */
export async function queryTimeType() {
  return request(`/spaceTime/queryTimeType`);
}

/**
 * 行为类型枚举接口
 */
export async function queryBehaviorType(params) {
  return request(`/spaceTime/queryBehaviorType?${stringify(params)}`);
}

/**
 * 附加条件配置接口
 */
export async function queryConditionType(params) {
  return request(`/spaceTime/queryConditionType?${stringify(params)}`);
}

// 标签市场标签搜索
export async function labelMarketSearch(data) {
  return request(`/label_market/search`, {
    method: 'POST',
    data,
  });
}

// 标签大盘
export async function queryLabelMarketStatistics() {
  return request(`/label_market/market_index/statistics`);
}

// 热门标签
export async function queryLabelMarketHotLabel() {
  return request(`/label_market/market_index/hot_label`);
}

// 新增标签
export async function queryLabelMarketNewLabel() {
  return request(`/label_market/market_index/new_label`);
}

// 标签市场账号类型
export async function queryLabelMarketAccountType() {
  return request(`/label_market/enum/account_type`);
}

// 标签市场标签状态
export async function queryLabelMarketLabelStatus() {
  return request(`/label_market/enum/label_status`);
}

// 标签市场标签时效
export async function queryLabelMarketTimeType() {
  return request(`/label_market/enum/time_type`);
}

// 标签市场分类tab
export async function queryLabelMarketClassifyTab() {
  return request(`/label_market/enum/classify_tab`);
}

// 标签市场排序类型
export async function queryLabelMarketSortType() {
  return request(`/label_market/enum/sort_type`);
}

// 标签市场标签收藏
export async function labelMarketCollect(data) {
  return request(`/collect/collect`, {
    method: 'POST',
    data,
  });
}

// 查询标签市场标签信息
export async function labelMarketDetailInfo(data) {
  return request(
    `/label_market/detail/label_info?isCore=${data.isCore}&labelCode=${data.labelCode}`,
    {
      method: 'POST',
    }
  );
}

// 标签市场取值分布
export async function labelMarketDetailEnumStart(data) {
  const { labelCode, pageNo, pageSize } = data
  return request(`/label_market/detail/enum_stat?labelCode=${labelCode}&pageNo=${pageNo}&pageSize=${pageSize}`, {
    method: 'POST',
    data,
  });
}

// 标签市场标签产出监控
export async function labelMarketDetailOutputMonitor(data) {
  return request(`/label_market/detail/output_monitor?labelCode=${data.labelCode}`, {
    method: 'POST',
    data,
  });
}

// 浏览访问
export async function labelMarketDetailPageView(data) {
  return request(`/pv/page_view`, {
    method: 'POST',
    data,
  });
}

//新标签标签管理列表
export async function queryNewLabelList(data) {
  return request(`/label_background/search`, {
    method: 'POST',
    data,
  });
}

//创建标签
export async function createLabel(data) {
  return request(`/label_background/create`, {
    method: 'POST',
    data,
  });
}

//编辑标签
export async function updateLabel(data) {
  return request(`/label_background/update`, {
    method: 'POST',
    data,
  });
}

//详情
export async function queryLabelDetail(id) {
  return request(`/label_background/query/id?id=${id}`);
}

//修改配置
export async function updateConfig(data) {
  return request(`/label_background/update_config`, {
    method: 'POST',
    data,
  });
}

//下线标签二次确认
export async function offlineLabel(labelCode) {
  return request(`/label_background/offline_double_check?labelCode=${labelCode}`);
}

//模糊查询表
export async function queryDwsTableWithDesc(data) {
  const { keyword = "", pageSize = 10 } = data
  return request(`/dataflow/fuzzyQuery/dwsTableWithDesc?keyword=${keyword}&pageSize=${pageSize}`, {
    method: 'POST',
  });
}

//查询授权表
export async function queryGrantedTable(data) {
  return request(`/dataflow/pageQuery/grantedTable?${stringify(data)}`, {
    method: 'POST',
    data,
  });
}

//查询字段信息
export async function queryColumnInfos(data) {
  return request(`/dataflow/query/table/columnInfos?tableGuid=${data.tableGuid}`, {
    method: 'POST',
  });
}

//sql校验
export async function sqlCheck(data) {
  return request(`/label_background/check/sql`, {
    method: 'POST',
    data,
  });
}

//授权表校验
export async function checkOdpsAuth(data) {
  return request(`/odps/table/check_odps_auth`, {
    method: 'POST',
    data,
  });
}

//人群分组创建
export async function createCrowdGroup(data) {
  return request(`/group/create`, {
    method: 'POST',
    data,
  });
}

//人群分组分页
export async function pageQueryCrowdGroup(params) {
  return request(`/group/page_query?${stringify(params)}`);
}

//人群分组编辑
export async function updateCrowdGroup(data) {
  return request(`/group/update`, {
    method: 'POST',
    data,
  });
}

//人群分组删除
export async function deleteCrowdGroup(id) {
  return request(`/group/delete?id=${id}`, {
    method: 'POST',
  });
}

//人群圈选列表分页
export async function pageQueryCrowd(data) {
  return request(`/crowd_circle/search`, {
    method: 'POST',
    data,
  });
}

//人群创建
export async function createCrowd(data) {
  return request(`/crowd_circle/create`, {
    method: 'POST',
    data,
  });
}

//人群圈选管理员编辑
export async function updateCrowdCircleManager(data) {
  return request(`/crowd_circle/update_manager`, {
    method: 'POST',
    data,
  });
}

//人群圈选详情
export async function queryCrowdCircle(id) {
  return request(`/crowd_circle/query/id?id=${id}`);
}

//人群删除
export async function deleteCrowdCircle(data) {
  return request(`/crowd_circle/delete?id=${data.id}`, {
    method: 'POST',
  });
}

//人群圈选编辑
export async function updateCrowdCircle(data) {
  return request(`/crowd_circle/update`, {
    method: 'POST',
    data,
  });
}

//人群圈选名字是否重复
export async function checkCrowdCircleName(data) {
  return request(`/crowd_circle/is_name_duplicate`, {
    method: 'POST',
    data,
  });
}

//人群圈选sql授权校验
export async function checkCrowdCircleSqlAuth(data) {
  return request(`/odps/table/check_sql_odps_auth`, {
    method: 'POST',
    data,
  });
}

//odps表分区字段
export async function queryPartitionFields(data) {
  return request(`/dataflow/query/table/partition_col?tableGuid=${data.tableGuid}`, {
    method: 'POST',
  });
}

//odps实时校验
export async function checkOdpsPreTable(data) {
  return request(`/odps/table/pre_check_odps_table?tableName=${data.tableName}`, {
    method: 'POST',
  });
}

//人群圈选是否实时
export async function queryIsRealTime(data) {
  return request(`/crowd_circle/is_realtime`, {
    method: 'POST',
    data,
  });
}

//人群圈选组合子人群
export async function queryCrowdCircleChilds(data) {
  return request(`/crowd_circle/find_childs`, {
    method: 'POST',
    data,
  });
}

//人群分组
export async function updateCrowdBatchGroup(data) {
  return request(`/group/batch_group`, {
    method: 'POST',
    data,
  });
}

//人群分组查询
export async function queryCrowdGroupId(params) {
  return request(`/group/find/grouped_id?${stringify(params)}`);
}

//人群圈选标签校验
export async function checkCrowdId(data) {
  return request(`/crowd_circle/check_validation?crowdId=${data.crowdId}`, {
    method: 'POST',
  });
}

//人群圈选嵌套校验
export async function checkOperateCntCrowdCircle(data) {
  return request(`/crowd_circle/check_operate_cnt?${stringify(data)}`, {
    method: 'POST',
  });
}

//流通中心审核判断
export async function crowdCircleApprovalJudge(data) {
  return request(`/crowd_circle/approval_judge`, {
    method: 'POST',
    data,
  });
}

//站内信列表
export async function queryMessageList(data) {
  return request(`/zhuge_message/search`, {
    method: 'POST',
    data,
  });
}

//未读消息数量
export async function queryUnreadMessageCount() {
  return request(`/zhuge_message/unread_count`);
}

//消息已读
export async function readMessage(id) {
  return request(`/zhuge_message/read?id=${id}`);
}

//消息全部已读
export async function readAllMessage() {
  return request(`/zhuge_message/read_all`);
}

//告警配置
export async function updateMonitorConfig(data) {
  return request(`/monitor_config/update`, {
    method: 'POST',
    data,
  });
}

//告警查询
export async function queryMonitorByCrowd(id) {
  return request(`/monitor_config/query_by_crowd?crowdId=${id}`);
}

//人群延期
export async function crowdCircleQuickPostpone(params) {
  return request(`/crowd_circle/quick_postpone`, {
    method: 'GET',
    params,
  });
}

//新画像分析列表
export async function queryNewInsightAnalysisList(params) {
  return request(`/analysisV2/list`, {
    method: 'GET',
    params,
  });
}

//人群列表
export async function queryNewAnalysisV2CrowdList(params) {
  return request(`/analysisV2/crowdList`, {
    method: 'GET',
    params,
  });
}

//新建新画像分析
export async function createNewInsightAnalysis(data) {
  return request(`/analysisV2/create`, {
    method: 'POST',
    data,
  });
}

//编辑新画像分析
export async function updateNewInsightAnalysis(data) {
  return request(`/analysisV2/update`, {
    method: 'POST',
    data,
  });
}

//画像分析人群接口
export async function queryNewAnalysisV2Crowd(params) {
  return request(`/crowd/meta/query`, {
    method: 'GET',
    params,
  });
}

//画像分析查询所有纬度
export async function queryNewAnalysisDimension(params) {
  return request(`/analysis/dim/listProfileDimInfo`, {
    method: 'GET',
    params,
  });
}

//画像分析标签列表
export async function queryNewAnalysisV2Label(data) {
  return request(`/label/analysisV2/pageQuery`, {
    method: 'POST',
    data,
  });
}

//新画像分析id查询
export async function queryNewInsightAnalysis(id) {
  return request(`/analysisV2/findById?id=${id}`);
}

//画像分析模版列表
export async function queryAnalysisTemplate(data) {
  return request(`/analysis/template/pageQuery`, {
    method: 'POST',
    data,
  });
}

//画像分析模版创建
export async function createAnalysisTemplate(data) {
  return request(`/analysis/template/add`, {
    method: 'POST',
    data,
  });
}

//画像分析模版编辑
export async function updateAnalysisTemplate(data) {
  return request(`/analysis/template/update`, {
    method: 'POST',
    data,
  });
}

//新画像分析删除
export async function deleteNewInsightAnalysis(id) {
  return request(`/analysisV2/del?id=${id}`);
}

//新画像分析重跑
export async function reRunNewInsightAnalysis(id) {
  return request(`/analysisV2/rerun?id=${id}`);
}

//经纬度数据
export async function queryAnalysisDimInfo(params) {
  return request(`/analysis/dim/listProfileDimInfo`, {
    method: 'GET',
    params,
  });
}

//公共模版删除
export async function deleteAnalysisTemplate(id) {
  return request(`/analysis/template/deleteById?id=${id}`, {
    method: 'POST',
  });
}

//新增模版分析
export async function createAnalysisTemplateAnalysis(data) {
  return request(`/analysis/template/analysis`, {
    method: 'POST',
    data,
  });
}

//人群血缘关系下载
export async function downloadCrowdBlood(parmas) {
  return request(`/crowd_blood_relation/download_blood_relation_excel?crowdId=${parmas.crowdId}`, {
    responseType: 'blob',
  });
}

//人群血缘关系人群查询
export async function queryCrowdBloodByKeyword(parmas) {
  return request(`/crowd_blood_relation/query_crowd_by_keyword?keyword=${parmas.keyword}`);
}


//人群血缘关系
export async function queryCrowdBlood(parmas) {
  return request(`/crowd_blood_relation/query_crowd_blood_relation?crowdId=${parmas.crowdId}`);
}

//人群血缘关系表格
export async function queryCrowdBloodTable(parmas) {
  return request(`${API_ROOT}/crowd/blood/table?${stringify(parmas)}`);
}

// 标签对账相关接口

/**
 * 创建或更新标签对账脚本配置
 * @param {*} data 参数
 */
export async function createOrUpdateLabelCheckScript(data) {
  return request(`/groovy/script/createOrUpdate`, {
    method: 'POST',
    data,
  });
}

/**
 * 测试标签对账脚本执行
 * @param {*} data 参数
 */
export async function testLabelCheckScript(data) {
  return request(`/groovy/script/test`, {
    method: 'POST',
    data,
  });
}

/**
 * 启用或禁用标签对账脚本
 * @param {*} params 参数
 */
export async function enableLabelCheckScript(params) {
  return request(`/groovy/script/enable?${stringify(params)}`, {
    method: 'POST',
  });
}

/**
 * 删除标签对账脚本配置
 * @param {*} params 参数
 */
export async function deleteLabelCheckScript(params) {
  return request(`/groovy/script/delete?${stringify(params)}`, {
    method: 'POST',
  });
}

/**
 * 获取标签对账脚本配置
 * @param {*} params 参数
 */
export async function getLabelCheckScript(params) {
  return request(`/groovy/script/get?${stringify(params)}`);
}

/**
 * 分页查询标签对账脚本配置列表
 * @param {*} params 参数
 */
export async function queryLabelCheckScriptList(params) {
  return request(`/groovy/script/list?${stringify(params)}`);
}

/**
 * 获取标签对账脚本执行统计信息
 */
export async function getLabelCheckScriptStats() {
  return request(`/groovy/script/stats`);
}

/**
 * 查询对账结果列表
 * @param {*} params 查询参数
 */
export async function queryLabelCheckResults(params) {
  return request(`/groovy/script/userResults?${stringify(params)}`, {
    method: 'GET',
  });
}

/**
 * 根据对账结果表ID重试脚本执行
 * @param {*} recordId 对账结果表记录ID
 */
export async function retryScriptExecutionById(recordId) {
  return request(`/groovy/script/retryById?recordId=${recordId}`, {
    method: 'POST',
  });
}

export async function labelMarketDetailEnumMonitor(data) {
  const { labelCode, dimEnumId, enumCode} = data
  return request(`/label_market/detail/enum_monitor?labelCode=${labelCode}&dimEnumId=${dimEnumId}&enumCode=${enumCode}`, {
    method: 'POST',
    data,
  });
};

// 注册表管理相关接口

/**
 * 注册表管理 - 分页查询列表
 */
export async function queryOdpsRegisterProfileTableList(params) {
  return request(`/odps/table/list?${stringify(params)}`, {
    method: 'GET',
  });
}

/**
 * 注册表管理 - 查找单个注册表
 */
export async function findOdpsRegisterProfileTable(params) {
  return request(`/odps/table/find?${stringify(params)}`, {
    method: 'GET',
  });
}

/**
 * 注册表管理 - 创建注册表
 */
export async function createOdpsRegisterProfileTable(params) {
  return request(`/odps/table/create?${stringify(params)}`, {
    method: 'POST',
  });
}

/**
 * 注册表管理 - 更新注册表 (使用updateSelective接口)
 */
export async function updateOdpsRegisterProfileTable(params) {
  return request(`/odps/table/update?${stringify(params)}`, {
    method: 'POST',
  });
}


