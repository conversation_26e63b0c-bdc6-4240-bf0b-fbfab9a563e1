// 策略层接口@唤酒
import request from '@/utils/wrapRequest';
import rawRequest from '@/utils/request';
import { get } from 'lodash';
// import { csrfRequest as request } from '@/utils/request';
/**
 * 日常：https://zhuge.fliggy.test/
 * 预发：https://pre-zhuge.fliggy.com/
 * 线上：https://zhuge.fliggy.com/
 */

const { hostname } = window.location;

let host = 'https://zhuge.fliggy.com';

if (hostname.includes('.net') || hostname.includes('localhost')) {
  host = 'https://pre-zhuge.fliggy.com';
} else if (hostname.includes('pre')) {
  host = 'https://pre-zhuge.fliggy.com';
}

const whiteList = [
  'fliggy.alibaba.net',
  'pre-fliggy.alibaba-inc.com',
  'fliggy.alibaba-inc.com',
];
// 这里要做区分
const baseKey = '/zhuge-fliggy'; // 此处和父应用server config里面配置保持一致
const currentHost = get(
  whiteList.filter(_ => window.location.href.indexOf(_) > -1),
  '[0]',
  null,
)

if (currentHost) {
  host = `//${currentHost}${baseKey}`;
}

console.log('currentHost= >>>>', currentHost)


export async function queryUser() {
  return request(`${host}/seller/getUser`);
}

/**
 * 批量插入商家
 * @param {*} data
 */
export async function batchAddMerchants(data) {
  return request(`${host}/seller/batchInsert`, {
    method: 'POST',
    data,
    requestType: 'form',
  });
}

/**
 * 模糊搜索
 * @param {*} sellerName
 */
export async function queryMerchantByName(sellerName) {
  return request(`${host}/seller/list`, {
    method: 'post',
    data: {
      sellerName
    }
  });
}

export async function deleteMerchants(sellerId) {
  return request(`${host}/seller/delete?id=${sellerId}`);
}

/**
 * 分页查询商家
 * @param {*} params
 */
export async function queryMerchants(data) {
  return new Promise((resolve, reject) => {
    request(`${host}/seller/list`, {
      method: 'POST',
      data,
    }).then((res = {}) => {
      const {
        data: resData = []
      } = res;
      const requestQueue = resData.map(item => {
        return hasCrowed(item.sellerId);
      })
      Promise.all(requestQueue)
        .then(resArr => {
          const end = resData.map((item, i) => {
            return {
              ...item,
              hasCrowd: resArr[i].data
            }
          });
          resolve({
            ...res,
            data: end
          });
        })
        .catch(ex => {
          reject(ex);
        })
    })
  });
}

/**
 * 判断是否商家有人群包
 * @param {} sellerId
 */
export async function hasCrowed(sellerId) {
  return request(`${host}/seller/hasCrowd?sellerId=${sellerId.toString()}`);
}

/**
 * 获取标签属性的枚举值
 * @param {*} data
 */
export async function queryTagAttrs(data) {
  return request(`${host}/seller/crowd/getTagAttrs`, {
    method: 'POST',
    data,
    requestType: 'form',
  });
}

/**
 * 获取标签总类枚举
 * @param {*} data
 */
export async function queryTagCategories(data) {
  return request(`${host}/seller/crowd/getTagCategories`, {
    method: 'POST',
    data,
  });
}

/**
 * 商家批量圈人
 * @param {*} data
 */
export async function batchAddCrowdByMerchant(data) {
  return request(`${host}/seller/crowd/batchCreate`, {
    method: 'POST',
    data,
  });
}

/**
 * 查询商家人群
 * @param {*} data
 */
export async function queryMerchantCrowd(data) {
  return request(`${host}/seller/crowd/list`, {
    method: 'POST',
    data,
  });
}

/**
 * 查询单个商家
 * @param {*} data
 */
export async function queryMerchantById(data) {
  return request(`${host}/seller/get`, {
    method: 'POST',
    requestType: 'form',
    data,
  });
}

// 删除商家圈人
export async function removeMerchantCrowd(data) {
  return request(`${host}/seller/crowd/delete`, {
    method: 'POST',
    requestType: 'form',
    data,
  });
}

// 单个商家圈人更新接口
export async function updateMerchantCrowd(data) {
  return request(`${host}/seller/crowd/update`, {
    method: 'POST',
    data,
  });
}

// 圈人名称校验接口
export async function hasMerchantCrowdName(crowdName) {
  return request(`${host}/seller/crowd/hasCrowdName?crowdName=${crowdName}`, {
    method: 'GET',
  });
}

/**
 * 通过商家Id查询单个商家
 * @param {*} data
 */
export async function queryMerchantByMerchantId(data) {
  return request(`${host}/seller/getBySellerId`, {
    method: 'POST',
    requestType: 'form',
    data,
  });
}

/**
 * 主题suggest类目树
 * @param {*} data
 */
export async function queryThemeSuggestCategory(data) {
  return request(`${host}/theme/suggest/categoryTree`, {
    method: 'POST',
    data,
  });
}

/**
 * 主题suggest城市
 * @param {*} data
 */
export async function queryThemeSuggestCity(data) {
  return request(`${host}/theme/suggest/city`, {
    method: 'POST',
    data,
  });
}

/**
 * 主题suggest本地poi
 * @param {*} data
 */
export async function queryThemeSuggestNativePoi(data) {
  return request(`${host}/theme/suggest/nativePoi`, {
    method: 'POST',
    data,
  });
}

/**
 * 主题suggest玩法标签
 * @param {*} data
 */
export async function queryThemeSuggestPlayTag(data) {
  return request(`${host}/theme/suggest/playTag`, {
    method: 'POST',
    data,
  });
}

/**
 * 主题suggest poi
 * @param {*} data
 */
export async function queryThemeSuggestPoi(data) {
  return request(`${host}/theme/suggest/poi`, {
    method: 'POST',
    data,
  });
}

export async function createThemeInsightTask(data) {
  return request(`${host}/theme/task/create`, {
    method: 'POST',
    data,
  });
}

export async function updateThemeInsightTask(data) {
  return request(`${host}/theme/task/update`, {
    method: 'POST',
    data,
  });
}

export async function getThemeInsightTaskList(data) {
  return request(`${host}/theme/task/list`, {
    method: 'POST',
    data,
  });
}

/**
 * 获取单个id
 * @param {} id
 */
export async function getThemeInsightTask(id) {
  return request(`${host}/theme/task/get?id=${id}`);
}

export async function submitThemeInsightTask(data) {
  return request(`${host}/theme/task/submit?id=${data.id}`);
}

export async function deleteThemeInsightTask(id) {
  return request(`${host}/theme/task/delete?id=${id}`);
}

export async function queryThemeInsightDetailList(data) {
  return request(`${host}/theme/task/item/list`, {
    method: 'POST',
    data,
  })
}
let kgHost = 'poiadmin.fliggy.com';

if (hostname.includes('.net') || hostname.includes('localhost') || hostname.includes('pre')) {
  kgHost = 'pre-poiadmin.fliggy.com';
}
/**
 * 通过名字获取知识图谱
 * @param {*} param0
 */
export async function queryKnowledgeByName({ name, type = 1, pageNo, pageSize }) {
  return new Promise(resolve => {
    const url = `https://${kgHost}/knowledge/graph/query/entityName?entityType=${type || '1'}&entityName=${encodeURIComponent(name)}&pageNo=${pageNo}&pageSize=${pageSize}`;
    rawRequest(url).then(resolve);
    setTimeout(() => {
      resolve({ success: false, message: '接口请求失败或超时，请联系开发人员，请求url请查看控制台！' });
      console.error('请求失败url: ', url);
    }, 5000);
  });
}

export async function queryKnowledgeById({ id, type }) {
  return new Promise(resolve => {
    const url = `https://${kgHost}/knowledge/graph/query/entityId?entityType=${type}&entityId=${id}`;
    rawRequest(url).then(resolve);
    setTimeout(() => {
      resolve({ success: false, message: '接口请求失败或超时，请联系开发人员，请求url请查看控制台！' });
      console.error('请求失败url: ', url);
    }, 5000);
  });
}

export async function queryKnowledgeTypeEnum() {
  return rawRequest(`https://${kgHost}/knowledge/graph/query/entities`);
}
