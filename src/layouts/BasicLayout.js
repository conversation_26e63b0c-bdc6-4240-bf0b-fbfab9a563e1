import React from 'react';
import { Layout, notification } from 'antd';
import DocumentTitle from 'react-document-title';
import SimpleAEM from '@ali/simple-aem';
import { connect } from 'dva';
import { ContainerQuery } from 'react-container-query';
import classNames from 'classnames';
import SiderMenu from '@/components/SiderMenu';
import getPageTitle from '@/utils/getPageTitle';
import Media from 'react-media';
import Footer from './Footer';
import Header from './Header';
import Context from './MenuContext';
import styles from './BasicLayout.less';
import { hideSideBar } from '../utils/nav';
import { getUrlParam, getNowFormatDate } from '../utils/utils';
import { globalNotification } from '@/services/api';
import aiAssistant from '@/assets/aiAssistant.png';

// 初始化AEM监控
if (window.location.hostname === 'zhuge.alibaba-inc.com') {
  // 由于涉及到有子应用自己需要独立运行，非重定向到运营工作台，这里应用域名为自己应用的原始域名
  const aem = new SimpleAEM({ pid: 'zhuge', is_spa: true }); // is_spa 指是否为hash类型的单页面应用
  aem.init();
}

const aiStudioChatConfig = {
  appCode: 'eOQCbasrDgV', // 发布的appCode
  title: '诸葛AI智能助手', // 弹窗标题
  showType: 'chat',
  icon: 'https://idealab-platform.oss-accelerate.aliyuncs.com/20250417/4ca7b8c0-ce47-4d2c-8128-0603ba21f87f_zgl-pic.png?Expires=4102329600&amp;OSSAccessKeyId=LTAI5tFJF3QLwHzEmkhLs9dB&amp;Signature=7VSJxsg89%2F2o7eKlcYsJekfDo4I%3D',
  robotConfig: {
    iconSrc: aiAssistant,
  }
};

const { Content } = Layout;
const logoPath = 'https://gw.alicdn.com/tfs/TB1B0X8p.H1gK0jSZSyXXXtlpXa-200-200.png';
const query = {
  'screen-xs': {
    maxWidth: 575,
  },
  'screen-sm': {
    minWidth: 576,
    maxWidth: 767,
  },
  'screen-md': {
    minWidth: 768,
    maxWidth: 991,
  },
  'screen-lg': {
    minWidth: 992,
    maxWidth: 1199,
  },
  'screen-xl': {
    minWidth: 1200,
    maxWidth: 1599,
  },
  'screen-xxl': {
    minWidth: 1600,
  },
};

class BasicLayout extends React.Component {
  constructor(props) {
    super(props);
    const isAiAssistantPage = window.location.hash?.includes('ai-assistant');
    this.state = {
      isInitialized: false,
      aiStudioChat: isAiAssistantPage ? null : window.AiStudioChat.init(aiStudioChatConfig),
    };
  }
  componentDidMount() {
    const {
      dispatch,
      route: { routes, path, authority },
    } = this.props;
    Promise.all([
      dispatch({
        type: 'user/fetchCurrent',
      }),
      dispatch({
        type: 'user/fetchSuperAdmin',
      }),
    ]).then(() => {
      // 在用户信息获取完成后再获取菜单数据
      dispatch({
        type: 'menu/getMenuData',
        payload: { routes, path, authority },
      });
    });
    this.openNotification();
  }

  componentDidUpdate(prevProps) {
    this.checkAndManageRobot();

    // 如果用户的超级管理员状态发生变化，重新获取菜单数据
    const prevUser = prevProps.user || {};
    const currentUser = this.props.user || {};
    if (prevUser.isSuperAdmin !== currentUser.isSuperAdmin) {
      const {
        dispatch,
        route: { routes, path, authority },
      } = this.props;
      dispatch({
        type: 'menu/getMenuData',
        payload: { routes, path, authority },
      });
    }
  }

  checkAndManageRobot() {
    const isAiAssistantPage = window.location.hash?.includes('ai-assistant');
    //这里是为了用户在不是Ai助手的页面刷新不初始化机器人
    if (!isAiAssistantPage && !this.state.aiStudioChat) {
      this.setState({
        aiStudioChat: window.AiStudioChat.init(aiStudioChatConfig),
      });
    }
    if (this.state.aiStudioChat) {
      if (isAiAssistantPage) {
        this.state.aiStudioChat.hideRobot();
      } else {
        this.state.aiStudioChat.showRobot();
      }
    }
  }

  getContext() {
    const { location, breadcrumbNameMap } = this.props;
    return {
      location,
      breadcrumbNameMap,
    };
  }

  getLayoutStyle = () => {
    const { fixSiderbar, isMobile, collapsed, layout } = this.props;
    if (fixSiderbar && layout !== 'topmenu' && !isMobile) {
      return {
        paddingLeft: collapsed ? '80px' : '256px',
      };
    }
    return null;
  };

  handleMenuCollapse = collapsed => {
    const { dispatch } = this.props;
    dispatch({
      type: 'global/changeLayoutCollapsed',
      payload: collapsed,
    });
  };

  openNotification = () => {
    if (!localStorage.getItem('NoticeTime')) {
      // 当没有NoticeTime时存入
      localStorage.setItem('NoticeTime', getNowFormatDate());
    } else if (
      localStorage.getItem('NoticeTime') &&
      getNowFormatDate() !== localStorage.getItem('NoticeTime')
    ) {
      localStorage.setItem('NoticeTime', getNowFormatDate());
    } else if (
      localStorage.getItem('NoticeTime') &&
      getNowFormatDate() === localStorage.getItem('NoticeTime')
    ) {
      // 同一天只弹一次
      return;
    }

    globalNotification().then(res => {
      if (res && res.data) {
        notification.open({
          message: '公告栏',
          duration: 10,
          description: res.data,
          style: {
            wordBreak: 'break-all',
          },
        });
      }
    });
  };

  render() {
    const {
      navTheme,
      layout: PropsLayout,
      children,
      location: { pathname },
      isMobile,
      menuData,
      breadcrumbNameMap,
      fixedHeader,
    } = this.props;

    // 标签市场的样式单独修改
    const isLabelMarket = window.location.href?.includes('label-market');
    const isTop = PropsLayout === 'topmenu';
    const contentStyle = !fixedHeader || getUrlParam('isFrame') ? { paddingTop: 0 } : { paddingTop: 64 };
    const layout = (
      <Layout>
        {(isTop && !isMobile) || hideSideBar || getUrlParam('isFrame') ? null : (
          <SiderMenu
            logo={logoPath}
            theme={navTheme}
            onCollapse={this.handleMenuCollapse}
            menuData={menuData}
            isMobile={isMobile}
            {...this.props}
          />
        )}
        <Layout
          style={{
            minHeight: '100vh',
            ...this.getLayoutStyle(),
          }}
        >
          {hideSideBar || getUrlParam('isFrame') ? null : (
            <Header
              menuData={menuData}
              handleMenuCollapse={this.handleMenuCollapse}
              logo={logoPath}
              isMobile={isMobile}
              {...this.props}
            />
          )}
          <Content
            className={classNames(styles.content, isLabelMarket && styles.marketContent)}
            style={{
                ...contentStyle,
              }
            }
          >
            {children}
          </Content>
          {hideSideBar || getUrlParam('isFrame') ? null : <Footer />}
        </Layout>
      </Layout>
    );
    return (
      <React.Fragment>
        <DocumentTitle title={getPageTitle(pathname, breadcrumbNameMap)}>
          <ContainerQuery query={query}>
            {params => (
              <Context.Provider value={this.getContext()}>
                <div className={classNames(params)}>{layout}</div>
              </Context.Provider>
            )}
          </ContainerQuery>
        </DocumentTitle>
      </React.Fragment>
    );
  }
}

export default connect(({ global, setting, menu: menuModel, user }) => ({
  collapsed: global.collapsed,
  layout: setting.layout,
  menuData: menuModel.menuData,
  breadcrumbNameMap: menuModel.breadcrumbNameMap,
  user,
  ...setting,
}))(props => (
  <Media query="(max-width: 599px)">
    {isMobile => <BasicLayout {...props} isMobile={isMobile} />}
  </Media>
));
