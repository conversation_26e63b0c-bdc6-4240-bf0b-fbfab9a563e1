export const CROWD_TYPE = {
  LABEL_CROWD: '标签圈人',
  ALGO_CROWD: '诸葛池圈人',
  FILE_CROWD: '文件上传',
  ALGO_ENLARGE_CROWD: '人群放大',
  OPERATE_CROWD: '人群运算',
  TRIP_GALAXY_CROWD: '星辰池圈人',
  ODPS_TABLE_CROWD: 'ODPS导入',
  ODPS_SQL_CROWD: 'SQL圈人',
  TRANSITION_CROWD: '跃迁圈人',
  BUCKET_CHILD: '实验分组-子人群'
};


// 预计运行耗时
// 标签圈人：预计10分钟
// 以货圈人：预计1小时
// 放大圈人：预计5小时
// ODPS导入：预计30分钟
// 文件上传：预计30分钟
// ODPS运算：预计30分钟
export const CROWD_PROCESS_TIME = {
  LABEL_CROWD: '预计10分钟',
  ALGO_CROWD: '预计1小时',
  FILE_CROWD: '预计30分钟',
  ALGO_ENLARGE_CROWD: '预计5小时',
  OPERATE_CROWD: '预计30分钟',
  TRIP_GALAXY_CROWD: '预计1小时',
  ODPS_TABLE_CROWD: '预计30分钟',
  ODPS_SQL_CROWD: '预计1小时',
}

export const CROWD_USE_SCENE = {
  PUSH: 'PUSH',
  MATCH: '人群匹配',
  ANALYSIS: '人群分析',
};

export const CROWD_REAL_TIME = {
  0: '非实时人群',
  1: '实时人群',
};

export const CROWD_NEED_UPDATE = {
  0: '不需要更新',
  1: '需要更新',
};

export const CROWD_USER_CONTROL = {
  0: '未设置黑白名单',
  1: '设置了黑白名单',
};

export const CROWD_PROGRESS_STATUS = {
  INIT: {
    index: 0,
    text: '初始化',
    status: 'default',
    color: '#108ee9'
  },
  RUNNING: {
    index: 1,
    text: '创建中',
    status: 'processing',
    color: '#2db7f5'
  },
  SUCCESS: {
    index: 2,
    text: '创建成功',
    status: 'success',
    color: '#87d068'
  },
  ERROR: {
    index: 2,
    text: '创建失败',
    status: 'error',
    color: '#f50'
  },
  APPROVAL_RUNNING: {
    index: 3,
    text: '审批中',
    status: 'approvalRunning',
    color: '#2db7f5'
  },
  APPROVAL_REJECTED: {
    index: 4,
    text: '审批拒绝',
    status: 'approvalRejected',
    color: '#FF4000'
  },
  APPROVAL_CANCELLED: {
    index: 5,
    text: '审批取消',
    status: 'approvalCancelled',
    color: '#01DFD7'
  },
  APPROVAL_ERROR: {
    index: 6,
    text: '审批出错',
    status: 'approvalError',
    color: '#FF0000'
  },
  APPROVAL_SUCCESS: {
    index: 7,
    text: '审批通过',
    status: 'approvalSuccess',
    color: '#87d068'
  },
  DRAFT: {
    index: 7,
    text: '草稿',
    status: 'draft',
    color: 'default'
  }
};

export const EXPORT_TASK_STATUS = {
  INIT: { text: '初始化', status: 'default' },
  RUNNING: { text: '进行中', status: 'processing' },
  SUCCESS: { text: '导出成功', status: 'success' },
  FAILED: { text: '导出失败', status: 'error' },
  APPROVAL_RUNNING: { text: '审批中', status: '#8181F7' },
  APPROVAL_REJECTED: { text: '审批拒绝', status: '#FF4000' },
  APPROVAL_CANCELLED: { text: '审批取消', status: '#01DFD7' },
  APPROVAL_SUCCESS: { text: '审批通过', status: '#87d068' },
  APPROVAL_ERROR: { text: '审批出错', status: '#FF0000' }
};

export const EXPORT_SYNC_STATUS = {
  SYNCING: { text: '同步中', status: 'processing' },
  SUCCESS: { text: '同步成功', status: 'success' },
  FAILED: { text: '同步失败', status: 'error' },
  EXPIRED: { text: '已过期', status: 'default' },
};


export const CROWD_STATUS = {
  INIT: '初始化',
  RUNNING: '创建中',
  SUCCESS: '成功',
  ERROR: '失败',
}

export const POPULATION_TYPE = {
  // '': '全部',
  'tbup': '淘宝id',
  'scrm_up': 'Oneid',
  'device_p': '设备id',
}


export const OPERATION_STATUS = {
  INIT: {
    text: '初始化',
    status: 'default',
    color: '#108ee9'
  },
  SUCCESS: {
    text: '成功',
    status: 'success',
    color: '#87d068'
  },
  FAILED: {
    text: '失败',
    status: 'failed',
    color: '#f50'
  },
  RUNNING: {
    text: '执行中',
    status: 'processing',
    color: '#2db7f5'
  },
  WAITING: {
    text: '待运行',
    status: 'waiting',
    color: '#d3d3d3'
  },
  APPROVAL_RUNNING: {
    text: '审批中',
    status: 'approvalRunning',
    color: '#8181F7'
  },
  APPROVAL_REJECTED: {
    text: '审批拒绝',
    status: 'approvalRejected',
    color: '#FF4000'
  },
  APPROVAL_CANCELLED: {
    text: '审批取消',
    status: 'approvalCancelled',
    color: '#01DFD7'
  },
  APPROVAL_ERROR: {
    text: '审批出错',
    status: 'approvalError',
    color: '#FF0000'
  },
  APPROVAL_SUCCESS: {
    text: '审批通过',
    status: 'approvalSuccess',
    color: '#87d068'
  },
};

export const CROWD_APPLE_SCENNE = {
  PUSH: 'PUSH',
  MATCH: '人群匹配',
  ANALYSIS: '人群分析',
}


export const OFFLINE_ODPS_STATUS = {
  INIT: { text: '初始化', status: 'default' },
  RUNNING: { text: '运行中', status: 'processing' },
  SUCCESS: { text: '成功', status: 'success' },
  FAILED: { text: '失败', status: 'error' },
};