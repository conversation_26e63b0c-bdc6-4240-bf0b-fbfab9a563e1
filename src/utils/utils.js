import dayjs from 'dayjs';
import React from 'react';
import nzh from 'nzh/cn';
import { parse, stringify } from 'qs';

/**
 * 解析ODPS表名，支持两段式和三段式格式
 * 两段式：project.table
 * 三段式：odps.project.table (第一段为环境标识)
 */
export function parseOdpsTableName(sourceGuid) {
  if (!sourceGuid) {
    return { project: '', table: '' };
  }
  
  const parts = sourceGuid.split('.');
  
  if (parts.length === 2) {
    // 两段式：project.table
    return {
      project: parts[0],
      table: parts[1]
    };
  } else if (parts.length === 3) {
    // 三段式：odps.project.table (忽略第一段环境标识)
    return {
      project: parts[1],
      table: parts[2]
    };
  } else {
    // 其他格式，返回原始逻辑
    return {
      project: parts[0] || '',
      table: parts[1] || ''
    };
  }
}

export function fixedZero(val) {
  return val * 1 < 10 ? `0${val}` : val;
}

export function getTimeDistance(type) {
  const now = new Date();
  const oneDay = 1000 * 60 * 60 * 24;

  if (type === 'today') {
    now.setHours(0);
    now.setMinutes(0);
    now.setSeconds(0);
    return [dayjs(now), dayjs(now.getTime() + (oneDay - 1000))];
  }

  if (type === 'week') {
    let day = now.getDay();
    now.setHours(0);
    now.setMinutes(0);
    now.setSeconds(0);

    if (day === 0) {
      day = 6;
    } else {
      day -= 1;
    }

    const beginTime = now.getTime() - day * oneDay;

    return [dayjs(beginTime), dayjs(beginTime + (7 * oneDay - 1000))];
  }

  if (type === 'month') {
    const year = now.getFullYear();
    const month = now.getMonth();
    const nextDate = dayjs(now).add(1, 'months');
    const nextYear = nextDate.year();
    const nextMonth = nextDate.month();

    return [
      dayjs(`${year}-${fixedZero(month + 1)}-01 00:00:00`),
      dayjs(dayjs(`${nextYear}-${fixedZero(nextMonth + 1)}-01 00:00:00`).valueOf() - 1000),
    ];
  }

  const year = now.getFullYear();
  return [dayjs(`${year}-01-01 00:00:00`), dayjs(`${year}-12-31 23:59:59`)];
}

export function getPlainNode(nodeList, parentPath = '') {
  const arr = [];
  nodeList.forEach(node => {
    const item = node;
    item.path = `${parentPath}/${item.path || ''}`.replace(/\/+/g, '/');
    item.exact = true;
    if (item.children && !item.component) {
      arr.push(...getPlainNode(item.children, item.path));
    } else {
      if (item.children && item.component) {
        item.exact = false;
      }
      arr.push(item);
    }
  });
  return arr;
}

export function digitUppercase(n) {
  return nzh.toMoney(n);
}

function getRelation(str1, str2) {
  if (str1 === str2) {
    console.warn('Two path are equal!'); // eslint-disable-line
  }
  const arr1 = str1.split('/');
  const arr2 = str2.split('/');
  if (arr2.every((item, index) => item === arr1[index])) {
    return 1;
  }
  if (arr1.every((item, index) => item === arr2[index])) {
    return 2;
  }
  return 3;
}

function getRenderArr(routes) {
  let renderArr = [];
  renderArr.push(routes[0]);
  for (let i = 1; i < routes.length; i += 1) {
    // 去重
    renderArr = renderArr.filter(item => getRelation(item, routes[i]) !== 1);
    // 是否包含
    const isAdd = renderArr.every(item => getRelation(item, routes[i]) === 3);
    if (isAdd) {
      renderArr.push(routes[i]);
    }
  }
  return renderArr;
}

/**
 * Get router routing configuration
 * { path:{name,...param}}=>Array<{name,path ...param}>
 * @param {string} path
 * @param {routerData} routerData
 */
export function getRoutes(path, routerData) {
  let routes = Object.keys(routerData).filter(
    routePath => routePath.indexOf(path) === 0 && routePath !== path
  );
  // Replace path to '' eg. path='user' /user/name => name
  routes = routes.map(item => item.replace(path, ''));
  // Get the route to be rendered to remove the deep rendering
  const renderArr = getRenderArr(routes);
  // Conversion and stitching parameters
  const renderRoutes = renderArr.map(item => {
    const exact = !routes.some(route => route !== item && getRelation(route, item) === 1);
    return {
      exact,
      ...routerData[`${path}${item}`],
      key: `${path}${item}`,
      path: `${path}${item}`,
    };
  });
  return renderRoutes;
}

export function getPageQuery() {
  return parse(window.location.href.split('?')[1]);
}

export function getQueryPath(path = '', query = {}) {
  const search = stringify(query);
  if (search.length) {
    return `${path}?${search}`;
  }
  return path;
}

/* eslint no-useless-escape:0 */
const reg = /(((^https?:(?:\/\/)?)(?:[-;:&=\+\$,\w]+@)?[A-Za-z0-9.-]+(?::\d+)?|(?:www.|[-;:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)#?(?:[\w]*))?)$/;

export function isUrl(path) {
  return reg.test(path);
}

export function formatWan(val) {
  const v = val * 1;
  if (!v) return '';

  let result = val;
  if (val > 10000) {
    result = Math.floor(val / 10000);
    result = (
      <span>
        {result}
        <span
          style={{
            position: 'relative',
            top: -2,
            fontSize: 14,
            fontStyle: 'normal',
            marginLeft: 2,
          }}
        >
          万
        </span>
      </span>
    );
  }
  return result;
}

// 给官方演示站点用，用于关闭真实开发环境不需要使用的特性
export function isAntdPro() {
  return window.location.hostname === 'preview.pro.ant.design';
}

export const importCDN = (url, name) =>
  new Promise(resolve => {
    const dom = document.createElement('script');
    dom.src = url;
    dom.type = 'text/javascript';
    dom.onload = () => {
      resolve(window[name]);
    };
    document.head.appendChild(dom);
  });

export function getHtmlParseValue(value) {
  if (!value) return null;
  const htmlParser = new DOMParser();
  const parseDom = htmlParser.parseFromString(value, 'text/html');
  let options;
  const text = parseDom.body.textContent;
  if (
    /^[\],:{}\s]*$/.test(
      text
        .replace(/\\["\\\/bfnrtu]/g, '@')
        .replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g, ']')
        .replace(/(?:^|:|,)(?:\s*\[)+/g, '')
    )
  ) {
    options = text && JSON.parse(text);
  } else {
    options = text && JSON.parse(text);
  }

  return options;
}


export const getUrlParam = par => {
  const reg = new RegExp('(^|&)' + par + '=([^&]*)(&|$)');
  const result = window.location.search.substr(1).match(reg);
  if (result != null) {
    return result[2];
  }
  return false;
};

export const OverTimeType = (type) => {
  switch (type) {
    case 'USER_DEFINED':
      return 'short'
    case 'NOWADAYS':
      return 'today'
    case 'NEVER_EXPIRED':
      return 'long'
    default:
      return 'short'
  }
}


export const EnumerationType = (type) => {
  switch (type) {
    case 'CUSTOMIZE':
      return '逻辑定义'
    case 'ODPS':
      return 'ODPS表'
    case 'SYSTEM':
      return '系统自动生成'
    default:
      return '暂无'
  }
}


// 当前环境是否是CRM的预发环境。
export const CRMUrl =
  window.location.host.indexOf('pre-') > -1
    ? 'https://pre-magic.alibaba-inc.com/'
    : 'https://magic-plan.alibaba-inc.com/';


// 洞察分析生成人群关闭标签跳转
export const zhugeUrl =
window.location.host.indexOf('pre-') > -1
  ? 'https://pre-zhuge.alibaba-inc.com/#/zhuge'
  : window.location.host.indexOf('localhost') > -1 
  ? 'http://localhost:6001/#/zhuge'
  : 'https://zhuge.alibaba-inc.com/#/zhuge';



// 获取当前年月日
export function getNowFormatDate() {
  let date = new Date(),
    seperator1 = '-', //格式分隔符
    year = date.getFullYear(), //获取完整的年份(4位)
    month = date.getMonth() + 1, //获取当前月份(0-11,0代表1月)
    strDate = date.getDate() // 获取当前日(1-31)
  if (month >= 1 && month <= 9) month = '0' + month // 如果月份是个位数，在前面补0
  if (strDate >= 0 && strDate <= 9) strDate = '0' + strDate // 如果日是个位数，在前面补0
 
  let currentdate = year + seperator1 + month + seperator1 + strDate 
  return currentdate
}


// 获取审批链接
export const getBpmsUrl = (procInsId) => {
  const { hostname } = window.location;

  let host = `https://bpms.alibaba-inc.com/workdesk/instDetailLegao?__id=inist_detail&procInsId=${procInsId}`;
  if (hostname.includes('.net') || hostname.includes('localhost')) {
    host = `https://pre-bpms.alibaba-inc.com/workdesk/instDetailLegao?__id=inist_detail&procInsId=${procInsId}`;
  } else if (hostname.includes('pre')) {
    host = `https://pre-bpms.alibaba-inc.com/workdesk/instDetailLegao?__id=inist_detail&procInsId=${procInsId}`;
  }

  return host
}

export const transformTree = (tree = []) => {
  return tree.map(node => {
    const transformedNode = {};
    if (node.id !== undefined) {
      transformedNode.key = node.id.toString();
    }
    if (node.name !== undefined) {
      transformedNode.title = node.name;
    }
    if (node.children && Array.isArray(node.children)) {
      transformedNode.children = transformTree(node.children);
    }
    return transformedNode;
  });
};

// 获取ULR中的参数
export const getUrlParams = (str) => {
  let results = {},
    hash;
  const loc = str || window.location.href;
  
  // 处理hash路由，优先从hash部分获取参数
  let queryString = '';
  if (loc.includes('#')) {
    const hashPart = loc.split('#')[1];
    if (hashPart && hashPart.includes('?')) {
      queryString = hashPart.split('?')[1];
    }
  }
  
  // 如果hash部分没有参数，再从主URL获取
  if (!queryString && loc.includes('?')) {
    queryString = loc.split('?')[1];
    if (queryString.includes('#')) {
      queryString = queryString.split('#')[0];
    }
  }
  
  if (!queryString) {
    return {};
  }
  
  // 常见的埋点参数，避免干扰业务逻辑
  const trackingParams = new Set(['spm', 'scm', 'from', 'utm_source', 'utm_medium', 'utm_campaign', 'utm_content', 'utm_term']);
  
  let params = queryString.split('&');
  for (let i = 0; i < params.length; i++) {
    hash = params[i].split('=');
    if (hash[0] && hash[1] !== undefined) {
      // 过滤掉不需要的埋点参数
      if (!trackingParams.has(hash[0])) {
        results[hash[0]] = decodeURIComponent(hash[1]);
      }
    }
  }
  return results;
};