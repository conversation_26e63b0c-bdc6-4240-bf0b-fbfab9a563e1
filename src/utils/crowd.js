import { message } from "antd";
import { setTagParams, setTimeSpaceLabelParams } from '@/pages/GatherPerson/GpByTag/common/utils'

const OPERATOR_MAP_REVERSE = {
  AND: 'and',
  OR: 'or',
  NOTIN: '-',
};

export function transform(conditions) {
  const { expression, group } = conditions;
  const outerExpression = {};

  if (expression && group && group.length > 0) {
    const arr = expression.split(' ');

    // 分离出变量和运算符
    let c = 0;
    for (let i = 0; i < arr.length; i += 2) {
      outerExpression[c] = i > 0 ? OPERATOR_MAP_REVERSE[arr[i - 1]] : 'and';
      c += 1;
    }

    const tagGroups = group.map((g, index) => {
      const { label, profileType } = g;
      const innerExpression = {};
      const a = g.expression.split(' ');

      let d = 0;
      for (let j = 0; j < a.length; j += 2) {
        innerExpression[d] = j > 0 ? OPERATOR_MAP_REVERSE[a[j - 1]] : 'and';
        d += 1;
      }

      const selectedTags = label.map((l, i) => {
        const { name, desc = '', type, value, dimEnumMetaId } = l;
        let values;
        let editFormData;
        let labelParamsData // 时空标签
        if (type === 'NONE_TYPE') {
          const { values, editFormData, params } = setTagParams(l.params)
          return {
            propertyName: name,
            propertyDescription: desc,
            dataType: type,
            dimEnumMetaId, // 由于服务端失误，同一意义变量取了不同名字，由于逻辑极其复杂，各种接口名字都有可能是不同的，所以此处都需要
            dimMetaId: dimEnumMetaId,
            values,
            operator: innerExpression[i],
            editFormData,
            sqlParams: params,
          };
        } else {
          if (type === 'DATE') {
            const [startDate, endDate] = value;
  
            if (value.length > 1) {
              values = [
                { label: `开始时间:${startDate}`, value: startDate },
                { label: `结束时间:${endDate}`, value: endDate },
              ];
              editFormData = {
                dateType: 'fixed',
                startDate,
                endDate,
              };
            } else if (String(value).indexOf('-') > -1) {
              values = [{ label: `最近${String(value).split('-')[1]}天`, value: value[0] }];
              editFormData = {
                dateType: 'relativePast',
                pastTime: String(value).split('-')[1],
              };
            } else {
              values = [{ label: `未来${value[0]}天`, value: value[0] }];
              editFormData = {
                dateType: 'relativeFuture',
                futureTime: parseInt(value[0], 10),
              };
            }
          } else if (type === 'ENUM' || type === 'MULTI_VALUE') {
            // 返回的数据有问题 iphone OS iphone OS 这种形式展示就会出错，所以得做处理

            // values = value.map(item => {
            //   const [v, t] = item.split(' ');
            //   return { label: t, value: v };
            // });
            values = value.map(item => {
              let temp = item.split(' ')
              const idx = temp.length / 2
              if (Math.floor(idx) === idx) {
                const v = temp.slice(0,idx).join(' ')
                const t = temp.slice(idx).join(' ')
                return { label: t, value: v }
              } else {
                message.error('数据有问题')
                const v = temp[0]
                const t = temp.slice(1).join(' ')
                return { label: t, value: v }
              }
            })
            editFormData = {
              checkedList: (values || []).map(item => item.value),
            };
          } else if (type === 'NUMBER') {
            const [min, max] = value;
            values = [
              { label: `最小值:${min}`, value: min },
              { label: `最大值:${max}`, value: max },
            ];
            editFormData = {
              numberValue: { min: parseInt(min, 10), max: parseInt(max, 10) },
            };
          } else if (type === 'KV') {
            if (!value) return;
            editFormData = {
              targets: value.map(item => {
                const [va, la] = item.split(' ');
                return {
                  label: la,
                  value: va,
                };
              }),
            };
  
            values = value.map(item => {
              const [va, la, min, max] = item.split(' ');
              editFormData[`${va}_weight`] = [parseFloat(min), parseFloat(max)];
              return {
                label: `${la}：${min}~${max}`,
                value: `${va} ${la} ${min} ${max}`,
              };
            });
          } else if (type === 'JSON_OBJECT') {
            const timeSpaceLabelData = setTimeSpaceLabelParams(l.labelParams)
            values = timeSpaceLabelData.values
            editFormData = timeSpaceLabelData.editFormData
            labelParamsData = l?.labelParams || []
          }
          const labelData = {
            propertyName: name,
            propertyDescription: desc,
            dataType: type,
            dimEnumMetaId, // 由于服务端失误，同一意义变量取了不同名字，由于逻辑极其复杂，各种接口名字都有可能是不同的，所以此处都需要
            dimMetaId: dimEnumMetaId,
            values, //列表回显
            operator: innerExpression[i],
            editFormData, //弹窗表单回显
          }

          // 表示存在实时hsf自定义标签
          if (l.params) {
            const { values, editFormData, params } = setTagParams(l.params)
            Object.assign(labelData, {
              hsfValues: values,
              hsfEditFormData: editFormData,
              hsfParams: params,
              serviceParams: l?.serviceParams
            })
           }


          if (type === 'JSON_OBJECT') {
            Object.assign(labelData, {
              labelParamsData
            })
          }

          return labelData

        }

        // eslint-disable-next-line consistent-return
        
      });

      return {
        id: index + 1,
        selectedTags,
        operator: outerExpression[index],
        profileType,
      };
    });


    return tagGroups;
  }

  return [];
}

export function circleform(conditions) {
    if (!conditions.code) {
      return {}
    }
    const { code, desc = '', type, value, dimEnumMetaId } = conditions;
    // console.log('conditions--',conditions)
    let values;
    let editFormData;
    if (type === 'DATE') {
      const [startDate, endDate] = value;
      if (value.length > 1) {
        values = [
          { label: `开始时间:${startDate}`, value: startDate },
          { label: `结束时间:${endDate}`, value: endDate },
        ];
        editFormData = {
          dateType: 'fixed',
          startDate,
          endDate,
        };
      } else if ((value[0]).toString()?.substring(0, 1) === '-') {
        values = [{ label: `最近${(value[0]).toString().substring(1)}天`, value: value[0] }];
        editFormData = {
          dateType: 'relativePast',
          pastTime: parseInt((value[0]).toString().substring(1), 10),
        };
      } else {
        values = [{ label: `未来${value[0]}天`, value: value[0] }];
        editFormData = {
          dateType: 'relativeFuture',
          futureTime: parseInt(value[0], 10),
        };
      }
    } else if (type === 'ENUM' || type === 'MULTI_VALUE') {
      // 返回的数据有问题 iphone OS iphone OS 这种形式展示就会出错，所以得做处理

      // values = value.map(item => {
      //   const [v, t] = item.split(' ');
      //   return { label: t, value: v };
      // });
      values = value.map(item => {
        let temp = item.split(' ')
        const idx = temp.length / 2
        if (Math.floor(idx) === idx) {
          const v = temp.slice(0,idx).join(' ')
          const t = temp.slice(idx).join(' ')
          return { label: t, value: v }
        } else {
          message.error('数据有问题')
          const v = temp[0]
          const t = temp.slice(1).join(' ')
          return { label: t, value: v }
        }
      })
      editFormData = {
        enumValues: values,
      };
    } else if (type === 'NUMBER') {
      const [min, max] = value;
      values = [
        { label: `最小值:${min}`, value: min },
        { label: `最大值:${max}`, value: max },
      ];
      editFormData = {
        numberValue: { min: parseInt(min, 10), max: parseInt(max, 10) },
      };
    } else if (type === 'KV') {
      if (!value) return;
      editFormData = {
        targets: value.map(item => {
          const [va, la] = item.split(' ');
          return {
            label: la,
            value: va,
          };
        }),
      };

      values = value.map(item => {
        const [va, la, min, max] = item.split(' ');
        editFormData[`${va}_weight`] = [parseFloat(min), parseFloat(max)];
        return {
          label: `${la}：${min}~${max}`,
          value: `${va} ${la} ${min} ${max}`,
        };
      });
    }
    
    return {
      propertyName: code,
      propertyDescription: desc,
      dataType: type,
      dimEnumMetaId, // 由于服务端失误，同一意义变量取了不同名字，由于逻辑极其复杂，各种接口名字都有可能是不同的，所以此处都需要
      dimMetaId: dimEnumMetaId,
      values,
      editFormData,
    };
}
