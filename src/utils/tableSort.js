/**
 * 对表名列表进行智能排序
 * 将用户输入能完整匹配的结果放到最前面
 * @param {Array} tableList - 表名列表，每个元素应包含label属性
 * @param {string} userInput - 用户输入的搜索关键词
 * @returns {Array} 排序后的表名列表
 */
export function sortTablesByUserInput(tableList, userInput) {
  if (!tableList || !Array.isArray(tableList) || !userInput) {
    return tableList;
  }

  return tableList.sort((a, b) => {
    const userInputLower = userInput.toLowerCase();
    const aLabelLower = a.label.toLowerCase();
    const bLabelLower = b.label.toLowerCase();

    // 1. 检查是否完整匹配整个表名
    const aExactMatch = aLabelLower === userInputLower;
    const bExactMatch = bLabelLower === userInputLower;

    if (aExactMatch && !bExactMatch) {
      return -1;
    }
    if (bExactMatch && !aExactMatch) {
      return 1;
    }

    // 2. 检查是否以用户输入开头（完整表名级别）
    const aStartsWithInput = aLabelLower.startsWith(userInputLower);
    const bStartsWithInput = bLabelLower.startsWith(userInputLower);

    if (aStartsWithInput && !bStartsWithInput) {
      return -1;
    }
    if (bStartsWithInput && !aStartsWithInput) {
      return 1;
    }

    // 3. 将表名按.分割，检查用户输入是否能完整匹配表名的某个部分
    const aTableParts = aLabelLower.split('.');
    const bTableParts = bLabelLower.split('.');

    const aHasPartMatch = aTableParts.some(part => part === userInputLower);
    const bHasPartMatch = bTableParts.some(part => part === userInputLower);

    if (aHasPartMatch && !bHasPartMatch) {
      return -1;
    }
    if (bHasPartMatch && !aHasPartMatch) {
      return 1;
    }

    // 4. 检查表名的各个部分是否以用户输入开头
    const aPartStartsWithInput = aTableParts.some(part => part.startsWith(userInputLower));
    const bPartStartsWithInput = bTableParts.some(part => part.startsWith(userInputLower));

    if (aPartStartsWithInput && !bPartStartsWithInput) {
      return -1;
    }
    if (bPartStartsWithInput && !aPartStartsWithInput) {
      return 1;
    }

    // 5. 检查是否包含用户输入（模糊匹配）
    const aContainsInput = aLabelLower.includes(userInputLower);
    const bContainsInput = bLabelLower.includes(userInputLower);

    if (aContainsInput && !bContainsInput) {
      return -1;
    }
    if (bContainsInput && !aContainsInput) {
      return 1;
    }

    // 6. 最后按字母顺序排序
    return a.label.localeCompare(b.label);
  });
}
