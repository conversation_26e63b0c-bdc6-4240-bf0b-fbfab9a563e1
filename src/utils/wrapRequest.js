/**
 * request 网络请求工具
 * 更详细的api文档: https://bigfish.alipay.com/doc/api#request
 */
import request from './request';
import { getSsoTicket } from '../services/user';
import { get } from 'lodash';

const { hostname } = window.location;


let host = 'https://zhuge.fliggy.com';

if (hostname.includes('.net') || hostname.includes('localhost')) {
  host = 'https://pre-zhuge.fliggy.com';
} else if (hostname.includes('pre')) {
  host = 'https://pre-zhuge.fliggy.com';
}


const whiteList = [
  'fliggy.alibaba.net',
  'pre-fliggy.alibaba-inc.com',
  'fliggy.alibaba-inc.com',
];

const baseKey = '/zhuge-fliggy';
const currentHost = get(
  whiteList.filter(_ => window.location.href.indexOf(_) > -1),
  '[0]',
  null,
)

if (currentHost) {
  host = `//${currentHost}${baseKey}`;
}

export async function getCSRFtoken(ssoTicket) {
  return new Promise((resolve, reject) => {
    request(`${host}/csrf/token?SSO_TICKET=${ssoTicket}`)
      .then(res => {
        if (!res.token) {
          reject(new Error('无csrf/token'));
        }
        resolve(res.token);
      })
      .catch(ex => {
        reject(ex);
      })
  });
}

const csrfRequest = (url, data) => {
  return new Promise((resolve, reject) => {
    getSsoTicket()
      .then(ssoTicket => {
        getCSRFtoken(ssoTicket)
          .then(csrf => {
            const currentUrl = `${url}${url.indexOf('?') > 0 ? '&' : '?'}_csrf=${csrf}&SSO_TICKET=${ssoTicket}`;
            request(currentUrl, data)
              .then(res => {
                resolve(res);
              })
              .catch(ex => {
                console.error(ex.message);
                reject(ex);
              })
          })
          .catch(ex => {
            console.error(ex.message);
            reject(ex);
          })
      })
      .catch(ex => {
        console.error(ex.message);
        reject(ex);
      });
  });
}

export default csrfRequest;
