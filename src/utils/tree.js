/**
 * 自己实现递归求多叉树的深度
 * @param {*} level 深度
 * @param {*} treeData 树
 */
export const getTreeLevel = (level, treeData) => {
  let ret = level;

  if (!treeData || !treeData.length) {
    return ret;
  }

  ret += 1;

  const data = treeData.map(node => {
    if (node.children && node.children.length > 0) {
      return getTreeLevel(ret, node.children);
    }
    return ret;
  });

  return Math.max(...data);
};

/**
 * 得到父节点key
 * @param {*} key key
 * @param {*} tree 树
 */
export const getParentKey = (key, tree) => {
  let parentKey;
  // eslint-disable-next-line no-plusplus
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (node.children && node.children.length > 0) {
      const pKey = getParentKey(key, node.children);
      if (pKey) {
        parentKey = pKey;
      } else if (node.children.some(item => String(item.id) === String(key))) {
        parentKey = String(node.id);
      }
    }
  }
  return parentKey;
};

/**
 * 得到树路径
 * @param {*} k key
 * @param {*} n name
 * @param {*} t tree
 */
export function getPath(k, t) {
  const path = [0];
  const dep = (key, tree) => {
    // eslint-disable-next-line no-plusplus
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i];
      if (node.children && node.children.length > 0) {
        const parent = node.children.find(item => String(item.id) === String(key));
        if (!parent) {
          dep(key, node.children);
        } else {
          dep(node.id, t);
          path.unshift(node.id);
        }
      }
    }
  };

  dep(k, t);

  path.unshift(k);
  path.reverse();

  return path;
}

/**
 * 预处理树节点字段
 * @param {*} data
 */
export function processTreeData(data) {
  if (!data || !data.length) {
    return [];
  }

  // eslint-disable-next-line no-restricted-syntax
  for (const node of data) {
    node.title = node.content;
    node.key = String(node.id);
    node.value = String(node.id);

    if (node.children) {
      node.children = processTreeData(node.children);
    }
  }
  return data;
}

/**
 * 得到树所有叶子节点
 * @param {*} TreeNode
 */
export function getAllLeaves(TreeNode) {
  const ret = [];
  if (!TreeNode) {
    return [];
  }

  function loop(node) {
    if (!node.children || !node.children.length) {
      ret.push(node);
      return;
    }
    node.children.forEach(item => {
      loop(item);
    });
  }

  loop(TreeNode);
  return ret;
}

export function generateList(dataSource) {
  const ret = [];
  function loop(data) {
    // eslint-disable-next-line no-plusplus
    for (let i = 0; i < data.length; i++) {
      const node = data[i];
      ret.push(node);
      if (node.children) {
        loop(node.children);
      }
    }
  }

  loop(dataSource);

  return ret;
}
