/* eslint-disable no-useless-escape */
import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Button, Select, Input, DatePicker, Upload, message, Radio } from 'antd';
import dayjs from 'dayjs';

const FormItem = Form.Item;
const { Option } = Select;

export function onUploadChange(info) {
  if (info.file.status === 'done') {
    message.success(`${info.file.name} 上传成功`);
  } else if (info.file.status === 'error') {
    message.error(`${info.file.name} 上传失败`);
  }
}

function beforeUpload(file) {
  const isTxt = file.type === 'text/plain';

  if (!isTxt) {
    message.error('你需要上传txt文件');
  }

  const isLt30M = file.size / 1024 / 1024 < 30;

  if (!isLt30M) {
    message.error('文件必须小于30M');
  }

  return isTxt && isLt30M;
}

// function onChange(e) {
//   console.log(`checked = ${e.target.checked}`);
// }

export function getHtmlParseValue(value) {
  const htmlParser = new DOMParser();
  const parseDom = htmlParser.parseFromString(value, 'text/html');
  let options;
  const text = parseDom.body.textContent;
  if (
    /^[\],:{}\s]*$/.test(
      text
        .replace(/\\["\\\/bfnrtu]/g, '@')
        .replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g, ']')
        .replace(/(?:^|:|,)(?:\s*\[)+/g, '')
    )
  ) {
    options = JSON.parse(text);
  } else {
    options = JSON.parse(text);
  }

  return options;
}

export function getComponentType(i) {
  let component;

  switch (i.argType) {
    case 'radio':
      if (i.value) {
        const options = getHtmlParseValue(i.value);
        component = (
          <Radio.Group>
            {options &&
              options.length > 0 &&
              options.map(o => {
                const [val, desc] = Object.entries(o)[0];
                return (
                  <Radio value={val} key={val}>
                    {desc}
                  </Radio>
                );
              })}
          </Radio.Group>
        );
      } else {
        component = <Select placeholder="请选择"></Select>;
      }
      break;
    case 'checkbox':
      if (i.value) {
        // [{"50011949":"特价酒店/特色客栈/公寓旅馆"},{"50025707":"度假线路/签证送关/旅游服务"},{"50454031":"景点门票/演艺演出/周边游"}]
        // [{"2018_D12":"18年双12"}]
        const options = getHtmlParseValue(i.value);
        component = (
          <Select placeholder="请选择" mode="multiple">
            {options &&
              options.length > 0 &&
              options.map(o => {
                const [val, desc] = Object.entries(o)[0];
                return <Option key={val}>{desc}</Option>;
              })}
          </Select>
        );
      } else {
        component = <Select placeholder="请选择"></Select>;
      }
      break;
    case 'datePicker':
      component = <DatePicker placeholder={i.argDesc || '请选择日期'} style={{ width: '100%' }} />;
      break;
    case 'input':
      component = <Input placeholder={i.argDesc || `请输入${i.labelName}`} />;
      break;
    case 'select':
      if (i.value) {
        const options = getHtmlParseValue(i.value);
        component = (
          <Select placeholder={i.argDesc || `请选择${i.labelName}`}>
            {options &&
              options.length > 0 &&
              options.map(o => {
                const [val, desc] = Object.entries(o)[0];
                return <Option key={val}>{desc}</Option>;
              })}
          </Select>
        );
      } else {
        component = <Select placeholder="请选择"></Select>;
      }
      break;
    case 'upload':
      component = (
        <Upload
          name="file"
          action="/api/file/upload"
          beforeUpload={beforeUpload}
          onChange={onUploadChange}
        >
          <Button>点击上传商品</Button>
          <div>最多30M,txt格式,UTF-8编码,一行一个</div>
        </Upload>
      );
      break;
    default:
      component = <Input placeholder="请选择" />;
      break;
  }
  return component;
}

function hanleEvent(e) {
  if (!e || !e.fileList) {
    return e;
  }

  const { fileList } = e;
  return fileList;
}

// 根据不同表单项，创建不同的初始值
function getInitialValue(i, editFormData) {
  const { argName, argType } = i;
  const value = editFormData[argName];

  let initialValue;
  let options = [];

  switch (argType) {
    case 'datePicker':
      initialValue = dayjs(value);
      break;
    case 'upload':
      initialValue = value && [
        {
          uid: -1,
          name: value.split('/')[value.split('/').length - 1],
          status: 'done',
          url: value,
        },
      ];
      break;
    case 'select':
      // select选项为1项时自动选中
      options = getHtmlParseValue(i.value);
      if (options.length === 1) {
        const [obj] = options;
        const [val] = Object.entries(obj)[0];
        initialValue = val;
      } else {
        initialValue = value;
      }
      break;
    default:
      initialValue = value;
      break;
  }

  return initialValue;
}

export function getFormComponent(i, getFieldDecorator, editFormData) {
  const component = getComponentType(i);
  const initialValue = getInitialValue(i, editFormData);

  return (
    <FormItem label={i.labelName} key={`formItem_${i.argName}`}>
      {getFieldDecorator(i.argName, {
        valuePropName: i.argType === 'upload' ? 'fileList' : 'value',
        // 文件上传特殊处理
        getValueFromEvent: i.argType === 'upload' && hanleEvent,
        initialValue,
        rules: [{ required: true, message: `请输入${i.labelName}` }],
      })(component)}
    </FormItem>
  );
}

export function createFormItems(curAlgoModel, getFieldDecorator, editFormData) {
  if (!curAlgoModel) {
    return null;
  }

  const { inputArgs, outputArgs } = curAlgoModel;
  const inputs =
    inputArgs &&
    inputArgs.length > 0 &&
    inputArgs.map(i => getFormComponent(i, getFieldDecorator, editFormData));

  const outputs =
    outputArgs &&
    outputArgs.length > 0 &&
    outputArgs.map(i => getFormComponent(i, getFieldDecorator, editFormData));

  return (inputs || []).concat(outputs || []);
}
