import React, { PureComponent } from 'react';
import { Card, Table, Divider, Row, Col, Radio, Button } from 'antd';
import { connect } from 'dva';
import dayjs from 'dayjs';
import { Link }  from 'umi';
import 'dayjs/locale/zh-cn';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import CreateCrowdByAlgoModal from '@/components/CreateChoosePersonModal';
import { history } from 'umi';
import { stringify } from 'qs';
import { getPageQuery } from '@/utils/utils';

dayjs.locale('zh-cn');
const RadioGroup = Radio.Group;
const RadioButton = Radio.Button;

@connect(state => ({ playProduct: state.playProduct }))
class PlayProduct extends PureComponent {
  onRadioChange = e => {
    const key = e.target.value;
    const { dispatch } = this.props;

    const type = key === '1' ? 'zhuge-primary' : 'zhuge-all';
    dispatch({
      type: 'playProduct/updateSearchFormData',
      payload: {
        type,
      },
    });

    dispatch({
      type: 'playProduct/queryActivies',
      payload: {
        type,
      },
    });
  };

  onOpenCrowdByGalaxyModal = record => {
    const { dispatch } = this.props;
    dispatch({
      type: 'playProduct/onOpenCrowdByGalaxyModal',
    });

    dispatch({
      type: 'playProduct/updateEditCrowdByGalaxyFormData',
      payload: {
        crowdType: 'TRIP_GALAXY_CROWD',
        linkGalaxyActivity: {
          name: record.activityTitle,
          taskId: record.id,
        },
      },
    });
  };

  onCancelCrowdByGalaxyModal = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'playProduct/onCrowdByGalaxyModalCancel',
    });
  };

  onSubmit = values => {
    const { dispatch } = this.props;

    dispatch({
      type: 'playProduct/submitCreateChoosePerson',
      payload: values,
    });
  };

  // 分页触发
  handleTableChange = pager => {
    const { dispatch } = this.props;

    dispatch({
      type: 'playProduct/queryActivies',
      payload: {
        pageNo: pager.current,
      },
    });
  };

  render() {
    const { playProduct } = this.props;
    const {
      activities,
      crowdByGalaxyModalVisible,
      editCrowdByGalaxyFormData,
      algoModels,
      pagination,
      searchFormData,
      loading,
    } = playProduct;

    const columns = [
      {
        title: '商品池ID',
        dataIndex: 'id',
        key: 'id',
      },
      {
        title: '商品池名称',
        dataIndex: 'activityTitle',
        key: 'activityTitle',
      },
      {
        title: '所属人',
        dataIndex: 'activityOwner',
        key: 'activityOwner',
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        render: (text, record) => {
          const query = getPageQuery();
          return (
            <span>
              <a onClick={() => this.onOpenCrowdByGalaxyModal(record)}>圈人</a>
              <Divider type="vertical" />
              <Link
                to={`/choose-product/play-product/pool?id=${record.id}${
                  query ? `&${stringify(query)}` : ''
                }`}
              >
                盘货
              </Link>
            </span>
          );
        },
      },
    ];

    const extraContent = (
      <Button
        type="primary"
        onClick={() => {
          history.push('/choose-product/play-product/pool');
        }}
      >
        创建商品池
      </Button>
    );

    const { type } = searchFormData;
    return (
      <PageHeaderWrapper inner title="时空玩法盘货">
        <Card bordered>
          <Row gutter={24} style={{ marginBottom: 10 }}>
            <Col span={6}>
              <RadioGroup value={type === 'zhuge-all' ? '2' : '1'} onChange={this.onRadioChange}>
                <RadioButton value="1">我的商品池</RadioButton>
                <RadioButton value="2">全部商品池</RadioButton>
              </RadioGroup>
            </Col>
            <Col span={12} style={{ display: 'flex', alignItems: 'center' }}>
              <span style={{ color: 'red' }}>商品池ID为星辰子活动ID，请直接以星辰池方式使用</span>
            </Col>
            <Col span={6} style={{ textAlign: 'right' }}>
              {extraContent}
            </Col>
          </Row>
          <Table
            loading={loading}
            bordered
            dataSource={activities}
            columns={columns}
            pagination={pagination}
            onChange={this.handleTableChange}
          />
        </Card>

        <CreateCrowdByAlgoModal
          algoModels={algoModels}
          editFormData={editCrowdByGalaxyFormData}
          editModalVisible={crowdByGalaxyModalVisible}
          onSubmit={this.onSubmit}
          onCancel={this.onCancelCrowdByGalaxyModal}
        />
      </PageHeaderWrapper>
    );
  }
}

export default PlayProduct;
