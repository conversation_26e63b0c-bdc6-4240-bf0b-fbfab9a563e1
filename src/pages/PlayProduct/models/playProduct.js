import { message } from 'antd';
import { queryActivityByCondition, editCrowdByAlgo, queryAlgoModel } from '@/services/api';

export default {
  namespace: 'playProduct',
  state: {
    loading: false,

    // 活动数据
    activities: [],
    crowdByGalaxyModalVisible: false,
    editCrowdByGalaxyFormData: {},
    algoModels: [],
    pagination: { pageSize: 10 },
    searchFormData: {},
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(({ pathname }) => {
        if (pathname === '/choose-product/play-product') {
          dispatch({ type: 'query' });
        }
      });
    },
  },
  effects: {
    *query(_, { put, all }) {
      yield all([
        put.resolve({
          type: 'queryActivies',
        }),
        put.resolve({
          type: 'queryAlgoModel',
        }),
      ]);
    },
    *queryActivies({ payload = {} }, { call, put, select }) {
      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const { pagination, searchFormData } = yield select(state => state.playProduct);
      const { pageNo, pageSize } = payload;

      const res = yield call(queryActivityByCondition, {
        ...searchFormData,
        pageNo: pageNo || 1,
        pageSize: pageSize || 10,
        ...payload,
      });

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      if (res.data) {
        const { subActivityDetailList, total } = res.data;

        yield put({
          type: 'updateState',
          payload: {
            activities: subActivityDetailList || [],
            pagination: {
              ...pagination,
              current: pageNo || 1,
              total,
            },
            loading: false,
          },
        });
      }
    },
    *submitCreateChoosePerson({ payload }, { call, put }) {
      const data = payload;

      const res = yield call(editCrowdByAlgo, data);

      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }
      message.success('创建成功');

      yield put({
        type: 'updateState',
        payload: {
          crowdByGalaxyModalVisible: false,
        },
      });
    },
    *queryAlgoModel(_, { put, call }) {
      // 获取全部的算法模型，目前设为100
      const res = yield call(queryAlgoModel, { pageNum: 1, pageSize: 100 });

      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }

      const rows = res.data && res.data.rows;

      yield put({
        type: 'updateState',
        payload: {
          algoModels: rows,
        },
      });
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    onOpenCrowdByGalaxyModal(state) {
      return {
        ...state,
        crowdByGalaxyModalVisible: true,
      };
    },
    onCrowdByGalaxyModalCancel(state) {
      return {
        ...state,
        crowdByGalaxyModalVisible: false,
        editCrowdByGalaxyFormData: {},
      };
    },
    updateSearchFormData(state, { payload }) {
      return {
        ...state,
        searchFormData: {
          ...state.searchFormData,
          ...payload,
        },
      };
    },
    updateEditCrowdByGalaxyFormData(state, { payload }) {
      return {
        ...state,
        editCrowdByGalaxyFormData: {
          ...state.editCrowdByGalaxyFormData,
          ...payload,
        },
      };
    },
  },
};
