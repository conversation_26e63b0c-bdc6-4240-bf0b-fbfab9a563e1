import React from 'react';
import { Input, Select } from 'antd';

const FORM_ITEM_LAYOUT = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

export default function walker(rules = []) {
  return rules.map(r => {
    const { ruleDisplayName, ruleName, ruleDom, description } = r;
    return {
      label: ruleDisplayName,
      formItemLayout: FORM_ITEM_LAYOUT,
      target: getComponent(ruleDom, {
        placeholder: description,
      }),
      name: ruleName,
    };
  });
}

function getComponent(type, options) {
  let component;

  switch (type) {
    case 'INPUT':
      component = <Input {...options} allowClear />;
      break;
    case 'TAG_INPUT':
      component = <Select {...options} allowClear></Select>;
      break;
    default:
      break;
  }

  return component;
}
