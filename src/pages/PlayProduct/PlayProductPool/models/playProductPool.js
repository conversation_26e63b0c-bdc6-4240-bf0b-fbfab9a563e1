import { queryZhugeRule } from '@/services/api';

export default {
  namespace: 'playProductPool',
  state: {
    loading: true,

    // 商品池数据
    data: [],
    rules: [],
    searchFormData: {},
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(({ pathname }) => {
        if (pathname === '/choose-product/play-product/pool') {
          dispatch({ type: 'query' });
        }
      });
    },
  },
  effects: {
    *query(_, { put, all }) {
      yield all([
        put.resolve({
          type: 'queryRule',
        }),
        put.resolve({
          type: 'queryAlgoModel',
        }),
      ]);
    },
    *queryRule(_, { call, put }) {
      const res = yield call(queryZhugeRule);

      yield put({
        type: 'updateState',
        payload: {
          rules: res.data,
          loading: false,
        },
      });
    },
  },

  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
