import React from 'react';
import { Table, Row, Col, Form } from 'antd';

const FormItem = Form.Item;

export default function PriceDetail({ base = {}, dataSoruce = [] }) {
  const { bizId, name } = base;
  const columns = [
    {
      title: '数据日期',
      dataIndex: 'ds',
      key: 'ds',
    },
    {
      title: '日历日期',
      dataIndex: 'splPriceDate',
      key: 'splPriceDate',
    },
    {
      title: 'SKUID',
      dataIndex: 'skuId',
      key: 'skuId',
    },
    {
      title: '套餐价',
      dataIndex: 'setPrice',
      key: 'setPrice',
    },
    {
      title: '资源价-不含餐',
      dataIndex: 'totalSubPrice',
      key: 'totalSubPrice',
    },
    {
      title: '折扣度',
      dataIndex: 'zkd',
      key: 'zkd',
      render: text => (text || text === -1 || text === '-1' ? parseFloat(text).toFixed(2) : '暂无'),
    },
    {
      title: '元素类型',
      dataIndex: 'splElementType',
      key: 'splElementType',
    },
    {
      title: '单元素价格',
      dataIndex: 'splElementPrice',
      key: 'splElementPrice',
    },
    {
      title: '元素名称',
      dataIndex: 'splElementName',
      key: 'splElementName',
    },
    {
      title: '元素数量',
      dataIndex: 'splElementNum',
      key: 'splElementNum',
    },
  ];

  return (
    <div>
      <Row gutter={24}>
        <Col span={8}>
          <FormItem label="商品ID">
            <span>{bizId || ''}</span>
          </FormItem>
        </Col>
        <Col span={16}>
          <FormItem label="商品名称">
            <span
              style={{
                width: '100%',
                overflow: 'hidden',
                whiteSpace: 'nowrap',
                textOverflow: 'ellipsis',
              }}
            >
              {name || ''}
            </span>
          </FormItem>
        </Col>
      </Row>
      <Table columns={columns} dataSource={dataSoruce} />
    </div>
  );
}
