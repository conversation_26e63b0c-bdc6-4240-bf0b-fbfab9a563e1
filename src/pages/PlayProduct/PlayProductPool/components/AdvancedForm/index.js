import React, { useEffect } from 'react';
import { Form, Row, Col, Input, Button, Radio } from 'antd';
import styles from './index.less';

const FORM_ITEM_LAYOUT = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};
const FormItem = Form.Item;
const RadioGroup = Radio.Group;

const AdvancedForm = props => {
  const [form] = Form.useForm();

  const submit = values => {
    if (props.submit) {
      props.submit(values);
    }
  };

  useEffect(() => {
    const { formData = {} } = props;
    const { activityTitle, changWay } = formData;
    form.setFieldsValue({
      activityTitle,
      changWay,
    });
  }, [props.formData]);

  return (
    <Form {...FORM_ITEM_LAYOUT} form={form} onFinish={submit} className={styles.advancedForm}>
      <Row gutter={24}>
        <Col span={8}>
          <FormItem
            name="changWay"
            label="是否更新"
            rules={[{ required: true }]}
          >
            <RadioGroup>
              <Radio value="1">是</Radio>
              <Radio value="0">否</Radio>
            </RadioGroup>
          </FormItem>
        </Col>
        <Col span={8}>
          <FormItem name="activityTitle" label="商品池名" rules={[{ required: true }]}>
            <Input placeholder="商品池名称" allowClear />
          </FormItem>
        </Col>
        <Col span={8}>
          <FormItem label=" " colon={false}>
            <Button type="primary" htmlType="submit">
              保存为商品池
            </Button>
          </FormItem>
        </Col>
      </Row>
    </Form>
  );
};

export default AdvancedForm;
