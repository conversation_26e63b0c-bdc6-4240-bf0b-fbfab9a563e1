import React from 'react';
import { Popover } from 'antd';
import PriceDetail from './components/PriceDetail';

export default [
  {
    title: '商品ID',
    dataIndex: 'bizId',
    render: id => (
      <a
        target="_blank"
        rel="noopener noreferrer"
        href={`https://traveldetail.fliggy.com/item.htm?id=${id}`}
      >
        {id}
      </a>
    ),
  },
  {
    title: '名称',
    dataIndex: 'name',
    width: 300,
  },
  {
    title: '卖家ID',
    dataIndex: 'sellerId',
  },
  {
    title: '卖家名称',
    dataIndex: 'sellerNick',
  },
  {
    title: '叶子类目',
    dataIndex: 'category',
  },
  {
    title: '玩法',
    dataIndex: 'play',
    render: text => (!text || text === 'NULL' ? '暂无' : text),
  },
  {
    title: '国家',
    dataIndex: 'countryName',
  },
  {
    title: '目的地',
    dataIndex: 'destName',
    render: text => (!text || text === 'NULL' ? '暂无' : text),
  },
  {
    title: 'POI',
    dataIndex: 'poiName',
    render: text => (!text || text === 'NULL' ? '暂无' : text),
  },
  {
    title: '商品价格分',
    dataIndex: 'priceScore',
    width: 150,
    render: (text, record) => {
      const { splDetails, name, bizId } = record;
      return (
        <Popover
          trigger="hover"
          content={<PriceDetail base={{ name, bizId }} dataSoruce={splDetails} />}
        >
          {!text || text === -1 || text === '-1' ? '暂无' : <a>{parseFloat(text).toFixed(2)} </a>}
        </Popover>
      );
    },
  },
  {
    title: 'ctr分数',
    dataIndex: 'ctrScore',
    width: 150,
    render: text => (!text || text === -1 || text === '-1' ? '暂无' : parseFloat(text).toFixed(2)),
  },
  {
    title: 'cvr分数',
    dataIndex: 'cvrScore',
    width: 150,
    render: text => (!text || text === -1 || text === '-1' ? '暂无' : parseFloat(text).toFixed(2)),
  },
  // {
  //   title: '日均曝光UV',
  //   dataIndex: 'exposeUv',
  //   render: text => (!text || text === -1 || text === '-1' ? '暂无' : parseFloat(text).toFixed(2)),
  // },
  {
    title: '日均IPVUV',
    dataIndex: 'ipvuv',
    width: 150,
    render: text => (!text || text === -1 || text === '-1' ? '暂无' : parseFloat(text).toFixed(2)),
  },
  {
    title: '日均购买UV',
    dataIndex: 'salesUv',
    width: 150,
    render: text => (!text || text === -1 || text === '-1' ? '暂无' : parseFloat(text).toFixed(2)),
  },
  {
    title: '日均购买件数',
    dataIndex: 'salesCount',
    width: 150,
    render: text => (!text || text === -1 || text === '-1' ? '暂无' : parseFloat(text).toFixed(2)),
  },
  {
    title: '日均成交转化率',
    dataIndex: 'payRate',
    width: 150,
    render: text => (!text || text === -1 || text === '-1' ? '暂无' : parseFloat(text).toFixed(2)),
  },
  {
    title: '月平均到手价',
    dataIndex: 'priceArg',
    width: 150,
    render: text => (!text || text === -1 || text === '-1' ? '暂无' : parseFloat(text).toFixed(2)),
  },
  {
    title: '库存',
    dataIndex: 'inventory',
    width: 100,
  },
];
