/* eslint-disable no-restricted-syntax */
import React, { PureComponent, createRef } from 'react';
import { Card, Table, Input, Select, message, TreeSelect, Tabs, Popconfirm } from 'antd';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import SearchForm from '@/components/SearchForm';
import RangeInput from '@/components/RangeInput';
import isArray from 'lodash/isArray';
import debounce from 'lodash/debounce';
import { history } from 'umi';
import { getAllLeaves, processTreeData, generateList } from '@/utils/tree';
import { getPageQuery } from '@/utils/utils';
import PieChart from '@/components/PieChartBizV4'
import AdvancedForm from './components/AdvancedForm';
import columns from './columns';
import {
  queryCommonSuggest,
  queryCategoryList,
  queryZhugeRule,
  queryTreeData,
  queryItemsPageByRule,
  addTopicActivity,
  batchQueryActivity,
  queryItemsPortrait,
  removeBlackItem,
  addBlackItem,
  publishActivity,
  queryBlackItemList,
  queryZhugeItemList,
} from './service';
import styles from './index.less';

const FORM_ITEM_LAYOUT = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};
const { SHOW_PARENT } = TreeSelect;
const { Option } = Select;
const { TabPane } = Tabs;

class PlayProductPool extends PureComponent {
  formRef = createRef();

  constructor(props) {
    super(props);
    this.suggest = debounce(this.suggest, 500);
    this.state = {
      loading: false,
      searchFormData: {},
      goodList: [],
      pagination: {
        pageSize: 10,
      },
      activity: undefined,
      portraitData: [],
      categoryId_options: [],
      categoryId_nodes: [],
      zhugePlayTag_options: [],
      zhugePlayTag_nodes: [],
      blackItems: [],
    };
  }

  componentWillMount() {
    const { id } = getPageQuery();
    this.init().then(() => {
      if (id) {
        this.getActivityById(id);
        this.fetchBlackItem(id);
      } else {
        // 当有传入筛选条件参数时自动请求
        const { categoryId, destName, zhugePlayTag, itemPOI } = getPageQuery();
        if (categoryId || destName || zhugePlayTag || itemPOI) {
          this.setState(
            {
              searchFormData: {
                categoryId,
                destName,
                zhugePlayTag,
                itemPOI,
              },
            },
            () => {
              this.fetch();
            }
          );
        }
      }
    });
  }

  getActivityById = id => {
    batchQueryActivity({ id }).then(res => {
      if (!res.success) {
        message.error(res.msg);
        return;
      }

      if (res.data && res.data.subActivityDetailList && res.data.subActivityDetailList.length > 0) {
        const [activity] = res.data.subActivityDetailList;
        const { ruleMap = {} } = activity;

        for (const k in ruleMap) {
          if (ruleMap.hasOwnProperty(k)) {
            const ruleValue = ruleMap[k];
            if (typeof ruleValue === 'string' && ruleValue.includes(',')) {
              ruleMap[k] = ruleValue.split(',');
            }

            if (k === 'zhugeItem') {
              queryZhugeItemList({ pageNo: 1, pageSize: 1000, type: 'all', id: ruleValue }).then(
                result => {
                  if (!result.success) {
                    message.error(result.msg);
                    return;
                  }

                  // 诸葛池
                  const zhugeItems = (result && result.data && result.data.rows) || [];

                  this.setState({
                    zhugeItem_options: zhugeItems.map(item => ({
                      topicId: String(item.id),
                      topicName: item.name,
                    })),
                  });
                }
              );
            }
          }
        }

        this.setState(
          {
            searchFormData: ruleMap,
            activity,
          },
          () => {
            // 查询到详情之后请求一次筛选
            this.fetch({ activityId: id });
          }
        );
      }
    });
  };

  init = () =>
    new Promise((resolve, reject) => {
      queryZhugeRule().then(res => {
        if (!res.success) {
          message.error(res.msg);
          return;
        }

        if (res.data && res.data.length > 0) {
          const rules = res.data;
          const categoryRule = rules.find(r => r.ruleName === 'categoryId');
          const playRule = rules.find(r => r.ruleName === 'zhugePlayTag');
          const promises = [
            categoryRule ? queryCategoryList() : Promise.resolve(),
            playRule ? queryTreeData() : Promise.resolve(),
          ];
          Promise.all(promises)
            .then(values => {
              const [res1, res2] = values;

              const categoryData = processTreeData(res1.data);
              const zhugePlayTagData = processTreeData(res2.data);

              this.setState(
                {
                  categoryId_options: categoryData,
                  categoryId_nodes: generateList(categoryData),
                  zhugePlayTag_options: zhugePlayTagData,
                  zhugePlayTag_nodes: generateList(zhugePlayTagData),
                },
                () => {
                  resolve(true);
                }
              );
            })
            .catch(err => {
              message.error(err);
              reject(err);
            });
          this.setState({
            rules,
          });
        }
      });
    });

  walker = (rules = []) =>
    rules
      .map(r => {
        const { ruleDisplayName, ruleName, description } = r;
        return {
          label: ruleDisplayName,
          formItemLayout: FORM_ITEM_LAYOUT,
          target: this.getComponent(r, {
            placeholder: description,
          }),
          name: ruleName,
        };
      })
      .filter(item => item.target);

  parseRuleOptions = ruleOptions => {
    try {
      const o = ruleOptions.split(' ');

      return o.map(item => {
        const [value, label] = item.split(',');
        return {
          label,
          value,
        };
      });
    } catch (err) {
      message.error('选项配置错误');
      return [];
    }
  };

  getComponent = (rule, props) => {
    const { ruleDom: type, ruleName, ruleOptions } = rule;
    let component;
    // 自定义联想或者固定联想
    const options = ruleOptions
      ? this.parseRuleOptions(ruleOptions)
      : this.state[`${ruleName}_options`];

    const renderSelect = mode =>
      ruleOptions ? (
        <Select
          mode={mode}
          allowClear
          showSearch
          defaultActiveFirstOption={false}
          filterOption={false}
          showArrow
          {...props}
        >
          {options &&
            options.length > 0 &&
            options.map(o => {
              const { label, value } = o;
              return <Option key={value}>{label}</Option>;
            })}
        </Select>
      ) : (
        <Select
          mode={mode}
          allowClear
          showSearch
          onSearch={value => this.suggest(value, ruleName)}
          defaultActiveFirstOption={false}
          filterOption={false}
          showArrow
          {...props}
        >
          {options &&
            options.length > 0 &&
            options.map(o => {
              const { topicName, topicId } = o;
              return <Option key={topicId}>{topicName}</Option>;
            })}
        </Select>
      );

    switch (type) {
      case 'INPUT':
        component = <Input {...props} allowClear />;
        break;
      case 'SELECT':
        component = renderSelect('defalult');
        break;
      case 'MULTIPLE_SELECT':
        component = renderSelect('multiple');
        break;
      case 'TREE_SELECT':
        component = (
          <TreeSelect
            allowClear
            showSearch
            treeData={options}
            treeCheckable
            showCheckedStrategy={SHOW_PARENT}
            treeNodeFilterProp="title"
            {...props}
          />
        );
        break;
      case 'TAG_SELECT':
        component = <Select allowClear mode="tags" {...props} />;
        break;
      case 'RANGE_INPUT':
        component = <RangeInput />;
        break;
      default:
        break;
    }

    return component;
  };

  // 筛选接口
  fetch = (params = {}) => {
    const { searchFormData, pagination } = this.state;
    const data = Object.assign({}, searchFormData);

    const payload = this.traverse(data);

    this.setState({ loading: true });

    // 删除为空值的key
    for (const key in payload) {
      if (payload.hasOwnProperty(key)) {
        if (!payload[key]) {
          delete payload[key];
        }
      }
    }

    const { pageNo = 1, pageSize = 10, ...rest } = params;

    queryItemsPageByRule({
      pageNo,
      pageSize,
      ...payload,
      ...rest,
    })
      .then(res => {
        if (!res.success) {
          message.error(res.msg);
          return;
        }

        const pager = {
          ...pagination,
        };
        // Read total count from server
        // pagination.total = data.totalCount;
        if (res.data) {
          pager.total = res.data.total;
          pager.current = pageNo;
          pager.pageSize = res.data.pageSize;

          this.setState({
            loading: false,
            goodList: res.data.data,
            pagination: pager,
          });
        }
      })
      .catch(err => {
        message.error(err);
      });

    // 商品画像接口
    queryItemsPortrait(payload)
      .then(res => {
        this.setState({
          portraitData: res.data,
        });
      })
      .catch(err => {
        message.error(err);
      });
  };

  // 遍历得到所有叶子节点
  traverse = data => {
    const payload = data;
    for (const key in payload) {
      if (payload.hasOwnProperty(key) && isArray(payload[key])) {
        if (key === 'categoryId' || key === 'zhugePlayTag') {
          const a = [];
          payload[key].forEach(id => {
            const node = this.state[`${key}_nodes`].find(n => n.key === id);
            const leaves = getAllLeaves(node);
            a.push(...leaves);
          });
          payload[key] = a.map(item => item.id);
        }
        payload[key] = payload[key].filter(item => item).join(',');
      }
    }
    return data;
  };

  // 分页触发
  handleTableChange = pager => {
    this.setState(({ pagination }) => ({
      pagination: {
        ...pagination,
        current: pager.current,
        pageSize: pager.pageSize,
      },
    }));

    this.fetch({
      pageNo: pager.current,
      pageSize: pager.pageSize,
    });
  };

  suggest = (value, key) => {
    if (key === 'zhugeItem') {
      const params = {};
      // eslint-disable-next-line no-restricted-globals
      if (!isNaN(Number(value))) {
        params.id = Number(value);
      } else {
        params.name = value;
      }
      queryZhugeItemList({ pageNo: 1, pageSize: 10000, type: 'all', ...params }).then(res => {
        if (!res.success) {
          message.error(res.msg);
          return;
        }

        if (res.data && res.data.rows) {
          const { rows } = res.data;
          this.setState({
            [`${key}_options`]: rows.map(item => ({
              topicId: String(item.id),
              topicName: item.name,
            })),
          });
        }
      });
    } else {
      queryCommonSuggest({ query: value, key }).then(res => {
        if (!res.success) {
          message.error(res.msg);
          return;
        }

        this.setState({
          [`${key}_options`]: res.data,
        });
      });
    }
  };

  filter = () => {
    this.setState(({ pagination }) => ({
      pagination: {
        ...pagination,
        current: 1,
      },
    }));
    this.fetch();
  };

  save = values => {
    const base = values;
    const { searchFormData, activity } = this.state;
    const data = Object.assign({}, searchFormData);

    const payload = { base: { ...base, dataSource: 'TRAVEL' }, ruleConfig: this.traverse(data) };

    // 更新时添加id
    if (activity) {
      payload.base.activityId = activity.id;
    }

    addTopicActivity(payload).then(res => {
      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success(activity ? '更新成功' : '创建成功');

      publishActivity({ activityId: res.data }).then(result => {
        if (!result.success) {
          message.error(result.msg);
          return;
        }

        history.push('/choose-product/play-product');
      });
    });
  };

  // 排除恢复商品
  addOrRemoveGood = (type, activityId, itemId) => {
    // 排除
    if (type === 'remove') {
      addBlackItem({
        activityId,
        itemIds: String(itemId),
      }).then(res => {
        if (!res.success) {
          message.error(res.msg);
          return;
        }

        message.success('排除成功');
        this.fetchBlackItem(activityId);
        this.fetch({ activityId });
      });
    } else {
      removeBlackItem({
        activityId,
        itemIds: String(itemId),
      }).then(res => {
        if (!res.success) {
          message.error(res.msg);
          return;
        }

        message.success('恢复成功');
        this.fetch({ activityId });
        this.fetchBlackItem(activityId);
      });
    }
  };

  // 得到黑名单商品
  fetchBlackItem = activityId => {
    if (!activityId) {
      return Promise.reject();
    }

    return queryBlackItemList({
      id: activityId,
      pageNo: 1,
      pageSize: 100,
    }).then(res => {
      if (!res.success) {
        message.error(res.msg);
        return;
      }
      const { data } = res.data;

      this.setState({
        blackItems: data,
      });
    });
  };

  render() {
    const {
      loading,
      searchFormData,
      rules,
      goodList,
      pagination,
      activity,
      portraitData,
      blackItems,
    } = this.state;
    const { id: activityId } = getPageQuery();


    // portraitData = [
    //   {
    //     "totalCount": null,
    //     "title": "二级类目近30天GMV分布图",
    //     "kvPair": {
    //       "境内周边游": "100.0",
    //       "国内票务": "0.0",
    //       "酒店客栈套餐": "0.0"
    //     },
    //     "matrix": null
    //   },
    //   {
    //     "totalCount": null,
    //     "title": "二级类目数量分布图",
    //     "kvPair": {
    //       "境内周边游": "5954",
    //       "酒店餐饮及服务": "94",
    //       "国内票务": "7325",
    //       "酒店客栈套餐": "5157"
    //     },
    //     "matrix": null
    //   },
    //   {
    //     "totalCount": null,
    //     "title": "玩法数量分布图",
    //     "kvPair": {
    //       "赏红叶": "43",
    //       "雪山冰川观光": "50",
    //       "自助餐": "627",
    //       "四星级酒店": "39",
    //       "游乐场": "46",
    //       "甜点饮品": "84",
    //       "山景房": "55",
    //       "动物园": "123",
    //       "海鲜": "73",
    //       "榻榻米": "30",
    //       "水世界": "428",
    //       "湿地观光": "234",
    //       "公园": "1417",
    //       "房车": "53",
    //       "按摩": "77",
    //       "影视城": "23",
    //       "赏玫瑰": "33",
    //       "欢乐谷": "17",
    //       "下午茶": "219",
    //       "私人别墅": "22",
    //       "中华恐龙园": "34",
    //       "无边泳池": "164",
    //       "古村古镇": "244",
    //       "健身房": "288",
    //       "宋城": "20",
    //       "五星级酒店": "390",
    //       "湖泊观光": "55",
    //       "盐浴": "24",
    //       "海滨观光": "364",
    //       "火山观光": "114",
    //       "湖景房": "63",
    //       "玻璃栈道": "66",
    //       "木屋": "117",
    //       "缆车": "53",
    //       "赏樱花": "70",
    //       "游泳": "306",
    //       "华侨城": "89",
    //       "滑雪": "148",
    //       "梯田观光": "17",
    //       "森林观光": "894",
    //       "瀑布观光": "82",
    //       "品酒": "26",
    //       "纪念馆": "90",
    //       "园林": "282",
    //       "海景房": "105",
    //       "民宿": "304",
    //       "赏海景": "335",
    //       "艺术馆": "25",
    //       "骑行": "45",
    //       "农家乐": "19",
    //       "汗蒸": "201",
    //       "温泉": "17583",
    //       "小火车": "18",
    //       "烧烤": "350",
    //       "酒店式公寓": "88",
    //       "树屋": "59",
    //       "登山": "63",
    //       "古城": "44",
    //       "度假酒店": "2424",
    //       "避暑": "45",
    //       "儿童乐园": "498",
    //       "农场游": "68",
    //       "植物园": "65",
    //       "峡谷观光": "75",
    //       "方特": "22",
    //       "水疗": "74",
    //       "高尔夫体验": "57",
    //       "滑梯": "18",
    //       "高达": "39",
    //       "温泉酒店": "4040"
    //     },
    //     "matrix": null
    //   },
    //   {
    //     "totalCount": null,
    //     "title": "国家分布TOP 10表",
    //     "kvPair": null,
    //     "matrix": [
    //       [
    //         "国家",
    //         "商品数量"
    //       ],
    //       [
    //         "中国",
    //         "18403"
    //       ]
    //     ]
    //   },
    //   {
    //     "totalCount": null,
    //     "title": "城市分布TOP 10表",
    //     "kvPair": null,
    //     "matrix": [
    //       [
    //         "城市",
    //         "商品数量"
    //       ],
    //       [
    //         "惠州",
    //         "1226"
    //       ],
    //       [
    //         "广州",
    //         "1130"
    //       ],
    //       [
    //         "清远",
    //         "1104"
    //       ],
    //       [
    //         "杭州",
    //         "737"
    //       ],
    //       [
    //         "江门",
    //         "594"
    //       ],
    //       [
    //         "北京",
    //         "577"
    //       ],
    //       [
    //         "河源",
    //         "400"
    //       ],
    //       [
    //         "佛山",
    //         "370"
    //       ],
    //       [
    //         "常州",
    //         "366"
    //       ],
    //       [
    //         "云浮",
    //         "360"
    //       ],
    //       [
    //         "福州",
    //         "328"
    //       ],
    //       [
    //         "南京",
    //         "293"
    //       ],
    //       [
    //         "珠海",
    //         "293"
    //       ],
    //       [
    //         "苏州",
    //         "278"
    //       ],
    //       [
    //         "成都",
    //         "268"
    //       ],
    //       [
    //         "宜春",
    //         "253"
    //       ],
    //       [
    //         "重庆",
    //         "251"
    //       ],
    //       [
    //         "韶关",
    //         "248"
    //       ],
    //       [
    //         "金华",
    //         "237"
    //       ],
    //       [
    //         "漳州",
    //         "211"
    //       ],
    //       [
    //         "石家庄",
    //         "207"
    //       ],
    //       [
    //         "郑州",
    //         "207"
    //       ],
    //       [
    //         "天津",
    //         "206"
    //       ],
    //       [
    //         "厦门",
    //         "202"
    //       ],
    //       [
    //         "保山",
    //         "194"
    //       ],
    //       [
    //         "大连",
    //         "191"
    //       ],
    //       [
    //         "宁波",
    //         "187"
    //       ],
    //       [
    //         "三亚",
    //         "185"
    //       ],
    //       [
    //         "威海",
    //         "173"
    //       ],
    //       [
    //         "九江",
    //         "166"
    //       ],
    //       [
    //         "湖州",
    //         "165"
    //       ],
    //       [
    //         "咸宁",
    //         "163"
    //       ],
    //       [
    //         "沈阳",
    //         "151"
    //       ],
    //       [
    //         "保定",
    //         "148"
    //       ],
    //       [
    //         "白山",
    //         "147"
    //       ],
    //       [
    //         "梅州",
    //         "147"
    //       ],
    //       [
    //         "中山",
    //         "141"
    //       ],
    //       [
    //         "营口",
    //         "135"
    //       ],
    //       [
    //         "青岛",
    //         "134"
    //       ],
    //       [
    //         "乐山",
    //         "130"
    //       ],
    //       [
    //         "茂名",
    //         "126"
    //       ],
    //       [
    //         "黄冈",
    //         "119"
    //       ],
    //       [
    //         "哈尔滨",
    //         "118"
    //       ],
    //       [
    //         "昆明",
    //         "118"
    //       ],
    //       [
    //         "长沙",
    //         "115"
    //       ],
    //       [
    //         "临沂",
    //         "112"
    //       ],
    //       [
    //         "黄山",
    //         "111"
    //       ],
    //       [
    //         "西安",
    //         "108"
    //       ],
    //       [
    //         "长春",
    //         "98"
    //       ],
    //       [
    //         "深圳",
    //         "95"
    //       ],
    //       [
    //         "贵阳",
    //         "93"
    //       ],
    //       [
    //         "烟台",
    //         "91"
    //       ],
    //       [
    //         "宝鸡",
    //         "90"
    //       ],
    //       [
    //         "扬州",
    //         "86"
    //       ],
    //       [
    //         "保亭",
    //         "82"
    //       ],
    //       [
    //         "德州",
    //         "75"
    //       ],
    //       [
    //         "龙岩",
    //         "72"
    //       ],
    //       [
    //         "本溪",
    //         "72"
    //       ],
    //       [
    //         "阿坝",
    //         "71"
    //       ],
    //       [
    //         "阳江",
    //         "70"
    //       ],
    //       [
    //         "盘锦",
    //         "69"
    //       ],
    //       [
    //         "东莞",
    //         "67"
    //       ],
    //       [
    //         "秦皇岛",
    //         "67"
    //       ],
    //       [
    //         "泰安",
    //         "65"
    //       ],
    //       [
    //         "嘉兴",
    //         "64"
    //       ],
    //       [
    //         "葫芦岛",
    //         "63"
    //       ],
    //       [
    //         "延边",
    //         "63"
    //       ],
    //       [
    //         "潍坊",
    //         "63"
    //       ],
    //       [
    //         "鞍山",
    //         "63"
    //       ],
    //       [
    //         "荆州",
    //         "62"
    //       ],
    //       [
    //         "南昌",
    //         "62"
    //       ],
    //       [
    //         "赣州",
    //         "60"
    //       ],
    //       [
    //         "甘孜",
    //         "58"
    //       ],
    //       [
    //         "桂林",
    //         "54"
    //       ],
    //       [
    //         "无锡",
    //         "53"
    //       ],
    //       [
    //         "温州",
    //         "50"
    //       ],
    //       [
    //         "泰州",
    //         "50"
    //       ],
    //       [
    //         "海口",
    //         "49"
    //       ],
    //       [
    //         "南通",
    //         "49"
    //       ],
    //       [
    //         "许昌",
    //         "47"
    //       ],
    //       [
    //         "洛阳",
    //         "45"
    //       ],
    //       [
    //         "黔东南",
    //         "45"
    //       ],
    //       [
    //         "揭阳",
    //         "42"
    //       ],
    //       [
    //         "凉山",
    //         "40"
    //       ],
    //       [
    //         "萍乡",
    //         "40"
    //       ],
    //       [
    //         "南宁",
    //         "39"
    //       ],
    //       [
    //         "廊坊",
    //         "37"
    //       ],
    //       [
    //         "赤峰",
    //         "36"
    //       ],
    //       [
    //         "贺州",
    //         "35"
    //       ],
    //       [
    //         "绵阳",
    //         "34"
    //       ],
    //       [
    //         "大庆",
    //         "33"
    //       ],
    //       [
    //         "吉林",
    //         "33"
    //       ],
    //       [
    //         "三门峡",
    //         "33"
    //       ],
    //       [
    //         "上海",
    //         "32"
    //       ],
    //       [
    //         "孝感",
    //         "31"
    //       ],
    //       [
    //         "锦州",
    //         "31"
    //       ],
    //       [
    //         "郴州",
    //         "31"
    //       ],
    //       [
    //         "辽阳",
    //         "30"
    //       ],
    //       [
    //         "泉州",
    //         "30"
    //       ],
    //       [
    //         "淮安",
    //         "30"
    //       ],
    //       [
    //         "西双版纳",
    //         "29"
    //       ],
    //       [
    //         "丹东",
    //         "28"
    //       ],
    //       [
    //         "潮州",
    //         "28"
    //       ],
    //       [
    //         "绍兴",
    //         "27"
    //       ],
    //       [
    //         "张家口",
    //         "27"
    //       ],
    //       [
    //         "合肥",
    //         "27"
    //       ],
    //       [
    //         "聊城",
    //         "27"
    //       ],
    //       [
    //         "承德",
    //         "25"
    //       ],
    //       [
    //         "德阳",
    //         "25"
    //       ],
    //       [
    //         "随州",
    //         "22"
    //       ],
    //       [
    //         "镇江",
    //         "22"
    //       ],
    //       [
    //         "济南",
    //         "21"
    //       ],
    //       [
    //         "肇庆",
    //         "21"
    //       ],
    //       [
    //         "三明",
    //         "20"
    //       ],
    //       [
    //         "滨州",
    //         "20"
    //       ],
    //       [
    //         "张家界",
    //         "19"
    //       ],
    //       [
    //         "铁岭",
    //         "19"
    //       ],
    //       [
    //         "汕尾",
    //         "19"
    //       ],
    //       [
    //         "大同",
    //         "19"
    //       ],
    //       [
    //         "防城港",
    //         "19"
    //       ],
    //       [
    //         "红河",
    //         "18"
    //       ],
    //       [
    //         "淄博",
    //         "17"
    //       ],
    //       [
    //         "湛江",
    //         "17"
    //       ]
    //     ]
    //   },
    //   {
    //     "totalCount": null,
    //     "title": "核心商家TOP 10表",
    //     "kvPair": null,
    //     "matrix": [
    //       [
    //         "商家ID",
    //         "商家昵称",
    //         "类型",
    //         "商品数量"
    //       ],
    //       [
    //         "2200773299636",
    //         "湖北中景美域旅游专营店",
    //         "B卖家",
    //         "1515"
    //       ],
    //       [
    //         "3543748652",
    //         "大余阳光假日旅游专营店",
    //         "B卖家",
    //         "438"
    //       ],
    //       [
    //         "711420554",
    //         "乐活游休闲票务",
    //         "B卖家",
    //         "404"
    //       ],
    //       [
    //         "2208304769901",
    //         "湖北头号玩家国旅武汉专营",
    //         "B卖家",
    //         "349"
    //       ],
    //       [
    //         "2091216302",
    //         "喜玩旅游旗舰店",
    //         "B卖家",
    //         "288"
    //       ],
    //       [
    //         "2206941505808",
    //         "广州青叶国际旅行社专营店",
    //         "B卖家",
    //         "231"
    //       ],
    //       [
    //         "2208342198135",
    //         "阿目旅游专营店",
    //         "B卖家",
    //         "222"
    //       ],
    //       [
    //         "1123437233",
    //         "要出发旅游",
    //         "B卖家",
    //         "172"
    //       ],
    //       [
    //         "2207113640590",
    //         "轻刻旅游旗舰店",
    //         "B卖家",
    //         "157"
    //       ],
    //       [
    //         "2201211644940",
    //         "西安美遇假期旅游专营店",
    //         "B卖家",
    //         "140"
    //       ],
    //       [
    //         "2208466412717",
    //         "汕头优优马旅游专营店",
    //         "B卖家",
    //         "128"
    //       ],
    //       [
    //         "2209235097244",
    //         "汕头小飞象酒店专营店",
    //         "B卖家",
    //         "127"
    //       ],
    //       [
    //         "4133929707",
    //         "辽宁中信旅游专营店",
    //         "B卖家",
    //         "115"
    //       ],
    //       [
    //         "3676200528",
    //         "成都易睿博旅游专营店",
    //         "B卖家",
    //         "114"
    //       ],
    //       [
    //         "2732422024",
    //         "武汉牛牛的旅游专营店",
    //         "B卖家",
    //         "111"
    //       ],
    //       [
    //         "2200637111923",
    //         "广州粤通旅游专营店",
    //         "B卖家",
    //         "105"
    //       ],
    //       [
    //         "3022091731",
    //         "杭州远大旅游专营店",
    //         "B卖家",
    //         "100"
    //       ],
    //       [
    //         "3855335263",
    //         "武汉完美旅程旅游专营店",
    //         "B卖家",
    //         "97"
    //       ],
    //       [
    //         "2208511864982",
    //         "武汉牛牛国旅襄阳专营店",
    //         "B卖家",
    //         "95"
    //       ],
    //       [
    //         "3166145007",
    //         "深圳途游旅游专营店",
    //         "B卖家",
    //         "93"
    //       ],
    //       [
    //         "2207674915",
    //         "深圳港澳之旅旅游专营店",
    //         "B卖家",
    //         "89"
    //       ],
    //       [
    //         "3963604862",
    //         "杭州钱唐旅行社专营店",
    //         "B卖家",
    //         "83"
    //       ],
    //       [
    //         "2201436497408",
    //         "河北寰宇运通旅游专营店",
    //         "B卖家",
    //         "83"
    //       ],
    //       [
    //         "2209503454069",
    //         "牛侠客旅游旗舰店",
    //         "B卖家",
    //         "82"
    //       ],
    //       [
    //         "2194790584",
    //         "深圳粤港假日旅游专营店",
    //         "B卖家",
    //         "81"
    //       ],
    //       [
    //         "902878012",
    //         "tb647354_33",
    //         "C卖家",
    //         "81"
    //       ],
    //       [
    //         "2750118539",
    //         "敦煌晨光旅游专营店",
    //         "B卖家",
    //         "80"
    //       ],
    //       [
    //         "248289806",
    //         "zym137",
    //         "C卖家",
    //         "80"
    //       ],
    //       [
    //         "2209228970825",
    //         "上海济致旅游专营店",
    //         "B卖家",
    //         "80"
    //       ],
    //       [
    //         "742658844",
    //         "筱湘19900119",
    //         "C卖家",
    //         "78"
    //       ],
    //       [
    //         "2208659592920",
    //         "佛山领驰旅游专营店",
    //         "B卖家",
    //         "74"
    //       ],
    //       [
    //         "54602483",
    //         "暗影逍遥_2005",
    //         "C卖家",
    //         "74"
    //       ],
    //       [
    //         "2201422592420",
    //         "深圳小袋鼠酒店专营店",
    //         "B卖家",
    //         "71"
    //       ],
    //       [
    //         "2206517088274",
    //         "中山考拉旅游专营店",
    //         "B卖家",
    //         "71"
    //       ],
    //       [
    //         "640106288",
    //         "同程旅游专卖店",
    //         "B卖家",
    //         "70"
    //       ],
    //       [
    //         "2206759570549",
    //         "深圳全旅慧通旅游专营店",
    //         "B卖家",
    //         "70"
    //       ],
    //       [
    //         "1830376490",
    //         "悠游之旅88",
    //         "C卖家",
    //         "70"
    //       ],
    //       [
    //         "2201552833474",
    //         "广东享旅惠旅游专营店",
    //         "B卖家",
    //         "70"
    //       ],
    //       [
    //         "2201547432968",
    //         "海汽旅游旗舰店",
    //         "B卖家",
    //         "68"
    //       ],
    //       [
    //         "4208360216",
    //         "中山最会玩旅行社专营店",
    //         "B卖家",
    //         "67"
    //       ],
    //       [
    //         "2160051554",
    //         "淘酒店专营店",
    //         "B卖家",
    //         "65"
    //       ],
    //       [
    //         "2774714677",
    //         "大连海娃娃旅游专营店",
    //         "B卖家",
    //         "64"
    //       ],
    //       [
    //         "2207378327",
    //         "厦门游哪儿国旅专营店",
    //         "B卖家",
    //         "62"
    //       ],
    //       [
    //         "2695197083",
    //         "福州择程旅游专营店",
    //         "B卖家",
    //         "62"
    //       ],
    //       [
    //         "3123610244",
    //         "华美之旅国旅专营店",
    //         "B卖家",
    //         "62"
    //       ],
    //       [
    //         "4218966484",
    //         "江门华烨旅游专营店",
    //         "B卖家",
    //         "61"
    //       ],
    //       [
    //         "3230040999",
    //         "鹤山广游旅游专营店",
    //         "B卖家",
    //         "61"
    //       ],
    //       [
    //         "40055034",
    //         "bingice_110",
    //         "C卖家",
    //         "60"
    //       ],
    //       [
    //         "2207337884608",
    //         "西安戌唐旅游专营店",
    //         "B卖家",
    //         "59"
    //       ],
    //       [
    //         "3881271312",
    //         "敦煌新概念车导租车专营店",
    //         "B卖家",
    //         "58"
    //       ],
    //       [
    //         "2201479367921",
    //         "上海薇客旅游专营店",
    //         "B卖家",
    //         "56"
    //       ],
    //       [
    //         "2758902895",
    //         "敦煌西部新干线旅游专营店",
    //         "B卖家",
    //         "56"
    //       ],
    //       [
    //         "3244571301",
    //         "福建度假无忧旅游专营店",
    //         "B卖家",
    //         "55"
    //       ],
    //       [
    //         "4259399648",
    //         "深圳温泉之家国旅专营店",
    //         "B卖家",
    //         "55"
    //       ],
    //       [
    //         "2200771338302",
    //         "深圳兴程酒店专营店",
    //         "B卖家",
    //         "54"
    //       ],
    //       [
    //         "134914414",
    //         "yangwufriend168",
    //         "C卖家",
    //         "54"
    //       ],
    //       [
    //         "2201401180160",
    //         "山东阳信鲁谊旅游专营店",
    //         "B卖家",
    //         "52"
    //       ],
    //       [
    //         "62232823",
    //         "xteeqg9606",
    //         "C卖家",
    //         "52"
    //       ],
    //       [
    //         "52495180",
    //         "tywtl111",
    //         "C卖家",
    //         "48"
    //       ],
    //       [
    //         "4232912715",
    //         "江门粤通酒店专营店",
    //         "B卖家",
    //         "48"
    //       ],
    //       [
    //         "2201501893698",
    //         "江门喜途酒店专营店",
    //         "B卖家",
    //         "48"
    //       ],
    //       [
    //         "2208578473312",
    //         "珠海海贝旅游专营店",
    //         "B卖家",
    //         "47"
    //       ],
    //       [
    //         "2702608706",
    //         "焦作明珠旅游专营",
    //         "B卖家",
    //         "45"
    //       ],
    //       [
    //         "2209416108861",
    //         "武汉欢乐视界国旅专营店",
    //         "B卖家",
    //         "44"
    //       ],
    //       [
    //         "2209222499999",
    //         "石家庄捷程旅游专营店",
    //         "B卖家",
    //         "43"
    //       ],
    //       [
    //         "2884734533",
    //         "旅游门票1",
    //         "C卖家",
    //         "42"
    //       ],
    //       [
    //         "2208856572218",
    //         "石家庄佰惠旅游专营店",
    //         "B卖家",
    //         "41"
    //       ],
    //       [
    //         "2200718764457",
    //         "山西中北旅游专营店",
    //         "B卖家",
    //         "41"
    //       ],
    //       [
    //         "2845847049",
    //         "云享旅游",
    //         "C卖家",
    //         "41"
    //       ],
    //       [
    //         "2206865604699",
    //         "鞍山圣东旅游专营店",
    //         "B卖家",
    //         "41"
    //       ],
    //       [
    //         "2200608580872",
    //         "阳江广游通旅游专营店",
    //         "B卖家",
    //         "40"
    //       ],
    //       [
    //         "665728903",
    //         "八达通商旅",
    //         "C卖家",
    //         "40"
    //       ],
    //       [
    //         "2870507823",
    //         "浮游票务",
    //         "C卖家",
    //         "40"
    //       ],
    //       [
    //         "2209267585674",
    //         "广州名广假期旅游专营店",
    //         "B卖家",
    //         "40"
    //       ],
    //       [
    //         "3981608061",
    //         "深圳卓越国旅南山专营店",
    //         "B卖家",
    //         "40"
    //       ],
    //       [
    //         "2994983420",
    //         "广州尊享国际旅行社专营店",
    //         "B卖家",
    //         "40"
    //       ],
    //       [
    //         "2200577794563",
    //         "北京途悦国旅专营店",
    //         "B卖家",
    //         "39"
    //       ],
    //       [
    //         "2207901143503",
    //         "威海嗨途旅游专营店",
    //         "B卖家",
    //         "39"
    //       ],
    //       [
    //         "2207947613331",
    //         "上海妘昊玩伴旅游专营店",
    //         "B卖家",
    //         "39"
    //       ],
    //       [
    //         "3791413043",
    //         "珠海爱尚旅游专营店",
    //         "B卖家",
    //         "39"
    //       ],
    //       [
    //         "2856437246",
    //         "飞猪度假官方旗舰店",
    //         "B卖家",
    //         "39"
    //       ],
    //       [
    //         "4060089610",
    //         "广州途驴酒店专营店",
    //         "B卖家",
    //         "38"
    //       ],
    //       [
    //         "2201418175803",
    //         "泉州煜坤旅游专营店",
    //         "B卖家",
    //         "38"
    //       ],
    //       [
    //         "2200722776705",
    //         "河南国景旅游专营店",
    //         "B卖家",
    //         "38"
    //       ],
    //       [
    //         "1879890250",
    //         "福州金桥国旅专营店",
    //         "B卖家",
    //         "37"
    //       ],
    //       [
    //         "2759179329",
    //         "西安新干线旅游专营店",
    //         "B卖家",
    //         "37"
    //       ],
    //       [
    //         "2208984060111",
    //         "深圳辰飞酒店专营店",
    //         "B卖家",
    //         "35"
    //       ],
    //       [
    //         "2201487032329",
    //         "深圳潮旅旅游专营店",
    //         "B卖家",
    //         "35"
    //       ],
    //       [
    //         "685302342",
    //         "konglinglan520",
    //         "C卖家",
    //         "34"
    //       ],
    //       [
    //         "226193432",
    //         "qq563399302",
    //         "C卖家",
    //         "34"
    //       ],
    //       [
    //         "54157057",
    //         "eastuman",
    //         "C卖家",
    //         "33"
    //       ],
    //       [
    //         "2207780811558",
    //         "深圳中青旅旅游专营店",
    //         "B卖家",
    //         "33"
    //       ],
    //       [
    //         "2144554133",
    //         "广州艺游旅游专营店",
    //         "B卖家",
    //         "33"
    //       ],
    //       [
    //         "2200710534940",
    //         "广东星途国际旅行社专营店",
    //         "B卖家",
    //         "33"
    //       ],
    //       [
    //         "490728022",
    //         "驴妈妈旅游专卖店",
    //         "B卖家",
    //         "33"
    //       ],
    //       [
    //         "847933875",
    //         "陕西中旅城南分公司",
    //         "B卖家",
    //         "32"
    //       ],
    //       [
    //         "2209015125945",
    //         "汕尾城区伴旅旅游专营店",
    //         "B卖家",
    //         "32"
    //       ],
    //       [
    //         "822108370",
    //         "西安新浪潮国旅",
    //         "B卖家",
    //         "32"
    //       ],
    //       [
    //         "475555734",
    //         "白浪游票务专营店",
    //         "B卖家",
    //         "32"
    //       ],
    //       [
    //         "2707640198",
    //         "维航旅游网",
    //         "C卖家",
    //         "32"
    //       ],
    //       [
    //         "3280608628",
    //         "东莞龙跃旅游专营店",
    //         "B卖家",
    //         "32"
    //       ],
    //       [
    //         "2206472671385",
    //         "河南元素旅游专营店",
    //         "B卖家",
    //         "32"
    //       ],
    //       [
    //         "2205819371113",
    //         "河南万景旅游专营店",
    //         "B卖家",
    //         "31"
    //       ],
    //       [
    //         "2981910568",
    //         "广州速旅旅游专营",
    //         "B卖家",
    //         "30"
    //       ],
    //       [
    //         "2208053161476",
    //         "广东玩客国旅专营店",
    //         "B卖家",
    //         "30"
    //       ],
    //       [
    //         "3415665461",
    //         "广州海星旅行社专营店",
    //         "B卖家",
    //         "29"
    //       ],
    //       [
    //         "2200672963635",
    //         "河北初春旅游专营店",
    //         "B卖家",
    //         "29"
    //       ],
    //       [
    //         "2200592073550",
    //         "山东汪途旅游专营店",
    //         "B卖家",
    //         "29"
    //       ],
    //       [
    //         "2528765902",
    //         "杭州盛途旅行社萧山专营店",
    //         "B卖家",
    //         "29"
    //       ],
    //       [
    //         "2200547679806",
    //         "广州会玩旅游专营店",
    //         "B卖家",
    //         "29"
    //       ],
    //       [
    //         "3162306700",
    //         "常州畅游旅游专营店",
    //         "B卖家",
    //         "29"
    //       ],
    //       [
    //         "2530526939",
    //         "泰安酷游旅游专营店",
    //         "B卖家",
    //         "28"
    //       ],
    //       [
    //         "748933029",
    //         "武汉民间旅游",
    //         "B卖家",
    //         "28"
    //       ],
    //       [
    //         "2200672503401",
    //         "深圳多利国际旅行社专营",
    //         "B卖家",
    //         "28"
    //       ],
    //       [
    //         "2711983280",
    //         "广州恒生国旅专营店",
    //         "B卖家",
    //         "28"
    //       ],
    //       [
    //         "2200707271865",
    //         "敦煌嘉年华旅游专营店",
    //         "B卖家",
    //         "28"
    //       ],
    //       [
    //         "2200712110024",
    //         "武义唐风旅游专营店",
    //         "B卖家",
    //         "27"
    //       ],
    //       [
    //         "2698909738",
    //         "大连鑫程商务旅行社专营店",
    //         "B卖家",
    //         "27"
    //       ],
    //       [
    //         "3683351472",
    //         "广州升途国际旅行社专营店",
    //         "B卖家",
    //         "27"
    //       ],
    //       [
    //         "2208652423625",
    //         "佛山深鹿旅游专营店",
    //         "B卖家",
    //         "27"
    //       ],
    //       [
    //         "2208416800664",
    //         "爱蜜旅行网",
    //         "C卖家",
    //         "26"
    //       ],
    //       [
    //         "2200575003636",
    //         "深圳小脚丫旅游专营店",
    //         "B卖家",
    //         "26"
    //       ],
    //       [
    //         "2200662072579",
    //         "广州享途旅游专营店",
    //         "B卖家",
    //         "26"
    //       ],
    //       [
    //         "2611945493",
    //         "四川合途在线旅游专营店",
    //         "B卖家",
    //         "26"
    //       ],
    //       [
    //         "3248600917",
    //         "英德观光旅行社专营店",
    //         "B卖家",
    //         "26"
    //       ],
    //       [
    //         "830163610",
    //         "xushijiaguo",
    //         "C卖家",
    //         "26"
    //       ],
    //       [
    //         "2024341293",
    //         "深圳出发点旅游专营店",
    //         "B卖家",
    //         "25"
    //       ],
    //       [
    //         "2836640859",
    //         "武汉一起旅行旅游专营店",
    //         "B卖家",
    //         "25"
    //       ],
    //       [
    //         "2861595206",
    //         "piggy0105",
    //         "C卖家",
    //         "25"
    //       ],
    //       [
    //         "4175907133",
    //         "广东遨游假期国旅专营店",
    //         "B卖家",
    //         "25"
    //       ],
    //       [
    //         "2250216426",
    //         "广州趣旅旅游专营店",
    //         "B卖家",
    //         "25"
    //       ],
    //       [
    //         "2035333588",
    //         "就是爽77",
    //         "C卖家",
    //         "24"
    //       ],
    //       [
    //         "1039285035",
    //         "温泉之家专卖店",
    //         "B卖家",
    //         "24"
    //       ],
    //       [
    //         "2239504722",
    //         "一帆风顺票务总公司",
    //         "C卖家",
    //         "24"
    //       ],
    //       [
    //         "2206863480133",
    //         "珠海国旅国际旅行社专营店",
    //         "B卖家",
    //         "24"
    //       ],
    //       [
    //         "2209130865381",
    //         "河北知见国旅专营店",
    //         "B卖家",
    //         "24"
    //       ],
    //       [
    //         "2086269728",
    //         "杭州杜拜旅游专营",
    //         "B卖家",
    //         "24"
    //       ],
    //       [
    //         "2200639245157",
    //         "江门新会理想旅游专营店",
    //         "B卖家",
    //         "23"
    //       ],
    //       [
    //         "3296847515",
    //         "东莞雅途旅游专营店",
    //         "B卖家",
    //         "23"
    //       ],
    //       [
    //         "2207610052138",
    //         "广州酷游旅游专营",
    //         "B卖家",
    //         "23"
    //       ],
    //       [
    //         "2200675747366",
    //         "广东雄讯旅游专营店",
    //         "B卖家",
    //         "23"
    //       ],
    //       [
    //         "2200699656779",
    //         "福建指间游旅游专营店",
    //         "B卖家",
    //         "22"
    //       ],
    //       [
    //         "3365151577",
    //         "广东口岸旅行社唯途专营店",
    //         "B卖家",
    //         "22"
    //       ],
    //       [
    //         "2861607266",
    //         "烟台小票虫旅游专营店",
    //         "B卖家",
    //         "22"
    //       ],
    //       [
    //         "4042334278",
    //         "福州路游旅游专营店",
    //         "B卖家",
    //         "21"
    //       ],
    //       [
    //         "2200774320637",
    //         "广州迎丰旅游专营店",
    //         "B卖家",
    //         "21"
    //       ],
    //       [
    //         "2990753207",
    //         "武汉好游汇旅游专营店",
    //         "B卖家",
    //         "21"
    //       ],
    //       [
    //         "2206767734799",
    //         "享行之旅旅行社专营店",
    //         "B卖家",
    //         "21"
    //       ],
    //       [
    //         "2206804980828",
    //         "广州途喜国旅专营店",
    //         "B卖家",
    //         "21"
    //       ],
    //       [
    //         "2871246268",
    //         "深圳卓悦国旅专营店",
    //         "B卖家",
    //         "21"
    //       ],
    //       [
    //         "3130696874",
    //         "广东同程创游国旅专营店",
    //         "B卖家",
    //         "21"
    //       ],
    //       [
    //         "2209221993433",
    //         "杭州乐多趣行旅游专营店",
    //         "B卖家",
    //         "21"
    //       ],
    //       [
    //         "847325554",
    //         "民间生活服务",
    //         "B卖家",
    //         "21"
    //       ],
    //       [
    //         "2294108597",
    //         "福建光大国际旅行社专营店",
    //         "B卖家",
    //         "21"
    //       ],
    //       [
    //         "2208097121626",
    //         "广东海泉文旅行社专营店",
    //         "B卖家",
    //         "20"
    //       ],
    //       [
    //         "643451760",
    //         "wodeazheng",
    //         "C卖家",
    //         "20"
    //       ],
    //       [
    //         "2472884144",
    //         "佛山爱度假旅行社专营店",
    //         "B卖家",
    //         "20"
    //       ],
    //       [
    //         "231901624",
    //         "广东粤东",
    //         "C卖家",
    //         "20"
    //       ],
    //       [
    //         "2208672679107",
    //         "莆田九龙谷景区旗舰店",
    //         "B卖家",
    //         "20"
    //       ],
    //       [
    //         "3471466586",
    //         "上海星共国际旅行社专营店",
    //         "B卖家",
    //         "20"
    //       ],
    //       [
    //         "2206594129235",
    //         "深圳首佳酒店专营店",
    //         "B卖家",
    //         "20"
    //       ],
    //       [
    //         "3585271979",
    //         "湖南尚上旅游专营店",
    //         "B卖家",
    //         "20"
    //       ],
    //       [
    //         "865126249",
    //         "北京中凯假日票务",
    //         "B卖家",
    //         "19"
    //       ],
    //       [
    //         "2208528811434",
    //         "清远粤游之旅旅游专营店",
    //         "B卖家",
    //         "19"
    //       ],
    //       [
    //         "2200717009817",
    //         "广州粤游国旅专营店",
    //         "B卖家",
    //         "19"
    //       ],
    //       [
    //         "1768216229",
    //         "陈宝贝ch",
    //         "C卖家",
    //         "18"
    //       ],
    //       [
    //         "931545445",
    //         "银旅通旅游",
    //         "B卖家",
    //         "18"
    //       ],
    //       [
    //         "2208244813875",
    //         "广州旅刻视界旅游专营店",
    //         "B卖家",
    //         "18"
    //       ],
    //       [
    //         "2206636046325",
    //         "广州叮叮游酒店专营店",
    //         "B卖家",
    //         "18"
    //       ],
    //       [
    //         "1650382590",
    //         "深圳平安假日国际旅行社",
    //         "B卖家",
    //         "18"
    //       ],
    //       [
    //         "3817970750",
    //         "秦皇岛逸行旅游专营店",
    //         "B卖家",
    //         "18"
    //       ],
    //       [
    //         "3430075157",
    //         "南京途之旅酒店专营店",
    //         "B卖家",
    //         "18"
    //       ],
    //       [
    //         "4258388475",
    //         "敦煌四季阳光旅游专营店",
    //         "B卖家",
    //         "18"
    //       ],
    //       [
    //         "2980372673",
    //         "广东栖游记国旅专营店",
    //         "B卖家",
    //         "18"
    //       ],
    //       [
    //         "2201294299149",
    //         "klook客路旅游旗舰店",
    //         "B卖家",
    //         "18"
    //       ],
    //       [
    //         "2050808813",
    //         "厦门阅旅销售旅游专营店",
    //         "B卖家",
    //         "17"
    //       ],
    //       [
    //         "2200692824051",
    //         "广州指南针旅行社专营店",
    //         "B卖家",
    //         "17"
    //       ],
    //       [
    //         "3822101231",
    //         "tb823779463",
    //         "C卖家",
    //         "17"
    //       ],
    //       [
    //         "516253376",
    //         "西安新浪潮国际旅行社",
    //         "C卖家",
    //         "17"
    //       ]
    //     ]
    //   }
    // ];

    const searchForm = {
      searchForm: searchFormData,
      setting: this.walker(rules),
      onValuesChange: values => {
        this.setState(prevState => ({
          searchFormData: {
            ...prevState.searchFormData,
            ...values,
          },
        }));
      },
      handleSubmit: values => {
        this.filter(values);
      },
    };

    // 白名单
    const whiteColumns = [
      ...columns,
      {
        title: '操作',
        dataIndex: 'action',
        fixed: 'right',
        render: (text, record) => (
          <span>
            <Popconfirm
              disabled={!activityId}
              title="确定排除该商品吗？"
              onConfirm={() => this.addOrRemoveGood('remove', activityId, record.bizId)}
              okText="确定"
              cancelText="取消"
            >
              <a disabled={!activityId}>排除</a>
            </Popconfirm>
          </span>
        ),
      },
    ];

    const blackColumns = [
      ...columns,
      {
        title: '操作',
        dataIndex: 'action',
        fixed: 'right',
        render: (text, record) => (
          <span>
            <Popconfirm
              disabled={!activityId}
              title="确定恢复该商品吗？"
              onConfirm={() => this.addOrRemoveGood('recover', activityId, record.bizId)}
              okText="确定"
              cancelText="取消"
            >
              <a disabled={!activityId}>恢复</a>
            </Popconfirm>
          </span>
        ),
      },
    ];
    return (
      <PageHeaderWrapper inner title="时空玩法盘货">
        <Card style={{ marginBottom: 12 }}>
          <SearchForm key="searchForm" {...searchForm} />
        </Card>

        <Card bordered>
          <div style={{ marginTop: 12, marginBottom: 12 }}>
            <AdvancedForm submit={this.save} formData={activity} />
          </div>

          <Tabs
            defaultActiveKey="1"
            tabBarExtraContent={<span>{`共筛选出${pagination.total || 0}个商品`}</span>}
          >
            <TabPane tab="商品列表" key="1">
              <Table
                bordered
                dataSource={goodList}
                columns={whiteColumns}
                rowKey={record => record.bizId}
                loading={loading}
                scroll={{ x: 3500 }}
                pagination={pagination}
                onChange={this.handleTableChange}
              />

              <Card title="被排除商品">
                <Table
                  bordered
                  dataSource={blackItems}
                  columns={blackColumns}
                  rowKey={record => record.bizId}
                  scroll={{ x: 3500 }}
                  onChange={this.handleTableChange}
                />
              </Card>
            </TabPane>
            <TabPane tab="商品画像" key="2">
              <div className={styles.portrait}>
                {portraitData &&
                  portraitData.map(item => {
                    const { title = '', kvPair, matrix } = item;

                    if (kvPair) {
                      const pieData = Object.keys(kvPair)
                        .slice(0, 10)
                        .filter(key => kvPair[key] !== '0.0')
                        .map(key => ({
                          item: key,
                          count: parseFloat(kvPair[key]),
                        }));
                      return (
                        <div style={{ minHeight: 280, width: 350 }}>
                          <PieChart data={pieData} height={240} />
                        </div>
                      );
                    }
                    const [head = [], ...tableData] = matrix;
                    const cols = head.map((h, i) => ({
                      title: h,
                      dataIndex: i,
                    }));
                    return (
                      <div style={{ marginRight: 40, marginLeft: 40 }}>
                        <h3 style={{ fontSize: 13, marginBottom: 20 }}>{title}</h3>
                        <Table
                          columns={cols}
                          dataSource={tableData.slice(0, 10)}
                          pagination={false}
                        />
                      </div>
                    );
                  })}
              </div>
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    );
  }
}

export default PlayProductPool;
