import { stringify } from 'qs';
import request from '@/utils/request';

/**
 * 联想接口（叶子类目）
 * @param {*} params
 */
export function queryCategoryList() {
  return request('/api/tripGalaxy/suggestQueryService/queryCategoryList');
}

/**
 * 联想接口（公共）
 * @param {*} params
 */
export function queryCommonSuggest(params) {
  return request(`/api/tripGalaxy/suggestQueryService/queryCommonSuggest?${stringify(params)}`);
}

/**
 * 联想接口（玩法）
 * @param {*} params
 */
export function queryTreeData(params) {
  return request(`/api/tripGalaxy/suggestQueryService/queryTreeData?${stringify(params)}`);
}

/**
 * 诸葛选品规则
 */
export async function queryZhugeRule() {
  return request('/api/rule/chooseProductRule/list');
}

/**
 * 商品list
 * @param {*} params
 */
export async function queryItemsPageByRule(data) {
  return request('/api/tripGalaxy/itemQueryService/queryItemsPageByRule', {
    method: 'POST',
    data,
  });
}

/**
 * 商品画像
 * @param {*} params
 */
export async function queryItemsPortrait(data) {
  return request('/api/tripGalaxy/itemQueryService/queryItemsPortrait', {
    method: 'POST',
    data,
  });
}

/**
 * 发布子活动接口
 * @param {*} data
 */
export async function addTopicActivity(data) {
  return request('/api/tripGalaxy/addActivityService/addTopicActivity', {
    method: 'POST',
    data,
  });
}

/**
 * 根据id查询子活动
 * @param {*} params
 */
export async function batchQueryActivity(params) {
  return request(`/api/tripGalaxy/activityQueryService/batchQueryActivity?${stringify(params)}`);
}

/**
 * 商品排除接口
 * @param {*} params
 */
export async function addBlackItem(data) {
  return request('/api/tripGalaxy/addWhiteItemService/addBlackItem', {
    method: 'POST',
    data,
  });
}

/**
 * 商品恢复接口
 * @param {*} params
 */
export async function removeBlackItem(data) {
  return request('/api/tripGalaxy/addWhiteItemService/removeBlackItem', {
    method: 'POST',
    data,
  });
}

/**
 * 选品发布接口
 * @param {*} data
 */
export async function publishActivity(data) {
  return request('/api/tripGalaxy/addActivityService/publishActivity', {
    method: 'POST',
    data,
  });
}

/**
 * 商品黑名单
 * @param {*} params
 */
export async function queryBlackItemList(params) {
  return request(`/api/tripGalaxy/addWhiteItemService/blackItemList?${stringify(params)}`);
}

/**
 * 诸葛选品任务接口
 * @param {*} params
 */
export async function queryZhugeItemList(params) {
  return request(`/api/crowd/algoModelTaskConfig/list?${stringify(params)}`);
}
