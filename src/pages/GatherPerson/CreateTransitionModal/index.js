import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Card,
  Input,
  Switch,
  Select,
  DatePicker,
  Row,
  Col,
  Button,
  message,
  Radio,
  Drawer,
  Spin,
  Modal,
  InputNumber,
} from 'antd';
import { connect } from 'dva';
import GetUser from '@/components/GetUser';
import TransCrowd from './components/TransCrowd';
import { get } from 'lodash';

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

const FormItem = Form.Item;
const { Option } = Select;
const RadioGroup = Radio.Group;

@Form.create()
@connect(state => ({ user: state.user }))
class CreateTransitionModal extends React.PureComponent {
  constructor(props) {
    super(props);
  }

  handleSubmit = () => {
    const {
      form,
      onSubmit,
      editFormData,
      user: { currentUser },
    } = this.props;
    form.validateFields((err, values) => {
      if (!err) {
        const {isLoss, needUpdate} = values;
        const data = values
        data.expiredDate = values.expiredDate.valueOf();
        data.needUpdate = needUpdate ? 1 : 0,
        data.crowdTags = [];

        if (values.mainNode.type === "LABEL") {
          if (!get(values,'mainNode.labelInfo.value','') || !get(values,'mainNode.labelInfo.value.length', 0)) {
            message.error('标签项不能为空')
            return
          }
        }

        if (values.mainNode.type === "CROWD") {
          if (!values.mainNode.code) {
            message.error('请输入人群ID')
            return
          }
        }

        if (!data.extInfo) {
          data.extInfo = {}
        }

        data.profileType = editFormData.profileType || 'TAOBAO_USER'

        // 涉及资损
        if (isLoss === 1) {
          data.crowdTags.push('LOSS_OF_ASSETS');
        }

        data.crowdType = 'TRANSITION_CROWD';

        // 提交审核信息
        if (data.applyApprovalReason && data.qualityOwner) {
          data.crowdApprovalInfo = {
            applyApprovalReason: data.applyApprovalReason,
            applyOwner:{empId: currentUser.workId, nickName: currentUser.name},
            qualityOwner: data.qualityOwner,
            needSubmit: true
          }
        }

        if (data.applyApprovalReason) {
          delete data.applyApprovalReason
        }
        if (data.qualityOwner) {
          delete data.qualityOwner
        }

        if (data.needUpdate) {
          if (!data.extInfo.UPDATE_MODEL) {
            data.extInfo.UPDATE_MODEL = {};
          }
          data.extInfo.UPDATE_MODEL.updateType = data.updateType === 1 ? 'dependence' : 'every_day';
        } else if (data.extInfo.UPDATE_MODEL) {
          delete data.extInfo.UPDATE_MODEL.updateType
        }

        data.extInfo.mainNode = values.mainNode;
        data.extInfo.subNode = values.subNode;
        data.extInfo.transitionDirection = values.transitionDirection
        data.extInfo.validTs = values.validTs * 86400000
        delete data.mainNode
        delete data.subNode
        delete data.validTs

        console.log('values---eeee-', data);
        // return

        onSubmit(data)

      }
    });
  };

  renderForm = () => {
    const {
      form: { getFieldDecorator, getFieldValue },
      editFormData,
    } = this.props;

    const disabledDate = current => {
      return current < dayjs().startOf('day') || current > dayjs() + 90 * 24 * 60 * 60 * 1000;
    };

    const topHander = data => {
      const {
        type,
        value,
      } = data;
      const formData = this.props.form.getFieldsValue();
      if (formData) {
        if (!formData.extInfo) {
          formData.extInfo = {};
        }
        if (type === 'needUpdate') {
          if (value) {
            if (!formData.extInfo.UPDATE_MODEL) {
              formData.extInfo.UPDATE_MODEL = {};
            }
            formData.extInfo.UPDATE_MODEL.updateType = formData.updateType;
          } else if (formData.extInfo.UPDATE_MODEL) {
            delete formData.extInfo.UPDATE_MODEL.updateType
          }
        }
      }

      if (value && type === 'needUpdate') {
        const {
          form: { setFieldsValue },
        } = this.props;
        Modal.confirm({
          title: '每日更新计算资源消耗较大，请确认是否是必要选项',
          okText:"确认",
          cancelText:"取消",
          onOk() {
            setFieldsValue({needUpdate: true})
          },
          onCancel() {
            setFieldsValue({needUpdate: false})
          },
        });
      }

      this.props.form.setFieldsValue({
        extInfo: formData.extInfo,
      });
    }

    return (
      <Form {...FORM_ITEM_LAYOUT} onSubmit={this.handleSubmit}>
        <FormItem label="人群名称">
          {getFieldDecorator('crowdName', {
            initialValue: editFormData.crowdName,
            rules: [{ required: true, message: '请输入人群名称' }],
          })(<Input placeholder="请输入人群名称" allowClear />)}
        </FormItem>
        <FormItem label="人群描述">
          {getFieldDecorator('crowdDescription', {
            initialValue: editFormData.crowdDescription,
            rules: [{ required: true, message: '请输入人群描述' }],
          })(<Input.TextArea placeholder="请输入人群描述" allowClear />)}
        </FormItem>
        <FormItem label="是否更新">
          {getFieldDecorator('needUpdate', {
            initialValue: !!editFormData.needUpdate,
            valuePropName: 'checked',
          })(<Switch onChange={checked => topHander({ type: 'needUpdate', value: checked })} />)}
        </FormItem>

        <FormItem label="更新类型" hidden={!getFieldValue('needUpdate')}>
          {
            getFieldDecorator('updateType', {
              initialValue: (
                editFormData.extInfo
                && editFormData.extInfo.UPDATE_MODEL
                && editFormData.extInfo.UPDATE_MODEL.updateType === 'dependence' ? 1 : 0
              )
            })(
              <RadioGroup onChange={e => topHander({ type: 'updateType', value: e.target.value })}>
                <Radio value={0}>每天更新</Radio>
              </RadioGroup>
            )
          }
        </FormItem>

        <FormItem label="是否涉及资损">
          {getFieldDecorator('isLoss', {
            initialValue:
              editFormData.crowdTags &&
              (editFormData.crowdTags.length > 0 &&
              editFormData.crowdTags.includes('LOSS_OF_ASSETS')
                ? 1
                : 0),
            rules: [{ required: true, message: '请选择是否涉及资损' }],
          })(
            <RadioGroup>
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </RadioGroup>
          )}
        </FormItem>

        {getFieldValue('isLoss') === 1 && (
          <FormItem
            label="详细描述"
            extra={
              <span style={{ color: 'red' }}>
                注：涉及资损的人群包将通过审批流获取额外测试保障，测试通过将后自动上线
              </span>
            }
          >
            {getFieldDecorator('applyApprovalReason', {
              initialValue: editFormData?.crowdApprovalInfo?.applyApprovalReason,
              rules: [{ required: true, message: '请输入详细描述' }],
            })(<Input.TextArea placeholder="请详细描述下可能发生资损的场景" allowClear />)}
          </FormItem>
        )}

        {getFieldValue('isLoss') === 1 && (
          <FormItem label="QA">
            {getFieldDecorator('qualityOwner', {
              initialValue: editFormData?.crowdApprovalInfo?.qualityOwner,
              rules: [{ required: true, message: '请输入业务对应的测试同学' }],
            })(<GetUser mode="default" />)}
          </FormItem>
        )}

        <FormItem label="跃迁类型">
          {getFieldDecorator('transitionDirection', {
            initialValue: editFormData?.extInfo?.transitionDirection || 'IN',
            rules: [{ required: true, message: '请选择跃迁类型' }],
          })(
            <RadioGroup>
              <Radio value="IN">迁入</Radio>
              <Radio value="OUT">迁出</Radio>
            </RadioGroup>
          )}
        </FormItem>

        {getFieldValue('transitionDirection') === 'IN' ? (
          <FormItem label="迁入人群">
            {getFieldDecorator('mainNode', {
              initialValue: editFormData?.extInfo?.mainNode || { type: 'LABEL' },
              rules: [{ required: true }],
            })(<TransCrowd />)}
          </FormItem>
        ) : (
          <FormItem label="迁出人群">
            {getFieldDecorator('mainNode', {
              initialValue: editFormData?.extInfo?.mainNode || { type: 'LABEL' },
              rules: [{ required: true }],
            })(<TransCrowd />)}
          </FormItem>
        )}

        {getFieldValue('transitionDirection') === 'IN' ? (
          <FormItem label="迁出人群">
            {getFieldDecorator('subNode', {
              initialValue: editFormData?.extInfo?.subNode || { type: 'LABEL' },
            })(<TransCrowd />)}
          </FormItem>
        ) : (
          <FormItem label="迁入人群">
            {getFieldDecorator('subNode', {
              initialValue: editFormData?.extInfo?.subNode || { type: 'LABEL' },
            })(<TransCrowd />)}
          </FormItem>
        )}

        <FormItem label="跃迁有效期">
          {getFieldDecorator('validTs', {
            initialValue: (editFormData?.extInfo?.validTs / 86400000 ) || 1,
            rules: [{ required: true, message: '请输入跃迁有效期' }],
          })(
            <InputNumber
              min={0}
              formatter={value => `${value}天`}
              parser={value => value.replace('天', '')}
            />
          )}
        </FormItem>

        <FormItem
          label="人群使用场景"
          extra={
            <span style={{ color: 'red' }}>
              注：非PUSH场景请选中人群匹配，否则会导致无法判断人群
            </span>
          }
        >
          {getFieldDecorator('crowdApplyScene', {
            initialValue: editFormData.crowdApplyScene,
            rules: [{ required: true, message: '请选择人群使用场景' }],
          })(
            <Select placeholder="请选择人群使用场景" mode="multiple">
              <Option key="PUSH">PUSH</Option>
              <Option key="MATCH">人群匹配</Option>
              {/* <Option key="ANALYSIS">人群分析</Option> */}
            </Select>
          )}
        </FormItem>

        <FormItem label="人群过期时间">
          {getFieldDecorator('expiredDate', {
            initialValue: editFormData.expiredDate ? dayjs(editFormData.expiredDate) : undefined,
            rules: [{ required: true, message: '请选择人群人群过期时间' }],
          })(
            <DatePicker
              disabledDate={disabledDate}
              format="YYYY-MM-DD HH:mm:ss"
              showTime={{
                defaultValue: dayjs('23:59:59', 'HH:mm:ss'),
              }}
            />
          )}
        </FormItem>

        <FormItem label="管理员">
          {getFieldDecorator('operator', {
            initialValue: editFormData.operator,
            rules: [{ required: true, message: '请选择人群管理员' }],
          })(<GetUser />)}
        </FormItem>
      </Form>
    );
  };

  onCancel = () => {
    const { onCancel = noop } = this.props;
    onCancel();
  };

  render() {
    const {
      editModalVisible,
      editFormData,
      user: { currentUser, isSuperAdmin },
    } = this.props;

    const isSubmit = () => {
      // 管理者和创建者及超级管理员能编辑
      const { creator = {}, operator = [] } = editFormData;
      const owners = [...operator, creator];

      const isGranted = owners.map(o => o.empId).includes(currentUser.workId);
      
      if(!editFormData.id || isSuperAdmin || isGranted) {
        return false
      }
     
      return true
    }

    return (
      <Drawer
        title={<div>跃迁圈人<a href= "https://aliyuque.antfin.com/dl7v63/zwzgqr/rnmh7trk6qqwgc6v?singleDoc#" target='_blank'>【公告】以下标签后续将开始进行费用分摊，请查阅</a></div>}
        placement="right"
        visible={editModalVisible}
        destroyOnClose
        width="800"
        onClose={this.onCancel}
        footer={
          <div
            style={{
              textAlign: 'right',
            }}
          >
            <Button onClick={() => this.onCancel()} style={{ marginRight: 15 }}>
              取消
            </Button>
            <Button type="primary" onClick={this.handleSubmit} disabled={isSubmit()}>
              保存
            </Button>
          </div>
        }
      >
        {this.renderForm()}
      </Drawer>
    );
  }
}

export default CreateTransitionModal;
