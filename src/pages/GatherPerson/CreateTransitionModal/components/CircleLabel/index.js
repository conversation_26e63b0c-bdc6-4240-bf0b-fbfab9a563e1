import React, { Fragment } from 'react';
import { Radio, Button, message, Spin, Tooltip, Tag } from 'antd';
import { CRMUrl, getUrlParam, zhugeUrl } from '@/utils/utils';
import SelectTagModal from '@/components/SelectTagModal';
import SelectEnumValue from '../../../GpByTag/components/SelectEnumValueModal';
import { circleform } from '@/utils/crowd';
import styles from './index.less';
import { cloneDeep } from 'lodash';

class CircleLabel extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      tagArr: props.value ? circleform(props.value) : {},
      selectTagModalVisible: false,
      selectEnumValueModalVisible: false,
    };
  }

  

  // 打开选择值弹窗 id 为标签id
  openSelectEnumValueModal = tag => {
    // const { isDisabled } = this.props
    // if (isDisabled) return
    // if ((tag && tag.dataType === 'ENUM') || tag.dataType === 'MULTI_VALUE') {
    //   const { dimEnumMetaId } = tag;
    //   if (!dimEnumMetaId || dimEnumMetaId === -1) {
    //     message.error('该标签未选择对应枚举对象');
    //     return;
    //   }
    // }

    // eslint-disable-next-line no-param-reassign
    tag.index = 0;

    this.setState({
      selectEnumValueModalVisible: true,
      curTag: tag,
    });
  };

  // 打开选择标签
  openSelectTagModal = () => {
    this.setState({
      selectTagModalVisible: true,
    });
  };

  cancelSelectTagModal = () => {
    // 修复antd的Modal和Drawer一起使用时的样式bug
    document.body.style.overflow = 'auto';
    this.setState({
      selectTagModalVisible: false,
    });
  };

  onSelectedTags = selectedTags => {
    this.setState({
      tagArr: selectedTags[0],
      selectTagModalVisible: false,
    });

    return true;
  };

  // 每次改变标签值触发这个方法
  triggerChange = () => {
    const { onChange } = this.props;
    const { tagArr } = this.state;
    let ret = {}
    if (JSON.stringify(tagArr) !== '{}') {
      ret = {
        code: tagArr.code || tagArr.propertyName,
        desc: tagArr.name || tagArr.propertyDescription,
        type: tagArr.dataType,
        dimEnumMetaId: tagArr.dimEnumMetaId,
        value: (tagArr.values || []).map(item => {
          let res = '';

          if (tagArr.dataType === 'ENUM' || tagArr.dataType === 'MULTI_VALUE') {
            res = `${item.value} ${item.label}`;
          } else {
            res = item.value;
          }
          return res;
        }), 
      }
    }

    if (onChange) {
      if (JSON.stringify(ret) !== '{}') {
        let temp = {
          code: ret.code,
          labelInfo: {
            desc: ret.desc,
            type: ret.type,
            dimEnumMetaId: ret.dimEnumMetaId,
            value: ret.value
          }
        }
        onChange(temp);
      } else {
        onChange(ret);
      } 
    }
  };

  // 选择值之后的回调
  onSelectedEnumValue = data => {
    const { tagArr } = this.state;
    const tag = cloneDeep(tagArr);
    if (tag) {
      tag.editFormData = data; // 记录提交的信息，编辑的时候使用
      // tag.values为显示使用，value为前后端数据格式使用
      if (tag.dataType === 'DATE') {
        const { dateType, startDate, endDate, pastTime, futureTime } = data;
        if (dateType === 'fixed') {
          tag.values = [
            { label: `开始时间:${startDate}`, value: startDate },
            { label: `结束时间:${endDate}`, value: endDate },
          ];
        } else if (dateType === 'relativePast') {
          tag.values = [{ label: `最近${pastTime}天`, value: parseInt(`-${pastTime}`, 10) }];
        } else if (dateType === 'relativeFuture') {
          tag.values = [{ label: `未来${futureTime}天`, value: futureTime }];
        }
      } else if (tag.dataType === 'ENUM' || tag.dataType === 'MULTI_VALUE') {
        const { enumValues } = data;
        tag.values = enumValues;
      } else if (tag.dataType === 'NUMBER') {
        const {
          numberValue: { min, max },
        } = data;
        tag.values = [
          { label: `最小值:${min}`, value: min },
          { label: `最大值:${max}`, value: max },
        ];
      } else if (tag.dataType === 'KV') {
        const { targets } = data;
        tag.values = targets.map(t => {
          const weight = data[`${t.value}_weight`];
          const labelWeight = weight.join('~');
          const valueWeight = weight.join(' ');
          return {
            label: `${t.label}：${labelWeight}`,
            value: `${t.value} ${t.label} ${valueWeight}`, // 服务端约定接口格式
          };
        });
      }

      this.setState({
        tagArr:tag,
        selectEnumValueModalVisible: false,
      },() => {
        this.triggerChange();
      });
    }
  };

  removeSelectedTag = () => {
    this.setState({
      tagArr: {},
    },() => {
      this.triggerChange();
    });
  };

  // 关闭弹窗
  cancelSelectEnumValueModal = () => {
    this.setState({
      selectEnumValueModalVisible: false,
      curTag: undefined,
    });
  };

  

  render() {
    const isShow = tag => {
      if (tag?.dataType === 'NONE_TYPE') {
        if (!tag?.sourceConfig?.sqlConfig?.sqlParams) {
          if (tag?.editFormData && !(JSON.stringify(tag?.editFormData) === '{}')) {
            return false;
          }
          if (getUrlParam('crowdParams') && tag?.sqlParams?.length === 0) {
            return false;
          }
          return true;
        }
      }
      return false;
    };

    const { tagArr, selectTagModalVisible, curTag, selectEnumValueModalVisible } = this.state;
    const { isDisabled } = this.props
    return (
      <div>
        {JSON.stringify(tagArr) === '{}' && (
          <Button shape="round" onClick={() => this.openSelectTagModal()}>
            添加标签
          </Button>
        )}
        {JSON.stringify(tagArr) !== '{}' && (
          <div className={styles.tagInfo}>
            <Tooltip
              title={`${tagArr.name || tagArr.propertyDescription}(${tagArr.code ||
                tagArr.propertyName})`}
            >
              <span className={styles.tagName}>{`${tagArr.name ||
                tagArr.propertyDescription}(${tagArr.code || tagArr.propertyName})`}</span>
            </Tooltip>

            <span className={styles.manzuText}> 满足 </span>
            {isShow(tagArr) ? (
              <div style={{ display: 'flex', flex: 1, justifyContent: 'center' }}>无需选择参数</div>
            ) : (
              <div
                className={styles.enumValue}
                onClick={() => this.openSelectEnumValueModal(tagArr)}
              >
                {tagArr.values &&
                  tagArr.values.length > 0 &&
                  tagArr.values.map(v => (
                    <Tag className={styles.myTag} color="cyan">
                      {/*  判断如果包含&gt; 替换为 >  */}
                      {v.label
                        ? v.label.includes('&gt;')
                          ? v.label.replace('&gt;', '>')
                          : v.label
                        : null}
                    </Tag>
                  ))}
              </div>
            )}
            {
              !isDisabled && <div className={styles.tagDelIcon} onClick={() => this.removeSelectedTag()}>
              x
            </div>
            }
          </div>
        )}

        <SelectTagModal
          // eslint-disable-next-line no-return-assign
          isCircle={true}
          visible={selectTagModalVisible}
          onCancel={this.cancelSelectTagModal}
          onOk={this.onSelectedTags}
        />

        <SelectEnumValue
          title={curTag ? curTag.name || curTag.propertyDescription : '请选择'}
          visible={selectEnumValueModalVisible}
          onCancel={this.cancelSelectEnumValueModal}
          tag={curTag}
          onOk={this.onSelectedEnumValue}
        />
      </div>
    );
  }
}

export default CircleLabel;
