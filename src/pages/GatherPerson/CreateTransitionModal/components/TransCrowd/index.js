import React, { Fragment } from 'react';
import { Radio, Button, message, Spin, Tooltip, Input } from 'antd';
import { CRMUrl, getUrlParam, zhugeUrl } from '@/utils/utils';
import CircleLabel from '../CircleLabel';
import { cloneDeep } from 'lodash';

class TransCrowd extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      radioValue: props.value && props.value.type ? props.value.type : 'LABEL'
    };
  }

  onCrowdRadio = e => {
    const { onChange, value } = this.props;
    this.setState({
      radioValue: e.target.value
    })
    onChange({ type: e.target.value });
  };

  onCrowdInput = e => {
    this.onTriggerChange({ code: e.target.value });
  };

  onCircleLabel = value => {
    this.onTriggerChange(value);
  };

  onTriggerChange = changeValue => {
    const { onChange, value } = this.props;
    const { radioValue } = this.state
    onChange({type:radioValue ,...changeValue });
  };

  onCircleLabelValue = (value) => {
    let temp = cloneDeep(value)
    delete temp.type
    let res = {
      code: temp.code,
      ...temp.labelInfo
    }
    return res
  }

  render() {
    const { value, isDisabled } = this.props;
    const { radioValue } = this.state
    return (
      <div>
        <Radio.Group
          onChange={this.onCrowdRadio}
          value={value && value.type ? value.type : 'LABEL'}
          disabled={isDisabled}
        >
          <Radio.Button value="LABEL">标签</Radio.Button>
          <Radio.Button value="CROWD">人群</Radio.Button>
        </Radio.Group>
        {radioValue === 'CROWD' ? (
          <Input
            onChange={this.onCrowdInput}
            value={value && value.code ? value.code : null}
          />
        ) : (
          <CircleLabel onChange={this.onCircleLabel} value={this.onCircleLabelValue(value)} isDisabled={isDisabled}/>
        )}
      </div>
    );
  }
}

export default TransCrowd;
