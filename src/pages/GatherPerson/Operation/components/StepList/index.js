import React, { useState, Fragment, useEffect } from 'react';
import '@ant-design/compatible/assets/index.css';
import { Modal, Button, Input, Row, Col, Radio, Select, Checkbox, Steps, Tag } from 'antd';
import { Link }  from 'umi';
import { OPERATION_STATUS } from '@/constants';
import { ExportOutlined } from '@ant-design/icons';
import { getBpmsUrl } from '@/utils/utils';

import styles from './index.less';
import Editor from '@monaco-editor/react';
import CodeMirror from '@/components/CodeMirror/CodeMirrorInput';
import dayjs from 'dayjs';

const { Step } = Steps;

const StepList = props => {
  const { dataSource, initVal, nodeNum } = props;

  const [sqlInfo, setSqlInfo] = useState(initVal);
  const [idx, setIdx] = useState(0);

  const iconStyle = (num, index, nodeDesc) => {
    let stepNum = num;
    if (nodeNum && num >= nodeNum && num < dataSource.nodes.length) {
      if (dataSource.nodes.length - nodeNum) {
        stepNum = `${nodeNum}.${num - (nodeNum - 1)}`;
      }
    }

    if (nodeNum && num === dataSource.nodes.length) {
      stepNum = nodeNum + 1
    }

    return <div className={`${styles.icon_style} ${index === idx ? styles.active : ''}`}>{stepNum}</div>;
  };

  const statusButton = (data, index) => {
    if (data && data.nodeStatus) {
      const obj = OPERATION_STATUS[data?.nodeStatus];
      const btnType = type => {
        if (type === 'SUCCESS' || type === 'FAILED' || type === 'INIT' || type === 'RUNNING') {
          return (
            <Button
              type="link"
              onClick={() => {
                setSqlInfo(data?.nodeSqlInfo);
                setIdx(index)
              }}
              className={index === idx ? styles.active_txt : ''}
            >
              查看
            </Button>
          );
        } else {
          return null;
        }
      };

      return (
        <span>
          <Tag color={obj.color}>{obj.text}</Tag>
          {btnType(data?.nodeStatus)}
          {['APPROVAL_RUNNING', 'APPROVAL_REJECTED']?.includes(data?.nodeStatus) && <Link style={{ marginLeft: 10 }} onClick={() => {window.open(getBpmsUrl(data?.approvalId))}}>
            <ExportOutlined />
          </Link>}
        </span>
      );
    }
  };

  // 判断是否为http或https开头的url
  const isHttp = url => {
    let expression = /(http|https):\/\/([\w.]+\/?)\S*/gi;
    let objExp = new RegExp(expression);
    return objExp.test(url);
  };

  const showSqlInfo = (sqlInfo) => {
    if (sqlInfo) {
      if (isHttp(sqlInfo)) {
        return <a href={sqlInfo} target="_blank">
          {sqlInfo}
        </a>
      } else {
        return <CodeMirror mode="sql" value={sqlInfo} style={{ lineHeight: '20px', height: '300px', width: "100%" }} />
      }
    } else {
      return <div>暂无可用信息</div>
    }
  }

  return (
    <div style={{ minHeight: '400px' }}>
      <Row>
        <Col span={10}>
          <p>
            {dataSource &&
              dataSource.nodes &&
              dataSource.nodes.length > 0 &&
              dataSource.nodes[0].nodeBeginTime &&
              `启动时间：${dayjs(dataSource.nodes[0].nodeBeginTime).format(
                'YYYY-MM-DD HH:mm:ss'
              )}`}
          </p>
          <Steps direction="vertical">
            {dataSource &&
              dataSource.nodes &&
              dataSource.nodes.length > 0 &&
              dataSource.nodes.map((ele, index) => {
                return (
                  <Step
                    title={
                      <div
                        style={{
                          width: '120px',
                          display: 'inline-block',
                          color: 'black',
                          fontWeight: '400',
                        }}
                      >
                        {ele.nodeDesc}
                      </div>
                    }
                    icon={iconStyle(index + 1, index, ele?.nodeDesc)}
                    subTitle={statusButton(ele, index)}
                  />
                );
              })}
          </Steps>
        </Col>
        <Col span={14}>
          <p>{dataSource?.dagId && `任务ID：${dataSource.dagId}`}</p>
          <div style={{ height: '100%' }}>
            {showSqlInfo(sqlInfo)}
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default StepList;
