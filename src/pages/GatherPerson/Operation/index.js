import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Card,
  Input,
  Switch,
  Select,
  DatePicker,
  Row,
  Col,
  Button,
  message,
  Radio,
  Drawer,
  Descriptions,
  Tag,
  Tabs,
} from 'antd';
import dayjs from 'dayjs';
import { Link }  from 'umi';
import { connect } from 'dva';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { CROWD_PROGRESS_STATUS, CROWD_TYPE, CROWD_APPLE_SCENNE } from '@/constants';
import { getCrowdTypeText } from '../../GatherPerson/constants'
import StepList from './components/StepList';
import styles from './index.less';
import { get } from 'lodash';
const { TabPane } = Tabs;
@connect(state => ({ operation: state.operation }))
class Operation extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {};
  }

  statusType = crowdStatus => {
    const obj = CROWD_PROGRESS_STATUS[crowdStatus];
    return (
      <span>
        <Tag color={obj.color}>{obj.text}</Tag>
      </span>
    );
  };

  toExpiredDate = time => {
    const isExpired = Date.now() > time;
    return isExpired ? '已过期' : dayjs(time).format('YYYY-MM-DD HH:mm:ss');
  };

  toSupport = crowdApplyScene => {
    return crowdApplyScene.map(ele => CROWD_APPLE_SCENNE[ele]).join('/');
  };

  onNodeNum = (ele) => {
    let nodeNum = 0
    ele.nodes && ele.nodes.length > 0 && ele.nodes.forEach((e,index) => {
      if (e.nodeDesc.includes('写入ODPS')) {
        nodeNum = index + 2
      }
    })
    return nodeNum
  }

  render() {
    const {
      dispatch,
      operation: { dagsList, descriptionArr, loadingTop,loadingBot },
    } = this.props;
    let showText = descriptionArr.crowdType && CROWD_TYPE[descriptionArr.crowdType]
    if ( descriptionArr.profileType && descriptionArr.profileType === 'LABEL_CROWD' && showText) {
      showText = getCrowdTypeText(descriptionArr.profileType, CROWD_TYPE[descriptionArr.crowdType])
    }
    return (
      <PageHeaderWrapper>
        <Card bordered={false} bodyStyle={{ paddingTop: '20px' }} loading={loadingTop || loadingBot}>
          <Descriptions
            title={
                                                        <div className={styles.operation_title}>
                <span
                  style={{
                    marginBottom: 0,
                    marginRight: '20px',
                    fontSize: '20px',
                    fontWeight: '500',
                    color: 'rgba(0, 0, 0, 0.85)',
                    lineHeight: '32px',
                    display: 'inline-block'
                  }}
                >{`${descriptionArr.crowdName}(${descriptionArr.id})`}</span>
                                {descriptionArr.crowdStatus && (
                  <span style={{
                    lineHeight: '32px',
                    display: 'inline-flex',
                    alignItems: 'center',
                    height: '32px'
                  }}>
                    {this.statusType(descriptionArr.crowdStatus)}
                  </span>
                )}
                {/* 错误信息显示在标题右边 */}
                {descriptionArr.crowdStatus === 'ERROR' && get(descriptionArr, 'extInfo.crowdBuildRecord.errMsg') && (
                  <span style={{
                    color: '#cf1322',
                    backgroundColor: '#fff1f0',
                    padding: '6px 12px',
                    borderRadius: '4px',
                    border: '1px solid #ffa39e',
                    fontSize: '13px',
                    lineHeight: '20px',
                    marginLeft: '12px',
                    display: 'inline-flex',
                    alignItems: 'center',
                    height: '32px'
                  }}>
                    <span style={{ marginRight: '4px' }}>⚠️</span>
                    {get(descriptionArr, 'extInfo.crowdBuildRecord.errMsg')}
                  </span>
                )}
              </div>
            }
          >
            <Descriptions.Item label="类型">
              {showText}
            </Descriptions.Item>
            <Descriptions.Item label="创建人">
              {descriptionArr.creator && descriptionArr.creator.nickName}
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {descriptionArr.gmtCreate
                ? dayjs(descriptionArr.gmtCreate).format('YYYY-MM-DD HH:mm:ss')
                : '暂无'}
            </Descriptions.Item>
            <Descriptions.Item label="支持场景">
              {descriptionArr.crowdApplyScene && descriptionArr.crowdApplyScene.length > 0
                ? this.toSupport(descriptionArr.crowdApplyScene)
                : '暂无'}
            </Descriptions.Item>
            <Descriptions.Item label="过期时间">
              {descriptionArr.expiredDate && this.toExpiredDate(descriptionArr.expiredDate)}
            </Descriptions.Item>
            <Descriptions.Item label="上次构建时间">
              {descriptionArr.gmtLastBuilt
                ? dayjs(descriptionArr.gmtLastBuilt).format('YYYY-MM-DD HH:mm:ss')
                : '暂无'}
            </Descriptions.Item>
                        <Descriptions.Item label="人群规模数量">
              {descriptionArr.crowdAmount ? descriptionArr.crowdAmount : '暂无'}
            </Descriptions.Item>
          </Descriptions>
          {dagsList && dagsList.length > 0 && (
            <Tabs>
              {dagsList.map((ele, index) => {
                return (
                  <TabPane tab={`版本${dagsList.length - index}`} key={index + 1}>
                    <StepList dataSource={ele} initVal = {(ele.nodes && ele.nodes.length > 0 && ele.nodes[0].nodeSqlInfo) ? ele.nodes[0].nodeSqlInfo : ''} nodeNum={this.onNodeNum(ele)}/>
                  </TabPane>
                );
              })}
            </Tabs>
          )}
        </Card>
      </PageHeaderWrapper>
    );
  }
}

export default Operation;
