import crowdApprovalTip from '../../GatherPerson/components/CrowdApprovalTip'
import { CROWD_PROGRESS_STATUS } from '@/constants';
import { approvalApplySceneEnum } from '../constants'
import { getBpmsUrl } from '@/utils/utils';
import { get } from 'lodash'

// 统一处理更新时数据，extInfo中数据提取
const walker = (algoModels, editFormData, record) => {
  const [recordModel] = algoModels.filter(
    item => String(item.id) === String(editFormData.modelId)
  );

  if (!recordModel) {
    message.error('无算法模型匹配，无法编辑');
    return [];
  }

  const recordModelInputType = {};
  recordModel.inputArgs.forEach(item => {
    recordModelInputType[item.argName] = item.argType;
  });
  const recordModelOutputType = {};
  recordModel.outputArgs.forEach(item => {
    recordModelOutputType[item.argName] = item.argType;
  });
  const arrayType = ['checkbox', 'multiSelect'];

  const { inputExtInfo = [], outputExtInfo = [] } = record.extInfo;
  // 处理动态添加的表单项的初始值
  const inputs = inputExtInfo.reduce(
    (acc, cur) => ({
      ...acc,
      [cur.key]:
        arrayType.indexOf(recordModelInputType[cur.key]) !== -1
          ? getHtmlParseValue(cur.value)
          : cur.value,
    }),
    {}
  );
  const outputs = outputExtInfo.reduce(
    (acc, cur) => ({
      ...acc,
      [cur.key]:
        arrayType.indexOf(recordModelOutputType[cur.key]) !== -1
          ? getHtmlParseValue(cur.value)
          : cur.value,
    }),
    {}
  );

  return [recordModel, inputs, outputs];
};

// 统一处理逻辑
export const onEdit = (record, props) => {
  if (!record) {
    return;
  }
  const {
    dispatch,
    gatherPerson: { algoModels },
  } = props;

  if (record.crowdType === 'LABEL_CROWD') {
    // 标签圈人
    // router.push(`/crowd-stategy/crowd-circle/gather-person-by-tag?id=${record.id}`);
    const editFormData = record;
    // 审核中不允许编辑
    const approvalObj = getApprovalObj(get(record, 'approvalInfos', []), approvalApplySceneEnum.CROWD_UPDATE)
    if (approvalObj?.approvalStatus === 'APPROVAL_RUNNING') {
      crowdApprovalTip(approvalObj?.approvalId)
      return
    }
    
    dispatch({
      type: 'gpByTag/updateTagFormState',
      payload: editFormData,
    });
    dispatch({
      type: 'gpByTag/onCrowdByTagModalCancelOrShow',
      payload: true,
    });
  } else if (record.crowdType === 'ALGO_CROWD' || record.crowdType === 'TRIP_GALAXY_CROWD') {
    // 星辰&算法圈人
    const editFormData = record;

    const {
      ALGO_MODEL_META_INFO_ID,
      ALGO_MODEL_TASK_CONFIG_ID,
      RESULT_CROWD_AMOUNT_LIMIT,
    } = editFormData.extInfo;

    editFormData.modelId = ALGO_MODEL_META_INFO_ID;
    if (record.crowdType === 'TRIP_GALAXY_CROWD') {
      editFormData.linkGalaxyActivity = parseInt(ALGO_MODEL_TASK_CONFIG_ID, 10);
    } else {
      editFormData.linkGoodId = parseInt(ALGO_MODEL_TASK_CONFIG_ID, 10);
    }
    editFormData.resultAmount = RESULT_CROWD_AMOUNT_LIMIT;

    const [recordModel, inputs, outputs] = walker(algoModels, editFormData, record);
    if (!recordModel) return;

    dispatch({
      type: 'gatherPerson/onEditCrowdByAlgo',
      payload: { ...editFormData, modelName: recordModel.modelName, ...inputs, ...outputs },
    });
  } else if (record.crowdType === 'ALGO_ENLARGE_CROWD') {
    // 人群放大圈人
    const editFormData = record;
    const { extInfo = {} } = editFormData;

    const {
      ALGO_MODEL_META_INFO_ID,
      RESULT_CROWD_AMOUNT_LIMIT,
      ALGO_ENLARGE_SEED_CROWD_ID,
      ALGO_ENLARGE_EXCEPT_CROWD_ID,
      FILTER_ORIGIN_CROWD,
    } = extInfo;

    editFormData.modelId = ALGO_MODEL_META_INFO_ID;
    editFormData.resultAmount = RESULT_CROWD_AMOUNT_LIMIT;
    editFormData.seedCrowdId = ALGO_ENLARGE_SEED_CROWD_ID;
    editFormData.exceptCrowdId = ALGO_ENLARGE_EXCEPT_CROWD_ID;
    editFormData.filterOriginCrowd = FILTER_ORIGIN_CROWD;

    // const [recordModel, inputs, outputs] = this.walker(algoModels, editFormData, record);
    // if (!recordModel) return;

    dispatch({
      type: 'gatherPerson/onEditCrowdByEnLarge',
      // payload: { ...editFormData, modelName: recordModel.modelName, ...inputs, ...outputs },
      payload: { ...editFormData},
    });
  } else if (record.crowdType === 'ODPS_TABLE_CROWD') {
    // odps圈人
    const editFormData = record;
    const { extInfo = {} } = editFormData;

    const {
      ODPS_ACCESS_ID,
      ODPS_ACCESS_KEY,
      ODPS_TABLE,
      ODPS_TABLE_PARTITION,
      ODPS_TABLE_UID,
      ODPS_TABLE_WHERE,
    } = extInfo;

    editFormData.tableName = ODPS_TABLE;
    editFormData.partition = ODPS_TABLE_PARTITION;
    editFormData.uid = ODPS_TABLE_UID;
    editFormData.where = ODPS_TABLE_WHERE;
    editFormData.accessId = ODPS_ACCESS_ID;
    editFormData.accessKey = ODPS_ACCESS_KEY;
    dispatch({
      type: 'gatherPerson/onEditCrowdByOdps',
      payload: editFormData,
    });
  } else if (record.crowdType === 'ODPS_SQL_CROWD') {
    // sql圈人
    let editFormData = record;
    const { extInfo = {} } = editFormData;

    const { ODPS_SQL, templateId, templateSql, templateParams = {}, isAllow } = extInfo;
    editFormData.template = templateId;
    editFormData.sql = templateId == 0 ? ODPS_SQL : templateSql;
    editFormData.isAllow = isAllow;

    editFormData = Object.assign(editFormData, templateParams);

    dispatch({
      type: 'gatherPerson/onEditCrowdBySql',
      payload: editFormData,
    });
  } else if (record.crowdType === 'OPERATE_CROWD') {
    // 人群逻辑运算
    const editFormData = record;
    const { extInfo = {} } = editFormData;

    const { OPERATE_CROWD_LEFT, OPERATE_CROWD_RIGHT, OPERATE_CROWD_TYPE } = extInfo;
    editFormData.crowdLeft = OPERATE_CROWD_LEFT;
    editFormData.crowdRight = OPERATE_CROWD_RIGHT;
    editFormData.operateCrowdType = OPERATE_CROWD_TYPE;

    dispatch({
      type: 'gatherPerson/onEditCrowdByOperate',
      payload: editFormData,
    });
  } else if (record.crowdType === 'FILE_CROWD') {
    const editFormData = record;
    const { extInfo = {} } = editFormData;

    const { UPLOAD_FILE_PATH } = extInfo;
    editFormData.pathName = UPLOAD_FILE_PATH;

    dispatch({
      type: 'gatherPerson/onEditCrowdByFile',
      payload: editFormData,
    });
  } else if (record.crowdType === 'TRANSITION_CROWD') {
    const editFormData = record;

    dispatch({
      type: 'gatherPerson/onEditCrowdTransition',
      payload: editFormData,
    });
  }
};


export const onCreateCrowd = (profileType, crowdType, props) => {
  const { dispatch } = props 
  dispatch({
    type: 'gatherPerson/updateState',
    payload: {
      crowdCreateVisible: false
    }
  });
  let editFormData = {
    profileType,
  }
  switch (crowdType) {
    case 'LABEL_CROWD':
      dispatch({
        type: 'gpByTag/updateTagFormState',
        payload: editFormData,
      });
      dispatch({
        type: 'gpByTag/onCrowdByTagModalCancelOrShow',
        payload: true,
      });
      break;
    case 'ODPS_SQL_CROWD':
      dispatch({
        type: 'gatherPerson/onEditCrowdBySql',
        payload: editFormData,
      });
      dispatch({
        type: 'gatherPerson/onOpenCrowdBySqlModal',
      });
      break;
    case 'FILE_CROWD':
      dispatch({
        type: 'gatherPerson/onEditCrowdByFile',
        payload: editFormData,
      });
      dispatch({
        type: 'gatherPerson/onOpenCrowdByFileModal',
      });
      break;
    case 'ODPS_TABLE_CROWD':
      dispatch({
        type: 'gatherPerson/onEditCrowdByOdps',
        payload: editFormData,
      });
      dispatch({
        type: 'gatherPerson/onOpenCrowdByOdpsModal',
      });
      break;
    case 'OPERATE_CROWD':
      dispatch({
        type: 'gatherPerson/onEditCrowdByOperate',
        payload: editFormData,
      });
      dispatch({
        type: 'gatherPerson/onOpenCrowdByOperateModal',
      });
      break;
    case 'TRANSITION_CROWD':
      dispatch({
        type: 'gatherPerson/onEditCrowdTransition',
        payload: editFormData,
      });
      dispatch({
        type: 'gatherPerson/onOpenCrowdTransitionModal',
        payload: true,
      });
      break;
    default:
      break;
  }
}


// 获取审批内容
export const getApprovalObj = (approvalInfos, applyScene) => {
  if (approvalInfos && approvalInfos.length) {
    return approvalInfos.find(ele => ele.applyScene === applyScene)
  }

  return ''
}


//状态跳转
export const goBpms = (record) => {
  if (record.crowdStatus === 'APPROVAL_RUNNING' || record.crowdStatus === 'APPROVAL_REJECTED' || record.crowdStatus === 'APPROVAL_CANCELLED' || record.crowdStatus === 'APPROVAL_ERROR') {
    if (record.crowdApprovalInfo && record.crowdApprovalInfo.approvalInstanceId) {
      window.open(getBpmsUrl(record.crowdApprovalInfo.approvalInstanceId))
    }
  }
}

// 审批状态跳转
export const goApprovalBpms = (record) => {
  const obj = getApprovalObj(get(record, 'approvalInfos', []), approvalApplySceneEnum.CROWD_UPDATE)
  if (obj.approvalStatus === 'APPROVAL_RUNNING' || obj.approvalStatus === 'APPROVAL_REJECTED' || obj.approvalStatus === 'APPROVAL_CANCELLED' || obj.approvalStatus === 'APPROVAL_ERROR') {
    if (obj?.approvalId) {
      window.open(getBpmsUrl(obj?.approvalId))
    }
  }
}