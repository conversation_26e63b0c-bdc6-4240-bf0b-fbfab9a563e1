import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Card,
  Input,
  Switch,
  Select,
  DatePicker,
  Row,
  Col,
  Button,
  message,
  Radio,
  Drawer,
  Spin,
  Modal
} from 'antd';
import dayjs from 'dayjs';
import { Link }  from 'umi';
import { connect } from 'dva';
import GetUser from '@/components/GetUser';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import SelectTag from './components/SelectTag';
import CrowdApprovalModal from './components/CrowdApprovalModal';
import { OUTPUT_CROWD } from './common/constants';
import { approvalApplySceneEnum } from '../../GatherPerson/constants'
import { DateInput } from '@/components/CreateSqlCrowdModal/index';
import { queryLabel, getIsSupportPush, isSuperAdmin, approvalJudge } from '@/services/api';
import { get } from 'lodash';

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

const FormItem = Form.Item;
const { Option } = Select;
const RadioGroup = Radio.Group;

@Form.create()
@connect(state => ({ gpByTag: state.gpByTag,user: state.user }))
class GpByTag extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      isSupportPush: [], // 判断实时标签是否支持push
      setConditions: null,
      supportPushLoading: false,
      checkNick: true, // 判断是否输入重复的人群名称
      checkNickMsg: '请输入人群名称', // 人群名称的信息
      queryList: [], //标签列表
      isApproval: false // 圈人审批
    };
  }

  componentDidMount() {
    queryLabel({
      deleted: 0,
      bizEntityName: 'TAOBAO_USER',
      status: 'ACTIVATE',
      bizRegionCode: 'public_region',
      pageNum: 1,
      pageSize: 100000,
    })
      .then(res => {
        if (!res.success) {
          message.error(res.msg);
          return;
        }
        this.setState({
          queryList: res.data && res.data.rows,
        });
      })
      .catch(() => {
        message.error('请求失败');
      });
  }

  validateConditions = conditions => {
    const {queryList} = this.state

    if (!conditions || !conditions.group || !conditions.group.length || !conditions.expression) {
      message.error('圈人条件不能为空');
      return false;
    }

    const { group } = conditions;

    for (let i = 0; i < group.length; i += 1) {
      const tg = group[i];

      if (!tg.expression || !tg.label || !tg.label.length) {
        message.error('圈人条件不能为空');
        return false;
      }

      const { label } = tg;

      let temp = []

      for (let j = 0; j < label.length; j += 1) {
        const l = label[j];
        if (!l.value || !l.value.length) {
          if (!l.params || !l.params.length) {
        
            if (!['NONE_TYPE', 'JSON_OBJECT'].includes(l.type)) {
              message.error('标签项不能为空');
              return false;
            }
            temp.push(l)
          }

          if (l.type === 'JSON_OBJECT' && !l?.labelParams?.length) { // 时空标签
            message.error('标签项不能为空');
            return false;
          }
        }
      }


      for (let z = 0; z < temp.length; z += 1) {
        let newList = queryList.find(item => item.code === temp[z].name)
        // 判断是否需要选择参数
        if (get(newList, 'sourceConfig.sqlConfig.sqlParams', undefined)) {
          if (!temp[z].value || !temp[z].value.length) {
            if (!temp[z].params || !temp[z].params.length) {
                message.error('标签项不能为空');
                return false;
            }
          }
        }
      }
    }

    return true;
  };


  getFormData = async () => {
    const {
      form: { validateFields },
      user: { currentUser },
      gpByTag: { editFormData },
    } = this.props;
    const values = await validateFields()
    const { conditions, isLoss, needUpdate, updateType, updateDependence } = values;

    const data = values;
    data.expiredDate = values.expiredDate.valueOf();
    data.needUpdate = values.needUpdate ? 1 : 0;
    data.crowdTags = [];
    data.profileType = editFormData.profileType || 'TAOBAO_USER'
    // 涉及资损
    if (isLoss === 1) {
      data.crowdTags.push('LOSS_OF_ASSETS');
    }

    if (!data.extInfo) {
      data.extInfo = {};
    }
    if (needUpdate) {
      if (!data.extInfo.UPDATE_MODEL) {
        data.extInfo.UPDATE_MODEL = {};
      }
      data.extInfo.UPDATE_MODEL.updateType = updateType ? 'dependence' : 'every_day';
      data.extInfo.UPDATE_MODEL.dependenceBizOption =
        updateType === 1 ? updateDependence : undefined;
    } else if (data.extInfo.UPDATE_MODEL) {
      delete data.extInfo.UPDATE_MODEL.updateType;
      delete data.extInfo.UPDATE_MODEL.dependenceBizOption;
    }
    // if (data.extInfo.dependence_biz_option) {
    //   delete data.extInfo.dependence_biz_option;
    // }

    data.crowdType = 'LABEL_CROWD';

    // 提交审核信息
    if (data.applyApprovalReason && data.qualityOwner) {
      data.crowdApprovalInfo = {
        applyApprovalReason: data.applyApprovalReason,
        applyOwner:{empId: currentUser.workId, nickName: currentUser.name},
        qualityOwner: data.qualityOwner,
        needSubmit: true
      }
    }

    if (data.applyApprovalReason) {
      delete data.applyApprovalReason
    }
    if (data.qualityOwner) {
      delete data.qualityOwner
    }
    return { data, conditions}
  }

  onSubmit = async () => {
    const {
      dispatch,
      gpByTag: { editFormData },
    } = this.props;
    const { data, conditions } = await this.getFormData()
    
    if (this.validateConditions(conditions)) {
      // 只有淘宝人群包，才进行判断是否审批
      if (data.profileType === 'TAOBAO_USER') {
        const res = await approvalJudge({id: editFormData?.id, ...data})
        if (!res?.success) {
          message.error(res?.msg)
          return
        }
        if (res?.data?.CROWD_UPDATE) {
          this.setState({
            isApproval: true,
            isSupportPush: []
          })
          return
        }
      }
      
      dispatch({
        type: 'gpByTag/editCrowdByLabel',
        payload: data,
      });
    }
    this.setState({ isSupportPush: [] });
  };

  handlePropertyNameIsExist = (rule, val, callback) => {
    const {
      dispatch,
      form: { getFieldDecorator },
      gpByTag: { editFormData },
    } = this.props;
    if (!val) {
      callback();
    }
    
    dispatch({
      type: 'gpByTag/queryGpByName',
      payload: {
        crowdName: val,
      },
    }).then(res => {

      if (res) {
        if (editFormData.id) {
          if (editFormData.id !== res.id) {
            callback('该人群名称已存在');
          }
        } else {
          callback('该人群名称已存在');
        }
      }

      callback();
    });
  }

  onfocus = () => {
    if (!this.state.setConditions) {
      return
    }
    this.setState({
      supportPushLoading:true
    })
    getIsSupportPush({
      conditions:this.state.setConditions
    }).then(res => {
      // 传值是否支持push给父组件
      if (res.code === '200') {
        if (res.data && res.data.length > 0) {
          this.setState({
            isSupportPush:get(res, 'data', []),
            supportPushLoading:false
          })
        } else {
          this.setState({
            isSupportPush:[],
            supportPushLoading:false
          })
        }
      } else {
        this.setState({
          isSupportPush:["PUSH", "MATCH"],
          supportPushLoading:false
        })
      }
    });
  }


  renderForm = () => {
    const {
      dispatch,
      form: { getFieldDecorator,getFieldValue },
      gpByTag: { editFormData },
    } = this.props;

    const { isSupportPush,supportPushLoading } = this.state;

    const disabledDate = current => {
      return current < dayjs().startOf('day') || current > dayjs() + 90 * 24 * 60 * 60 * 1000;
    };

    const onChangeUpdate = value => {
      const {
        form: { setFieldsValue },
      } = this.props;
      if (value) {
        Modal.confirm({
          title: '每日更新计算资源消耗较大，请确认是否是必要选项',
          okText:"确认",
          cancelText:"取消",
          onOk() {
            setFieldsValue({needUpdate: true})
          },
          onCancel() {
            setFieldsValue({needUpdate: false})
          },
        });
      }
      dispatch({
        type: 'gpByTag/changeUpdate',
        payload: value,
      });
    };

    const onChangeType = e => {
      dispatch({
        type: 'gpByTag/changeType',
        payload: e.target.value,
      });
    };

    const crowdApplySceneType = (type) => {
      switch (type) {
        case 'PUSH':
          return 'PUSH'
        case 'MATCH':
          return '人群匹配'
        case 'ANALYSIS':
          return '人群分析'
        default:
          return '无可选场景'
      }
    }

    return (
      <Form {...FORM_ITEM_LAYOUT}>
        <FormItem
          label="产出人群"
        >
          {
            OUTPUT_CROWD[editFormData.profileType]
          }
        </FormItem>
        <FormItem
          label="标签"
          // extra={<span style={{ color: 'red' }}>注意：实时标签不支持人群数量计算</span>}
        >
          {getFieldDecorator('conditions', {
            initialValue: editFormData.conditions,
            rules: [{ required: true, message: '请选择圈人条件' }],
          })(
            <SelectTag
              changeConditions={setConditions => this.setState({ setConditions })}
              crowdAmount={editFormData.crowdAmount}
              profileType={editFormData.profileType}
            />
          )}
        </FormItem>
        <FormItem label="人群名称">
          {getFieldDecorator('crowdName', {
            initialValue: editFormData.crowdName,
            rules: [
              { required: true, message: '请输入人群名称' },
              {
                validator: this.handlePropertyNameIsExist,
              },
            ],
            validateTrigger: 'onBlur'
          })(
            <Input
              placeholder="请输入人群名称"
              allowClear
              // onBlur={this.handlePropertyNameIsExist}
            />
          )}
        </FormItem>
        <FormItem label="人群描述">
          {getFieldDecorator('crowdDescription', {
            initialValue: editFormData.crowdDescription,
            rules: [{ required: true, message: '请输入人群描述' }],
          })(<Input.TextArea placeholder="请输入人群描述" allowClear />)}
        </FormItem>
        <FormItem label="是否更新">
          {getFieldDecorator('needUpdate', {
            initialValue: editFormData.needUpdate === 1,
            valuePropName: 'checked',
          })(<Switch onChange={onChangeUpdate} />)}
        </FormItem>

        <FormItem label="更新类型" hidden={!editFormData.needUpdate}>
          {getFieldDecorator('updateType', {
            initialValue:
              editFormData.extInfo &&
              editFormData.extInfo.UPDATE_MODEL &&
              editFormData.extInfo.UPDATE_MODEL.updateType === 'dependence'
                ? 1
                : 0,
          })(
            <RadioGroup onChange={onChangeType}>
              <Radio value={0}>每天更新</Radio>
              <Radio value={1}>依赖更新</Radio>
            </RadioGroup>
          )}
        </FormItem>

        <FormItem
          label="依赖更新方"
          hidden={
            !(
              editFormData.needUpdate &&
              (editFormData.updateType === 1 ||
                (editFormData.extInfo &&
                  editFormData.extInfo.UPDATE_MODEL &&
                  // && editFormData.extInfo.UPDATE_MODEL === 'dependence'
                  editFormData.extInfo.UPDATE_MODEL.updateType === 'dependence'))
            )
          }
        >
          {getFieldDecorator('updateDependence', {
            initialValue: 'huokebao',
          })(
            <Select defaultValue="huokebao">
              <Option value="huokebao">获客宝</Option>
            </Select>
          )}
        </FormItem>

        <FormItem label="是否涉及资损">
          {getFieldDecorator('isLoss', {
            initialValue:
              editFormData.crowdTags &&
              (editFormData.crowdTags.length > 0 &&
              editFormData.crowdTags.includes('LOSS_OF_ASSETS')
                ? 1
                : 0),
            rules: [{ required: true, message: '请选择是否涉及资损' }],
          })(
            <RadioGroup>
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </RadioGroup>
          )}
        </FormItem>
        {
          getFieldValue('isLoss') === 1 && (<FormItem 
            label="详细描述"
            extra={
              <span style={{ color: 'red' }}>
                注：涉及资损的人群包将通过审批流获取额外测试保障，测试通过将后自动上线
              </span>
            }
          >
            {getFieldDecorator('applyApprovalReason', {
              initialValue: editFormData?.crowdApprovalInfo?.applyApprovalReason,
              rules: [{ required: true, message: '请输入详细描述' }],
            })(<Input.TextArea placeholder="请详细描述下可能发生资损的场景" allowClear />)}
          </FormItem>)
        }

        {
          getFieldValue('isLoss') === 1 && (
            <FormItem label="QA">
              {getFieldDecorator('qualityOwner', {
                initialValue: editFormData?.crowdApprovalInfo?.qualityOwner,
                rules: [{ required: true, message: '请输入业务对应的测试同学' }],
              })(<GetUser mode="default"/>)}
            </FormItem>
          )

        }

        <FormItem
          label="人群使用场景"
          // eslint-disable-next-line jsx-a11y/accessible-emoji
          extra={
            <span style={{ color: 'red' }}>
              注：非PUSH场景请选中人群匹配，否则会导致无法判断人群, 选择实时标签之后只能选择人群匹配
            </span>
          }
        >
          {getFieldDecorator('crowdApplyScene', {
            initialValue: editFormData.crowdApplyScene,
            rules: [{ required: true, message: '请选择人群使用场景' }],
          })(
            <Select placeholder="请选择人群使用场景" mode="multiple" onFocus={()=> {this.onfocus()}} notFoundContent={supportPushLoading ? <Spin size="small" /> : <div>无可选场景</div>}>
              {/* 以前的实时标签只有人群匹配 */}
              {isSupportPush && isSupportPush.map(e => {
                return <Option key={e}>{crowdApplySceneType(e)}</Option>
              })}
            </Select>
          )}
        </FormItem>
        <FormItem label="管理员">
          {getFieldDecorator('operator', {
            initialValue: editFormData.operator,
            rules: [{ required: true, message: '请选择人群管理员' }],
          })(<GetUser />)}
        </FormItem>
        {/* <FormItem label="运行周期">
            {getFieldDecorator('scheduleModel', {
              initialValue: editFormData.scheduleModel && Object.keys(editFormData.scheduleModel).length > 0? editFormData.scheduleModel: {updatePeriod: 'DAY', updateTime: '0:00:00'}
            })(<DateInput />)}
          </FormItem> */}
        <FormItem label="人群过期时间">
          {getFieldDecorator('expiredDate', {
            initialValue: editFormData.expiredDate && dayjs(editFormData.expiredDate),
            rules: [{ required: true, message: '请选择人群过期时间' }],
          })(
            <DatePicker
              disabledDate={disabledDate}
              format="YYYY-MM-DD HH:mm:ss"
              showTime={{
                defaultValue: dayjs('23:59:59', 'HH:mm:ss'),
              }}
            />
          )}
        </FormItem>

        <FormItem hidden>
          {getFieldDecorator('extInfo', {
            initialValue: editFormData.extInfo,
          })(<Input />)}
        </FormItem>
      </Form>
    );
  };

  onApprovalSubmit = async (values) => {
    const { user: { currentUser }, dispatch } = this.props;
    const { data } = await this.getFormData()
    const payload = {
      ...data,
      approvalInfos: {
        approvalCreator: {empId: currentUser?.workId, nickName: currentUser?.name},
        applyScene: approvalApplySceneEnum.CROWD_UPDATE,
        entityType: 'TAOBAO_USER',
        approvalInfo: values
      }
    }

    const res = await dispatch({
      type: 'gpByTag/editCrowdByLabel',
      payload,
    });

    return res
  }

  render() {
    const {
      // gpByTag: { loading },
      editModalVisible,
      onCancel,
      gpByTag: { editFormData },
      user: { currentUser, isSuperAdmin },
      dispatch
    } = this.props;
    const { isApproval } = this.state

    const isSubmit = () => {
      // 管理者和创建者及超级管理员能编辑
      const { creator = {}, operator = [] } = editFormData;
      const owners = [...operator, creator];

      const isGranted = owners.map(o => o.empId).includes(currentUser.workId);
      
      if(!editFormData.id || isSuperAdmin || isGranted) {
        return false
      }
     
      return true
    }

    return (
      // <PageHeaderWrapper title="标签圈人">
      //   <Card bordered={false} loading={loading}>
      //     {this.renderForm()}
      //   </Card>
      // </PageHeaderWrapper>
      <>
        <Drawer
          title={<div>标签圈人<a href= "https://aliyuque.antfin.com/dl7v63/zwzgqr/rnmh7trk6qqwgc6v?singleDoc#" target='_blank'>【公告】以下标签后续将开始进行费用分摊，请查阅</a></div>}
          placement="right"
          visible={editModalVisible}
          destroyOnClose
          width="800"
          onClose={() => {
            onCancel();
            this.setState({ isSupportPush: false });
          }}
          footer={
            <div
              style={{
                textAlign: 'right',
              }}
            >
              <Button
                onClick={() => {
                  onCancel();
                  this.setState({ isSupportPush: false });
                }}
                style={{ marginRight: 15 }}
              >
                取消
              </Button>
              <Button type="primary" onClick={this.onSubmit} disabled={isSubmit()}>
                保存
              </Button>
            </div>
          }
        >
          {this.renderForm()}
        </Drawer>
        <CrowdApprovalModal
          title= '所选标签在使用场景下需要审批后才可生成人群，审批流程按照标签最高等级进行生成'
          reasonLabel = '请填写人群创建理由'
          visible={isApproval}
          onSubmit={this.onApprovalSubmit}
          close={() => {
            this.setState({
              isApproval: false
            })
          }}
        />
      </>
    );
  }
}

export default GpByTag;
