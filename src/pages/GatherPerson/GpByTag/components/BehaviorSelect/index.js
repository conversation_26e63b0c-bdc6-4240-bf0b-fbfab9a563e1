// 行为
import React, { useState, useEffect } from 'react';
import { Select } from 'antd';

const BehaviorSelect = (props) => {
  const { value = undefined, onChange, behaviorTypeOptions, behaviorCode } = props;
  const setOptions = (behaviorCode) => {
    if (behaviorTypeOptions?.length) {
      return behaviorTypeOptions?.filter(ele => ele.value !== behaviorCode)
    }

    return []
  }

  return (
    <Select
      placeholder="请选择行为"
      showSearch
      style={{ width: '100%' }}
      filterOption={(input, option) => option.props.children.indexOf(input) >= 0}
      onChange={onChange}
      value={value}
    >
      {
        setOptions(behaviorCode)?.map(o => (
          <Option key={o.value} value={o.value}>
            {o.label}
          </Option>
        ))}
    </Select>
  );
};

export default BehaviorSelect;
