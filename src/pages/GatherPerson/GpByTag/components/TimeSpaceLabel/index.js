// 时空标签
import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import { Input, Select, Button, Row, Card, message, DatePicker, Radio, Divider, Col } from 'antd';
import { cloneDeep } from 'lodash';

import TimeComponent from '../TimeComponent';
import BehaviorSelect from '../BehaviorSelect';
import AdditionalConditions from '../AdditionalConditions';
import { TIME_TYPE_TEXT } from '../../common/constants';


const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const { RangePicker } = DatePicker;
const { Option } = Select;

class TimeSpaceLabel extends React.Component {
  
  componentDidMount() {
    const { onChangeBehaviorType, tag } = this.props
    if (tag?.editFormData?.timeType) {
      onChangeBehaviorType(tag?.editFormData?.timeType)
    }
  }
  
  onChangeTimeType = (values) => {
    const { onChangeBehaviorType, form } = this.props
    onChangeBehaviorType(values)
    form.setFieldsValue({ additional: [], keys: [], behaviorCode: undefined });
  }

  render() {
    const {
      form: { getFieldDecorator, getFieldValue },
      tag,
      conditionOptions,
      behaviorTypeOptions,
      timeTypeOptions,
      onChangeConditionOptions,
      onChangeBehaviorType
    } = this.props;

    if (!tag) {
      return;
    }

    const { editFormData } = tag;

    const disabledDate = (current) => {
      const today = dayjs();
      const beforeThirtyDays = today.clone().subtract(30, 'days');
      const afterThirtyDays = today.clone().add(30, 'days');
      return current && (current < beforeThirtyDays || current > afterThirtyDays);
    };

    return (
      <Fragment>
        <h3>必填条件</h3>
        <Row>
          <Col span={24}>
            <FormItem
              label="时间"
              extra={
                <span style={{ color: '#666666', fontSize: 12 }}>
                  使用说明：1.时间区间：闭区间；2.过去N天：过去N天；3.今天：当天自然日；4.未来N天：今天+未来N天
                </span>
              }
            >
              {getFieldDecorator('dateType', {
                initialValue: editFormData && editFormData.dateType,
                rules: [{ required: true, message: '请选择类型' }],
              })(
                <RadioGroup>
                  <Radio value="fixed">时间区间</Radio>
                  <Radio value="relativePast">过去N天</Radio>
                  <Radio value="intraday">当天</Radio>
                  <Radio value="relativeFuture">未来N天</Radio>
                </RadioGroup>
              )}
            </FormItem>
          </Col>
          <Col span={24}>
            {getFieldValue('dateType') === 'fixed' && (
              <FormItem label="日期">
                {getFieldDecorator('date', {
                  initialValue: editFormData && editFormData.date,
                  rules: [{ required: true, message: '请选择日期' }],
                })(<RangePicker disabledDate={disabledDate} />)}
              </FormItem>
            )}
            {getFieldValue('dateType') === 'relativePast' && (
              <FormItem label="天数">
                {getFieldDecorator('pastTime', {
                  initialValue: editFormData && editFormData.pastTime,
                  rules: [{ required: true, message: '请选择天数' }],
                })(<TimeComponent start="过去" max={30}/>)}
              </FormItem>
            )}
            {getFieldValue('dateType') === 'relativeFuture' && (
              <FormItem label="天数">
                {getFieldDecorator('futureTime', {
                  initialValue: editFormData && editFormData.futureTime,
                  rules: [{ required: true, message: '请选择天数' }],
                })(<TimeComponent start="未来" max={30} />)}
              </FormItem>
            )}
          </Col>
        </Row>
        <Col span={22}>
          <FormItem label="时间类型">
            {getFieldDecorator('timeType', {
              initialValue: editFormData && editFormData.timeType,
              rules: [{ required: true, message: '请选择时间类型' }],
            })(
              <Select
                placeholder="请选择时间类型"
                showSearch
                filterOption={(input, option) => option.props.children.indexOf(input) >= 0}
                onChange={this.onChangeTimeType}
              >
                {timeTypeOptions &&
                  timeTypeOptions.length > 0 &&
                  timeTypeOptions.map(o => (
                    <Option key={o.value} value={o.value}>
                      {o.label}
                    </Option>
                  ))}
              </Select>
            )}
          </FormItem>
        </Col>
        <Col span={22}>
          <FormItem label="行为">
            {getFieldDecorator('behaviorCode', {
              initialValue: editFormData && editFormData.behaviorCode,
              rules: [{ required: true, message: '请选择行为' }],
            })(<BehaviorSelect behaviorTypeOptions={cloneDeep(behaviorTypeOptions)}/>)}
          </FormItem>
        </Col>
        <Divider />
        <AdditionalConditions
          form={this.props.form}
          behaviorCode={getFieldValue('behaviorCode')}
          value={editFormData?.additional || []}
          conditionOptions={conditionOptions}
          onChangeConditionOptions={onChangeConditionOptions}
        />
      </Fragment>
    );
  }
}

export default TimeSpaceLabel;
