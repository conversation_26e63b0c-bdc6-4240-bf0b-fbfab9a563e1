/* eslint-disable consistent-return */
import React, { Fragment } from 'react';
import dayjs from 'dayjs';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { cloneDeep, get, pick } from 'lodash';

import {
  Modal,
  Radio,
  Checkbox,
  message,
  DatePicker,
  Tag,
  Spin,
  Select,
  Slider,
  Row,
  Col,
  Input,
} from 'antd';
import {
  queryLabelEnumValueBuildTree,
  queryLabelByName,
  queryTimeType,
  queryBehaviorType,
} from '@/services/api';
import TimeComponent from '../TimeComponent';
import SelectMaxAndMin from '../SelectMaxAndMin';
import DateTimePicker from '../DateTimePicker';
import TimeSpaceLabel from '../TimeSpaceLabel';
import {
  hsfOrSqlParams,
  isHsfCustom,
  getHsfParams,
  getFlattenParams,
  getHsfServiceParams,
  timeSpaceLabelSubmit,
  setTimeSpaceLabelParams,
} from '../../common/utils';

const NOOP = () => {};
const FormItem = Form.Item;
const CheckboxGroup = Checkbox.Group;
const RadioGroup = Radio.Group;
const { RangePicker } = DatePicker;
const { Option } = Select;

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

@Form.create()
class SelectEnumValueModal extends React.PureComponent {
  state = {
    checkedList: [],
    indeterminate: false,
    checkAll: false,
    options: [],
    curDateType: undefined,
    loading: false,
    selectedTargets: [],
    editWeightRange: [],
    isloading: false,
    mock: [],
    dataConfig: {},
    hsfParamsList: [], // 实时hsf自定义标签
    timeTypeOptions: [], // 时空标签中的时间类型枚举
    behaviorTypeOptions: [], // 时空标签行为类型枚举
    conditionOptions: [], // 时空标签中的附加条件配置值
  };

  componentWillReceiveProps(nextProps) {
    if (nextProps !== this.props && this.props.tag !== nextProps.tag) {
      let { tag } = nextProps;
      if (tag) {
        const { dataType, editFormData, sourceConfig } = tag;

        if (dataType === 'ENUM' || dataType === 'MULTI_VALUE') {
          // 枚举型和KV型
          this.getEnumMateIdOrDataValue(tag);

          if (editFormData) {
            this.setState({
              checkedList: (editFormData.enumValues || []).map(item => item.value),
            });
          }
        } else if (dataType === 'KV') {
          this.getEnumMateIdOrDataValue(tag);

          if (editFormData) {
            this.setState({
              selectedTargets: (editFormData.targets || []).map(item => item.value),
            });
          }
        } else if (dataType === 'DATE') {
          if (editFormData) {
            this.setState({
              curDateType: editFormData.dateType,
            });
          }
        } else if (dataType === 'NONE_TYPE') {
          this.setState({
            loading: true,
          });
          let sqlParams;
          if (sourceConfig) {
            sqlParams = tag?.sourceConfig?.sqlConfig?.sqlParams;
          } else {
            sqlParams = tag?.sqlParams;
          }

          hsfOrSqlParams(sqlParams, params => {
            this.setState({
              mock: cloneDeep(params),
              loading: false,
            });
          });
        } else if (dataType === 'JSON_OBJECT') {
          const timeSpaceLabel = tag?.editFormData?.data;
          if (timeSpaceLabel) {
            tag.editFormData = setTimeSpaceLabelParams(timeSpaceLabel)?.editFormData;
          }
          this.getTimeType();
        }
        // 如果存在实时hsf自定义类型要与原有标签类型合并
        if (isHsfCustom(tag)) {
          let hsfParams = getHsfParams(tag);
          hsfOrSqlParams(hsfParams, params => {
            this.setState({
              hsfParamsList: cloneDeep(params),
              loading: false,
            });
          });
        }
      }
    }
  }

  getEnumMateIdOrDataValue = tag => {
    const { dimEnumMetaId, dataConfig, editFormData, propertyName } = tag;
    const { dataValue } = dataConfig || { dataValue: {} };

    console.log(tag,'tag')

    if (dimEnumMetaId) this.getEnumValues(dimEnumMetaId);
    if (propertyName && editFormData) this.getLabel(propertyName);
    if (dataConfig) {
      this.setState({
        dataConfig,
      });
    }
    if (dataValue) {
        const options = this.getDataValueEnum(dataValue);
        this.setState({
          options,
        });
    }
  };

  getDataValueEnum = dataValue => {
    if (!dataValue) return [];
    if (Object.keys(dataValue).length > 0) {
      let options = [];
      Object.keys(dataValue).forEach(key => {
        if (dataValue[key]) {
          options.push({
            label: dataValue[key],
            value: key,
          });
        }
      });
      return options;
    }
    return [];
  };

  // 查询时空标签时间类型
  getTimeType = () => {
    queryTimeType().then(res => {
      if (!res.success) {
        message.error(res.msg);
        return;
      }

      if (res?.data) {
        this.setState({
          timeTypeOptions: Object.keys(res?.data || {}).map(ele => ({
            label: res?.data[ele],
            value: ele,
          })),
        });
      }
    });
  };

  // 行为类型枚举接口
  getBehaviorType = timeType => {
    queryBehaviorType({ filterNoDest: false, timeType }).then(res => {
      if (!res.success) {
        message.error(res.msg);
        return;
      }

      if (res?.data) {
        this.setState({
          behaviorTypeOptions: Object.keys(res?.data || {}).map(ele => ({
            label: res?.data[ele],
            value: ele,
          })),
        });
      }
    });
  };

  getEnumValues = dimEnumMetaId => {
    this.setState({
      loading: true,
    });

    queryLabelEnumValueBuildTree({ dimMetaId: dimEnumMetaId })
      .then(res => {
        if (!res.success) {
          message.error(res.msg);
          return;
        }

        const o = res.data && res.data.root && res.data.root.children;

        this.setState({
          options: o.map(item => ({ label: item.name, value: item.enumCode })),
          loading: false,
        });
      })
      .catch(() => {
        message.error('请求失败');
      });
  };

  getLabel = name => {
    queryLabelByName({ name }).then(res => {
      if (!res.success) {
        message.error(res.msg);
        return;
      }

      const { extInfo, dataConfig } = res.data;
      const { weightRange } = extInfo || {};
      const { dataValue, kvWeightRange } = dataConfig || {};

      if (dataValue) {
        const options = this.getDataValueEnum(dataValue);
        this.setState({
          options,
        });
      }

      this.setState({
        editWeightRange: (kvWeightRange ?? weightRange) || [],
        dataConfig,
      });
    });
  };

  onCheckBoxChange = checkedList => {
    const { options } = this.state;
    this.setState({
      checkedList,
      indeterminate: !!checkedList.length && checkedList.length < options.length,
      checkAll: checkedList.length === options.length,
    });
  };

  onCheckChange = checkedList => {
    this.setState({
      checkedList,
    });
  };

  onCheckAllChange = e => {
    const { options } = this.state;

    this.setState({
      checkedList: e.target.checked ? options.map(item => item.value) : [],
      indeterminate: false,
      checkAll: e.target.checked,
    });
  };

  onCancel = () => {
    const { onCancel = NOOP, form } = this.props;

    this.setState(
      {
        checkedList: [],
        indeterminate: false,
        checkAll: false,
        options: [],
        mock: [],
        hsfParamsList: [],
        selectedTargets: [],
      },
      () => {
        onCancel();
      }
    );

    form.resetFields();
  };

  // 选择目标时回调
  onSelectChange = values => {
    this.setState({
      selectedTargets: values,
    });
  };

  // 点击标签关闭按钮
  onTagClose = id => {
    this.setState(({ checkedList }) => {
      const index = checkedList.indexOf(id);

      if (index !== -1) {
        checkedList.splice(index, 1);
      }

      return {
        checkedList,
      };
    });
  };

  onSetConditionOptions = value => {
    this.setState({
      conditionOptions: value,
    });
  };

  getContent = () => {
    const { tag } = this.props;
    const { conditionOptions, behaviorTypeOptions, timeTypeOptions } = this.state;
    if (!tag) {
      return;
    }

    const { dataType } = tag;

    let content;

    switch (dataType) {
      case 'NUMBER':
        content = this.getNumberTypeContent();
        break;
      case 'ENUM':
        content = this.getEnumTypeContent();
        break;
      case 'MULTI_VALUE':
        content = this.getEnumTypeContent();
        break;
      case 'DATE':
        content = this.getDateTypeContent();
        break;
      case 'KV':
        content = this.getKVTypeContent();
        break;
      case 'NONE_TYPE':
        content = this.getSqlTypeContent();
        break;
      case 'JSON_OBJECT':
        content = (
          <TimeSpaceLabel
            {...this.props}
            conditionOptions={conditionOptions}
            behaviorTypeOptions={behaviorTypeOptions}
            timeTypeOptions={timeTypeOptions}
            onChangeConditionOptions={this.onSetConditionOptions}
            onChangeBehaviorType={timeType => {
              this.getBehaviorType(timeType);
            }}
          />
        );
        break;
      default:
        content = '暂不支持此类型';
        break;
    }

    return content;
  };

  // 日期类型下所渲染的表单
  getDateTypeContent = () => {
    const {
      form: { getFieldDecorator },
      tag,
    } = this.props;

    if (!tag) {
      return;
    }

    const { curDateType } = this.state;
    const { editFormData } = tag;
    return (
      <Fragment>
        <FormItem
          label="类型"
          extra={
            <span style={{ color: '#666666', fontSize: 12 }}>
              使用说明：1.时间区间：闭区间；2.过去N天：过去N天；3.未来N天：今天+未来N天
            </span>
          }
        >
          {getFieldDecorator('dateType', {
            initialValue: editFormData && editFormData.dateType,
            rules: [{ required: true, message: '请选择类型' }],
          })(
            <RadioGroup onChange={this.onDateTypeChange}>
              <Radio value="fixed">时间区间</Radio>
              <Radio value="relativePast">过去N天</Radio>
              <Radio value="relativeFuture">未来N天</Radio>
            </RadioGroup>
          )}
        </FormItem>
        {curDateType === 'fixed' && (
          <FormItem label="日期">
            {getFieldDecorator('date', {
              initialValue: editFormData && [
                dayjs(editFormData.startDate),
                dayjs(editFormData.endDate),
              ],
              rules: [{ required: true, message: '请选择日期' }],
            })(<RangePicker />)}
          </FormItem>
        )}
        {curDateType === 'relativePast' && (
          <FormItem label="天数">
            {getFieldDecorator('pastTime', {
              initialValue: editFormData && editFormData.pastTime,
              rules: [{ required: true, message: '请选择天数' }],
            })(<TimeComponent start="过去" />)}
          </FormItem>
        )}
        {curDateType === 'relativeFuture' && (
          <FormItem label="天数">
            {getFieldDecorator('futureTime', {
              initialValue: editFormData && editFormData.futureTime,
              rules: [{ required: true, message: '请选择天数' }],
            })(<TimeComponent start="未来" />)}
          </FormItem>
        )}
      </Fragment>
    );
  };

  // 枚举类型下所渲染的表单
  // 旧枚举类型下所渲染的表单，已舍弃
  getEnumTypeContentOld = () => {
    const { options, checkedList, indeterminate, checkAll, loading } = this.state;
    console.log('checkedList-----我这是啥', checkedList);
    return (
      <Spin spinning={loading}>
        <div>
          <div>
            <Checkbox
              indeterminate={indeterminate}
              onChange={this.onCheckAllChange}
              checked={checkAll}
            >
              全选
            </Checkbox>
          </div>
          <br />
          <div
            style={{
              height: 300,
              overflow: 'auto',
              border: '1px solid #E9E9E9',
              padding: '10px',
            }}
          >
            <CheckboxGroup
              style={{ width: '100%' }}
              value={checkedList}
              onChange={this.onCheckBoxChange}
            >
              <Row gutter={24}>
                {options &&
                  options.length > 0 &&
                  options.map(o => (
                    <Col span={6}>
                      <Checkbox key={o.value} value={o.value}>
                        {/*  判断如果包含&gt; 替换为 >  */}
                        {o.label
                          ? o.label.includes('&gt;')
                            ? o.label.replace('&gt;', '>')
                            : o.label
                          : null}
                      </Checkbox>
                    </Col>
                  ))}
              </Row>
            </CheckboxGroup>
          </div>
          <div>
            <div style={{ marginBottom: 10 }}>
              <span>已选</span>
              <span style={{ color: 'orange' }}>{checkedList.length}</span>
              <span>项</span>
            </div>
            <div>
              {options &&
                options.length > 0 &&
                options
                  .filter(o => checkedList.indexOf(o.value) !== -1)
                  .map(o => (
                    <Tag
                      key={`tag_${o.value}`}
                      style={{ marginRight: 5 }}
                      closable
                      onClose={() => this.onTagClose(o.value)}
                    >
                      {/*  判断如果包含&gt; 替换为 >  */}
                      {o.label
                        ? o.label.includes('&gt;')
                          ? o.label.replace('&gt;', '>')
                          : o.label
                        : null}
                    </Tag>
                  ))}
            </div>
          </div>
        </div>
      </Spin>
    );
  };

  // 新枚举类型下所渲染的表单
  getEnumTypeContent = () => {
    const {
      form: { getFieldDecorator },
      tag,
    } = this.props;
    const { options, checkedList, indeterminate, checkAll, loading } = this.state;
    return (
      <Fragment>
        <FormItem label="目标">
          {getFieldDecorator('checkedList', {
            initialValue: checkedList || [],
            rules: [{ required: true, message: '请选择目标' }],
          })(
            <Select
              mode="multiple"
              placeholder="请选择目标"
              showSearch
              style={{ width: 500 }}
              onChange={this.onCheckChange}
              filterOption={(input, option) => option.props.children.indexOf(input) >= 0}
            >
              {options &&
                options.length > 0 &&
                options.map(o => (
                  <Option key={o.value} value={o.value}>
                    {/*  判断如果包含&gt; 替换为 >  */}
                    {o.label
                      ? o.label.includes('&gt;')
                        ? o.label.replace('&gt;', '>')
                        : o.label
                      : null}
                  </Option>
                ))}
            </Select>
          )}
        </FormItem>
      </Fragment>
    );
  };

  // 数值类型下所渲染的表单
  getNumberTypeContent = () => {
    const {
      form: { getFieldDecorator },
      tag,
    } = this.props;

    if (!tag) {
      return;
    }

    const { editFormData } = tag;

    return (
      <FormItem label="最小值">
        {getFieldDecorator('numberValue', {
          initialValue: (editFormData && editFormData.numberValue) || { min: 0, max: 0 },
          rule: {
            rules: [{ required: true, message: '请输入最大值最小值' }],
          },
        })(<SelectMaxAndMin />)}
      </FormItem>
    );
  };

  // KV类型下所渲染的表单
  getKVTypeContent = () => {
    const {
      form: { getFieldDecorator },
      tag,
    } = this.props;
    const { options, selectedTargets, editWeightRange, dataConfig } = this.state;

    const { extInfo } = tag;

    const { kvDisplayKey, kvDisplayValue, kvWeightRange } = dataConfig || { kvDisplayKey: '', kvDisplayValue: '', kvWeightRange: [] };
    
    console.log(tag, dataConfig,'dataConfig')

    const { weightRange } = extInfo || {};
    const [minValue, maxValue, stepLength] = (kvWeightRange ?? weightRange ) || [];
    const { editFormData } = tag;
    let temp = cloneDeep(editFormData || {});
    let newData = {};
    if (editFormData && JSON.stringify(editFormData) !== '{}') {
      Object.keys(temp).forEach(ele => {
        let val = temp[ele];
        let key = ele.includes('.') ? ele.replaceAll('.', '$') : ele;
        newData[key] = val;
      });
    }

    // 更新时候的步长
    const [min, max, step] = editWeightRange;

    return (
      <Fragment>
        <FormItem label={kvDisplayKey ?? '目标'}>
          {getFieldDecorator('targets', {
            initialValue: selectedTargets || [],
            rules: [{ required: true, message: '请选择目标' }],
          })(
            <Select
              mode="multiple"
              placeholder="请选择目标"
              showSearch
              style={{ width: 400 }}
              onChange={this.onSelectChange}
              filterOption={(input, option) => option.props.children.indexOf(input) >= 0}
            >
              {options &&
                options.length > 0 &&
                options.map(o => (
                  <Option key={o.value} value={o.value}>
                    {/*  判断如果包含&gt; 替换为 >  */}
                    {o.label
                      ? o.label.includes('&gt;')
                        ? o.label.replace('&gt;', '>')
                        : o.label
                      : null}
                  </Option>
                ))}
            </Select>
          )}
        </FormItem>
        {selectedTargets &&
          selectedTargets.length > 0 &&
          selectedTargets.map(t => {
            let opts = options.find(o => o.value === t);
            let opt = cloneDeep(opts);
            if (opt) {
              opt.value = opt.value.includes('.') ? opt.value.replaceAll('.', '$') : opt.value;
            }
            return (
              opt && (
                <FormItem
                  label={`${opt.label}-${kvDisplayValue ?? '权重'}`}
                  key={`${opt.value}_weight`}
                >
                  {getFieldDecorator(`${opt.value}_weight`, {
                    initialValue:
                      newData && JSON.stringify(newData) !== '{}'
                        ? newData[`${opt.value}_weight`]
                        : [0, 0],
                    rules: [{ required: true, message: '请选择目标权重' }],
                  })(
                    <Slider
                      className="ant-pro-sider-menu-sider"
                      range
                      style={{ width: 400 }}
                      marks={{
                        0: minValue ? parseFloat(minValue) : min,
                        [maxValue ? parseFloat(maxValue) : max]: {
                          style: {
                            left: '100%',
                          },
                          label: maxValue ? parseFloat(maxValue) : max,
                        },
                      }}
                      step={stepLength || step}
                      min={minValue ? parseFloat(minValue) : min}
                      max={maxValue ? parseFloat(maxValue) : max}
                    />
                  )}
                </FormItem>
              )
            );
          })}
      </Fragment>
    );
  };

  // 自定义类型下所渲染的表单
  getSqlTypeContent = () => {
    const {
      form: { getFieldDecorator },
      tag,
    } = this.props;

    if (!tag) {
      return;
    }

    const { mock, hsfParamsList, loading } = this.state;
    const list = hsfParamsList.length ? hsfParamsList : mock;
    const editFormData = isHsfCustom(tag) ? tag?.hsfEditFormData : tag?.editFormData;

    return (
      <Spin spinning={loading}>
        {list &&
          list.length > 0 &&
          list.map((ele, index) => {
            if (ele.paramType === 'TEXT') {
              return (
                <Form.Item label={ele.description} key={index}>
                  {getFieldDecorator(ele.name, {
                    initialValue: editFormData && editFormData[ele.name],
                    rules: [{ required: true, message: '请输入内容' }],
                  })(<Input placeholder="请输入文本" />)}
                </Form.Item>
              );
            } else if (['ENUM', 'SINGLE_ENUM']?.includes(ele.paramType)) {
              return (
                <Form.Item label={ele.description} key={index}>
                  {getFieldDecorator(ele.name, {
                    initialValue: editFormData && editFormData[ele.name],
                    rules: [{ required: true, message: '请选择选项' }],
                  })(
                    // <CheckboxGroup>
                    //   <Row>
                    //     {ele.enumList && ele.enumList.length > 0 && ele.enumList.map((n,j) => {
                    //       return <Col key={j}>
                    //         <Checkbox value={n.value}>{n.label}</Checkbox>
                    //       </Col>
                    //     })}
                    //   </Row>
                    // </CheckboxGroup>
                    <Select
                      mode={ele.paramType !== 'SINGLE_ENUM' ? 'multiple' : undefined}
                      placeholder="请选择目标"
                      showSearch
                      style={{ width: 500 }}
                      filterOption={(input, option) => option.props.children.indexOf(input) >= 0}
                    >
                      {ele.enumList &&
                        ele.enumList.length > 0 &&
                        ele.enumList.map(o => (
                          <Option key={o.value} value={o.value}>
                            {o.label}
                          </Option>
                        ))}
                    </Select>
                  )}
                </Form.Item>
              );
            } else if (ele.paramType === 'DATE') {
              return (
                <Form.Item label={ele.description} key={index}>
                  {getFieldDecorator(ele.name, {
                    initialValue: editFormData && dayjs(editFormData[ele.name]),
                    rules: [{ required: true, message: '请选择日期' }],
                  })(<DatePicker format="YYYYMMDD" />)}
                </Form.Item>
              );
            } else if (ele.paramType === 'NUMBER') {
              return (
                <Form.Item label={ele.description} key={index}>
                  {getFieldDecorator(ele.name, {
                    initialValue: editFormData && editFormData[ele.name],
                    rules: [
                      {
                        required: true,
                        pattern: new RegExp(/^[0-9]\d*$/, 'g'),
                        message: '请输入数字',
                      },
                    ],
                    getValueFromEvent: event => {
                      return event.target.value.replace(/\D/g, '');
                    },
                  })(<Input placeholder="请输入数字" />)}
                </Form.Item>
              );
            } else if (ele.paramType === 'TIME') {
              return (
                <Form.Item label={ele.description} key={index}>
                  {getFieldDecorator(ele.name, {
                    initialValue: editFormData && editFormData[ele.name],
                    rules: [{ required: true, message: '请选择时间' }],
                  })(<DateTimePicker />)}
                </Form.Item>
              );
            }
          })}
      </Spin>
    );
  };

  // 处理日期类型联动
  onDateTypeChange = e => {
    this.setState({
      curDateType: e.target.value,
    });
  };

  onReset = () => {
    const { form } = this.props;
    this.setState({
      checkedList: [],
      indeterminate: false,
      checkAll: false,
      options: [],
      mock: [],
      hsfParamsList: [],
    });
    form.resetFields();
  };

  // 处理提交逻辑
  onOk = async () => {
    const {
      form: { validateFields, resetFields },
      onOk = NOOP,
      tag,
    } = this.props;

    if (!tag) {
      return;
    }

    const { dataType } = tag;
    const {
      options,
      checkedList,
      conditionOptions,
      behaviorTypeOptions,
      timeTypeOptions,
    } = this.state;
    const params = {};
    let data;

    try {
      data = await validateFields();
    } catch (error) {
      // 时空标签校验
      if (error?.errors?.additional) {
        message.error('请将数据填写完整');
      }
    }

    // 拿到老数据
    if (dataType) {
      if (dataType === 'ENUM' || dataType === 'MULTI_VALUE') {
        // 枚举类型提交特殊处理
        Object.assign(params, {
          enumValues: options ? options.filter(o => checkedList.indexOf(o.value) !== -1) : [],
        });
      } else if (dataType === 'JSON_OBJECT') {
        // 时空标签处理
        Object.assign(params, {
          data: timeSpaceLabelSubmit(data, conditionOptions, behaviorTypeOptions, timeTypeOptions),
        });
      } else {
        if (data.date) {
          const [startDate, endDate] = data.date;
          data.startDate = startDate.format('YYYY-MM-DD');
          data.endDate = endDate.format('YYYY-MM-DD');

          delete data.date;
        }

        if (dataType === 'KV') {
          let newData = {};
          Object.keys(data).forEach(ele => {
            let val = data[ele];
            let key = ele.includes('$') ? ele.replaceAll('$', '.') : ele;
            newData[key] = val;
          });
          data = cloneDeep(newData);
          data.targets = options ? options.filter(o => data.targets.indexOf(o.value) !== -1) : [];

          // 初始化
          this.setState({
            selectedTargets: [],
            options: [],
          });
        }

        if (dataType === 'NONE_TYPE') {
          Object.keys(data).forEach(function(key) {
            if (data[key]._isAMomentObject) {
              data[key] = data[key].format('YYYYMMDD');
            }
          });

          data.mock = this.state.mock;
        }

        Object.assign(params, data);
      }
    }

    // hsf自定义标签
    if (isHsfCustom(tag)) {
      // 将hsf标签跟其他类型标签区分开来
      let hsfParams = getHsfParams(tag);
      const flattenParams = getFlattenParams(getHsfServiceParams(tag));
      const hsfNameList = hsfParams.map(ele => ele?.name);
      const hsfData = pick(data, hsfNameList);
      Object.keys(hsfData).forEach(function(key) {
        if (hsfData[key]._isAMomentObject) {
          hsfData[key] = hsfData[key].format('YYYYMMDD');
        }
      });

      let hsfParamsList = this.state.hsfParamsList;
      hsfParamsList = hsfParamsList?.map(ele => {
        const valueSource = flattenParams?.find(item => item?.paramName === ele?.name)?.valueSource;
        if (valueSource) {
          return {
            ...ele,
            valueSource,
          };
        }

        return ele;
      });

      onOk({
        hsfData,
        otherData: params,
        mock: hsfParamsList,
      });
      this.onReset();
      return;
    }

    console.log(params, 'params')
    
    onOk(params);
    this.onReset();
  };

  render() {
    const { title = '', visible, tag } = this.props;
    const isHsf = isHsfCustom(tag);
    return (
      <Modal visible={visible} onCancel={this.onCancel} onOk={this.onOk} width={800} title={title}>
        <Form {...FORM_ITEM_LAYOUT}>
          {this.getContent()}
          {isHsf && this.getSqlTypeContent()}
        </Form>
      </Modal>
    );
  }
}

export default SelectEnumValueModal;
