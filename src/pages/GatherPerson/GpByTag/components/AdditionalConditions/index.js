import React, { Fragment } from 'react';
import { Input, Select, But<PERSON>, Row, message, Popconfirm, Col } from 'antd';
import { MinusCircleOutlined, PlusOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { Form } from '@ant-design/compatible';
import styles from './index.less'

import { getContent } from './content';
import { queryConditionType, queryBehaviorType } from '@/services/api';

const { Option } = Select;
const FormItem = Form.Item;
let id = 0;

class AdditionalConditions extends React.Component {
  static getDerivedStateFromProps(nextProps) {
    // Should be a controlled component.
    if ('value' in nextProps) {
      const { value } = nextProps;
      return {
        value,
      };
    }
    return null;
  }

  constructor(props) {
    super(props);
    const value = props.value || [];
    this.state = {
      value,
      behaviorTypeOptions: []
    };
  }

  componentDidMount() {
    id = this.props.value.length;
    if (this.props.behaviorCode) {
      this.fetchData(this.props.behaviorCode);
    }
    this.getSameDestBehavior()
  }

  componentDidUpdate(preProps) {
    // 检查特定props是否有变化
    const { behaviorCode, form } = this.props
    if (this.props.behaviorCode !== preProps.behaviorCode) {
      form.setFieldsValue({ additional: [], keys: [] });
      this.fetchData(this.props.behaviorCode);
    }
  }

  // 用来查询附加条件
  fetchData = value => {
    const { onChangeConditionOptions } = this.props;
    if (!value) return;
    queryConditionType({behaviorType: value}).then(res => {
      if (!res?.success) {
        message.error(res?.msg)
        return
      }
      onChangeConditionOptions(res?.data || []);
    })
    
  };

  remove = k => {
    const { form } = this.props;
    const keys = form.getFieldValue('keys');
    form.setFieldsValue({
      keys: keys.filter(key => key !== k),
    });
  };

  add = () => {
    const { form } = this.props;
    const keys = form.getFieldValue('keys');
    const nextKeys = keys.concat(id++);
    form.setFieldsValue({
      keys: nextKeys,
    });
  };

  getConditions = (opts, code) => {
    if (code && opts?.length) {
      return opts?.find(ele => ele?.paramCode === code || `${ele.paramCode}-${ele.paramDesc}` === code) || {};
    }
  };

  // 目的地行为跟是否出行的code是一样的，所以要区分一下
  setParamCodeValue = (o) => { 
    if (o.paramDesc === '同目的地行为') {
      return `${o.paramCode}-${o.paramDesc}`
    }

    return o.paramCode
  }
  
  // 同目的地行为枚举接口
  getSameDestBehavior = () => {
    queryBehaviorType({filterNoDest: true}).then(res => {
      if (!res.success) {
        message.error(res.msg);
        return;
      }

      if (res?.data) {
        this.setState({
          behaviorTypeOptions: Object.keys(res?.data || {}).map(ele =>  ({label: res?.data[ele], value: ele}))
        })
      }
    })
  }

  setDisabled = (o) => {
    const { form } = this.props;
    const { getFieldValue } = form;
    const list = form.getFieldValue('additional') || []
    const newList = list?.map(ele => ele?.paramCode)?.filter(i => i !== 'valid-同目的地行为')
    return newList?.includes(o)
  }

  render() {
    const { value, behaviorTypeOptions } = this.state;
    const { form, behaviorCode, conditionOptions } = this.props;
    const { getFieldDecorator, getFieldValue } = form;
    const initData = [];
    value.forEach((item, index) => {//根据真实数据，设置默认keys数组
      initData.push(index);
    });
    getFieldDecorator('keys', { initialValue: initData });
    const keys = getFieldValue('keys');

    const formItems = keys.map((k, index) => (
      <Form.Item required={false} key={k} wrapperCol={{ span: 24 }} style={{ marginBottom: 10 }}>
        <Row type="flex" style={{ width: '100%', marginBottom: 10}}>
          <Col span={6} style={{ marginRight: 10 }}>
            {getFieldDecorator(
              `additional[${k}].paramCode`,
              {
                initialValue: value[k]?.paramCode || '',
              }
            )(
              <Select
                placeholder="请选择条件"
                showSearch
                filterOption={(input, option) => option.props.children.indexOf(input) >= 0}
                style={{ width: '100%' }}
                onChange={() => {
                  form.setFieldsValue({ [`additional[${k}].values`]: undefined });
                }}
              >
                {conditionOptions &&
                  conditionOptions.length > 0 &&
                  conditionOptions.map(o => (
                    <Option value={this.setParamCodeValue(o)} disabled={this.setDisabled(this.setParamCodeValue(o))}>
                      {o.paramDesc}
                    </Option>
                  ))}
              </Select>
            )}
          </Col>
          {getFieldValue(`additional[${k}].paramCode`) && (
            <Col span={15} style={{ marginRight: 12 }}>
              {getContent(
                k,
                value,
                getFieldDecorator,
                getFieldValue,
                this.getConditions(conditionOptions, getFieldValue(`additional[${k}].paramCode`)),
                behaviorTypeOptions
              )}
            </Col>
          )}

          <Col span={2} style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Popconfirm
              title="是否继续执行删除"
              onConfirm={() => this.remove(k)}
              onCancel={() => this.cancel()}
              okText="继续执行"
              cancelText="取消"
            >
              <CloseCircleOutlined />
            </Popconfirm>
          </Col>
        </Row>
      </Form.Item>
    ));

    return (
      <Fragment>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 10 }}>
          <h3 style={{ margin: '0px 10px 0px 0' }}>附加条件</h3>
          <Button type="primary" onClick={this.add} disabled={behaviorCode === 'VISA'}>
            <PlusOutlined /> 增加
          </Button>
          <span style={{color: 'red'}}>（不同附加条件为“且”的逻辑）</span>
        </div>
        <div className={styles.additionalForm}>
          {formItems}
        </div>
      </Fragment>
    );
  }
}

export default AdditionalConditions;
