import React, { useState, useEffect } from 'react';
import { DatePicker } from 'antd';


const DateTimePicker = (props) => {
  const { onChange, value } = props

  const handleChange = (date, dateString) => {
    onChange(dateString)
  }

  return (
    <DatePicker
      value={ value && dayjs(value, 'YYYY-MM-DD HH:mm:ss')}
      format="YYYY-MM-DD HH:mm:ss"
      onChange={handleChange}
      showTime={{
        defaultValue: dayjs('23:59:59', 'HH:mm:ss'),
      }}
    />
  )
}

export default DateTimePicker
