/* eslint-disable no-nested-ternary */
import React, { Fragment } from 'react';
import { LoadingOutlined } from '@ant-design/icons';
import { Radio, Button, Tag, message, Spin, Tooltip, Table } from 'antd';
import { transform } from '@/utils/crowd';
import { queryCrowdCount, getIsSupportPush, queryLabelByName } from '@/services/api';
import SelectTagModal from '@/components/SelectTagModal';
import SelectEnumValue from '../SelectEnumValueModal';
import { getUrlParam } from '@/utils/utils';
import { SELECT_CROWD_TAGS_TEXT,  SELECT_CROWD_TAGS_CODE} from '../../common/constants';
import { getSelectEnumData, isHsfCustom, setTagGroup, setTagShow, setHsfOrSqlEnumValue} from '../../common/utils'
import styles from './index.less';
import { get } from 'lodash';

const RadioGroup = Radio.Group;
const RadioButton = Radio.Button;

const OPERATOR_MAP = {
  and: 'AND',
  or: 'OR',
  '-': 'NOTIN',
};

const loadingIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;

class SelectTag extends React.PureComponent {
  constructor(props) {
    super(props);
    this.id = props.value && props.value.group ? props.value.group.length + 1 : 1;
    this.stmRef = null;
    this.state = {
      selectTagModalVisible: false,
      selectEnumValueModalVisible: false,
      expression: '',
      tagGroups: props.value ? transform(props.value) : [],
      curTagGroupId: -1,
      calculating: false,
      profileCode: '',
      mock: [],
    };
  }

  componentWillReceiveProps(nextProps) {
    if (
      nextProps !== this.props &&
      typeof this.props.value === 'undefined' &&
      nextProps.value !== this.props.value
    ) {
      console.log(nextProps,'nextPropsnextProps')
      this.id = nextProps.value && nextProps.value.group ? nextProps.value.group.length + 1 : 1;
      this.setState({
        tagGroups: nextProps.value ? transform(nextProps.value) : [],
      }, () => {
        if (getUrlParam('isCrowd')) {
          this.getExpression();
          this.firstTriggerChange()
        }
      });
    }
  }

  componentDidMount() {
    this.getExpression();
    this.firstTriggerChange()
  }

  getCode = (index1, index2, len) =>
    len === 1
      ? String.fromCharCode(65 + index1)
      : `${String.fromCharCode(65 + index1)}${index2 + 1}`;

  // 每次改变标签值触发这个方法
  triggerChange = needCalculateCrowdCount => {
    const { onChange, changeConditions } = this.props
    const {group, outerExpression} = setTagGroup(this.props, this.state, OPERATOR_MAP)

    if (!outerExpression) {
      message.error('圈人条件不能为空');
      return;
    }

    const conditions = {
      group,
      expression: outerExpression,
    };

    if (onChange) {
      onChange(conditions);
      changeConditions(conditions)
    }

    if (needCalculateCrowdCount) {
      this.setState({
        calculating: true,
      });

      queryCrowdCount({
        conditions,
      }).then(res => {
        // 只要有返回就取消loading
        this.setState({
          calculating: false,
        });

        if (!res || !res.success) {
          message.error(res.msg || '计算失败');
          return;
        }

        this.setState({
          crowdCount: res.data,
        });
      });
    }
  };

  // 第一次进来的时候请求
  firstTriggerChange = () => {
    const { onChange, changeConditions } = this.props
    const {group, outerExpression} = setTagGroup(this.props, this.state, OPERATOR_MAP)

    if (!outerExpression) {
      return;
    }

    const conditions = {
      group,
      expression: outerExpression,
    };

    changeConditions(conditions)
  };

  // 计算处理表达式
  getExpression = () => {
    const { tagGroups } = this.state;

    let expression = '';
    tagGroups.forEach((tg, index, arr) => {
      const { selectedTags, operator, profileType } = tg;

      if (index > 0) {
        expression += ` ${operator} `;
      }

      if (selectedTags && selectedTags.length > 1) {
        if (arr.length > 1) {
          expression += '( ';
        }

        selectedTags.forEach((st, i) => {
          if (i > 0) {
            expression += ` ${st.operator} `;
          }
          expression += this.getCode(index, i, selectedTags.length);
        });

        if (arr.length > 1) {
          expression += ' )';
        }
      } else {
        expression += String.fromCharCode(65 + index);
      }
    });

    this.setState({
      expression,
    });
  };

  // 添加标签组
  addTagGroup = (profileType) => {
    const { tagGroups } = this.state;

    // eslint-disable-next-line no-plusplus
    const nextTagGroups = tagGroups.concat({ id: this.id++, selectedTags: [], operator: 'and', profileType });
    this.setState(
      {
        tagGroups: nextTagGroups,
      },
      () => {
        this.getExpression();
        this.triggerChange();
      }
    );
  };

  // 删除标签组
  removeTagGroup = id => {
    const { tagGroups } = this.state;
    if (tagGroups.length === 0) {
      return;
    }

    this.setState(
      {
        tagGroups: tagGroups.filter(t => t.id !== id),
      },
      () => {
        this.getExpression();
        this.triggerChange(true);
      }
    );
  };

  // 打开选择标签  id 为标签组id
  openSelectTagModal = (id, profileType) => {
    this.setState({
      selectTagModalVisible: true,
      curTagGroupId: id,
      profileCode: SELECT_CROWD_TAGS_CODE[profileType]
    });
  };

  cancelSelectTagModal = () => {
    // 修复antd的Modal和Drawer一起使用时的样式bug
    document.body.style.overflow = 'auto';
    this.setState({
      selectTagModalVisible: false,
      curTagGroupId: -1, // 每次选择完毕初始化
      profileCode: ''
    });

    if (this.stmRef) {
      this.stmRef.clear();
    }
  };

  // 打开选择值弹窗 id 为标签id
  openSelectEnumValueModal = async (groupId, tag, index) => {
    // if ((tag && tag.dataType === 'ENUM') || tag.dataType === 'MULTI_VALUE') {
    //   const { dimEnumMetaId } = tag;
    //   if (!dimEnumMetaId || dimEnumMetaId === -1) {
    //     message.error('该标签未选择对应枚举对象');
    //     return;
    //   }
    // }

    const { propertyName } = tag;

    if (propertyName) {
      const result = await queryLabelByName({ name: propertyName });
      if (!result.success) {
        message.error(result.msg);
        return;
      }
      const { status } = result.data;
      if (status !== "ACTIVATE") {
        message.warning("仅可操作使用中标签，如有疑问可联系诸葛产技！")
        return
      }
    }

    // eslint-disable-next-line no-param-reassign
    tag.index = index;

    this.setState({
      selectEnumValueModalVisible: true,
      curTag: tag,
      // mock:mockData,
      curTagGroupId: groupId,
    });

    // console.log('sqlParams',this.state.selectEnumValueModalVisible)
  };

  // 关闭弹窗
  cancelSelectEnumValueModal = () => {
    this.setState({
      selectEnumValueModalVisible: false,
      curTagGroupId: -1,
      curTag: undefined,
      profileCode: ''
    });
  };

  onSelectedTags = selectedTags => {
    const { curTagGroupId, tagGroups } = this.state;

    const tagGroup = tagGroups.find(item => item.id === curTagGroupId);
    if (tagGroup && tagGroup.selectedTags) {
      // eslint-disable-next-line no-restricted-syntax
      for (const tag of selectedTags) {
        if (tagGroup.selectedTags.find(item => item.id === tag.id)) {
          message.error('同一个标签组不能存在相同标签');
          return false;
        }
      }

      tagGroup.selectedTags = tagGroup.selectedTags.concat(
        selectedTags.map(item => ({ ...item, operator: 'and', dimMetaId: item.dimEnumMetaId }))
      );
      // 修复antd的Modal和Drawer一起使用时的样式bug
      document.body.style.overflow = 'auto';


      this.setState(
        {
          tagGroups,
          selectTagModalVisible: false,
        },
        () => {
          this.getExpression();
          this.triggerChange();
        }
      );
    }
    return true;
  };

  onTagGroupOperatorChange = (id, e) => {
    const { tagGroups } = this.state;

    const tagGroup = tagGroups.find(item => item.id === id);

    if (tagGroup && tagGroup.operator) {
      tagGroup.operator = e.target.value;
      this.setState(
        {
          tagGroups,
        },
        () => {
          this.getExpression();
          this.triggerChange(true);
        }
      );
    }
  };

  onTagOperatorChange = (groupId, index, e) => {
    const { tagGroups } = this.state;

    const tagGroup = tagGroups.find(item => item.id === groupId);

    if (tagGroup && tagGroup.selectedTags && tagGroup.selectedTags.length > 0) {
      const { selectedTags } = tagGroup;
      const tag = selectedTags.find((item, i) => i === index);

      if (tag) {
        tag.operator = e.target.value;
        this.setState(
          {
            tagGroups,
          },
          () => {
            this.getExpression();
            this.triggerChange(true);
          }
        );
      }
    }
  };

  // 选择值之后的回调
  onSelectedEnumValue = record => {
    const { curTagGroupId, curTag, tagGroups } = this.state;

    console.log(record,'record')

    // 找到标签组
    const tagGroup = tagGroups.find(item => item.id === curTagGroupId);
    if (tagGroup && tagGroup.selectedTags && tagGroup.selectedTags.length > 0) {
      const { selectedTags } = tagGroup;
      // 找到标签组下选择的标签
      const tag = selectedTags.find((item, index) => index === curTag.index);
      const data = getSelectEnumData(record)
      if (tag) {
        tag.editFormData = data; // 记录提交的信息，编辑的时候使用
        // tag.values为显示使用，value为前后端数据格式使用
        if (tag.dataType === 'DATE') {
          const { dateType, startDate, endDate, pastTime, futureTime } = data;
          if (dateType === 'fixed') {
            tag.values = [
              { label: `开始时间:${startDate}`, value: startDate },
              { label: `结束时间:${endDate}`, value: endDate },
            ];
          } else if (dateType === 'relativePast') {
            tag.values = [{ label: `最近${pastTime}天`, value: parseInt(`-${pastTime}`, 10) }];
          } else if (dateType === 'relativeFuture') {
            tag.values = [{ label: `未来${futureTime}天`, value: futureTime }];
          }
        } else if (tag.dataType === 'ENUM' || tag.dataType === 'MULTI_VALUE') {
          const { enumValues } = data;
          tag.values = enumValues;
        } else if (tag.dataType === 'NUMBER') {
          const {
            numberValue: { min, max },
          } = data;
          tag.values = [
            { label: `最小值:${min}`, value: min },
            { label: `最大值:${max}`, value: max },
          ];
        } else if (tag.dataType === 'NONE_TYPE') {
          tag.values = [...setHsfOrSqlEnumValue(data)];
        } else if (tag.dataType === 'KV') {
          const { targets } = data;
          tag.values = targets.map(t => {
            const weight = data[`${t.value}_weight`];
            const labelWeight = weight.join('~');
            const valueWeight = weight.join(' ');
            return {
              label: `${t.label}：${labelWeight}`,
              value: `${t.value} ${t.label} ${valueWeight}`, // 服务端约定接口格式
            };
          });
        } else if (tag.dataType === 'JSON_OBJECT') {
          tag.values = data?.data?.[0]?.paramLabel || []
        }

        // 存在hsf实时自定义
        if (isHsfCustom(tag)) {
          tag.hsfEditFormData = record?.hsfData
          tag.hsfValues = record?.hsfData && record?.mock ? [...setHsfOrSqlEnumValue( {...record?.hsfData, mock: record?.mock })] : [];
        }
        console.log(tagGroups,'tagGroups')
        this.setState(
          {
            tagGroups,
            selectEnumValueModalVisible: false,
            curTagGroupId: -1, // 每次选择完毕初始化
            curTag: undefined,
            profileCode: ''
          },
          () => {
            this.triggerChange(true);
          }
        );
      }
    }
  };

  // 删除已选择的标签 id代表标签组id，index代表标签所在标签组位置
  removeSelectedTag = (groupId, index) => {
    const { tagGroups } = this.state;

    const tagGroup = tagGroups.find(item => item.id === groupId);

    if (tagGroup && tagGroup.selectedTags && tagGroup.selectedTags.length > 0) {
      const { selectedTags } = tagGroup;

      tagGroup.selectedTags = selectedTags.filter((item, i) => i !== index);
      this.setState(
        {
          tagGroups: tagGroups.slice(),
        },
        () => {
          this.getExpression();
          this.triggerChange(true);
        }
      );
    }
  };

  renderTable = () => {
    const columns = [{
      title: '数据级',
      dataIndex: 'data',
      key: 'data',
    }, {
      title: '误差系数',
      dataIndex: 'error',
      key: 'error',
    }]

    const dataSource = [
      {
        key: '1',
        data: '100万以上',
        error: '<0.1%',
      },
      {
        key: '2',
        data: '10万～100万',
        error: '1%',
      },
      {
        key: '3',
        data: '1万～10万',
        error: '1%~5%',
      },
      {
        key: '4',
        data: '1万以下',
        error: '>10%',
      },
    ];

    return (
      <div>
        <div style={{ color: 'red' }}>预估值，存在误差</div>
        <p>汇总后的误差</p>
        <Table dataSource={dataSource} columns={columns} pagination={false} />
      </div>
    )
  }

  render() {
    const { crowdAmount, profileType } = this.props;
    const {
      tagGroups,
      selectTagModalVisible,
      expression,
      selectEnumValueModalVisible,
      curTag,
      crowdCount,
      calculating,
      profileCode,
      mock,
    } = this.state;

    const count =
      typeof crowdCount !== 'undefined' && crowdCount !== null ? crowdCount : crowdAmount;

    const isShow = (tag) => {
      if (tag?.dataType === 'NONE_TYPE') {
        if (!tag?.sourceConfig?.sqlConfig?.sqlParams) {
          if (tag?.editFormData && !(JSON.stringify(tag?.editFormData) === '{}')) {
            return false
          }
          if (getUrlParam('crowdParams') && tag?.sqlParams?.length === 0) {
            return false
          }
          return true
        }
      }
      return false
    }

    return (
      <div className={styles.container}>
        <div className={styles.wrapper}>
          {
            ['TAOBAO_USER', 'SCRM_USER'].includes(profileType) && <Button shape="round" onClick={() => { this.addTagGroup('tbup') }} style={{ marginRight: '20px' }}>
              添加淘宝ID标签组
            </Button>
          }
          {
            ['SCRM_USER'].includes(profileType) && <Button shape="round" onClick={() => { this.addTagGroup('scrm_up') }} >
              添加Scrm-Oneid标签组
            </Button>
          }
          {
            ['DEVICE'].includes(profileType) && <Button shape="round" onClick={() => { this.addTagGroup('device_p') }} >
              添加Device Oneid标签组
            </Button>
          }
        </div>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div className={styles.crowdCountText}>人群数量：</div>
          <Spin indicator={loadingIcon} spinning={calculating}>
            <Tooltip title={this.renderTable()} placement="bottom">
              <span style={{ fontSize: 16, color: '#ffa940', fontWeight: 'bolder' }}>
                {calculating ? '计算中...' : count === -1 ? '暂无' : count}
              </span>
            </Tooltip>
          </Spin>
        </div>
        <div className={styles.expression}>{expression}</div>
        {/* 计算人群数量目前不需要 */}
        {/* <div className={styles.crowdCountText} style={{ color: 'red' }}>圈人任务运行成功之后展示人群数量</div> */}
        {tagGroups &&
          tagGroups.length > 0 &&
          tagGroups.map((t, i) => {
            const { id: groupId, selectedTags, operator, profileType } = t;
            return (
              <Fragment key={`tagGroup_${groupId}`}>
                {i !== 0 && (
                  <div style={{ width: '100%', textAlign: 'center' }}>
                    <RadioGroup
                      defaultValue={operator}
                      onChange={e => this.onTagGroupOperatorChange(groupId, e)}
                    >
                      <RadioButton value="and">并且</RadioButton>
                      <RadioButton value="or">或者</RadioButton>
                      <RadioButton value="-">排除</RadioButton>
                    </RadioGroup>
                  </div>
                )}
                <div className={i === 0 ? `${styles.tagGroupTop} ${styles.tagGroup}` : styles.tagGroup}>
                  {selectedTags &&
                    selectedTags.length > 0 &&
                    selectedTags.map((tag, index) => (
                      // eslint-disable-next-line react/no-array-index-key
                      <Fragment key={`tag_${tag.id}_${index}`}>
                        {index !== 0 && (
                          <div style={{ width: '100%', textAlign: 'center' }}>
                            <RadioGroup
                              defaultValue={tag.operator}
                              onChange={e => this.onTagOperatorChange(groupId, index, e)} // id为标签组id
                            >
                              <RadioButton value="and">并且</RadioButton>
                              <RadioButton value="or">或者</RadioButton>
                              <RadioButton value="-">排除</RadioButton>
                            </RadioGroup>
                          </div>
                        )}
                        <div className={styles.tag} key={`tag_${tag.id}`}>
                          <div className={styles.tagOrder}>
                            {this.getCode(i, index, selectedTags.length)}
                          </div>
                          <div className={styles.tagInfo}>
                            <Tooltip title={`${tag.name || tag.propertyDescription}(${tag.code || tag.propertyName})`}>
                              <span className={styles.tagName}>{`${tag.name ||
                                tag.propertyDescription}(${tag.code || tag.propertyName})`}</span>
                            </Tooltip>

                            <span className={styles.manzuText}> 满足 </span>
                            {isShow(tag) ? (<div style={{ display: 'flex', flex: 1, justifyContent: 'center' }}>无需选择参数</div>) : (
                              <div
                                className={styles.enumValue}
                                onClick={() => this.openSelectEnumValueModal(groupId, tag, index)}
                              >
                                {setTagShow(tag).map(v => (
                                    <Tag
                                      className={styles.myTag}
                                      key={`value_${groupId}_${tag.id}_${v.label}`}
                                      color="cyan"
                                    >
                                      {/*  判断如果包含&gt; 替换为 >  */}
                                      {v.label
                                        ? v.label.includes('&gt;')
                                          ? v.label.replace('&gt;', '>')
                                          : v.label
                                        : null}
                                    </Tag>
                                  ))}
                              </div>
                            )}
                            <div
                              className={styles.tagDelIcon}
                              onClick={() => this.removeSelectedTag(groupId, index)}
                            >
                              x
                            </div>
                          </div>
                        </div>
                      </Fragment>
                    ))}

                  <div>
                    {!selectedTags ||
                      (selectedTags.length === 0 && (
                        <div className={styles.tagOrder}>{String.fromCharCode(65 + i)}</div>
                      ))}
                    <Button shape="round" onClick={() => this.openSelectTagModal(groupId, profileType)}>
                      {SELECT_CROWD_TAGS_TEXT[profileType || 'tbup']}
                    </Button>
                  </div>
                  <div
                    className={styles.tagGroupDelIcon}
                    onClick={() => this.removeTagGroup(groupId)}
                  >
                    x
                  </div>
                </div>
              </Fragment>
            );
          })}
        <SelectTagModal
          // eslint-disable-next-line no-return-assign
          ref={ref => (this.stmRef = ref)}
          visible={selectTagModalVisible}
          profileCode={profileCode}
          onCancel={this.cancelSelectTagModal}
          onOk={this.onSelectedTags}
        />

        <SelectEnumValue
          title={curTag ? curTag.name || curTag.propertyDescription : '请选择'}
          visible={selectEnumValueModalVisible}
          onCancel={this.cancelSelectEnumValueModal}
          tag={curTag}
          onOk={this.onSelectedEnumValue}
          mock={mock}
        />
      </div>
    );
  }
}

export default SelectTag;
