import React from 'react';
import { InputNumber } from 'antd';

class SelectMaxAndMin extends React.PureComponent {
  handleMinChange = value => {
    const min = parseInt(value, 10);
    if (Number.isNaN(min)) {
      return;
    }
    this.triggerChange({ min });
  };

  handleMaxChange = value => {
    const max = parseInt(value, 10);
    if (Number.isNaN(max)) {
      return;
    }
    this.triggerChange({ max });
  };

  triggerChange = changedValue => {
    const { onChange, value } = this.props;
    if (onChange) {
      onChange({
        ...value,
        ...changedValue,
      });
    }
  };

  render() {
    const {
      value: { max = 0, min = 0 },
    } = this.props;
    return (
      <div>
        <InputNumber min={0} max={max} value={min} onChange={this.handleMinChange} />
        <span style={{ marginLeft: 4 }}>最大值：</span>
        <InputNumber min={0} value={max} onChange={this.handleMaxChange} />
      </div>
    );
  }
}

export default SelectMaxAndMin;
