import React, { useState, useEffect } from 'react';
import { Select, message } from 'antd';
import { queryLabelEnumValueBuildTree } from '@/services/api';

const ConditionSelect = (props) => {
  const [loading, setLoading] = useState(false)
  const [options, setOptions] = useState([])
  const { value, onChange, dimEnumMetaId } = props;

  useEffect(() => {
    if (dimEnumMetaId) {
      getEnumValues(dimEnumMetaId)
    }
  }, [dimEnumMetaId])

  const getEnumValues = (dimEnumMetaId) => {
    queryLabelEnumValueBuildTree({ dimMetaId: dimEnumMetaId })
      .then(res => {
        if (!res.success) {
          message.error(res.msg);
          return;
        }

        const o = res.data && res.data.root && res.data.root.children;
        setLoading(false)
        setOptions(o.map(item => ({ label: item.name, value: `${item.enumCode} ${item.name}` })))
      })
      .catch(() => {
        message.error('请求失败');
      });
  }

  return (
    <Select
      loading={loading}
      placeholder="请选择"
      showSearch
      filterOption={(input, option) => option.props.children.indexOf(input) >= 0}
      onChange={onChange}
      value={value}
    >
      {options &&
        options.length > 0 &&
        options.map(o => (
          <Option key={o.value} value={o.value}>
            {o.label}
          </Option>
        ))}
    </Select>
  )

}

export default ConditionSelect