.copBtns {
  margin-bottom: 50px;

  .copBtn {
    margin-left: 10px;
  }
}

.crowdBtns {
  margin-bottom: 20px;
  .crowdBtn {
    margin-right: 10px;
  }
}

.crowdName {
  max-width: 180px;
  overflow: hidden;
  // white-space: nowrap;
  text-overflow: ellipsis;
  color: #1890ff;
}

:global {
  .fl-search-form-wrap{
    .ant-row.ant-form-item{
      height: 32px;
    }
  }
    // .ant-table-row .ant-table-cell {
      
    // }

    // .ant-table-row .ant-table-cell:nth-child(2) {
    //   max-width: 150px;
    // }
  
    .ant-table-row .ant-table-cell:nth-child(4) {
      max-width: 130px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .bucket{
      max-width: 130px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
}