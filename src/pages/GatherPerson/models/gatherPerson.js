import { message } from 'antd';
import {
  queryAlgoModel,
  queryCrowd,
  errorMsg,
  editCrowdByAlgo,
  editCrowdTransition,
  editCrowdByEnLarge,
  editCrowdByOdps,
  editCrowdByFile,
  queryCrowdById,
  queryAlgoModelTaskConfig,
  deleteCrowd,
  delayCrowd,
  queryActivityByCondition,
  isSeedCrowd,
  editExportCrowdTask,
  editCrowdByOperate,
  queryblackwhitelist,
  addBlackWhiteListCrowdTask,
  updateBlackWhiteListCrowdTask,
  queryBlackWhiteListRecords,
  editCrowdBySQL,
  getRerunCrowd,
  updateCreator
} from '@/services/api';
import dayjs from 'dayjs'
import { crowdErrMsgMap } from '../constants'

export default {
  namespace: 'gatherPerson',
  state: {
    loading: false,
    refreshLoading: false, // 刷新进度时loading
    type: 'primary', // primary: 我的，all:全部， channel:渠道

    algoModels: [],

    editCrowdByAlgoFormData: {},
    /**
     * 编辑
     */
    editFormData: {}, // 算法圈人
    crowdByAlgoModalVisible: false,
    editCrowdTransitionFormData: {},
    crowdTransitionModalVisible: false, //跃迁圈人
    searchFormData: {},
    pagination: {},
    crowdList: [],
    detailModalVisible: false,
    curCrowd: null, // 查看详情时当前的人群
    crowdDelayModalVisible: false,
    editCrowdDelayFormData: {},
    crowdByEnLargeModalVisible: false, // 人群放大表单
    editCrowdByEnLargeFormData: {},
    crowdTestModalVisible: false,
    BlackOrWhiteListModalVisible: false, // 黑白名单
    BlackOrWhiteListFormData: {},
    editCrowdTestFormData: {},
    editCrowdByOdpsFormData: {}, // odps导入表单
    crowdByOdpsModalVisible: false,
    editCrowdBySqlFormData: {}, // sql圈人
    editCrowdByFileFormData: {}, // 文件圈人
    crowdBySqlModalVisible: false,
    crowdExportDrawerVisible: false, // 人群导出
    editCrowdExportFormData: {}, // 人群导出
    crowdByOperateModalVisible: false, // 人群逻辑运算
    editCrowdByOperateFormData: {}, // 人群逻辑运算
    addGroupVisible: false, //添加实验分组
    hasGroup:false, //是否存在实现分组
    groupModeFormData:{}, //实验分组
    crowdMonitorVisible: false, //人群监控弹窗
    crowdMonitorFormData:{}, //人群监控值
    // https://yuque.antfin-inc.com/zhuge/workbench/so2o2g#UxBuX
    crowdTransferVisible: false, //转交人群
    crowdTransferFormData:{}, //转交人群
    crowdCreateVisible: false, // 创建人群弹窗
    crowdCreateFormData:{}, // 创建人群
    formData: {
      id: '',
      crowdName: '',
      // creator: '',
      // creator: [{
      //   empId: '',
      //   nickName: '',
      // }],
      // dateRange: [
      //   dayjs(
      //     dayjs()
      //       .subtract(7, 'd')
      //       .format('YYYYMMDD')
      //   ),
      //   dayjs(
      //     dayjs()
      //       .subtract(1, 'd')
      //       .format('YYYYMMDD')
      //   ),
      // ],
      // createBegin: '',
      // createEnd: '',
      // crowdType: '',
      // crowdStatus: '',
      crowdApplyScene: '', // 使用场景
      // deleted: '',
      // crowdTags: '',
      realTime: '', // 1: 实时人群 0: 非实时人群
      needUpdate: '', // 1: 需要更新 0: 不需要更新
      userControl: '', // 1: 设置了黑白名单 0: 未设置黑白名单
    },
    crowdErrMsgMap,
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(({ pathname, query }) => {
        if (pathname.includes('/crowd-stategy/crowd-circle')) {
          const payload = {};
          if (query.crowdId) {
            payload.id = query.crowdId;
            payload.type = 'all';
            dispatch({
              type: 'updateState',
              payload: {
                searchValue: query.crowdId,
              },
            });

            dispatch({
              type: 'showDetailFromQuery',
              payload,
            });
          }
          dispatch({
            type: 'query',
            payload,
          });
        }
      });
    },
  },
  effects: {
    // url 上有圈人ID时，直接打开弹窗
    *showDetailFromQuery({ payload }, { put, call }) {
      const res = yield call(queryCrowdById, {
        id: payload.id,
      });
      // gatherPerson/onOpenDetailModal

      yield put({
        type: 'onOpenDetailModal',
        payload: {
          curCrowd: res.data || {},
        }
      });
    },
    *query({ payload }, { put, all }) {
      yield all([
        put.resolve({
          type: 'queryCrowd',
          payload,
        }),
        put.resolve({
          type: 'queryAlgoModel',
        }),
        put.resolve({
          type: 'queryErrorMsg',
        })
      ]);
    },
    *queryErrorMsg({ payload = {} }, { call, put }) {
      const { errorCode } = payload
      const res = yield call(errorMsg, {
        errorCode: errorCode || '10000',
      });

      // console.log('res=========>', res)

      const errMsgMap = res && res.data && res.data.crowdBuildErrMsg ? getErrMsgMap(res.data.crowdBuildErrMsg) : crowdErrMsgMap

      yield put({
        type: 'updateState',
        payload: {
          crowdErrMsgMap: errMsgMap,
        },
      });
    },
    *queryAlgoModel(model, { put, call }) {
      // 获取全部的算法模型，目前设为100
      const res = yield call(queryAlgoModel, { pageNum: 1, pageSize: 100 });

      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }

      const rows = res.data && res.data.rows;

      yield put({
        type: 'updateState',
        payload: {
          algoModels: rows,
        },
      });
    },
    *queryCrowd({ payload = {} }, { call, put, select }) {
      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const { pagination, formData, type } = yield select(state => state.gatherPerson);

      const param = {
        ...payload
      }
      const time = {
        createBegin: '',
        createEnd: '',
      }
      if (formData.dateRange) {
        if (formData.dateRange[0]) {
          time.createBegin = dayjs(formData.dateRange[0]).valueOf()
        }
        if (formData.dateRange[1]) {
          time.createEnd = dayjs(formData.dateRange[1]).valueOf()
        }
      }
      if (payload.dateRange) {
        if (payload.dateRange[0]) {
          time.createBegin = dayjs(dayjs(payload.dateRange[0]).format('YYYY-MM-DD')).valueOf()
        }
        if (payload.dateRange[1]) {
          time.createEnd = dayjs(dayjs(payload.dateRange[1]).add(1, 'days').format('YYYY-MM-DD')).valueOf()
        }
      }
      // if (formData.createBegin) {
      //   time.createBegin = dayjs(formData.createBegin).valueOf()
      // }
      // if (formData.createEnd) {
      //   time.createEnd = dayjs(dayjs(formData.createEnd).add(1, 'days').format('YYYY-MM-DD')).valueOf()
      // }
      // if (param.createBegin) {
      //   time.createBegin = dayjs(payload.createBegin).valueOf()
      // }
      // if (payload.createEnd) {
      //   time.createEnd = dayjs(dayjs(payload.createEnd).add(1, 'days').format('YYYY-MM-DD')).valueOf()
      // }

      let creator;
      if (formData.creatorObj) {
        const creatorObjParsed = JSON.parse(formData.creatorObj);
        creator = creatorObjParsed ? creatorObjParsed.empId : '';
      }

      // console.log(pagination,'pagination999')

      const res = yield call(queryCrowd, {
        deleted: 0,
        pageNum: pagination.current || 1,
        pageSize: pagination.pageSize || 10,
        bizRegionCode: 'public_region',
        type,
        ...formData,
        ...payload,
        ...time,
        creator,
      });

      if (!res.success) {
        message.error(res.msg || '获取人群列表失败');
        return;
      }

      const { rows, totalNum } = res.data;

      pagination.total = totalNum;

      yield put({
        type: 'updateState',
        payload: {
          crowdList: rows,
          loading: false,
          pagination,
          type: payload.type || type,
        },
      });
    },
    *delete({ payload }, { call, put }) {
      const result = yield call(isSeedCrowd, payload);

      if (!result.success || !result.data) {
        message.error(result.msg || '请求失败');
        return;
      }

      if (result.data.length) {
        message.error('该人群已作为种子人群，无法删除');
        return;
      }

      const res = yield call(deleteCrowd, payload);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success('删除成功');

      yield put({
        type: 'queryCrowd',
      });
    },
    *reRun({ payload }, { call, put }) {

      const res = yield call(getRerunCrowd, payload);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success('重跑成功');
    },
    *queryCrowdById(_, { call, put, select }) {
      const { curCrowd } = yield select(state => state.gatherPerson);

      if (!curCrowd || !curCrowd.id) {
        message.error('不存在该人群');
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          refreshLoading: true,
        },
      });

      const res = yield call(queryCrowdById, {
        id: curCrowd.id,
      });

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success('刷新成功');

      yield put({
        type: 'updateState',
        payload: {
          curCrowd: { ...curCrowd, ...(res.data || {}) },
          refreshLoading: false,
        },
      });
    },
    *queryCrowdAndUpdateOperateFormData({ payload }, { put, all }) {
      const { crowdLeft, crowdRight } = payload;

      const [res1, res2] = yield all([
        crowdLeft ? queryCrowdById({ id: crowdLeft }) : Promise.resolve(),
        crowdRight ? queryCrowdById({ id: crowdRight }) : Promise.resolve(),
      ]);

      const left = res1 && res1.data;
      const right = res2 && res2.data;
      yield put({
        type: 'onUpdateEditCrowdByOperateFormData',
        payload: {
          leftCrowdNum: left && left.crowdAmount,
          rightCrowdNum: right && right.crowdAmount,
          crowdLeft: left && {
            label: left.crowdName,
            key: left.id,
          },
          crowdRight: right && {
            label: right.crowdName,
            key: right.id,
          },
        },
      });
    },
    *queryCrowdAndUpdateEnLargeFormData({ payload }, { put, all }) {
      const { seedCrowdId, exceptCrowdId } = payload;

      const [res1, res2] = yield all([
        seedCrowdId ? queryCrowdById({ id: seedCrowdId }) : Promise.resolve(),
        exceptCrowdId ? queryCrowdById({ id: exceptCrowdId }) : Promise.resolve(),
      ]);

      const seedCrowd = res1 && res1.data;
      const exceptCrowd = res2 && res2.data;
      yield put({
        type: 'onUpdateEditCrowdByEnLargeFormData',
        payload: {
          limitCrowdLimitNum: seedCrowd && seedCrowd.crowdAmount,
          seedCrowdId: seedCrowd && {
            label: seedCrowd.crowdName,
            key: seedCrowd.id,
          },
          exceptCrowdId: exceptCrowd && {
            label: exceptCrowd.crowdName,
            key: exceptCrowd.id,
          },
        },
      });
    },
    *editCrowdByAlgo({ payload }, { call, put, select }) {
      const data = payload;

      const { editCrowdByAlgoFormData } = yield select(state => state.gatherPerson);

      const isUpdate = !!(editCrowdByAlgoFormData && editCrowdByAlgoFormData.id);
      if (isUpdate) {
        data.id = editCrowdByAlgoFormData.id;
      }

      const res = yield call(editCrowdByAlgo, data);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success(isUpdate ? '更新成功' : '创建成功');

      yield put({
        type: 'updateState',
        payload: {
          crowdByAlgoModalVisible: false,
          editCrowdByAlgoFormData: {},
        },
      });

      yield put({
        type: 'queryCrowd',
      });
    },
    *editCrowdTransition({ payload }, { call, put, select }) {
      const data = payload;

      const { editCrowdTransitionFormData } = yield select(state => state.gatherPerson);

      const isUpdate = !!(editCrowdTransitionFormData && editCrowdTransitionFormData.id);
      if (isUpdate) {
        data.id = editCrowdTransitionFormData.id;
      }

      const res = yield call(editCrowdTransition, data);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success(isUpdate ? '更新成功' : '创建成功');

      yield put({
        type: 'updateState',
        payload: {
          crowdTransitionModalVisible: false,
          editCrowdTransitionFormData: {},
        },
      });

      yield put({
        type: 'queryCrowd',
      });
    },
    *editCrowdByEnLarge({ payload }, { call, put, select }) {
      const data = payload;

      const { editCrowdByEnLargeFormData } = yield select(state => state.gatherPerson);

      const isUpdate = !!(editCrowdByEnLargeFormData && editCrowdByEnLargeFormData.id);
      if (isUpdate) {
        data.id = editCrowdByEnLargeFormData.id;
      }

      const res = yield call(editCrowdByEnLarge, data);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      if (window.location.href.includes('isCrowd=true') && !isUpdate) {
        window.parent.postMessage({id:res && res.data}, '*');
      }

      message.success(isUpdate ? '更新成功' : '创建成功');

      yield put({
        type: 'updateState',
        payload: {
          crowdByEnLargeModalVisible: false,
          editCrowdByEnLargeFormData: {},
        },
      });

      yield put({
        type: 'queryCrowd',
      });
    },
    *editCrowdByOperate({ payload }, { call, put, select }) {
      const data = payload;

      const { editCrowdByOperateFormData } = yield select(state => state.gatherPerson);

      const isUpdate = !!(editCrowdByOperateFormData && editCrowdByOperateFormData.id);
      if (isUpdate) {
        data.id = editCrowdByOperateFormData.id;
      }

      const res = yield call(editCrowdByOperate, data);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success(isUpdate ? '更新成功' : '创建成功');

      yield put({
        type: 'onCrowdByOperateModalCancel',
      });

      yield put({
        type: 'queryCrowd',
      });
    },
    *editCrowdDelayTime({ payload }, { call, put }) {
      const data = payload;

      const res = yield call(delayCrowd, data);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success('延期成功');

      yield put({
        type: 'queryCrowd',
      });

      yield put({
        type: 'onCrowdDelayModalCancel',
      });
    },
    *editCrowdByOdps({ payload }, { call, put, select }) {
      const data = payload;

      const { editCrowdByOdpsFormData } = yield select(state => state.gatherPerson);

      const isUpdate = !!(editCrowdByOdpsFormData && editCrowdByOdpsFormData.id);
      if (isUpdate) {
        data.id = editCrowdByOdpsFormData.id;
      }

      const res = yield call(editCrowdByOdps, data);
      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success(isUpdate ? '更新成功' : '创建成功');

      yield put({
        type: 'updateState',
        payload: {
          crowdByOdpsModalVisible: false,
          editCrowdByOdpsFormData: {},
        },
      });

      yield put({
        type: 'queryCrowd',
      });
    },
    *editCrowdByFile({ payload }, { call, put, select }) {
      const data = payload;

      const { editCrowdByFileFormData } = yield select(state => state.gatherPerson);

      const isUpdate = !!(editCrowdByFileFormData && editCrowdByFileFormData.id);
      if (isUpdate) {
        data.id = editCrowdByFileFormData.id;
      }

      const res = yield call(editCrowdByFile, {...data, needUpdate: 0}); // 与odps导入共用一个接口
      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success(isUpdate ? '更新成功' : '创建成功');

      yield put({
        type: 'updateState',
        payload: {
          crowdByFileModalVisible: false,
          editCrowdByFileFormData: {},
        },
      });

      yield put({
        type: 'queryCrowd',
      });
    },
    *editCrowdBySql({ payload }, { call, put, select }) {
      const data = payload;

      const { editCrowdBySqlFormData } = yield select(state => state.gatherPerson);

      const isUpdate = !!(editCrowdBySqlFormData && editCrowdBySqlFormData.id);
      if (isUpdate) {
        data.id = editCrowdBySqlFormData.id;
      }

      const res = yield call(editCrowdBySQL, data);
      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success(isUpdate ? '更新成功' : '创建成功');

      yield put({
        type: 'updateState',
        payload: {
          crowdBySqlModalVisible: false,
          editCrowdBySqlFormData: {},
        },
      });

      yield put({
        type: 'queryCrowd',
      });
    },
    *editExportCrowdTask({ payload }, { call, put }) {
      const data = payload;
      const isUpdate = !!data.id;

      const res = yield call(editExportCrowdTask, data);

      if (!res.success) {
        message.error(res.msg);
        return res;
      }

      message.success(isUpdate ? '导出任务更新成功' : '导出任务创建成功');

      yield put({
        type: 'onCrowdExportModalCancel',
      });

      return res
    },
    *queryblackwhitelist({payload}, {call, put, select}){
      const res = yield call(queryblackwhitelist, payload);


      if(!res.success){
        return;
      }
      const id = res.data ? res.data.id : null;
      const whiteList = res.data ? res.data.whiteList : '';
      const blackList = res.data ? res.data.blackList : '';

      yield put({
        type: 'updateBlackWhiteData',
        payload: {id, whiteList, blackList, groupType: "TAOBAO_USER"} // 群组类型，目前均默认为：TAOBAO_USER
      })

    },
    *editBlackWhiteListCrowdTask({ payload }, { call, put, select }){
      const { BlackOrWhiteListFormData} = yield select(state => state.gatherPerson);

      if(BlackOrWhiteListFormData.id){
        // 更新群组黑白名单
        const data = {id: BlackOrWhiteListFormData.id, operator: BlackOrWhiteListFormData.operator, groupId: BlackOrWhiteListFormData.groupId, groupType: BlackOrWhiteListFormData.groupType}
        const res = yield call(updateBlackWhiteListCrowdTask, {...data, ...payload})
        if(res.success){
          message.success('更新成功！')
          yield put({
            type: 'onCrowdBlackOrWhiteListModalCancel',
          });
        } else {
          message.error(res?.code || res?.msg || '更新失败')
        }

      }else{
        const data = {groupId: BlackOrWhiteListFormData.groupId, creator: BlackOrWhiteListFormData.creator, groupType: BlackOrWhiteListFormData.groupType}
        // 新增群组黑白名单
        const res = yield call(addBlackWhiteListCrowdTask, {...data, ...payload})
        if(res.success){
          message.success('添加成功！')
          yield put({
            type: 'onCrowdBlackOrWhiteListModalCancel',
          });
        } else {
          message.error(res?.code || res?.msg || '添加失败')
        }
      }
    },
    *queryAlgoModelTaskConfig({ payload }, { call, put }) {
      const res = yield call(queryAlgoModelTaskConfig, payload);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      if (res.data && res.data.rows && res.data.rows.length > 0) {
        const [linkGood] = res.data.rows;
        yield put({
          type: 'onUpdateEditCrowdByAlgoFormData',
          payload: {
            linkGood: {
              name: linkGood.name,
              taskId: linkGood.id,
            },
          },
        });
      }
    },
    *queryActivityByCondition({ payload }, { call, put }) {
      const res = yield call(queryActivityByCondition, payload);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      if (res.data && res.data.subActivityDetailList && res.data.subActivityDetailList.length > 0) {
        const [linkGalaxyActivity] = res.data.subActivityDetailList;
        yield put({
          type: 'onUpdateEditCrowdByAlgoFormData',
          payload: {
            linkGalaxyActivity: {
              name: linkGalaxyActivity.activityTitle,
              taskId: linkGalaxyActivity.id,
            },
          },
        });
      }
    },
    /* 打开人群详情 */
    *onOpenDetailModal({ payload }, { call, put, all }) {
      const { curCrowd } = payload;

      if (curCrowd.crowdType === 'ALGO_CROWD') {
        const { extInfo } = curCrowd;
        const algoModelTaskId = extInfo.ALGO_MODEL_TASK_CONFIG_ID;
        const res = yield call(queryAlgoModelTaskConfig, {
          id: algoModelTaskId,
          type: 'all',
        });

        if (!res.success) {
          message.error(res.msg);
          return;
        }

        if (res.data && res.data.rows && res.data.rows.length > 0) {
          const [linkGood] = res.data.rows;
          curCrowd.taskName = linkGood.name;
        }
      } else if (curCrowd.crowdType === 'TRIP_GALAXY_CROWD') {
        const { extInfo } = curCrowd;
        const algoModelTaskId = extInfo.ALGO_MODEL_TASK_CONFIG_ID;
        const res = yield call(queryActivityByCondition, {
          activityId: algoModelTaskId,
          type: 'all',
        });

        if (!res.success) {
          message.error(res.msg);
          return;
        }

        if (
          res.data &&
          res.data.subActivityDetailList &&
          res.data.subActivityDetailList.length > 0
        ) {
          const [linkGalaxyActivity] = res.data.subActivityDetailList;
          curCrowd.taskName = linkGalaxyActivity.activityTitle;
        }
      } else if (curCrowd.crowdType === 'ALGO_ENLARGE_CROWD') {
        const { extInfo } = curCrowd;
        const { ALGO_ENLARGE_SEED_CROWD_ID, ALGO_ENLARGE_EXCEPT_CROWD_ID } = extInfo;
        const [res1, res2] = yield all([
          ALGO_ENLARGE_SEED_CROWD_ID
            ? queryCrowdById({ id: ALGO_ENLARGE_SEED_CROWD_ID })
            : Promise.resolve(),
          ALGO_ENLARGE_EXCEPT_CROWD_ID
            ? queryCrowdById({ id: ALGO_ENLARGE_EXCEPT_CROWD_ID })
            : Promise.resolve(),
        ]);

        const seedCrowd = res1 && res1.data;
        const exceptCrowd = res2 && res2.data;
        curCrowd.seedCrowdID = seedCrowd && seedCrowd.id;
        curCrowd.seedCrowdName = seedCrowd && seedCrowd.crowdName;
        curCrowd.exceptCrowdName = exceptCrowd && exceptCrowd.crowdName;
      } else if (curCrowd.crowdType === 'ODPS_TABLE_CROWD') {
        const { extInfo = {} } = curCrowd;

        const {
          ODPS_ACCESS_ID,
          ODPS_ACCESS_KEY,
          ODPS_TABLE,
          ODPS_TABLE_PARTITION,
          ODPS_TABLE_UID,
          ODPS_TABLE_WHERE,
        } = extInfo;

        curCrowd.tableName = ODPS_TABLE;
        curCrowd.partition = ODPS_TABLE_PARTITION;
        curCrowd.uid = ODPS_TABLE_UID;
        curCrowd.where = ODPS_TABLE_WHERE;
        curCrowd.accessId = ODPS_ACCESS_ID;
        curCrowd.accessKey = ODPS_ACCESS_KEY;
      } else if (curCrowd.crowdType === 'ODPS_SQL_CROWD') {
        const { extInfo = {} } = curCrowd;

        const { ODPS_SQL } = extInfo;
        curCrowd.sql = ODPS_SQL;
      } else if (curCrowd.crowdType === 'OPERATE_CROWD') {
        const MAP = {
          AND: '交集',
          OR: '并集',
          NOTIN: '差集',
        };
        const { extInfo } = curCrowd;
        const { OPERATE_CROWD_LEFT, OPERATE_CROWD_RIGHT, OPERATE_CROWD_TYPE } = extInfo;
        const [res1, res2] = yield all([
          OPERATE_CROWD_LEFT ? queryCrowdById({ id: OPERATE_CROWD_LEFT }) : Promise.resolve(),
          OPERATE_CROWD_RIGHT ? queryCrowdById({ id: OPERATE_CROWD_RIGHT }) : Promise.resolve(),
        ]);

        const left = res1 && res1.data;
        const right = res2 && res2.data;
        curCrowd.crowdLeftName = left && left.crowdName;
        curCrowd.crowdLeftId = left && left.id;
        curCrowd.crowdRightName = right && right.crowdName;
        curCrowd.crowdRightId = right && right.id;
        curCrowd.operateCrowdType = MAP[OPERATE_CROWD_TYPE];
      } else if (curCrowd.crowdType === 'FILE_CROWD') {
        const { extInfo = {} } = curCrowd;
        const { UPLOAD_FILE_PATH } = extInfo;
        curCrowd.pathName = UPLOAD_FILE_PATH;
      }

      yield put({
        type: 'updateState',
        payload: {
          curCrowd,
          detailModalVisible: true,
        },
      });
    },
    *onEditCrowdByAlgo({ payload }, { put }) {
      if (payload.linkGoodId) {
        yield put({
          type: 'queryAlgoModelTaskConfig',
          payload: {
            id: payload.linkGoodId,
            type: 'all',
          },
        });
      }

      if (payload.linkGalaxyActivity) {
        yield put({
          type: 'queryActivityByCondition',
          payload: {
            activityId: payload.linkGalaxyActivity,
            type: 'all',
          },
        });
      }

      yield put({
        type: 'onUpdateEditCrowdByAlgoFormData',
        payload,
      });

      yield put({
        type: 'onOpenCrowdByAlgoModal',
      });
    },
    *onEditCrowdByEnLarge({ payload }, { put }) {
      console.log('人群放大传参',JSON.stringify(payload))
      yield put({
        type: 'onUpdateEditCrowdByEnLargeFormData',
        payload,
      });

      yield put({
        type: 'queryCrowdAndUpdateEnLargeFormData',
        payload,
      });

      yield put({
        type: 'onOpenCrowdByEnLargeModal',
      });
    },
    *onEditCrowdByOdps({ payload }, { put }) {
      console.log('odps传参',JSON.stringify(payload))
      yield put({
        type: 'onUpdateEditCrowdByOdpsFormData',
        payload,
      });

      yield put({
        type: 'onOpenCrowdByOdpsModal',
      });
    },
    *onEditCrowdBySql({ payload }, { put }) {
      console.log('sql传参',JSON.stringify(payload))
      yield put({
        type: 'onUpdateEditCrowdBySqlFormData',
        payload,
      });

      yield put({
        type: 'onOpenCrowdBySqlModal',
      });
    },
    *onEditCrowdTransition({ payload }, { put }) {
      yield put({
        type: 'onUpdateEditCrowdTransitionFormData',
        payload,
      });

      yield put({
        type: 'onOpenCrowdTransitionModal',
      });
    },
    *onEditCrowdByFile({ payload }, { put }) {
      console.log('文件上传传参',JSON.stringify(payload))
      yield put({
        type: 'onUpdateEditCrowdByFileFormData',
        payload,
      });

      yield put({
        type: 'onOpenCrowdByFileModal',
      });
    },
    *onEditCrowdByOperate({ payload }, { put }) {
      console.log('人群逻辑传参',JSON.stringify(payload))
      yield put({
        type: 'onUpdateEditCrowdByOperateFormData',
        payload,
      });

      yield put({
        type: 'queryCrowdAndUpdateOperateFormData',
        payload,
      });

      yield put({
        type: 'onOpenCrowdByOperateModal',
      });
    },

    *editUpdateCreator({ payload }, { call, put }) {

      const res = yield call(updateCreator, payload);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success('人群交接成功！')

      yield put({
        type: 'onCrowdTransferModalCancel',
      });

      yield put({
        type: 'queryCrowd',
      });
    },
    *queryBlackWhiteListRecords({ payload }, { call, put }) {
      const res = yield call(queryBlackWhiteListRecords, payload);

      if (!res.success) {
        message.error(res.msg || '查询变更记录失败');
        return [];
      }

      return res.data || [];
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    updateFormCreator(state, { payload }) {
      // const creator = payload ? JSON.parse(payload).empId : ''
      return {
        ...state,
        formData: {
          ...state.formData,
          // ...creator,
        },
      }
    },
    updateFormData(state, { payload }) {
      return {
        ...state,
        formData: {
          ...state.formData,
          ...payload,
        },
      }
    },
    onOpenCrowdTransitionModal(state) {
      return {
        ...state,
        crowdTransitionModalVisible: true,
      };
    },
    onCrowdTransitionModalCancel(state) {
      return {
        ...state,
        crowdTransitionModalVisible: false,
        editCrowdTransitionFormData: {},
      };
    },

    onOpenCrowdByAlgoModal(state) {
      return {
        ...state,
        crowdByAlgoModalVisible: true,
      };
    },
    onCrowdByAlgoModalCancel(state) {
      return {
        ...state,
        crowdByAlgoModalVisible: false,
        editCrowdByAlgoFormData: {},
      };
    },
    onOpenCrowdDelayModal(state) {
      return {
        ...state,
        crowdDelayModalVisible: true,
      };
    },
    onCrowdDelayModalCancel(state) {
      return {
        ...state,
        crowdDelayModalVisible: false,
      };
    },
    onOpenCrowdTestModal(state) {
      return {
        ...state,
        crowdTestModalVisible: true,
      };
    },
    onCrowdTestModalCancel(state) {
      return {
        ...state,
        crowdTestModalVisible: false,
      };
    },
    onUpdateEditCrowdTransitionFormData(state, { payload }) {
      return {
        ...state,
        editCrowdTransitionFormData: {
          ...state.editCrowdTransitionFormData,
          ...payload,
        },
      };
    },
    onUpdateEditCrowdByAlgoFormData(state, { payload }) {
      return {
        ...state,
        editCrowdByAlgoFormData: {
          ...state.editCrowdByAlgoFormData,
          ...payload,
        },
      };
    },
    onUpdateEditCrowdByOperateFormData(state, { payload }) {
      return {
        ...state,
        editCrowdByOperateFormData: {
          ...state.editCrowdByOperateFormData,
          ...payload,
        },
      };
    },
    onUpdateEditCrowdByEnLargeFormData(state, { payload }) {
      return {
        ...state,
        editCrowdByEnLargeFormData: {
          ...state.editCrowdByEnLargeFormData,
          ...payload,
        },
      };
    },
    onUpdateEditCrowdByOdpsFormData(state, { payload }) {
      return {
        ...state,
        editCrowdByOdpsFormData: {
          ...state.editCrowdByOdpsFormData,
          ...payload,
        },
      };
    },
    onUpdateEditCrowdBySqlFormData(state, { payload }) {
      return {
        ...state,
        editCrowdBySqlFormData: {
          ...state.editCrowdBySqlFormData,
          ...payload,
        },
      };
    },
    onUpdateEditCrowdByFileFormData(state, { payload }) {
      return {
        ...state,
        editCrowdByFileFormData: {
          ...state.editCrowdByFileFormData,
          ...payload,
        },
      };
    },
    onOpenCrowdByEnLargeModal(state) {
      return {
        ...state,
        crowdByEnLargeModalVisible: true,
      };
    },
    onCrowdByEnLargeModalCancel(state) {
      return {
        ...state,
        crowdByEnLargeModalVisible: false,
        editCrowdByEnLargeFormData: {},
      };
    },
    onOpenCrowdByOdpsModal(state) {
      return {
        ...state,
        crowdByOdpsModalVisible: true,
      };
    },
    onCrowdByOdpsModalCancel(state) {
      return {
        ...state,
        crowdByOdpsModalVisible: false,
        editCrowdByOdpsFormData: {},
      };
    },
    onOpenCrowdBySqlModal(state) {
      return {
        ...state,
        crowdBySqlModalVisible: true,
      };
    },
    onOpenCrowdByFileModal(state) {
      return {
        ...state,
        crowdByFileModalVisible: true,
      };
    },
    onCrowdBySqlModalCancel(state) {
      return {
        ...state,
        crowdBySqlModalVisible: false,
        editCrowdBySqlFormData: {},
      };
    },
    onCrowdByFileModalCancel(state) {
      return {
        ...state,
        crowdByFileModalVisible: false,
        editCrowdByFileFormData: {},
      };
    },
    onOpenCrowdByOperateModal(state) {
      return {
        ...state,
        crowdByOperateModalVisible: true, // 人群逻辑运算
      };
    },
    onCrowdByOperateModalCancel(state) {
      return {
        ...state,
        crowdByOperateModalVisible: false, // 人群逻辑运算
        editCrowdByOperateFormData: {}, // 人群逻辑运算
      };
    },
    onOpenCrowdExportModal(state, { payload }) {
      return {
        ...state,
        crowdExportModalVisible: true,
        editCrowdExportFormData: payload,
      };
    },
    onCrowdExportModalCancel(state) {
      return {
        ...state,
        crowdExportModalVisible: false,
        editCrowdExportFormData: {},
      };
    },
    onCrowdBlackOrWhiteListModalCancel(state) {
      return {
        ...state,
        BlackOrWhiteListModalVisible: false,
        BlackOrWhiteListFormData: {},
      };
    },
    onCrowdTransferModalCancel(state) {
      return {
        ...state,
        crowdTransferVisible: false,
        crowdTransferFormData: {},
      };
    },
    updateBlackWhiteData(state, {payload}){
      return {
        ...state,
        BlackOrWhiteListFormData: {
          ...state.BlackOrWhiteListFormData,
          ...payload,
        }
      }
    }
  },
};
