import { message } from 'antd';
import { history } from 'umi';
import { crowdDagQuery, queryCrowd } from '@/services/api';
import { getApprovalObj } from '@/pages/GatherPerson/common/utils'
import { approvalApplySceneEnum } from '@/pages/GatherPerson/constants'
import { CROWD_PROGRESS_STATUS } from '@/constants'
import { get } from 'lodash'

export default {
  namespace: 'operation',
  state: {
    loadingTop: false,
    loadingBot: false,
    crowdId:'',
    dagsList:[],
    type: '',
    descriptionArr:{}
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(({ pathname, query }) => {
        if (pathname.includes('/crowd-stategy/operation-gather-person')) {
          if (query.id) {
            dispatch({
              type: 'updateState',
              payload: {
                crowdId: query.id,
                type: query.type
              },
            });
          } else {
            dispatch({
              type: 'updateState',
              payload: {
                dataList:[]
              },
            });
          }

          dispatch({
            type: 'queryCrowd',
            payload: {
              id: query.id,
              type: query.type
            },
          });

          dispatch({
            type: 'query',
            payload: {
              crowdId: query.id,
              count: 3
            },
          });
        }
      });
    },
  },
  effects: {
    *query({ payload }, { put, call }) {
      yield put({
        type: 'updateState',
        payload: {
          loadingBot:true
        },
      });
      const res = yield call(crowdDagQuery,payload);
      if (!res.success) {
        message.error(res.msg)
        return
      }

      let dagsList = res?.data?.dags || []
      const approvalInfo = getApprovalObj(get(res, 'data.approvalInfos', []), approvalApplySceneEnum.CROWD_UPDATE)
      // 如果存在审批，构建一个审批
      if (approvalInfo) {
        const node = {
          nodeDesc: CROWD_PROGRESS_STATUS[approvalInfo?.approvalStatus]?.text,
          nodeName: approvalInfo?.approvalStatus,
          nodeStatus: approvalInfo?.approvalStatus,
          approvalId: approvalInfo?.approvalId,
        }

        if (dagsList?.length) {
          // 如果审批通过，展示原先版本，否则为新版本
          approvalInfo?.approvalStatus === 'APPROVAL_SUCCESS' ? dagsList[0]?.nodes?.unshift(node) : dagsList?.unshift({nodes: [node]})
        } else {
          dagsList.push({
            nodes: [node]
          })
        }
        
      }

      yield put({
        type: 'updateState',
        payload: {
          dagsList,
          loadingBot:false
        },
      });
    },
    *queryCrowd({ payload }, { put, call }) {
      yield put({
        type: 'updateState',
        payload: {
          loadingTop:true
        },
      });
      const res = yield call(queryCrowd, {
        deleted: 0,
        pageNum: 1,
        pageSize: 10,
        bizRegionCode: 'public_region',
        ...payload
      });

      if(res.code === '200' && res.data && res.data.rows && res.data.rows.length > 0) {
        yield put({
          type: 'updateState',
          payload: {
            descriptionArr:res.data.rows[0],
            loadingTop:false
          },
        });
      } else {
        yield put({
          type: 'updateState',
          payload: {
            descriptionArr:{},
            loadingTop:false
          },
        });
        message.error(res.msg)
      }
    }
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
