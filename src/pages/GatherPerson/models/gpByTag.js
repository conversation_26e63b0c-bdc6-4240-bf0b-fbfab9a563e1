import { message } from 'antd';
import { history } from 'umi';
import { queryBuildTree, queryLabel, editCrowdByLabel, queryCrowdById, queryGpByName } from '@/services/api';

export default {
  namespace: 'gpByTag',
  state: {
    treeData: [],
    corwdId: -1,
    editFormData: {},
    loading: false,
    showTagModal: false
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(({ pathname, query }) => {
        if (pathname.includes('/crowd-stategy/crowd-circle/gather-person-by-tag')) {
          if (query.id) {
            dispatch({
              type: 'updateState',
              payload: {
                crowdId: query.id,
              },
            });
          } else {
            dispatch({
              type: 'updateState',
              payload: {
                editFormData: {},
              },
            });
          }

          dispatch({
            type: 'query',
            payload: {
              crowdId: query.id,
            },
          });
        }
      });
    },
  },
  effects: {
    *query({ payload }, { put, all }) {
      const arr = [];

      // 更新时的请求
      if (payload.crowdId && payload.crowdId !== -1) {
        arr.push(
          put.resolve({
            type: 'queryCrowdById',
            payload: {
              crowdId: payload.crowdId,
            },
          })
        );
      }

      yield all(arr);
    },
    *queryTreeData(model, { call, put }) {
      const result = yield call(queryBuildTree, {
        bizEntityName: 'TAOBAO_USER',
      });

      if (!result.success) {
        message.error(result.msg);
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          treeData: (result.data && result.data.root && result.data.root.children) || [],
        },
      });
    },
    *queryCrowdById({ payload }, { put, call }) {
      if (!payload.crowdId || payload.crowdId === -1) {
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const res = yield call(queryCrowdById, {
        id: payload.crowdId,
      });

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          editFormData: res.data || {},
          loading: false,
        },
      });
    },
    *queryLabel({ payload = {} }, { call }) {
      // 删除空值筛选条件
      const res = yield call(queryLabel, {
        deleted: 0,
        bizEntityId: 1,
        pageNum: 1,
        pageSize: 100000,
        ...payload,
      });

      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }

      // eslint-disable-next-line consistent-return
      return {
        success: true,
        data: res.data,
      };
    },
    *editCrowdByLabel({ payload }, { call, select, put }) {
      const data = payload;

      const { editFormData } = yield select(state => state.gpByTag);
      const isUpdate = !!(editFormData && editFormData.id);
      if (isUpdate) {
        data.id = editFormData.id;
      }

      const res = yield call(editCrowdByLabel, data);

      if (!res.success) {
        message.error(res.msg);
        return res;
      }
      if (window.location.href.includes('isCrowd=true') && !isUpdate) {
        window.parent.postMessage({id:res.data}, '*');
      }

      message.success(isUpdate ? '更新成功' : '创建成功');

      yield put({
        type: 'updateState',
        payload: {
          editFormData: {},
        },
      });
      // history.push('/crowd-stategy/crowd-circle');
      yield put({
        type: 'onCrowdByTagModalCancelOrShow',
        payload: false
      })
      yield put({
        type: 'gatherPerson/queryCrowd'
      })

      return res
    },
    *changeUpdate({ payload }, { call, select, put }) {
      const { editFormData } = yield select(state => state.gpByTag);
      yield put({
        type: 'updateState',
        payload: {
          editFormData: {
            ...editFormData,
            needUpdate: payload
          },
        },
      });
    },
    *changeType({ payload }, { call, select, put }) {
      const { editFormData = {} } = yield select(state => state.gpByTag);
      if (!editFormData.extInfo) {
        editFormData.extInfo = {};
      }
      if (!editFormData.extInfo.UPDATE_MODEL) {
        editFormData.extInfo.UPDATE_MODEL = {};
      }
      editFormData.extInfo.UPDATE_MODEL.updateType = payload ? 'dependence' : 'every_day';
      editFormData.extInfo.UPDATE_MODEL.dependenceBizOption = payload ? 'huokebao' : undefined;

      yield put({
        type: 'updateState',
        payload: {
          editFormData: {
            ...editFormData,
            updateType: payload
          },
        },
      });
    },
    *queryGpByName({ payload }, { call }) {
      const res = yield call(queryGpByName, { crowdName: payload.crowdName });
      if (res.success === false) {
        message.error(res.msg || '查询失败');
        return;
      }

      return res.data;
    },

    *queryAccessCrm({ payload }, { call, select, put }) {
      yield put({
        type: 'onCrowdByTagModalCancelOrShow',
        payload: true
      })
      console.log('payload---',payload)
      yield put({
        type: 'updateState',
        payload: {
          editFormData: payload || {},
          loading: false,
        },
      });
      // yield put({
      //   type: 'queryCrowdById',
      //   payload: {
      //     crowdId: payload,
      //   },
      // });
    }
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    updateTagFormState(state, { payload }){
      return {
        ...state,
        editFormData: {
          ...state.editFormData,
          ...payload
        }
      }
    },
    // 标签圈人抽屉显示和隐藏
    onCrowdByTagModalCancelOrShow(state, {payload}){
      return {
        ...state,
        showTagModal: payload
      }
    }
  },
};
