import React, { PureComponent, Fragment } from 'react';
import {
  Card,
  Row,
  Col,
  Table,
  Input,
  Button,
  Divider,
  Popconfirm,
  message,
  Badge,
  Radio,
  Tag,
  Popover,
  Tooltip,
  DatePicker,
  Select,
  Dropdown,
  Menu,
} from 'antd';
import dayjs from 'dayjs';
import { connect } from 'dva';
import { Link }  from 'umi';
import { QuestionCircleOutlined, ClockCircleOutlined, ExportOutlined } from '@ant-design/icons';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { getHtmlParseValue } from '@/utils/createFormItem';
import {
  CROWD_TYPE,
  CROWD_STATUS,
  CROWD_PROGRESS_STATUS,
  CROWD_PROCESS_TIME,
  CROWD_USE_SCENE,
  CROWD_REAL_TIME,
  CROWD_NEED_UPDATE,
  CROWD_USER_CONTROL,
  POPULATION_TYPE
} from '@/constants';
// import SelectUser from '@ali/select-user';
import CreateCrowdByAlgoModal from '@/components/CreateChoosePersonModal';
import CreateCrowdByEnLargeModal from '@/components/CreateEnLargeCrowdModal';
import GetUser from '@/components/GetUser';
import CreateOdpsCrowdModal from '@/components/CreateOdpsCrowdModal';
import CreateTransitionModal from '@/pages/GatherPerson/CreateTransitionModal';
import CreateSqlCrowdModal from '@/components/CreateSqlCrowdModal';
import CrowdExportModal from '@/components/CrowdExportModal';
import CreateOperateCrowdModal from '@/components/CreateOperateCrowdModal';
import CreateFileCrowdModal from '@/components/CreateFileCrowdModal';
import CrowdBlackOrWhiteList from '@/components/CrowdBlackOrWhiteList';
import CreateCrowdModal from '@/components/CreateCrowdModal';
import SearchForm from '@/components/SearchForm';
import CreateCrowdByTagModal from '@/pages/GatherPerson/GpByTag';
import Space from '@/components/Space';
import CrowdDetailModal from './components/CrowdDetailModal';
import CrowdDelayModal from './components/CrowdDelayModal';
import CrowdTestModal from './components/CrowdTestModal';
import AddGroupModel from './components/AddGroupModel';
import CrowdMonitorModel from './components/CrowdMonitorModel'
import CrowdTransferModal from './components/CrowdTransferModal'
import { getCrowdTypeText, approvalApplySceneEnum,  } from './constants'
import { CRMUrl, getUrlParam, zhugeUrl } from '@/utils/utils';
import { onEdit, onCreateCrowd, getApprovalObj, goBpms, goApprovalBpms } from './common/utils'
import styles from './index.less';
import { get } from 'lodash';

// const { Search } = Input;
const RadioGroup = Radio.Group;
const RadioButton = Radio.Button;
const { Option } = Select;
const { RangePicker } = DatePicker;

@connect(state => ({ gatherPerson: state.gatherPerson, user: state.user, gpByTag: state.gpByTag }))
export default class GatherPerson extends PureComponent {
  state = {
    isRealTime: false,
    isOwner: false
  };

  componentDidMount() {
    // 修复antd的Modal和Drawer一起使用时的样式bug
    const {
      dispatch,
      gatherPerson: { algoModels },
    } = this.props;
    document.body.style.overflow = 'auto';
    if (getUrlParam('crowdParams')) {
      dispatch({
        type: 'gpByTag/queryAccessCrm',
        payload: JSON.parse(decodeURIComponent(getUrlParam('crowdParams'))),
      });
    }
    if(getUrlParam('isCrowd')) {
      const receiveMessageFromIframePage = ({
        origin,
        data
      }) => {
        // iframe中点击保存时数据处理
        console.log('data--',data)
        if (data && data.params) {
          // 透出其他圈人给其他平台使用
          /**
           * 其他平台接入诸葛isCrowd 为 true 时，进行postMessage通信
           * url添加isCrowdType=true,弹出其他圈人反之弹出标签圈人
           */
          if (getUrlParam('isCrowdType')) {
            onEdit(data?.params && JSON.parse(data?.params), this.props)
          } else {
            dispatch({
              type: 'gpByTag/queryAccessCrm',
              payload:data?.params && JSON.parse(data?.params),
            });
          }
        }
      };
      window.addEventListener('message', receiveMessageFromIframePage, false);
      return () => {
        window.removeEventListener(
          'message',
          receiveMessageFromIframePage,
          false,
        );
      };
    }
  }
  onRadioChange = e => {
    const {
      dispatch,
      gatherPerson: { pagination },
    } = this.props;
    const { value } = e.target;

    let type = 'primary';
    if (value === '2') {
      type = 'all';
    } else if (value === '3') {
      type = 'channel';
    }

    // 置为第一页
    dispatch({
      type: 'gatherPerson/updateState',
      payload: {
        type,
        pagination: {
          ...pagination,
          current: 1,
        },
      },
    });

    dispatch({
      type: 'gatherPerson/queryCrowd',
      payload: {
        pageNum: 1,
        type,
      },
    });
  };

  onDelete = id => {
    const { dispatch } = this.props;

    dispatch({
      type: 'gatherPerson/delete',
      payload: {
        id,
      },
    });
  };
  reRun = crowdId => {
    const { dispatch } = this.props;
    dispatch({
      type: 'gatherPerson/reRun',
      payload: {
        crowdId,
      },
    });
  };

  handleTableChange = pagination => {
    const {
      dispatch,
      gatherPerson: { pagination: pager },
    } = this.props;

    dispatch({
      type: 'gatherPerson/updateState',
      payload: {
        pagination: {
          ...pager,
          current: pagination.current,
          pageSize: pagination.pageSize,
        },
      },
    });

    dispatch({
      type: 'gatherPerson/queryCrowd',
      payload: {
        pageNum: pagination.current,
      },
    });
  };

  onOpenDetailModal = crowd => {
    const {
      dispatch,
      gatherPerson: { algoModels },
    } = this.props;

    let modelName = '';
    if (crowd && crowd.extInfo && crowd.extInfo.ALGO_MODEL_META_INFO_ID) {
      const [recordModel] = algoModels.filter(
        item => String(item.id) === String(crowd.extInfo.ALGO_MODEL_META_INFO_ID)
      );
      // eslint-disable-next-line prefer-destructuring
      modelName = recordModel.modelName;
    }

    dispatch({
      type: 'gatherPerson/onOpenDetailModal',
      payload: {
        curCrowd: { ...crowd, modelName },
      },
    });
  };

  onDetailModalCancel = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'gatherPerson/updateState',
      payload: {
        detailModalVisible: false,
      },
    });
  };

  refresh = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'gatherPerson/queryCrowdById',
    });
  };

  onSearch = value => {
    const sfd = {};
    const {
      dispatch,
      gatherPerson: { pagination },
    } = this.props;

    if (value) {
      if (/^\d+$/.test(value)) {
        sfd.id = value;
      } else {
        sfd.crowdName = value;
      }
    }

    // 置为第一页
    dispatch({
      type: 'crowdTag/updateState',
      payload: {
        pagination: {
          ...pagination,
          current: 1,
        },
      },
    });

    dispatch({
      type: 'gatherPerson/queryCrowd',
      payload: {
        pageNum: 1,
        ...sfd,
      },
    });
  };

  onSubmit = values => {
    const { dispatch } = this.props;

    dispatch({
      type: 'gatherPerson/editCrowdByAlgo',
      payload: values,
    });
  };

  onOpenCrowdDelayModal = record => {
    const { dispatch } = this.props;
    dispatch({
      type: 'gatherPerson/updateState',
      payload: {
        editCrowdDelayFormData: record,
        crowdDelayModalVisible: true,
      },
    });
  };

  onCrowdDelayModalCancel = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'gatherPerson/onCrowdDelayModalCancel',
    });
  };

  // 提交过期时间
  onSubmitDelayTime = values => {
    const { dispatch } = this.props;

    dispatch({
      type: 'gatherPerson/editCrowdDelayTime',
      payload: values,
    });
  };

  onSubmitEnLarge = values => {
    const { dispatch } = this.props;

    dispatch({
      type: 'gatherPerson/editCrowdByEnLarge',
      payload: values,
    });
  };

  onSubmitTransition = values => {
    const { dispatch } = this.props;

    dispatch({
      type: 'gatherPerson/editCrowdTransition',
      payload: values,
    });
  }

  onCrowdTestModalCancel = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'gatherPerson/onCrowdTestModalCancel',
    });
  };

  onOpenCrowdTestModal = record => {
    const { dispatch } = this.props;

    dispatch({
      type: 'gatherPerson/updateState',
      payload: {
        editCrowdTestFormData: record,
        crowdTestModalVisible: true,
      },
    });
  };

  onCrowdOdpsModalCancel = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'gatherPerson/onCrowdByOdpsModalCancel',
    });
  };

  onSubmitOdps = values => {
    const { dispatch } = this.props;

    dispatch({
      type: 'gatherPerson/editCrowdByOdps',
      payload: values,
    });
  };

  onCrowdSqlModalCancel = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'gatherPerson/onCrowdBySqlModalCancel',
    });
  };

  onCrowdFileModalCancel = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'gatherPerson/onCrowdByFileModalCancel',
    });
  };

  onSubmitSql = values => {
    const { dispatch } = this.props;

    dispatch({
      type: 'gatherPerson/editCrowdBySql',
      payload: values,
    });
  };

  onSubmitFile = values => {
    const { dispatch } = this.props;

    dispatch({
      type: 'gatherPerson/editCrowdByFile',
      payload: values,
    });
  };

  onOpenCrowdExportModal = (record, isOwner) => {
    this.setState({ isRealTime: !!record.realTime, isOwner, });
    const { dispatch } = this.props;
    dispatch({
      type: 'gatherPerson/onOpenCrowdExportModal',
      payload: record,
    });
  };

  onCrowdExportModalClose = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'gatherPerson/onCrowdExportModalCancel',
    });
  };
  // 打开黑白名单弹框
  onOpenCrowdBlackOrWhiteListModal = record => {
    const { dispatch } = this.props;
    const { creator = {}, id } = record;
    dispatch({
      type: 'gatherPerson/updateState',
      payload: {
        BlackOrWhiteListFormData: {
          creator: creator.nickName,
          groupId: id,
          operator: creator.nickName,
        },
        BlackOrWhiteListModalVisible: true,
      },
    });
    dispatch({
      type: 'gatherPerson/queryblackwhitelist',
      payload: { groupId: record.id, groupType: 'TAOBAO_USER' },
    });
  };
  onCrowdBlackOrWhiteListModalClose = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'gatherPerson/onCrowdBlackOrWhiteListModalCancel',
    });
  };

  onSubmitExportCrowd = async (values) => {
    const { dispatch } = this.props;
    const res = await dispatch({
      type: 'gatherPerson/editExportCrowdTask',
      payload: values,
    });

    return res
  };
  onSubmitBlackOrWhiteListCrowd = values => {
    const { dispatch } = this.props;
    dispatch({
      type: 'gatherPerson/editBlackWhiteListCrowdTask',
      payload: values,
    });
  };

  onCrowdOperateModalCancel = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'gatherPerson/onCrowdByOperateModalCancel',
    });
  };

  onSubmitOperate = values => {
    const { dispatch } = this.props;

    dispatch({
      type: 'gatherPerson/editCrowdByOperate',
      payload: values,
    });
  };
  onCreatorChange = value => {
    const { dispatch } = this.props;
    dispatch({
      type: 'gatherPerson/updateFormCreator',
      payload: value,
    });
  };

  //添加实验分组
  onAddGroup = record => {
    const {
      dispatch,
      user: { currentUser },
    } = this.props;
    const { id } = record;
    const operator = {
      empId: currentUser.workId,
      nickName: currentUser.name,
    };
    if (record.crowdBucketInfo) {
      dispatch({
        type: 'gatherPerson/updateState',
        payload: {
          addGroupVisible: true,
          groupModeFormData: {
            parentId: id,
            crowdBucketInfo: record.crowdBucketInfo,
            operator,
          },
        },
      });
    } else {
      dispatch({
        type: 'gatherPerson/updateState',
        payload: {
          addGroupVisible: true,
          groupModeFormData: {
            parentId: id,
            operator,
          },
        },
      });
    }
  };

  //关闭实验分组
  onAddGroupModelCancel = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'gatherPerson/updateState',
      payload: {
        addGroupVisible: false,
      },
    });
  };

  //人群监控弹窗
  onCrowdMonitor = record => {
    const {
      dispatch,
      user: { currentUser },
    } = this.props;
    dispatch({
      type: 'gatherPerson/updateState',
      payload: {
        crowdMonitorFormData: record,
        crowdMonitorVisible: true,
      },
    });
  }


  onCrowdMonitorCancel = (record) => {
    const { dispatch } = this.props;
    dispatch({
      type: 'gatherPerson/updateState',
      payload: {
        crowdMonitorFormData: {},
        crowdMonitorVisible: false,
      },
    });
  };


  //人群转交功能
  onOpenCrowdTransfer = record => {
    const { dispatch } = this.props;
    dispatch({
      type: 'gatherPerson/updateState',
      payload: {
        crowdTransferFormData: record,
        crowdTransferVisible: true,
      },
    });
  };

  onCrowdTransferModalCancel = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'gatherPerson/onCrowdTransferModalCancel',
    });
  };

  onSubmitUpdateCreator = values => {
    const { dispatch } = this.props;
    dispatch({
      type: 'gatherPerson/editUpdateCreator',
      payload: values,
    });
  };

  //刷新
  onRefresh = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'gatherPerson/queryCrowd',
    });
  };

  render() {
    const {
      gatherPerson: {
        crowdByAlgoModalVisible,
        algoModels,
        editCrowdByAlgoFormData,
        pagination,
        loading,
        crowdList,
        detailModalVisible,
        curCrowd,
        refreshLoading,
        editCrowdDelayFormData,
        crowdDelayModalVisible,
        type,
        editCrowdByEnLargeFormData,
        crowdByEnLargeModalVisible,
        editCrowdTestFormData,
        crowdTestModalVisible,
        crowdByOdpsModalVisible,
        editCrowdByOdpsFormData,
        editCrowdBySqlFormData, // sql圈人
        editCrowdByFileFormData, // 文件圈人
        crowdBySqlModalVisible,
        crowdByFileModalVisible, // 文件圈人
        crowdExportModalVisible,
        editCrowdExportFormData,
        // searchValue,
        crowdByOperateModalVisible,
        editCrowdByOperateFormData,
        formData,
        crowdErrMsgMap,
        BlackOrWhiteListModalVisible, // 黑白名单
        BlackOrWhiteListFormData,
        addGroupVisible, //添加实验分组
        groupModeFormData,
        crowdMonitorVisible, //人群监控弹窗
        crowdMonitorFormData,
        crowdTransferFormData,
        crowdTransferVisible,
        editCrowdTransitionFormData,
        crowdTransitionModalVisible,
        crowdCreateVisible,
        crowdCreateFormData
      },
      gpByTag: {
        showTagModal, // 标签圈人
      },
      dispatch,
    } = this.props;
    const { isRealTime, isOwner } = this.state;

    let defaultActiveKey = '1';
    if (type === 'all') {
      defaultActiveKey = '2';
    } else if (type === 'channel') {
      defaultActiveKey = '3';
    }

    const columns = [
      {
        title: '人群ID',
        dataIndex: 'id',
        width: 100,
        align: 'center',
      },
      {
        title: '人群名称',
        dataIndex: 'crowdName',
        width: 150,
        // className: styles.crowdName,
        render: (text, record) => (
          // <Popover content={text} placement="topLeft">
          <a onClick={() => this.onOpenDetailModal(record)}>
            {record.realTime ? (
              <Tooltip title="实时人群">
                <ClockCircleOutlined style={{ color: '#87D068' }} />{' '}
              </Tooltip>
            ) : null}
            {text}
          </a>
          // </Popover>
        ),
      },
      {
        title: '类型 | 数量',
        dataIndex: 'crowdType',
        width: 140,
        render: (text, record) => {
          let showText = CROWD_TYPE[text]
          if ( text === 'LABEL_CROWD') {
            showText = getCrowdTypeText(record?.profileType, CROWD_TYPE[text])
          }
          return (
            <div>
              <div>{showText}</div>
              <div>{record.crowdAmount ? record.crowdAmount : '暂无'}</div>
            </div>
          );
        },
      },
      {
        title: '实验人群包ID',
        dataIndex: 'crowdBucketInfo',
        width: 130,
        render: (text, record) => {
          if (
            record.crowdBucketInfo &&
            record.crowdBucketInfo.childCrowdList &&
            record.crowdBucketInfo.childCrowdList.length > 0
          ) {
            const childCrowdId = record.crowdBucketInfo.childCrowdList.map(ele => {
              return ele.childCrowdId;
            });
            return (
              <Popover content={childCrowdId && childCrowdId.toString()}>
                <span className={styles.bucket}>{childCrowdId && childCrowdId.toString()}</span>
              </Popover>
            );
          } else if (record.crowdBucketInfo && record.crowdBucketInfo.parentId) {
            return <div>父人群：{record.crowdBucketInfo.parentId}</div>;
          } else {
            return <div>暂无</div>;
          }
        },
      },
      // {
      //   title: '数量',
      //   dataIndex: 'crowdAmount',
      //   width: 150,
      //   render: text => (text === -1 || !text ? '暂无' : text),
      // },
      {
        title: '状态',
        dataIndex: 'crowdStatus',
        width: 100,
        render: (crowdStatus, record) => {
          const obj = CROWD_PROGRESS_STATUS[crowdStatus];
          const errMsg = get(record, 'extInfo.crowdBuildRecord', null)
            ? get(record, 'extInfo.crowdBuildRecord.errMsg', null)
            : null;
          const approvalObj = CROWD_PROGRESS_STATUS[getApprovalObj(get(record, 'approvalInfos', []), approvalApplySceneEnum.CROWD_UPDATE)?.approvalStatus] || undefined;
          return (
            <>
              <span style={{display:'flex',alignItems:'center'}}>
                {/* 只有失败状态才有提示文案 */}
                {/* 状态为错误的时候先拿 extInfo.crowdBuildRecord.errMsg ，如果为空，再用现有的提示文案 */}
                {crowdStatus === 'ERROR' && obj ? (
                  <div>
                    <Tooltip
                      title={
                        errMsg ||
                        (record.errorCode
                          ? crowdErrMsgMap[record.errorCode]
                          : `预计运行耗时: ${CROWD_PROCESS_TIME[record.crowdType]}`)
                      }
                    >
                      <Tag color={obj.color}>{obj.text}</Tag>
                    </Tooltip>
                    <div style={{color:'red',cursor:'pointer'}} onClick={() => window.open('https://yuque.antfin.com/qnwrq9/kzl68s/bpqsue#%20')}>错误自查</div>
                  </div>
                ) : (
                  <Tag color={obj.color} onClick={()=> { goBpms(record) }}>{obj.text}</Tag>
                )}
                <Link to={`/crowd-stategy/operation-gather-person?id=${record.id}&type=${type}`}>
                  <ExportOutlined />
                </Link>
              </span>
              { approvalObj && <div style={{ marginTop: 10 }}>
                <Tag color={approvalObj?.color} onClick={() => { goApprovalBpms(record) }} style={{ cursor: 'pointer' }}>{approvalObj.text}</Tag>
              </div>}
            </>
          );
        },
      },
      {
        title: '上次构建时间',
        dataIndex: 'gmtLastBuilt',
        width: 136,
        render: text => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '暂无'),
      },
      {
        title: '过期时间',
        dataIndex: 'expiredDate',
        width: 120,
        render: (text, record) => {
          const isExpired = Date.now() > text;
          return isExpired ? (
            <Badge status="error" text="已过期" />
          ) : (
            dayjs(text).format('YYYY-MM-DD HH:mm:ss')
          );
        },
      },
      {
        title: '创建人 | 数据日期',
        dataIndex: 'creator',
        width: 160,
        render: (creator, record) => {
          return (
            <div>
              <div>{!!creator && creator.nickName}</div>
              <div>{record.bizDate || '暂无数据'}</div>
            </div>
          );
        },
      },
      // {
      //   title: '数据日期',
      //   dataIndex: 'bizDate',
      //   align: 'center',
      //   render: text => text || '暂无数据'
      // },
      {
        title: '操作',
        width: 144,
        fixed: 'right',
        render: (text, record) => {
          const { creator = {}, operator = [] } = record;
          const owners = [...operator, creator];

          const {
            user: { currentUser, isSuperAdmin },
          } = this.props;
  
          const isGranted = owners.map(o => o.nickName).includes(currentUser.name);
          
          // 子人群禁止编辑
          const isBucketChild = record && record.crowdType === "BUCKET_CHILD"

          return (
            <span>
              <a
                disabled={!isGranted}
                type="link"
                onClick={() => this.onOpenCrowdDelayModal(record)}
              >
                延期
              </a>
              <Divider type="vertical" style={{ marginLeft: '4px', marginRight: '4px' }} />
              <a disabled={!isGranted && isBucketChild} type="link" onClick={() => onEdit(record, this.props)}>
                编辑
              </a>
              <Divider type="vertical" style={{ marginLeft: '4px', marginRight: '4px' }} />
              <Dropdown overlay={menu(record, isGranted, isSuperAdmin)} placement="bottomCenter">
                <a>更多</a>
              </Dropdown>
            </span>
          );
        },
      },
    ];
    // 操作更多选项
    const menu = (record, isGranted , isSuperAdmin) => (
      <Menu>
        <Menu.Item>
          <a type="link" onClick={() => this.onOpenCrowdTestModal(record)}>
            测试
          </a>
        </Menu.Item>
        <Menu.Item>
          {/* 0为非实时 大于0为实时 */}
          {!record.supportExport && (
            <div>
              <a type="link" disabled={true}>
                导出
              </a>
              <Tooltip title="使用支持push实时标签的实时人群才支持导出">
                <QuestionCircleOutlined />
              </Tooltip>
            </div>
          )}
          {!!record.supportExport && (
            // 设备人群圈选暂时不支持导出
            <a type="link" onClick={() => this.onOpenCrowdExportModal(record,isGranted)} disabled={(!isGranted && !isSuperAdmin) || ['DEVICE'].includes(record.profileType)}>
              导出
              {record.exported ? (
                <Tooltip title={<span>已导出</span>}>
                  <QuestionCircleOutlined />
                </Tooltip>
              ) : null}
            </a>
          )}
        </Menu.Item>
        <Menu.Item>
          <div>
            <a
              type="link"
              disabled={
                // 设备人群圈选暂时不支持黑白名单
                (!isGranted && !isSuperAdmin) || !(record.crowdApplyScene && record.crowdApplyScene.includes('MATCH')) || ['DEVICE'].includes(record.profileType)
              }
              style={
                isGranted && record.crowdApplyScene.includes('MATCH') ? { color: '#666' } : null
              }
              onClick={() => this.onOpenCrowdBlackOrWhiteListModal(record)}
            >
              黑白名单
            </a>
            <Tooltip title="人群匹配才能配置黑白名单">
              {!record.crowdApplyScene.includes('MATCH') && <QuestionCircleOutlined />}
            </Tooltip>
          </div>
        </Menu.Item>
        <Menu.Item>
          <Popconfirm
            disabled={!isGranted && !isSuperAdmin}
            title="确定删除该人群吗？"
            onConfirm={() => this.onDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <a disabled={!isGranted}>删除</a>
          </Popconfirm>
        </Menu.Item>
        <Menu.Item>
          <Popconfirm
            title="会覆盖已有人群数据，请知悉"
            onConfirm={() => this.reRun(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <a disabled={!isGranted && !isSuperAdmin}>重跑</a>
          </Popconfirm>
        </Menu.Item>
        <Menu.Item>
          <a
            onClick={() => {
              this.onCrowdMonitor(record);
            }}
            disabled={!isGranted && !isSuperAdmin}
          >人群监控</a>
        </Menu.Item>
        {/* {(record.crowdType === 'LABEL_CROWD' && record.realTime !== 0) || record.crowdType === 'ALGO_CROWD' ||  */}
        {record.crowdType === 'ALGO_CROWD' || 
        record.crowdType === 'TRIP_GALAXY_CROWD' ||
        record.crowdType === 'FILE_CROWD' ||
        record.crowdType === 'ALGO_ENLARGE_CROWD' ? null : (
          <Menu.Item>
            <a
              onClick={() => {
                this.onAddGroup(record);
              }}
              disabled={(!isGranted && !isSuperAdmin) || (record.crowdBucketInfo && record.crowdBucketInfo.parentId)}
            >
              实验分组
            </a>
          </Menu.Item>
        )}
        <Menu.Item>
          <a
            onClick={() => {
              this.onOpenCrowdTransfer(record);
            }}
            disabled={!isGranted && !isSuperAdmin}
          >转交人群</a>
        </Menu.Item>
      </Menu>
    );
    const table = (
      <Table
        rowSelection={
          window.location.href.includes('needRadio=true')
            ? {
                type: 'radio',
                onChange: (selectedRowKeys, selectedRows) => {
                  const obj = { id: selectedRowKeys[0], name: selectedRows[0].crowdName };
                  window.parent.postMessage(obj, '*');
                },
              }
            : null
        }
        scroll={{ x: 1300 }}
        rowKey={record => record.id}
        loading={loading}
        dataSource={crowdList}
        columns={columns}
        pagination={pagination}
        onChange={this.handleTableChange}
      />
    );

    const btns = (
      <div className={styles.crowdBtns}>
        {/* 暂时隐藏放大圈人入口 */}
        {/* <Button
          className={styles.crowdBtn}
          type="primary"
          onClick={() => {
            dispatch({
              type: 'gatherPerson/onOpenCrowdByEnLargeModal',
            });
          }}
        >
          放大圈人
        </Button> */}
        <Button
          className={styles.crowdBtn}
          type="primary"
          onClick={() => {
            dispatch({
              type: 'gatherPerson/updateState',
              payload: {
                crowdCreateVisible: true
              }
            });
          }}
        >
          创建人群
        </Button>
      </div>
    );
    const extraContent = (
      <Fragment>
        {/* <Search
          placeholder="请输入人群名称/ID"
          defaultValue={searchValue}
          onSearch={this.onSearch}
          style={{ width: 200 }}
        /> */}
        <Link to="/develop-tool/crowd-tag">
          <Button type="link">标签列表</Button>
        </Link>
        <Button type="link" disabled>
          用户画像工具
        </Button>
      </Fragment>
    );

    const creatorObj = [];

    // 占位置，使查询按钮靠右边
    let seat = [{},{}]

    if (type === 'all') {
      creatorObj.push({
        label: '创建人',
        target: (
          <GetUser
            placeholder="选择输入创建人"
            // mode="tags"
            onChange={this.onCreatorChange}
          />
        ),
        name: 'creatorObj',
      });
      seat = [{}]
    }

    const form = {
      searchForm: formData,
      setting: [
        {
          label: '圈人ID',
          target: <Input allowClear placeholder="选输入圈人ID" />,
          name: 'id',
        },
        {
          label: '圈人名称',
          target: <Input allowClear placeholder="选输入圈人名称" />,
          name: 'crowdName',
        },
        ...creatorObj,
        // {
        //   label: '创建人',
        //   target: <Input allowClear placeholder="选输入创建人" />,
        //   name: 'creator',
        // },
        {
          label: '创建时间',
          target: <RangePicker placeholder="选择时间" style={{ width: '100%' }} />,
          name: 'dateRange',
        },
        // {
        //   label: '开始时间',
        //   target: <DatePicker placeholder="选输入开始时间" />,
        //   name: 'createBegin',
        // },
        // {
        //   label: '结束时间',
        //   target: <DatePicker placeholder="选输入结束时间" />,
        //   name: 'createEnd',
        // },
        {
          label: '圈人方式',
          target: (
            <Select placeholder="请选择圈人方式">
              {Object.keys(CROWD_TYPE).map(item => (
                <Option key={item} value={item}>
                  {CROWD_TYPE[item]}
                </Option>
              ))}
            </Select>
          ),
          name: 'crowdType',
        },
        {
          label: '使用场景',
          target: (
            <Select placeholder="请选择使用场景">
              {Object.keys(CROWD_USE_SCENE).map(item => (
                <Option key={item} value={item}>
                  {CROWD_USE_SCENE[item]}
                </Option>
              ))}
            </Select>
          ),
          name: 'crowdApplyScene',
        },
        {
          label: '标签时效',
          target: (
            <Select placeholder="请选择标签时效">
              {Object.keys(CROWD_REAL_TIME).map(item => (
                <Option key={item} value={item}>
                  {CROWD_REAL_TIME[item]}
                </Option>
              ))}
            </Select>
          ),
          name: 'realTime',
        },
        {
          label: '是否更新',
          target: (
            <Select placeholder="请选择是否更新">
              {Object.keys(CROWD_NEED_UPDATE).map(item => (
                <Option key={item} value={item}>
                  {CROWD_NEED_UPDATE[item]}
                </Option>
              ))}
            </Select>
          ),
          name: 'needUpdate',
        },
        // {
        //   label: '是否黑白名单',
        //   target: <Select placeholder="请选择是否黑白名单">
        //     {Object.keys(CROWD_USER_CONTROL).map(item => (
        //       <Option key={item} value={item}>
        //         {CROWD_USER_CONTROL[item]}
        //       </Option>
        //     ))}
        //   </Select>,
        //   name: 'userControl',
        // },
        {
          label: '状态',
          target: (
            <Select placeholder="请选择人群状态">
              {Object.keys(CROWD_STATUS).map(item => (
                <Option key={item} value={item}>
                  {CROWD_STATUS[item]}
                </Option>
              ))}
            </Select>
          ),
          name: 'crowdStatus',
        },
        {
          label: '人群类型',
          target: (
            <Select placeholder="请选择人群类型">
              {Object.keys(POPULATION_TYPE).map(item => (
                <Option key={item} value={item}>
                  {POPULATION_TYPE[item]}
                </Option>
              ))}
            </Select>
          ),
          name: 'profileType',
        },
        // 用作占位
        ...seat
      ],
      onValuesChange: values => {
        dispatch({
          type: 'gatherPerson/updateFormData',
          payload: values,
        });
      },
      handleSubmit: values => {
        dispatch({
          type: 'gatherPerson/queryCrowd',
          payload: values,
        });
      },
    };

    return (
      <PageHeaderWrapper title="圈人任务">
        <Card bordered={false} bodyStyle={{ paddingTop: 0 }}>
          {/* 搜索 */}
          <SearchForm key="searchForm" {...form} />
          <div
            style={{
              height: '12px',
              backgroundColor: '#f2f3f7',
              marginLeft: '-24px',
              marginRight: '-24px',
            }}
          ></div>
          <Space distance={15} />
          {/* 人群列表 */}
          <Row gutter={24} gutter={[8, 0]}>
            <Col span={6}>
              <RadioGroup value={defaultActiveKey} onChange={this.onRadioChange}>
                <RadioButton value="1">我的人群</RadioButton>
                <RadioButton value="2">全部人群</RadioButton>
              </RadioGroup>
            </Col>
            <Col span={18} style={{ textAlign: 'right' }}>
              {/* {extraContent} */}
              {btns}
            </Col>
          </Row>
          {table}
        </Card>

        {/* 创建人群 */}
        <CreateCrowdModal
          editModalVisible={crowdCreateVisible}
          onSubmit={(profileType, crowdType) => onCreateCrowd(profileType, crowdType, this.props)}
          onCancel={() => {
            dispatch({
              type: 'gatherPerson/updateState',
              payload: { crowdCreateVisible: false, crowdCreateFormData: {} },
            });
          }}
        />

        {/* 标签圈人 */}
        <CreateCrowdByTagModal
          editModalVisible={showTagModal}
          onCancel={() => {
            if(getUrlParam('crowdParams')) {
              //画像分享url跳转
              history.replaceState(null,'',`${zhugeUrl}/crowd-stategy/crowd-circle`)
            }
            dispatch({
              type: 'gpByTag/onCrowdByTagModalCancelOrShow',
              payload: false,
            });
            dispatch({
              type: 'gpByTag/updateState',
              payload: { editFormData: {} },
            });
          }}
        />

        {/** 跃迁圈人*/}
        <CreateTransitionModal 
          editModalVisible= {crowdTransitionModalVisible}
          editFormData={editCrowdTransitionFormData}
          onSubmit={this.onSubmitTransition}
          onCancel={() => {
            dispatch({
              type: 'gatherPerson/onCrowdTransitionModalCancel',
            })
          }}
        />


        {/**
         * 算法圈人
         */}
        <CreateCrowdByAlgoModal
          algoModels={algoModels}
          editFormData={editCrowdByAlgoFormData}
          editModalVisible={crowdByAlgoModalVisible}
          onSubmit={this.onSubmit}
          onCancel={() =>
            dispatch({
              type: 'gatherPerson/onCrowdByAlgoModalCancel',
            })
          }
        />
        {/**
         * 人群放大
         */}
        <CreateCrowdByEnLargeModal
          algoModels={algoModels}
          editFormData={editCrowdByEnLargeFormData}
          editModalVisible={crowdByEnLargeModalVisible}
          onSubmit={this.onSubmitEnLarge}
          onCancel={() =>
            dispatch({
              type: 'gatherPerson/onCrowdByEnLargeModalCancel',
            })
          }
        />

        {/* odps圈人 */}
        <CreateOdpsCrowdModal
          editModalVisible={crowdByOdpsModalVisible}
          editFormData={editCrowdByOdpsFormData}
          onSubmit={this.onSubmitOdps}
          onCancel={this.onCrowdOdpsModalCancel}
        />

        {/**
         * sql圈人
         */}
        <CreateSqlCrowdModal
          editModalVisible={crowdBySqlModalVisible}
          editFormData={editCrowdBySqlFormData}
          onSubmit={this.onSubmitSql}
          onCancel={this.onCrowdSqlModalCancel}
        />

        {/**
         * 文件圈人
         */}
        <CreateFileCrowdModal
          editModalVisible={crowdByFileModalVisible}
          editFormData={editCrowdByFileFormData}
          onSubmit={this.onSubmitFile}
          onCancel={this.onCrowdFileModalCancel}
        />

        {curCrowd && (
          <CrowdDetailModal
            curCrowd={curCrowd}
            visible={detailModalVisible}
            onCancel={this.onDetailModalCancel}
            refresh={this.refresh}
            loading={refreshLoading}
            crowdErrMsgMap={crowdErrMsgMap}
          />
        )}

        {/**
         * 延时
         */}
        <CrowdDelayModal
          editFormData={editCrowdDelayFormData}
          editModalVisible={crowdDelayModalVisible}
          onCancel={this.onCrowdDelayModalCancel}
          onSubmit={this.onSubmitDelayTime}
        />
        {/* 测试 */}
        <CrowdTestModal
          editModalVisible={crowdTestModalVisible}
          editFormData={editCrowdTestFormData}
          onCancel={this.onCrowdTestModalCancel}
        />

        {/** 导出 */}
        <CrowdExportModal
          isRealTime={isRealTime}
          isOwner={isOwner}
          editCrowdExportFormData={editCrowdExportFormData}
          editModalVisible={crowdExportModalVisible}
          onCancel={this.onCrowdExportModalClose}
          onSubmit={this.onSubmitExportCrowd}
        />
        {/* 黑白名单 */}
        <CrowdBlackOrWhiteList
          BlackOrWhiteListFormData={BlackOrWhiteListFormData}
          editModalVisible={BlackOrWhiteListModalVisible}
          onCancel={this.onCrowdBlackOrWhiteListModalClose}
          onSubmit={this.onSubmitBlackOrWhiteListCrowd}
        />
        {/** 人群逻辑运算 */}
        <CreateOperateCrowdModal
          editModalVisible={crowdByOperateModalVisible}
          editFormData={editCrowdByOperateFormData}
          onCancel={this.onCrowdOperateModalCancel}
          onSubmit={this.onSubmitOperate}
        />
        <AddGroupModel
          addGroupVisible={addGroupVisible}
          onCancel={this.onAddGroupModelCancel}
          onRefresh={this.onRefresh}
          groupModeFormData={groupModeFormData}
        />
        {/** 配置人群监控 */}
         <CrowdMonitorModel
          crowdMonitorVisible={crowdMonitorVisible}
          onCancel={this.onCrowdMonitorCancel}
          onRefresh={this.onRefresh}
          crowdMonitorFormData={crowdMonitorFormData}
         />

        {/** 转交人群 */}
        <CrowdTransferModal
          editCrowdTransferFormData={crowdTransferFormData}
          editModalVisible={crowdTransferVisible}
          onCancel={this.onCrowdTransferModalCancel}
          onSubmit={this.onSubmitUpdateCreator}
        />

      </PageHeaderWrapper>
    );
  }
}
