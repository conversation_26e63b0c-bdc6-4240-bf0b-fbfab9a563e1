import React from 'react';
import dayjs from 'dayjs';
import { Link }  from 'umi';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Card, Tag, Steps, Row, Col, <PERSON><PERSON>, Drawer, Tooltip } from 'antd';
import { QuestionCircleOutlined, ExportOutlined } from '@ant-design/icons';
import { transform } from '@/utils/crowd';
import { CROWD_USE_SCENE, CROWD_PROGRESS_STATUS } from '@/constants';
import { getApprovalObj } from '@/pages/GatherPerson/common/utils'
import { approvalApplySceneEnum } from '@/pages/GatherPerson/constants'
import { getBpmsUrl } from '@/utils/utils';
import TagGroups from '../TagGroups';
import TransCrowd from '../../CreateTransitionModal/components/TransCrowd';
import { get } from 'lodash';
import styles from './index.less';

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
const { Step } = Steps;
const FormItem = Form.Item;
const errorCodeLink = 'https://yuque.antfin-inc.com/fbrain/zhuge-doc/lxf1lr';

class CrowdModal extends React.PureComponent {
  refresh = () => {
    const { refresh = () => { } } = this.props;

    refresh();
  };

  approvalStepTitle = (status, approvalId) => {
    const { text } = CROWD_PROGRESS_STATUS[status];
    // 审批中、被拒绝时展示
    if (['APPROVAL_RUNNING', 'APPROVAL_REJECTED']?.includes(status)) {
      return <span>
        {text}  
        <Link style={{marginLeft: 10}} onClick={() => {window.open(getBpmsUrl(approvalId))}}>
          <ExportOutlined />
        </Link> 
      </span>
    }

    return text
  }

  walker = curCrowd => {
    const {
      crowdType,
      crowdName,
      crowdAmount,
      operator,
      gmtCreate,
      gmtModified,
      crowdApplyScene,
      crowdTags,
      progress,
      conditions,
      taskName,
      modelName,
      errorCode,
      creator,
      gmtLastBuilt,
      seedCrowdID,
      seedCrowdName,
      exceptCrowdName,
      tableName,
      uid,
      partition,
      where,
      sql,
      crowdLeftName,
      crowdLeftId,
      crowdRightName,
      crowdRightId,
      operateCrowdType,
      extInfo,
      bizDate,
      approvalInfos,
    } = curCrowd;
    const { loading, crowdErrMsgMap } = this.props;
    const approvalObj = getApprovalObj(approvalInfos || [], approvalApplySceneEnum.CROWD_UPDATE)
    const approvalStatus = approvalObj?.approvalStatus
    const approvalId = approvalObj?.approvalId
    let content;

    const pg = progress && Object.entries(progress);
    const common = (
      <>
        <FormItem label="人群名称">{crowdName}</FormItem>
        <FormItem label="用户数量">
          {crowdAmount === -1 || !crowdAmount ? '暂无' : crowdAmount}
        </FormItem>
        <FormItem label="管理员">
          {operator && operator.length > 0 && (
            <span>
              {operator.map(u => (
                <Tag color="green" key={u.empId}>
                  {u.nickName}
                </Tag>
              ))}
            </span>
          )}
        </FormItem>
        <FormItem label="创建人">
          {creator ? (
            <span>
              <Tag color="green">{creator.nickName}</Tag>
            </span>
          ) : (
            '无'
          )}
        </FormItem>
        <FormItem label="创建时间">
          {gmtCreate ? dayjs(gmtCreate).format('YYYY-MM-DD HH:mm:ss') : '暂无'}
        </FormItem>
        <FormItem label="更新时间">
          {gmtModified ? dayjs(gmtModified).format('YYYY-MM-DD HH:mm:ss') : '暂无'}
        </FormItem>
        <FormItem label="数据日期">
          {bizDate || '暂无数据'}
        </FormItem>
        <FormItem label="上次构建时间">
          {gmtLastBuilt ? dayjs(gmtLastBuilt).format('YYYY-MM-DD HH:mm:ss') : '暂无'}
        </FormItem>
        <FormItem label="人群使用场景">
          {crowdApplyScene &&
            crowdApplyScene.length > 0 &&
            crowdApplyScene.map(s => (
              <Tag color="orange" key={s}>
                {CROWD_USE_SCENE[s]}
              </Tag>
            ))}
        </FormItem>
        {crowdTags && crowdTags.length > 0 && (
          <FormItem label="涉及资损">{crowdTags.includes('LOSS_OF_ASSETS') ? '是' : '否'}</FormItem>
        )}
        <FormItem
          label="人群状态"
          extra={
            <Button type="primary" loading={loading} onClick={this.refresh}>
              进度刷新
            </Button>
          }
        >
          {pg && pg.length > 0 ? (
            <Row gutter={24}>
              {pg.map(p => {
                const [scene, info] = p;
                const { status } = info;
                const { index, text } = CROWD_PROGRESS_STATUS[status];
                return (
                  <Col span={24 / pg.length} key={scene}>
                    <div>{CROWD_USE_SCENE[scene]}</div>
                    <Steps direction="vertical" current={this.stepCurrent(approvalStatus, index)}>
                      {
                        this.approvalStep(approvalStatus, approvalId)
                      }
                      <Step title="初始化" status={this.initStepStatus(approvalStatus, status)} />
                      <Step
                        title="创建中"
                        status={
                          // eslint-disable-next-line no-nested-ternary
                          status === 'RUNNING' ? 'process' : status === 'INIT' ? 'wait' : 'finish'
                        }
                      />
                      {status === 'ERROR' ? (
                        <Step title="创建失败" status="error" />
                      ) : (
                        <Step title="创建完成" status={status !== 'SUCCESS' ? 'wait' : 'finish'} />
                      )}
                    </Steps>
                  </Col>
                );
              })}
            </Row>
          ) : (
            '暂无'
          )}
        </FormItem>
        {errorCode && (
          <FormItem label="错误码" extra={<a href={errorCodeLink} target="_blank">查看错误码分类</a>}>
            {errorCode}<Tooltip title={<span>{crowdErrMsgMap[errorCode]}</span>}><QuestionCircleOutlined /></Tooltip>
          </FormItem>
        )}
      </>
    );

    switch (crowdType) {
      case 'LABEL_CROWD':
        content = (
          <Form {...FORM_ITEM_LAYOUT} className={styles.form}>
            {common}
            <FormItem
              label="人群逻辑"
              labelCol={{
                xs: { span: 24 },
                sm: { span: 6 },
              }}
              wrapperCol={{
                xs: { span: 24 },
                sm: { span: 18 },
              }}
            >
              <TagGroups tagGroups={transform(conditions)} />
            </FormItem>
          </Form>
        );
        break;
      case 'ALGO_CROWD':
        content = (
          <Form {...FORM_ITEM_LAYOUT}>
            {common}
            <FormItem label="人群逻辑">
              <div>
                <span>关联选品任务：</span>
                <span>
                  <Tag color="volcano">{taskName}</Tag>
                </span>
              </div>
              <div>
                <span>算法模型：</span>
                <span>
                  <Tag color="volcano">{modelName}</Tag>
                </span>
              </div>
            </FormItem>
          </Form>
        );

        break;
      case 'TRIP_GALAXY_CROWD':
        content = (
          <Form {...FORM_ITEM_LAYOUT}>
            {common}
            <FormItem label="人群逻辑">
              <div>
                <span>关联选品池：</span>
                <span>
                  <Tag color="volcano">{taskName}</Tag>
                </span>
              </div>
              <div>
                <span>算法模型：</span>
                <span>
                  <Tag color="volcano">{modelName}</Tag>
                </span>
              </div>
            </FormItem>
          </Form>
        );
        break;
      case 'ALGO_ENLARGE_CROWD':
        content = (
          <Form {...FORM_ITEM_LAYOUT}>
            {common}
            <FormItem label="种子人群">
              {seedCrowdName ? <div><Tag color="volcano">{seedCrowdName}-{seedCrowdID}</Tag>  <Link to={`/crowd-stategy/crowd-circle?crowdId=${seedCrowdID}`} target="_blank">去查看</Link></div> : '暂无'}
            </FormItem>
            <FormItem label="过滤人群">
              {exceptCrowdName ? <Tag color="volcano">{exceptCrowdName}</Tag> : '暂无'}
            </FormItem>
            <FormItem label="人群逻辑">
              <div>
                <span>算法模型：</span>
                <span>
                  <Tag color="volcano">{modelName}</Tag>
                </span>
              </div>
            </FormItem>
          </Form>
        );
        break;
      case 'ODPS_TABLE_CROWD':
        content = (
          <Form {...FORM_ITEM_LAYOUT}>
            {common}
            <FormItem label="ODPS表名">{tableName || '暂无'}</FormItem>
            <FormItem label="用户id字段">{uid || '暂无'}</FormItem>
            <FormItem label="分区名">{partition || '暂无'}</FormItem>
            <FormItem label="where条件">{where || '暂无'}</FormItem>
          </Form>
        );
        break;
      case 'ODPS_SQL_CROWD':
        content = (
          <Form {...FORM_ITEM_LAYOUT}>
            {common}
            <FormItem label="SQL语句">{sql}</FormItem>
          </Form>
        );
        break;
      case 'OPERATE_CROWD':
        content = (
          <Form {...FORM_ITEM_LAYOUT}>
            {common}
            <FormItem label="人群1">
              {crowdLeftName ? <div><Tag color="volcano">{crowdLeftId}</Tag><Link to={`/crowd-stategy/crowd-circle?crowdId=${crowdLeftId}`} target="_blank">去查看</Link></div> : '暂无'}
            </FormItem>
            <FormItem label="人群2">
              {crowdRightName ? <div><Tag color="volcano">{crowdRightName}</Tag><Link to={`/crowd-stategy/crowd-circle?crowdId=${crowdRightId}`} target="_blank">去查看</Link></div> : '暂无'}
            </FormItem>
            <FormItem label="逻辑关系">{operateCrowdType}</FormItem>
          </Form>
        );
        break;
      case 'FILE_CROWD':
        content = (
          <Form {...FORM_ITEM_LAYOUT}>
            {common}
            <FormItem label="用户ID文件名">
              {(extInfo && extInfo.UPLOAD_FILE_NAME || '').replace(/.*\//g, '') || '暂无'}
            </FormItem>
          </Form>
        );
        break;
      case 'TRANSITION_CROWD':
        content = (
          <Form {...FORM_ITEM_LAYOUT} className={styles.form}>
            {common}
            <FormItem label={extInfo.transitionDirection === 'IN' ? '迁入人群' : '迁出人群'}>
              <TransCrowd value={extInfo.mainNode} isDisabled={true} />
            </FormItem>
            <FormItem label={extInfo.transitionDirection === 'IN' ? '迁出人群' : '迁入人群'}>
              <TransCrowd value={extInfo.subNode} isDisabled={true} />
            </FormItem>
          </Form>
        );
        break;
      default:
        content = '';
        break;
    }

    return content;
  };

  stepCurrent = (approvalStatus, index) => {
    if (approvalStatus) {
      // 有审批状态，返回0
      if (approvalStatus !== "APPROVAL_SUCCESS") {
        return 0
      }
      return index + 1
    }
    return index
  }

  // 审批步骤
  approvalStep = (approvalStatus, approvalId) => {
    if (!approvalStatus) return
    return ['APPROVAL_REJECTED', 'APPROVAL_ERROR'].includes(approvalStatus) ? (
      <Step title={this.approvalStepTitle(approvalStatus, approvalId)} status="error" />
    ) : (
      <Step title={this.approvalStepTitle(approvalStatus, approvalId)} status={['APPROVAL_RUNNING', 'APPROVAL_REJECTED', 'APPROVAL_CANCELLED'].includes(approvalStatus) ? 'process' : 'finish'} />
    )
  }

  initStepStatus = (approvalStatus, status) => {
    if ((!approvalStatus && status === 'INIT') || (approvalStatus && approvalStatus === "APPROVAL_SUCCESS")) {
      return 'process'
    }

    if (approvalStatus && approvalStatus !== "APPROVAL_SUCCESS") {
      return 'wait'
    }

    return 'finish'
  }

  render() {

    const { visible, onCancel, curCrowd = {} } = this.props;
    const { crowdName } = curCrowd;
    return (
      <Drawer title={crowdName} visible={visible} onClose={onCancel} width={800} destroyOnClose={true}>
        <Card className={styles.container} bordered={false}>
          {this.walker(curCrowd)}
        </Card>
      </Drawer>
    );
  }
}

export default CrowdModal;
