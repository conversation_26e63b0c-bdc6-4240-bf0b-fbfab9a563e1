import React, { Fragment } from 'react';
import { Radio, Tag } from 'antd';
import styles from './index.less';
import { setTagShow } from '@/pages/GatherPerson/GpByTag/common/utils'

const getCode = (index1, index2, len) => {
  return len === 1
    ? String.fromCharCode(65 + index1)
    : `${String.fromCharCode(65 + index1)}${index2 + 1}`;
};

const getExpression = tagGroups => {
  let expression = '';
  tagGroups.forEach((tg, index, arr) => {
    const { selectedTags, operator } = tg;

    if (index > 0) {
      expression += ` ${operator} `;
    }

    if (selectedTags && selectedTags.length > 1) {
      if (arr.length > 1) {
        expression += '( ';
      }

      selectedTags.forEach((st, i) => {
        if (i > 0) {
          expression += ` ${st.operator} `;
        }
        expression += getCode(index, i, selectedTags.length);
      });

      if (arr.length > 1) {
        expression += ' )';
      }
    } else {
      expression += String.fromCharCode(65 + index);
    }
  });

  return expression;
};

const RadioGroup = Radio.Group;
const RadioButton = Radio.Button;
const TagGroups = ({ tagGroups = [] }) => {
  const expression = getExpression(tagGroups);

  return (
    <div className={styles.container}>
      <div className={styles.wrapper}>
        <div className={styles.expression}>{expression}</div>
      </div>
      {tagGroups &&
        tagGroups.length > 0 &&
        tagGroups.map((t, i) => {
          const { id: groupId, selectedTags, operator } = t;
          // 确保每一个标签点击重新渲染，key值取人群id + index的形式
          const crowd_id = selectedTags.length && selectedTags[0].editFormData ? selectedTags[0].editFormData.crowd_id || new Date() : '';
          return (
            <Fragment key={`tagGroup_${crowd_id}_${i}`}>
              {i !== 0 && (
                <div style={{ width: '100%', textAlign: 'center' }}>
                  <RadioGroup defaultValue={operator}>
                    <RadioButton value="and">并且</RadioButton>
                    <RadioButton value="or">或者</RadioButton>
                    <RadioButton value="-">排除</RadioButton>
                  </RadioGroup>
                </div>
              )}
              <div className={styles.tagGroup}>
                {selectedTags &&
                  selectedTags.length > 0 &&
                  selectedTags.map((tag, index) => (
                    // eslint-disable-next-line react/no-array-index-key
                    <Fragment key={`tag_${tag.id}_${index}`}>
                      {index !== 0 && (
                        <div style={{ width: '100%', textAlign: 'center' }}>
                          <RadioGroup defaultValue={tag.operator}>
                            <RadioButton value="and">并且</RadioButton>
                            <RadioButton value="or">或者</RadioButton>
                            <RadioButton value="-">排除</RadioButton>
                          </RadioGroup>
                        </div>
                      )}
                      <div className={styles.tag} key={`tag_${tag.id}`}>
                        <div className={styles.tagOrder}>
                          {getCode(i, index, selectedTags.length)}
                        </div>
                        <div className={styles.tagInfo}>
                          <span className={styles.tagName}>{tag.propertyDescription}</span>
                          <span className={styles.manzuText}> 满足 </span>
                          <div className={styles.enumValue}>
                            {setTagShow(tag).map(v => (
                                <Tag
                                  className={styles.myTag}
                                  key={`value_${groupId}_${tag.id}_${v.label}`}
                                  color="cyan"
                                >
                                  {/*  判断如果包含&gt; 替换为 >  */}
                                  {v.label ? v.label.includes('&gt;') ? v.label.replace('&gt;', '>') : v.label : null}
                                </Tag>
                              ))}
                          </div>
                        </div>
                      </div>
                    </Fragment>
                  ))}

                <div>
                  {!selectedTags ||
                    (selectedTags.length === 0 && (
                      <div className={styles.tagOrder}>{String.fromCharCode(65 + i)}</div>
                    ))}
                </div>
              </div>
            </Fragment>
          );
        })}
    </div>
  );
};

export default TagGroups;
