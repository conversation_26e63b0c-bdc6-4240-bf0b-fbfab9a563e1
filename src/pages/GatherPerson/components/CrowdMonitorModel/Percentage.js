import React from 'react';
import {
  Input,
  Button
} from 'antd';

class Percentage extends React.PureComponent {

  onSetVal = (e) => {
    const { onChange } = this.props;
    onChange(e.target.value)
  }
  
  render() {
    const {value,onChange,typeName} = this.props
    return (
      <div style={{display:'flex',alignItems:'center'}}>
        <Button disabled style={{width:120}}>{typeName}</Button>
        <span style={{marginLeft:10,marginRight:10}}>波动超过</span>
        <Input value={value} style={{width:60,textAlign:'center'}} onChange={(e)=>this.onSetVal(e)}/>
        <span style={{marginLeft:10}}>%</span>
      </div>
    )
  }
}

export default Percentage;
