import React from 'react';
import { monitorConfig } from '@/services/api';
import { Form } from '@ant-design/compatible';
import {
  Modal,
  Card,
  DatePicker,
  Row,
  Col,
  Button,
  Input,
  Popconfirm,
  notification,
  message,
  Table,
  Tag,
  Popover,
} from 'antd';

import GetUser from '@/components/GetUser';
import Percentage from './Percentage.js';

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

const FormItem = Form.Item;

@Form.create()
class CrowdMonitorModel extends React.PureComponent {
  state = { reload: false };
  handleSubmit = e => {
    const { crowdMonitorFormData, onRefresh, onCancel } = this.props;
    e.preventDefault();
    this.props.form.validateFields((err, values) => {
      if (!err) {
        values.monitorReceiver = values.monitorReceiver.map(e => {
          return {
            empId: e.empId,
            empName: e.nickName,
          };
        });
        const data = {
          monitorReceiver: values.monitorReceiver,
          monitorType: [
            // 固定值
            {
              detail: '钉钉',
              type: 'ding',
            },
            {
              detail: '邮箱',
              type: 'mail',
            },
          ],
          monitorConfig: [
            {
              monitorType: 105, // 前一天数据对比
              monitorValue: Number(values?.monitorValueOne), // 用户配置
            },
            {
              monitorType: 106, // 7天前数据对比
              monitorValue: Number(values?.monitorValueTwo), // 用户配置
            },
            {
              monitorType: 107, // 前7天均值对比
              monitorValue: Number(values?.monitorValueThree), // 用户配置
            },
          ],
          crowdId: crowdMonitorFormData.id,
        };

        monitorConfig(data).then(res => {
          if (res.code === '200') {
            message.success('保存成功');
            onCancel();
            this.props.form.resetFields();
            onRefresh();
          } else {
            message.error(res.msg);
          }
        });
      }
    });
  };

  checkPercent = (rule, value, callback) => {
    var patt = new RegExp(/^[1-9]\d*$/, 'g');
    if (patt.test(value)) {
      callback();
    }
    callback('请输入数字');
  };

  render() {
    const {
      crowdMonitorVisible,
      onCancel,
      crowdMonitorFormData,
      form: { getFieldDecorator },
    } = this.props;
    let { monitorConfig = [], monitorReceiver = [] } =
      crowdMonitorFormData?.crowdMonitorConfig || {};
    if (monitorReceiver && monitorReceiver.length > 0) {
      monitorReceiver = monitorReceiver.map(e => {
        return {
          empId: e.empId,
          nickName: e.empName,
        };
      });
    }

    const { reload } = this.state;

    return (
      <>
        <Modal
          title="人群监控"
          footer={null}
          visible={crowdMonitorVisible}
          onCancel={() => {
            this.props.form.resetFields();
            onCancel();
            this.setState({
              reload: false,
            });
          }}
          width={'600px'}
        >
          {reload || crowdMonitorFormData?.crowdMonitorConfig ? (
            <Card bordered={false}>
              <Form {...FORM_ITEM_LAYOUT} onSubmit={this.handleSubmit}>
                <Form.Item label="监控配置" name="monitorValueOne">
                  {getFieldDecorator('monitorValueOne', {
                    initialValue: monitorConfig.length > 0 ? monitorConfig[0]?.monitorValue : null,
                    rules: [{ validator: this.checkPercent }],
                  })(<Percentage typeName="对比昨天" />)}
                </Form.Item>
                <Form.Item name="monitorValueTwo" wrapperCol={{ offset: 6, span: 14 }}>
                  {getFieldDecorator('monitorValueTwo', {
                    initialValue: monitorConfig.length > 0 ? monitorConfig[1]?.monitorValue : null,
                    rules: [{ validator: this.checkPercent }],
                  })(<Percentage typeName="对比7天前" />)}
                </Form.Item>
                <Form.Item name="monitorValueTwo" wrapperCol={{ offset: 6, span: 14 }}>
                  {getFieldDecorator('monitorValueThree', {
                    initialValue: monitorConfig.length > 0 ? monitorConfig[2]?.monitorValue : null,
                    rules: [{ validator: this.checkPercent }],
                  })(<Percentage typeName="前7天均值" />)}
                </Form.Item>
                <FormItem label="报警接收人">
                  {getFieldDecorator('monitorReceiver', {
                    initialValue: monitorReceiver.length > 0 ? monitorReceiver : null,
                    rules: [{ required: true, message: '请输入业务对应的测试同学' }],
                  })(<GetUser />)}
                </FormItem>
                <Row gutter={24}>
                  <Col span={4} />
                  <Col span={14} style={{ textAlign: 'center' }}>
                    <Button
                      onClick={() => {
                        this.props.form.resetFields();
                        onCancel();
                        this.setState({
                          reload: false,
                        });
                      }}
                      style={{ marginRight: 15 }}
                    >
                      取消
                    </Button>
                    <Button type="primary" onClick={this.handleSubmit}>
                      保存
                    </Button>
                  </Col>
                </Row>
              </Form>
            </Card>
          ) : (
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <Button
                type="primary"
                onClick={() => {
                  this.setState({
                    reload: true,
                  });
                }}
              >
                开启监控
              </Button>
            </div>
          )}
        </Modal>
      </>
    );
  }
}

export default CrowdMonitorModel;
