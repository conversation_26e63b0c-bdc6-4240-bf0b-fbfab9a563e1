import React, { useState, Fragment, useEffect } from 'react';
import { Modal, Select, Form, Row, Col, Button, Radio, DatePicker, message, Tag } from 'antd';

import GetUser from '@/components/GetUser';

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const NOOP = () => {};
const { Option } = Select;

const CrowdTransferModal = ({
  editCrowdTransferFormData,
  editModalVisible = false,
  onCancel = NOOP,
  onSubmit = NOOP,
}) => {
  const [form] = Form.useForm();
  const crowdName = editCrowdTransferFormData.crowdName || '';
  const crowdId = editCrowdTransferFormData.id || '';

  const onFinish = values => {
    const { exportEnd } = values;
    const data = { ...values, id: crowdId };
    onSubmit(data);
  };

  const onModalCancel = () => {
    onCancel()
    form.resetFields()
  }

  const title = `人群转交：${crowdName}(人群ID：${crowdId})`;
  return (
      <Modal title={title} width={800} visible={editModalVisible} onCancel={onModalCancel} footer={null} destroyOnClose>
        <Form {...FORM_ITEM_LAYOUT} form={form} onFinish={onFinish}>
          <FormItem
            label="人群管理者转交给"
            name="creator"
            rules={[{ required: true, message: '请选择转交人群' }]}
          >
           <GetUser mode='default'/>
          </FormItem>
          <Row gutter={24}>
            <Col span={4} />
            <Col span={14} style={{ textAlign: 'center' }}>
              <Button onClick={onModalCancel} style={{ marginRight: 15 }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                授权
              </Button>
            </Col>
          </Row>
        </Form>
      </Modal>
  );
};

export default CrowdTransferModal;
