import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, Card, DatePicker, Row, Col, Button } from 'antd';
import dayjs from 'dayjs';

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

const FormItem = Form.Item;
@Form.create()
class CrowdDelayModal extends React.PureComponent {
  static defaultProps = {
    title: '人群过期时间',
  }

  handleSubmit = () => {
    const {
      form: { validateFields },
      onSubmit = () => {},
      editFormData,
    } = this.props;

    validateFields(['expiredDate'], (err, values) => {
      if (!err) {
        const { expiredDate } = values;
        onSubmit({
          expired: expiredDate.valueOf(),
          id: editFormData.id,
          crowdType: editFormData.crowdType,
        });
      }
    });
  };

  render() {
    const {
      title,
      editModalVisible,
      onCancel,
      form: { getFieldDecorator },
      editFormData,
    } = this.props;

    const disabledDate = current => {
      return current < dayjs().startOf('day') || current > dayjs() + 90 * 24 * 60 * 60 * 1000;
    };

    return (
      <Modal
        title={editFormData.crowdName}
        footer={null}
        visible={editModalVisible}
        onCancel={onCancel}
      >
        <Card bordered={false}>
          <Form {...FORM_ITEM_LAYOUT}>
            <FormItem label={title}>
              {getFieldDecorator('expiredDate', {
                initialValue: editFormData.expiredDate
                  ? dayjs(editFormData.expiredDate)
                  : undefined,
                rules: [{ required: true, message: title }],
              })(
                <DatePicker
                  disabledDate={disabledDate}
                  format="YYYY-MM-DD HH:mm:ss"
                  showTime={{
                    defaultValue: dayjs('23:59:59', 'HH:mm:ss'),
                  }}
                />
              )}
            </FormItem>
            <Row gutter={24}>
              <Col span={4} />
              <Col span={14} style={{ textAlign: 'center' }}>
                <Button onClick={onCancel} style={{ marginRight: 15 }}>
                  取消
                </Button>
                <Button type="primary" onClick={this.handleSubmit}>
                  保存
                </Button>
              </Col>
            </Row>
          </Form>
        </Card>
      </Modal>
    );
  }
}

export default CrowdDelayModal;
