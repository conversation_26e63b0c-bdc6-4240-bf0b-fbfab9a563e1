import React from 'react';
import { Modal } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { getBpmsUrl } from '@/utils/utils';

const { confirm } = Modal;

const CrowdApprovalTip = (approvalId) => {

  confirm({
    title: '当前人群有审批中流程，不可进行变更，若需变更先停止流程',
    icon: <ExclamationCircleOutlined />,
    okText: '去取消',
    cancelText:  '暂不操作',
    onOk() {
      return new Promise((resolve, reject) => {
        window.open(getBpmsUrl(approvalId))
        resolve(null)
      }).catch(() => console.log('Oops errors!'));
    },
    onCancel() {},
  });
};

export default CrowdApprovalTip;
