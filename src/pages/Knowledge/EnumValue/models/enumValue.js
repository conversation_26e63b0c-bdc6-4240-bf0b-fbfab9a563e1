import { message } from 'antd';
import {
  queryLabelEnumValueObj,
  queryOdpsGrantedTable,
  queryOdpsTableColumn,
  editLabelEnumValueObj,
  queryOdpsTable,
  editGrantedTable,
  updateFromOdps,
} from '@/services/api';

export default {
  namespace: 'enumValue',
  state: {
    loading: false,
    enumValues: [],
    pagination: {
      showTotal: total => `共 ${total} 条`,
    },
    metaType: '',
    grantedTables: [],
    labelEnumColumns: [],
    modalVisible: false,
    addGrantModalVisible: false,
    keyword: null,
    odpsTables: [],
    tableColumns: [],
    editFormData: {},
    syncLoading: false,
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(({ pathname }) => {
        if (pathname === '/develop-tool/enumValue') {
          dispatch({ type: 'query' });
        }
      });
    },
  },
  effects: {
    *query(model, { put, all }) {
      yield all([
        put.resolve({
          type: 'queryLabelEnumValueObj',
        }),
        put.resolve({
          type: 'queryOdpsGrantedTable',
        }),
      ]);
    },
    *queryOdpsTable({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: {
          keyword: payload.keyword,
        },
      });

      const res = yield call(queryOdpsTable, payload);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          odpsTables: res.data,
        },
      });
    },
    *queryTableColumns({ payload }, { call, put }) {
      const res = yield call(queryOdpsTableColumn, payload);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          tableColumns: res.data,
        },
      });
    },
    *queryOdpsGrantedTable(model, { call, put }) {
      const res = yield call(queryOdpsGrantedTable, {
        sceneType: 'LABEL_ENUM_VALUE_SCENE',
      });

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          grantedTables: res.data,
        },
      });
    },
    // 授权表的枚举值联动出的列
    *queryGrantedTableEnumColumns({ payload }, { call, put }) {
      const res = yield call(queryOdpsTableColumn, payload);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          labelEnumColumns: res.data,
        },
      });
    },
    *queryLabelEnumValueObj({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const res = yield call(queryLabelEnumValueObj, {
        ...payload,
        deleted: 0,
      });

      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          loading: false,
          enumValues: res.data,
        },
      });
    },
    // 添加枚举值
    *editLabelEnumValueObj({ payload }, { call, put }) {
      const isUpdate = !!payload.id;
      if(isUpdate) {
        delete payload.creator;
      }
      const res = yield call(editLabelEnumValueObj, payload);

      if (!res.success) {
        message.error(res.msg || '新增失败');
        return;
      }

      message.success(isUpdate ? '更新成功' : '新增成功');

      yield put({
        type: 'onModalCancel',
      });

      yield put({
        type: 'queryLabelEnumValueObj',
      });
    },
    *deleteLabelEnumValueObj({ payload }, { call, put }) {
      const res = yield call(editLabelEnumValueObj, payload);

      if (!res.success) {
        message.error(res.msg || '新增失败');
        return;
      }

      message.success('删除成功');

      yield put({
        type: 'queryLabelEnumValueObj',
      });
    },
    // 添加授权表
    *editGrantedTable({ payload }, { call, put }) {
      const res = yield call(editGrantedTable, payload);

      if (!res.success) {
        message.error(res.msg || '添加失败');
        return;
      }

      message.success('添加成功');

      yield put({
        type: 'updateState',
        payload: {
          addGrantModalVisible: false,
        },
      });

      yield put({
        type: 'queryOdpsGrantedTable',
      });
    },
    *updateFromOdps({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: {
          syncLoading: true,
        },
      });

      const res = yield call(updateFromOdps, payload);

      yield put({
        type: 'updateState',
        payload: {
          syncLoading: false,
        },
      });

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success('同步成功');
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    onModalCancel(state) {
      return {
        ...state,
        modalVisible: false,
        editFormData: {},
      };
    },
    onModalOpen(state) {
      return {
        ...state,
        modalVisible: true,
      };
    },
    onAddGrantModalOpen(state) {
      return {
        ...state,
        addGrantModalVisible: true,
      };
    },
    onAddGrantModalCancel(state) {
      return {
        ...state,
        addGrantModalVisible: false,
      };
    },
  },
};
