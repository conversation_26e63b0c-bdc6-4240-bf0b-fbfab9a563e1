import React from 'react';
import { Card, Table, Button, Row, Col, Divider, Popconfirm, Tag, Input } from 'antd';
import { connect } from 'dva';
import { Link } from 'umi';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import CreateTagEnum from '../components/CreateTagEnum';
import AddEnumValueGrantTable from './components/AddEnumValueGrantTable';
import SearchForm from './components/SearchForm';
import { EnumerationType } from '@/utils/utils';
@connect(state => ({ enumValue: state.enumValue, user: state.user }))
class EnumValue extends React.PureComponent {
  handleTableChange = pagination => {
    const {
      dispatch,
      enumValue: { pagination: pager },
    } = this.props;

    dispatch({
      type: 'enumValue/updateState',
      payload: {
        pagination: {
          ...pager,
          current: pagination.current,
          pageSize: pagination.pageSize,
        },
      },
    });

    dispatch({
      type: 'enumValue/queryBizEntity',
      payload: {
        pageNum: pagination.current,
      },
    });
  };

  onOpenEditModal = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'enumValue/onModalOpen',
    });
  };

  onCancelEditModal = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'enumValue/onModalCancel',
    });
  };

  onMetaTypeChange = e => {
    const { dispatch } = this.props;

    dispatch({
      type: 'enumValue/updateState',
      payload: {
        metaType: e.target.value,
      },
    });
  };

  onLabelEnumValueTableChange = val => {
    const { dispatch } = this.props;

    dispatch({
      type: 'enumValue/queryGrantedTableEnumColumns',
      payload: {
        tableGuid: val,
      },
    });
  };

  onSubmitAddLabelEnumValue = values => {
    const { dispatch, user: { currentUser } } = this.props;

    dispatch({
      type: 'enumValue/editLabelEnumValueObj',
      payload: {
        ...values,
        creator: { empId: currentUser.workId, nickName: currentUser.name }
      },
    });
  };

  onAddGrantModalOpen = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'enumValue/onAddGrantModalOpen',
    });
  };

  onAddGrantModalCancel = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'enumValue/onAddGrantModalCancel',
    });
  };

  onTableChange = val => {
    const { dispatch } = this.props;
    // 查询所选表中的所有字段
    dispatch({
      type: 'enumValue/queryTableColumns',
      payload: {
        tableGuid: val,
      },
    });
  };

  onTableSearch = val => {
    const { dispatch } = this.props;
    // 查询odps表
    dispatch({
      type: 'enumValue/queryOdpsTable',
      payload: {
        keyword: val,
      },
    });
  };

  onEditGrantedTable = values => {
    const { dispatch } = this.props;

    dispatch({
      type: 'enumValue/editGrantedTable',
      payload: { ...values, sceneType: 'LABEL_ENUM_VALUE_SCENE' },
    });
  };

  // 删除枚举对象
  onDelete = id => {
    const { dispatch } = this.props;

    dispatch({
      type: 'enumValue/deleteLabelEnumValueObj',
      payload: {
        id,
        deleted: 1,
      },
    });
  };

  onEdit = record => {
    const { dispatch } = this.props;

    dispatch({
      type: 'enumValue/updateState',
      payload: {
        editFormData: record,
        modalVisible: true,
      },
    });
  };

  // 同步odps
  onSyncOdps = id => {
    const { dispatch } = this.props;

    dispatch({
      type: 'enumValue/updateFromOdps',
      payload: {
        id,
      },
    });
  };

  onSubmitSearch = value => {
    const { dispatch } = this.props;
    dispatch({
      type: 'enumValue/queryLabelEnumValueObj',
      payload: value,
    });
  };

  render() {
    const {
      enumValue: {
        pagination,
        enumValues,
        loading,
        metaType,
        modalVisible,
        grantedTables,
        labelEnumColumns,
        addGrantModalVisible,
        tableColumns,
        odpsTables,
        keyword,
        editFormData,
        syncLoading,
        formData,
      },
    } = this.props;

    const columns = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        align: 'center',
      },
      {
        title: '枚举对象code',
        dataIndex: 'code',
        key: 'code',
        align: 'center',
      },
      {
        title: '枚举值对象名称',
        dataIndex: 'name',
        key: 'name',
        align: 'center',
      },
      {
        title: '枚举值对象类型',
        dataIndex: 'type',
        key: 'type',
        align: 'center',
        render: type => EnumerationType(type),
      },
      {
        title: '管理员',
        dataIndex: 'operator',
        key: 'operator',
        align: 'center',
        render: users => (
          <span>
            {users &&
              users.length > 0 &&
              users.map(u => (
                <Tag color="green" key={u.empId}>
                  {u.nickName}
                </Tag>
              ))}
          </span>
        ),
      },
      {
        title: '创建人',
        dataIndex: 'creator',
        key: 'creator',
        align: 'center',
        render: (u, i) =>
          u && (
            <Tag color="green" key={`${u.empId}_${i}`}>
              {u.nickName}
            </Tag>
          ),
      },
      {
        title: '数据日期',
        dataIndex: 'ds',
        key: 'ds',
        align: 'center',
        render: text => text || '暂无数据',
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        align: 'center',
        fixed: 'right',
        render: (text, record) => {
          let { creator, operator } = record;
          if (!creator) {
            creator = {};
          }

          if (!operator) {
            operator = [];
          }
          const owners = [...operator, creator];

          const {
            user: { currentUser, isSuperAdmin },
          } = this.props;

          const isGranted = owners.map(o => o.nickName).includes(currentUser.name);

          return (
            <span>
              <Link to={`/knowledge/enumValueDetail?id=${record.id}`}>查看详情</Link>
              <>
                <Divider type="vertical" />
                <a
                  onClick={() => {
                    // if (!isGranted && !isSuperAdmin) {
                    //   return;
                    // }
                    this.onEdit(record);
                  }}
                  disabled={!isGranted && !isSuperAdmin}
                >
                  编辑
                </a>
                <Divider type="vertical" />
                <Popconfirm
                  title="确定删除？"
                  okText="确定"
                  cancelText="取消"
                  onConfirm={() => this.onDelete(record.id)}
                  disabled={!isGranted && !isSuperAdmin}
                >
                  <a disabled={!isGranted && !isSuperAdmin}>删除</a>
                </Popconfirm>
              </>
            </span>
          );
        },
      },
    ];

    return (
      <PageHeaderWrapper title="枚举值管理">
        <Card bordered={false}>
          {/* 搜索 */}
          <SearchForm
            onOpenEditModal={this.onOpenEditModal}
            onAddGrantModalOpen={this.onAddGrantModalOpen}
            onSearchSubmit={this.onSubmitSearch}
          />
          <Table
            loading={loading}
            rowKey={record => record.id}
            columns={columns}
            scroll={{ x: 'max-content' }}
            dataSource={enumValues}
            pagination={pagination}
            onChange={this.handleTableChange}
          />
        </Card>

        <CreateTagEnum
          syncLoading={syncLoading}
          metaType={metaType}
          editFormData={editFormData}
          onMetaTypeChange={this.onMetaTypeChange}
          grantedTables={grantedTables}
          modalVisible={modalVisible}
          onCancel={this.onCancelEditModal}
          labelEnumColumns={labelEnumColumns}
          onLabelEnumValueTableChange={this.onLabelEnumValueTableChange}
          onSubmit={this.onSubmitAddLabelEnumValue}
          onSyncOdps={this.onSyncOdps}
        />

        <AddEnumValueGrantTable
          modalVisible={addGrantModalVisible}
          onCancel={this.onAddGrantModalCancel}
          tableColumns={tableColumns}
          odpsTables={odpsTables}
          onTableChange={this.onTableChange}
          onTableSearch={this.onTableSearch}
          keyword={keyword}
          onEditGrantedTable={this.onEditGrantedTable}
        />
      </PageHeaderWrapper>
    );
  }
}

export default EnumValue;
