import React, { useState } from 'react';
import { Form, Row, Col, Input, Button, Select } from 'antd';
const layout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};
const tailLayout = {
  wrapperCol: { span: 16 },
};
const { Option } = Select;
const SearchForm = props => {
  const { onOpenEditModal, onAddGrantModalOpen, onSearchSubmit } = props;
  const [form] = Form.useForm();

  const onSubmit = () => {
    form.submit();
  };

  const onReset = () => {
    onSearchSubmit({})
    form.resetFields();
  };

  const onFinish = values => {
    onSearchSubmit(values)
  };

  return (
    <Form form={form} {...layout} onFinish={onFinish}>
      <Row gutter={24}>
      <Col span={8}>
          <Form.Item name="id" label="ID">
            <Input placeholder="请输入枚举id" />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item name="metaCode" label="枚举对象code">
            <Input placeholder="请输入枚举对象code" />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item name="metaName" label="枚举对象名称">
            <Input placeholder="请输入枚举对象名称" />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item name="metaType" label="枚举对象类型">
            <Select placeholder="请输入枚举对象类型">
              <Option key={'CUSTOMIZE'} value={'CUSTOMIZE'}>
                自定义
              </Option>
              <Option key={'ODPS'} value={'ODPS'}>
                ODPS源
              </Option>
              <Option key={'SYSTEM'} value={'SYSTEM'}>
                系统自动生成
              </Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24} style={{ marginBottom: 10 }}>
        <Col span={14}>
          <Button style={{ marginRight: 10 }} type="primary" onClick={onOpenEditModal}>
            创建枚举值
          </Button>
          <Button type="primary" onClick={onAddGrantModalOpen}>
            新增枚举值授权表
          </Button>
        </Col>
        <Col span={10} style={{textAlign:'right'}}>
          <Button type="primary" onClick={onSubmit} style={{ marginRight: 10 }}>
            查询
          </Button>
          <Button htmlType="button" onClick={onReset} style={{ marginRight: 10 }}>
            重置
          </Button>
        </Col>
      </Row>
    </Form>
  );
};

export default SearchForm;
