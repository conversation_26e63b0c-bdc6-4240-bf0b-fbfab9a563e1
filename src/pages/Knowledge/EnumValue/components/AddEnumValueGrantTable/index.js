import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, Row, Col, Button, Select, Input, Checkbox } from 'antd';

const NOOP = () => {};

const FormItem = Form.Item;
const CheckboxGroup = Checkbox.Group;
const { Option } = Select;

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
@Form.create()
class AddEnumValueGrantTable extends React.PureComponent {
  onSubmit = () => {
    const { form, onEditGrantedTable } = this.props;

    form.validateFields((err, values) => {
      if (!err) {
        onEditGrantedTable(values);
      }
    });
  };

  render() {
    const {
      modalVisible,
      onCancel = NOOP,
      odpsTables,
      keyword,
      form: { getFieldDecorator },
      onTableChange = NOOP,
      onTableSearch = NOOP,
      tableColumns,
      loading,
    } = this.props;

    return (
      modalVisible && (
        <Modal visible={modalVisible} onCancel={onCancel} width={800} footer={null}>
          <Form {...FORM_ITEM_LAYOUT}>
            <FormItem label="表">
              {getFieldDecorator('tableName', {
                rules: [{ required: true, message: '请输入表' }],
              })(
                <Select
                  showSearch
                  onSearch={onTableSearch}
                  onChange={onTableChange}
                  placeholder="选择表"
                  optionLabelProp="value"
                >
                  {odpsTables.length && // 高亮显示
                    odpsTables.map(t => {
                      const index = t.indexOf(keyword);
                      const beforeStr = t.substr(0, index);
                      const afterStr = t.substr(index + keyword.length);
                      const content =
                        index > -1 ? (
                          <span>
                            {beforeStr}
                            <span style={{ color: '#f50' }}>{keyword}</span>
                            {afterStr}
                          </span>
                        ) : (
                          <span>{t}</span>
                        );
                      return <Option key={t}>{content}</Option>;
                    })}
                </Select>
              )}
            </FormItem>
            <FormItem label="分区分段名">
              {getFieldDecorator('partitionField', {
                rules: [{ required: true, message: '请输入分区分段名' }],
              })(<Input placeholder="请输入分区分段名" />)}
            </FormItem>
            <FormItem label="用户主键字段">
              {getFieldDecorator('primaryKey', {
                rules: [{ required: true, message: '请输入字段' }], // 服务端数据问题先放开做测试
              })(
                <Select showSearch placeholder="请输入字段">
                  {tableColumns.length &&
                    tableColumns.map(c => <Option key={c.columnName}>{c.columnName}</Option>)}
                </Select>
              )}
            </FormItem>
            <FormItem
              label="确认授权"
              extra={
                <span style={{ color: 'red' }}>
                  add user aliyun$<EMAIL>; 
                  GRANT Describe,Select ON TABLE 表名称 TO USER ALIYUN$<EMAIL>; 
                  GRANT LABEL 2 ON TABLE 表名称 TO USER ALIYUN$<EMAIL>;
                </span>
              }
            >
              {getFieldDecorator('isGranted', {
                rules: [{ required: true, message: '请确认授权' }],
              })(
                // <CheckboxGroup disabled={isSelectedGrantedTable}>
                <CheckboxGroup>
                  <Checkbox value="hasGranted">已经授权</Checkbox>
                </CheckboxGroup>
              )}
            </FormItem>
          </Form>
          <Row gutter={24}>
            <Col span={4} />
            <Col span={14} style={{ textAlign: 'center' }}>
              <Button onClick={() => onCancel()} style={{ marginRight: 15 }}>
                取消
              </Button>
              <Button type="primary" onClick={this.onSubmit}>
                保存
              </Button>
            </Col>
          </Row>
        </Modal>
      )
    );
  }
}

export default AddEnumValueGrantTable;
