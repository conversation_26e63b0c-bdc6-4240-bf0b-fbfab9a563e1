.scriptPreview {
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}

.codeEditor {
  position: relative;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;

  &:hover {
    border-color: #40a9ff;
  }

  &:focus-within {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  // Monaco Editor 容器样式
  :global(.monaco-editor) {
    .monaco-editor-background {
      background-color: #fafafa;
    }
  }

  // 禁用状态样式
  &.disabled {
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    cursor: not-allowed;

    :global(.monaco-editor) {
      .monaco-editor-background {
        background-color: #f5f5f5;
      }
    }
  }
}

.testResult {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  margin-top: 16px;
  max-height: 500px;
  overflow-y: auto;

  pre {
    margin: 0;
    white-space: pre-wrap;
    word-break: break-word;
    font-family: 'Monaco', 'Men<PERSON>', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
    font-size: 13px;
    line-height: 1.6;
    color: #24292e;

    // 日志级别颜色
    .log-error {
      color: #d73a49;
      font-weight: 500;
    }

    .log-warn {
      color: #f66a0a;
      font-weight: 500;
    }

    .log-info {
      color: #0366d6;
    }

    .log-debug {
      color: #6f42c1;
    }
  }

  // 滚动条样式
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

.environmentTabs {
  .ant-tabs-tab {
    font-weight: 500;
  }

  .ant-tabs-tab-active {
    color: #1890ff;
  }

  // 为标签页内的编辑器添加间距
  .ant-tabs-tabpane {
    padding-top: 8px;
  }
}

.preScriptEditor {
  background-color: #f9f9f9;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;

  .editorHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .editorTitle {
      font-weight: 500;
      color: #262626;
    }

    .editorTip {
      color: #8c8c8c;
      font-size: 12px;
    }
  }

  .codeEditor {
    border: 1px solid #d9d9d9;
    background-color: #fff;
  }
}

.testModalFooter {
  .saveButton {
    background-color: #52c41a;
    border-color: #52c41a;

    &:hover {
      background-color: #73d13d;
      border-color: #73d13d;
    }
  }
}

// 执行结果样式
.executionResult {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 12px;
  max-height: 200px;
  overflow-y: auto;

  pre {
    margin: 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
    font-size: 12px;
    line-height: 1.4;
    color: #24292e;
  }

  // 滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// 日志折叠面板样式
.logCollapse {
  .ant-collapse-header {
    padding: 8px 16px !important;
    font-weight: 500;
  }

  .ant-collapse-content-box {
    padding: 0 !important;
  }
}

// 日志容器样式
.logContainer {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background-color: #fff;

  // 滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// 日志项样式
.logItem {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 10px;
  margin: 8px;
  background-color: #fafafa;
  transition: all 0.2s;
  cursor: pointer;

  &:hover {
    border-color: #1890ff;
    background-color: #f0f8ff;
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
  }

  &:active {
    transform: translateY(1px);
    box-shadow: 0 1px 2px rgba(24, 144, 255, 0.2);
  }

  &:first-child {
    margin-top: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

// 日志头部样式
.logHeader {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding-bottom: 4px;
  border-bottom: 1px solid #f0f0f0;

  .logIndex {
    font-size: 12px;
    color: #8c8c8c;
    font-weight: 500;
    min-width: 30px;
  }

  .logLevel {
    margin: 0;
    font-size: 11px;
    font-weight: 500;
  }

  .logTime {
    font-size: 11px;
    color: #8c8c8c;
    flex: 1;
  }

  .copyIcon {
    color: #8c8c8c;
    font-size: 12px;
    opacity: 0.6;
    transition: opacity 0.2s;

    .logItem:hover & {
      opacity: 1;
      color: #1890ff;
    }
  }
}

// 日志消息样式
.logMessage {
  color: #24292e;
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 4px;
  word-break: break-word;
  max-height: 100px;
  overflow: hidden;

  // 截断提示样式
  &.truncated {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      right: 0;
      width: 30px;
      height: 20px;
      background: linear-gradient(to right, transparent, #f5f5f5);
    }
  }
}

// 异常信息样式
.logException {
  color: #d73a49;
  font-size: 11px;
  margin-top: 6px;
  padding: 6px;
  background-color: #ffeaea;
  border-radius: 4px;
  border-left: 3px solid #d73a49;
  max-height: 80px;
  overflow: hidden;
  word-break: break-word;
}

// 堆栈跟踪样式
.logStackTrace {
  margin-top: 6px;

  strong {
    color: #24292e;
    font-size: 11px;
    display: block;
    margin-bottom: 4px;
  }

  pre {
    background-color: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 4px;
    padding: 6px;
    margin: 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
    font-size: 10px;
    line-height: 1.3;
    color: #586069;
    overflow-x: auto;
    max-height: 120px;
    overflow-y: auto;
    word-break: break-all;
  }
}
