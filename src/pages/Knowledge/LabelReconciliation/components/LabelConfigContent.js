import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Form,
  Space,
  Popconfirm,
  message,
  Tag,
  Row,
  Col,
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, PlayCircleOutlined, PauseCircleOutlined, ExperimentOutlined } from '@ant-design/icons';
import {
  queryLabelCheckScriptList,
  enableLabelCheckScript,
  deleteLabelCheckScript,
} from '@/services/api';
import CreateOrEditModal from '../components/CreateOrEditModal';
import TestModal from '../components/TestModal';
import styles from '../index.less';

const { Option } = Select;

// 日期格式化工具函数
const formatDateTime = (text) => {
  if (!text) return '-';
  const date = new Date(text);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
};

// 将采样率转换为中文说明
const getSampleRateDescription = (rate) => {
  if (!rate || rate <= 0) return '';

  if (rate >= 1) return '全量';
  if (rate >= 0.5) return '一半';
  if (rate >= 0.1) return '十分之' + Math.round(rate * 10);
  if (rate >= 0.01) return '百分之' + Math.round(rate * 100);
  if (rate >= 0.001) return '千分之' + Math.round(rate * 1000);
  if (rate >= 0.0001) return '万分之' + Math.round(rate * 10000);
  if (rate >= 0.00001) return '十万分之' + Math.round(rate * 100000);
  if (rate >= 0.000001) return '百万分之' + Math.round(rate * 1000000);

  return '极小比例';
};

const LabelConfigContent = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
  });
  const [searchParams, setSearchParams] = useState({});
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [testModalVisible, setTestModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState(null);

  // 获取列表数据
  const fetchData = async (params = {}) => {
    setLoading(true);
    try {
      const requestParams = {
        pageNum: params.pageNum || pagination.current,
        pageSize: params.pageSize || pagination.pageSize,
        ...searchParams,
        ...params,
      };

      const response = await queryLabelCheckScriptList(requestParams);
      if (response.success) {
        const { list = [], total = 0, pageNum, pageSize: size } = response.data;
        setDataSource(list);
        setPagination(prev => ({
          ...prev,
          current: pageNum,
          pageSize: size,
          total,
        }));
      } else {
        message.error(response.msg || '查询失败');
      }
    } catch (error) {
      message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // 搜索
  const handleSearch = (values) => {
    const params = { ...values };
    setSearchParams(params);
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchData({ ...params, pageNum: 1 });
  };

  // 重置搜索
  const handleReset = () => {
    form.resetFields();
    setSearchParams({});
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchData({ pageNum: 1 });
  };

  // 表格分页变化
  const handleTableChange = (paginationConfig) => {
    setPagination(paginationConfig);
    fetchData({
      pageNum: paginationConfig.current,
      pageSize: paginationConfig.pageSize,
    });
  };

  // 启用/禁用脚本
  const handleToggleEnable = async (record) => {
    try {
      const response = await enableLabelCheckScript({
        labelCode: record.labelCode,
        enable: record.enable === 1 ? false : true,
      });
      if (response.success) {
        message.success(`${record.enable === 1 ? '禁用' : '启用'}成功`);
        fetchData();
      } else {
        message.error(response.msg || `${record.enable === 1 ? '禁用' : '启用'}失败`);
      }
    } catch (error) {
      message.error(`${record.enable === 1 ? '禁用' : '启用'}失败`);
    }
  };

  // 删除脚本
  const handleDelete = async (record) => {
    try {
      const response = await deleteLabelCheckScript({
        labelCode: record.labelCode,
      });
      if (response.success) {
        message.success('删除成功');
        fetchData();
      } else {
        message.error(response.msg || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 新增
  const handleCreate = () => {
    setCurrentRecord(null);
    setCreateModalVisible(true);
  };

  // 编辑
  const handleEdit = (record) => {
    setCurrentRecord(record);
    setEditModalVisible(true);
  };

  // 测试
  const handleTest = (record) => {
    setCurrentRecord(record);
    setTestModalVisible(true);
  };

  // 创建/编辑成功回调
  const handleModalSuccess = () => {
    setCreateModalVisible(false);
    setEditModalVisible(false);
    fetchData();
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
    },
    {
      title: '标签代码',
      dataIndex: 'labelCode',
      width: 200,
      ellipsis: true,
      render: (text, record) => {
        // 检查线上脚本是否为简单的 return true
        const isSimpleScript = record.onlineScript &&
          record.onlineScript.trim().replace(/\s+/g, ' ').toLowerCase() === 'return true';

        return (
          <div>
            <div style={{ fontWeight: 500 }}>
              {text}
            </div>
            {record.labelName && (
              <div style={{ color: '#666', fontSize: 12, marginTop: 2, display: 'flex', alignItems: 'center', gap: 4 }}>
                {isSimpleScript && (
                  <Tag color="orange" size="small" style={{ fontSize: 10, lineHeight: '14px' }}>
                    未实现
                  </Tag>
                )}
                {record.labelName}
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'enable',
      width: 100,
      render: (enable) => (
        <Tag color={enable === 1 ? 'green' : 'red'}>
          {enable === 1 ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '采样率',
      dataIndex: 'sampleRate',
      width: 100,
      render: (sampleRate) => {
        if (sampleRate === null || sampleRate === undefined) return '0';
        // 如果是整数，不显示小数位；如果是小数，最多显示6位小数
        const num = parseFloat(sampleRate);
        const value = num % 1 === 0 ? num.toString() : num.toFixed(6).replace(/\.?0+$/, '');
        const description = getSampleRateDescription(sampleRate);

        return (
          <div>
            <div>{value}</div>
            {description && (
              <div style={{ color: '#666', fontSize: 11 }}>
                {description}
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: '通过率',
      dataIndex: 'successDailyRate',
      width: 80,
      render: (rate) => {
        if (rate === null || rate === undefined) return '-';
        const percentage = (rate * 100).toFixed(1);
        const color = rate >= 0.9 ? '#52c41a' : rate >= 0.7 ? '#faad14' : '#ff4d4f';
        return (
          <span style={{ color, fontWeight: 500, fontSize: 13 }}>
            {percentage}%
          </span>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate',
      width: 180,
      render: (text) => formatDateTime(text),
    },
    {
      title: '修改时间',
      dataIndex: 'gmtModified',
      width: 180,
      render: (text) => formatDateTime(text),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            icon={<ExperimentOutlined />}
            onClick={() => handleTest(record)}
          >
            测试
          </Button>
          <Button
            type="link"
            size="small"
            icon={record.enable === 1 ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
            onClick={() => handleToggleEnable(record)}
          >
            {record.enable === 1 ? '禁用' : '启用'}
          </Button>
          <Popconfirm
            title="确定要删除这个脚本配置吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <Card bordered={false}>
        {/* 搜索表单 */}
        <Form
          form={form}
          layout="inline"
          onFinish={handleSearch}
          style={{ marginBottom: 16 }}
        >
          <Form.Item name="labelCode" label="标签代码">
            <Input placeholder="请输入标签代码" allowClear />
          </Form.Item>
          <Form.Item name="enable" label="状态">
            <Select placeholder="请选择状态" allowClear style={{ width: 120 }}>
              <Option value={true}>启用</Option>
              <Option value={false}>禁用</Option>
            </Select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                查询
              </Button>
              <Button onClick={handleReset}>
                重置
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreate}
              >
                新增配置
              </Button>
            </Space>
          </Form.Item>
        </Form>

        {/* 表格 */}
        <Table
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          rowKey="id"
          pagination={pagination}
          onChange={handleTableChange}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* 新增弹窗 */}
      <CreateOrEditModal
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onSuccess={handleModalSuccess}
        record={null}
        title="新增标签对账配置"
      />

      {/* 编辑弹窗 */}
      <CreateOrEditModal
        visible={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onSuccess={handleModalSuccess}
        record={currentRecord}
        title="编辑标签对账配置"
      />

      {/* 测试弹窗 */}
      <TestModal
        visible={testModalVisible}
        onCancel={() => setTestModalVisible(false)}
        record={currentRecord}
      />
    </>
  );
};

export default LabelConfigContent;
