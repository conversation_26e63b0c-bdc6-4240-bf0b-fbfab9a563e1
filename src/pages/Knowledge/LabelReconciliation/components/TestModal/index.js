import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Switch,
  Button,
  Space,
  message,
  Alert,
  Divider,
  Collapse,
  Typography,
  Tag,
  Tooltip,
} from 'antd';
import { CopyOutlined, QuestionCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import { testLabelCheckScript, createOrUpdateLabelCheckScript, getLabelCheckScript } from '@/services/api';
import CodeEditor from '../CodeEditor';
import styles from '../../index.less';

const { Panel } = Collapse;
const { Text } = Typography;

const TestModal = ({ visible, onCancel, record }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [testContext, setTestContext] = useState('{\n  "userId": "2213279420117",\n  "result": true\n}');
  const [testResult, setTestResult] = useState('');
  const [executionResult, setExecutionResult] = useState(null);
  const [executionLogs, setExecutionLogs] = useState([]);
  const [usePreScript, setUsePreScript] = useState(true);
  const [preScript, setPreScript] = useState('');
  const [originalData, setOriginalData] = useState(null);

  // 当弹窗打开时，获取当前脚本数据
  useEffect(() => {
    if (visible && record) {
      fetchScriptData();
      // 设置表单默认值和状态同步
      setUsePreScript(true);
      form.setFieldsValue({
        usePreScript: true,
      });
    }
  }, [visible, record]);

  const fetchScriptData = async () => {
    try {
      const response = await getLabelCheckScript({ labelCode: record.labelCode });
      if (response.success) {
        const data = response.data;
        setOriginalData(data);
        setPreScript(data.preScript || '');

        // 使用接口返回的testContext作为默认值
        if (data.testContext) {
          setTestContext(data.testContext);
        }
      } else {
        message.error('获取脚本数据失败');
      }
    } catch (error) {
      message.error('获取脚本数据失败');
    }
  };

  const handleSavePreScript = async () => {
    if (!originalData) {
      message.error('原始数据未加载，无法保存');
      return false;
    }

    try {
      const requestData = {
        labelCode: record.labelCode,
        preScript,
        onlineScript: originalData.onlineScript,
        enable: originalData.enable === 1,
        sampleRate: originalData.sampleRate,
        testContext,
      };

      const response = await createOrUpdateLabelCheckScript(requestData);
      if (response.success) {
        message.success('预发脚本保存成功');
        return true;
      } else {
        message.error(response.msg || '预发脚本保存失败');
        return false;
      }
    } catch (error) {
      message.error('预发脚本保存失败');
      return false;
    }
  };

  const handleTest = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 如果勾选了使用预发脚本，总是先保存预发脚本内容
      if (values.usePreScript) {
        const saveSuccess = await handleSavePreScript();
        if (!saveSuccess) {
          return;
        }
      }

      // 验证JSON格式
      let contextObj;
      try {
        contextObj = JSON.parse(testContext);
      } catch (error) {
        message.error('测试上下文必须是有效的JSON格式');
        return;
      }

      const requestData = {
        labelCode: record.labelCode,
        testContext,
        usePreScript: values.usePreScript,
      };

      const response = await testLabelCheckScript(requestData);
      if (response.success) {
        const { result, logs = [] } = response.data;

        // 设置解析后的结果和日志
        setExecutionResult(result);
        setExecutionLogs(logs);
        setTestResult(''); // 清空旧的文本结果

        // 根据执行结果显示不同的提示
        if (result !== null && result !== undefined) {
          message.success('脚本测试执行成功');
        } else {
          // 检查是否有错误日志
          const hasError = logs.some(log => log.level === 'ERROR');
          if (hasError) {
            message.warning('脚本执行完成，但发现错误，请查看日志详情');
          } else {
            message.success('脚本测试执行完成');
          }
        }
      } else {
        setTestResult(`执行失败: ${response.msg}`);
        setExecutionResult(null);
        setExecutionLogs([]);
        message.error(response.msg || '脚本测试执行失败');
      }
    } catch (error) {
      if (error.errorFields) {
        message.error('请检查表单输入');
      } else {
        const errorMsg = error.message || '脚本测试执行失败';
        setTestResult(`执行异常: ${errorMsg}`);
        message.error(errorMsg);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setTestContext('{\n  "userId": "2213279420117",\n  "result": true\n}');
    setTestResult('');
    setExecutionResult(null);
    setExecutionLogs([]);
    setUsePreScript(true);
    setPreScript('');
    setOriginalData(null);
    // 确保表单字段也被重置
    form.setFieldsValue({
      usePreScript: true,
    });
    onCancel();
  };

  const handleUsePreScriptChange = (checked) => {
    setUsePreScript(checked);
    form.setFieldsValue({ usePreScript: checked });
  };

  // 截断文本内容
  const truncateText = (text, maxLength = 150) => {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  // 截断堆栈跟踪
  const truncateStackTrace = (stackTrace, maxLines = 8) => {
    if (!stackTrace) return '';
    const lines = stackTrace.split('\n');
    if (lines.length <= maxLines) return stackTrace;
    return lines.slice(0, maxLines).join('\n') + '\n... (还有 ' + (lines.length - maxLines) + ' 行，点击复制查看完整内容)';
  };

  // 复制日志内容
  const handleCopyLog = (log) => {
    const timestamp = new Date(log.timestamp).toLocaleString('zh-CN');
    let logText = `[${timestamp}] ${log.level}: ${log.message}`;

    if (log.exceptionMessage) {
      logText += `\n异常: ${log.exceptionMessage}`;
    }

    if (log.stackTrace) {
      logText += `\n堆栈跟踪:\n${log.stackTrace}`;
    }

    navigator.clipboard.writeText(logText).then(() => {
      message.success('日志已复制到剪贴板');
    }).catch(() => {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = logText;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      message.success('日志已复制到剪贴板');
    });
  };

  // 获取日志级别的颜色和图标
  const getLogLevelInfo = (level) => {
    const levelMap = {
      'ERROR': { color: 'red', icon: '❌' },
      'WARN': { color: 'orange', icon: '⚠️' },
      'INFO': { color: 'blue', icon: 'ℹ️' },
      'DEBUG': { color: 'purple', icon: '🔍' }
    };
    return levelMap[level] || { color: 'default', icon: '📝' };
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div>
            <div style={{ fontWeight: 500 }}>测试脚本 - {record?.labelCode}</div>
            {record?.labelName && (
              <div style={{ color: '#666', fontSize: 12, marginTop: 2 }}>
                {record.labelName}
              </div>
            )}
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <span style={{ fontSize: 14, color: '#666' }}>使用预发脚本</span>
            <Switch
              size="small"
              checked={usePreScript}
              checkedChildren="预发"
              unCheckedChildren="线上"
              onChange={handleUsePreScriptChange}
            />
          </div>
        </div>
      }
      visible={visible}
      onCancel={handleCancel}
      closable={false}
      width={1400}
      bodyStyle={{
        padding: 0,
        height: 'calc(100vh - 200px)',
        maxHeight: '800px'
      }}
      footer={
        <Space>
          <Button onClick={handleCancel}>关闭</Button>
          {usePreScript && (
            <Button
              onClick={handleSavePreScript}
              className={styles.saveButton}
            >
              保存预发脚本
            </Button>
          )}
          <Button type="primary" loading={loading} onClick={handleTest}>
            执行测试
          </Button>
        </Space>
      }
    >
      <div style={{ display: 'flex', height: '100%' }}>
        {/* 左侧：测试结果区域 */}
        <div style={{
          width: '40%',
          borderRight: '1px solid #f0f0f0',
          padding: '24px 16px 24px 24px',
          overflow: 'auto',
          backgroundColor: '#fafafa'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: 16,
            position: 'sticky',
            top: 0,
            backgroundColor: '#fafafa',
            paddingBottom: 8,
            borderBottom: '1px solid #e8e8e8'
          }}>
            <h4 style={{ margin: 0, fontSize: 16, fontWeight: 600 }}>测试结果</h4>
            <Button
              type="primary"
              size="small"
              loading={loading}
              onClick={handleTest}
              icon={<ReloadOutlined />}
            >
              重新测试
            </Button>
          </div>

          {(executionResult !== null || executionLogs.length > 0 || testResult) ? (
            <>
              {/* 执行结果 */}
              {executionResult !== null && (
                <div style={{ marginBottom: 16 }}>
                  <div style={{ marginBottom: 8, fontWeight: 500, fontSize: 14 }}>执行结果：</div>
                  <div className={styles.executionResult}>
                    <pre>{JSON.stringify(executionResult, null, 2)}</pre>
                  </div>
                </div>
              )}

              {/* 执行日志 */}
              {executionLogs.length > 0 && (
                <div style={{ marginBottom: 16 }}>
                  <div style={{ marginBottom: 8, fontWeight: 500, fontSize: 14 }}>
                    执行日志 ({executionLogs.length} 条)：
                  </div>
                  <div className={styles.logContainer} style={{ maxHeight: 'calc(100vh - 400px)' }}>
                    {executionLogs.map((log, index) => {
                      const { color, icon } = getLogLevelInfo(log.level);
                      const timestamp = new Date(log.timestamp).toLocaleString('zh-CN');

                      return (
                        <div
                          key={index}
                          className={styles.logItem}
                          onClick={() => handleCopyLog(log)}
                          title="点击复制日志内容"
                        >
                          <div className={styles.logHeader}>
                            <span className={styles.logIndex}>#{index + 1}</span>
                            <Tag color={color} className={styles.logLevel}>
                              {icon} {log.level}
                            </Tag>
                            <span className={styles.logTime}>{timestamp}</span>
                            <CopyOutlined className={styles.copyIcon} />
                          </div>
                          <div className={styles.logMessage}>
                            {truncateText(log.message)}
                          </div>
                          {log.exceptionMessage && (
                            <div className={styles.logException}>
                              <strong>异常:</strong> {truncateText(log.exceptionMessage)}
                            </div>
                          )}
                          {log.stackTrace && (
                            <div className={styles.logStackTrace}>
                              <strong>堆栈跟踪:</strong>
                              <pre>{truncateStackTrace(log.stackTrace)}</pre>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* 旧版本的文本结果（兼容性） */}
              {testResult && !executionResult && executionLogs.length === 0 && (
                <div className={styles.testResult}>
                  <pre>{testResult}</pre>
                </div>
              )}
            </>
          ) : (
            <div style={{
              textAlign: 'center',
              color: '#8c8c8c',
              padding: '60px 20px',
              fontSize: 14
            }}>
              <div style={{ fontSize: 48, marginBottom: 16 }}>🧪</div>
              <div>点击"执行测试"查看结果</div>
            </div>
          )}
        </div>

        {/* 右侧：编辑区域 */}
        <div style={{
          flex: 1,
          padding: '24px',
          overflow: 'auto'
        }}>
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              usePreScript: true,
            }}
          >
            <Form.Item name="usePreScript" style={{ display: 'none' }}>
              <Switch />
            </Form.Item>
            {usePreScript && (
              <Form.Item
                label={
                  <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                    <span>预发脚本内容</span>
                    <Tooltip
                      title="在这里可以直接编辑预发脚本内容。点击'执行测试'时会先自动保存预发脚本，然后执行测试。您也可以点击'保存预发脚本'按钮单独保存。"
                      placement="right"
                    >
                      <QuestionCircleOutlined style={{ color: '#8c8c8c', cursor: 'help' }} />
                    </Tooltip>
                  </div>
                }
              >
                <div className={styles.preScriptEditor}>
                  <div className={styles.editorHeader}>
                    <span className={styles.editorTitle}>预发脚本编辑器</span>
                    <span className={styles.editorTip}>
                      编辑预发脚本内容，点击"执行测试"时会先保存脚本再执行测试
                    </span>
                  </div>
                  <CodeEditor
                    value={preScript}
                    onChange={setPreScript}
                    placeholder="请输入预发环境的 Groovy 脚本内容..."
                    height={350}
                  />
                </div>
              </Form.Item>
            )}

            <Form.Item
              label={
                <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                  <span>测试上下文（JSON格式）</span>
                  <Tooltip
                    title="请提供测试上下文数据（JSON格式），脚本将基于这些数据进行执行。测试上下文会自动从脚本配置中加载，您也可以手动修改用于测试。"
                    placement="right"
                  >
                    <QuestionCircleOutlined style={{ color: '#8c8c8c', cursor: 'help' }} />
                  </Tooltip>
                </div>
              }
            >
              <CodeEditor
                value={testContext}
                onChange={setTestContext}
                placeholder="请输入测试上下文数据，JSON格式..."
                height={200}
              />
            </Form.Item>
          </Form>
        </div>
      </div>
    </Modal>
  );
};

export default TestModal;
