import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Form,
  Space,
  message,
  Tag,
  DatePicker,
  Tooltip,
} from 'antd';
import { SearchOutlined, ReloadOutlined, RedoOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { queryLabelCheckResults, retryScriptExecutionById } from '@/services/api';
import styles from '../checkResults.less';

const { Option } = Select;
const { RangePicker } = DatePicker;

// 日期格式化工具函数
const formatDateTime = (text) => {
  if (!text) return '-';
  const date = new Date(text);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 获取当天的开始和结束时间
const getTodayRange = () => {
  const startOfDay = dayjs().startOf('day');
  const endOfDay = dayjs().endOf('day');
  return [startOfDay, endOfDay];
};

// 格式化时间为API需要的格式
const formatTimeForApi = (date) => {
  if (!date) return null;
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

const CheckResultsContent = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [retryLoading, setRetryLoading] = useState({});
  const [dataSource, setDataSource] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 25,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    pageSizeOptions: ['10', '25', '50', '100'],
  });
  const [searchParams, setSearchParams] = useState({});

  // 初始化表单默认值（当天时间范围）
  useEffect(() => {
    const todayRange = getTodayRange();
    form.setFieldsValue({
      timeRange: todayRange,
    });
    // 设置默认搜索参数
    setSearchParams({
      startTime: formatTimeForApi(todayRange[0]),
      endTime: formatTimeForApi(todayRange[1]),
    });
  }, [form]);

  // 获取列表数据
  const fetchData = async (params = {}) => {
    setLoading(true);
    try {
      const requestParams = {
        pageNum: params.pageNum || pagination.current,
        pageSize: params.pageSize || pagination.pageSize,
        ...searchParams,
        ...params,
      };

      const response = await queryLabelCheckResults(requestParams);
      if (response.success) {
        const { list = [], total = 0, pageNum, pageSize: size } = response.data;
        setDataSource(list);
        setPagination(prev => ({
          ...prev,
          current: pageNum,
          pageSize: size,
          total,
        }));
      } else {
        message.error(response.msg || '查询失败');
      }
    } catch (error) {
      message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 延迟执行，确保默认时间范围已设置
    const timer = setTimeout(() => {
      fetchData();
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  // 搜索
  const handleSearch = (values) => {
    const params = { ...values };

    // 处理时间范围
    if (params.timeRange && params.timeRange.length === 2) {
      params.startTime = formatTimeForApi(params.timeRange[0]);
      params.endTime = formatTimeForApi(params.timeRange[1]);
    }
    delete params.timeRange;

    setSearchParams(params);
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchData({ ...params, pageNum: 1 });
  };

  // 重置搜索
  const handleReset = () => {
    const todayRange = getTodayRange();
    form.resetFields();
    form.setFieldsValue({
      timeRange: todayRange,
    });

    const defaultParams = {
      startTime: formatTimeForApi(todayRange[0]),
      endTime: formatTimeForApi(todayRange[1]),
    };

    setSearchParams(defaultParams);
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchData({ ...defaultParams, pageNum: 1 });
  };

  // 表格分页变化
  const handleTableChange = (paginationConfig) => {
    setPagination(paginationConfig);
    fetchData({
      pageNum: paginationConfig.current,
      pageSize: paginationConfig.pageSize,
    });
  };

  // 重试脚本执行
  const handleRetry = async (recordId) => {
    setRetryLoading(prev => ({ ...prev, [recordId]: true }));
    try {
      const response = await retryScriptExecutionById(recordId);
      if (response.success) {
        message.success('重试执行成功');
        // 重新加载数据
        fetchData();
      } else {
        message.error(response.msg || '重试执行失败');
      }
    } catch (error) {
      message.error('重试执行失败');
    } finally {
      setRetryLoading(prev => ({ ...prev, [recordId]: false }));
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
    },
    {
      title: '标签代码',
      dataIndex: 'labelCode',
      width: 150,
      ellipsis: {
        showTitle: false,
      },
      render: (labelCode) => (
        <Tooltip title={labelCode} placement="topLeft">
          <span style={{ cursor: 'pointer' }}>{labelCode}</span>
        </Tooltip>
      ),
    },
    {
      title: '用户ID',
      dataIndex: 'userId',
      width: 150,
      ellipsis: {
        showTitle: false,
      },
      render: (userId) => (
        <Tooltip title={userId} placement="topLeft">
          <span style={{ cursor: 'pointer' }}>{userId}</span>
        </Tooltip>
      ),
    },
    {
      title: '结果',
      dataIndex: 'result',
      width: 200,
      render: (result) => {
        if (!result) {
          return <Tag color="default">-</Tag>;
        }

        // 如果结果文本较短，直接显示
        if (result.length <= 20) {
          return <Tag color="blue">{result}</Tag>;
        }

        // 如果结果文本较长，使用Tooltip显示完整内容
        return (
          <Tooltip title={result} placement="topLeft">
            <Tag color="blue" style={{ maxWidth: '180px', cursor: 'pointer' }}>
              <div style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}>
                {result}
              </div>
            </Tag>
          </Tooltip>
        );
      },
    },
    {
      title: '是否通过',
      dataIndex: 'pass',
      width: 100,
      render: (pass) => (
        <Tag color={pass === 1 ? 'green' : 'red'}>
          {pass === 1 ? '通过' : '未通过'}
        </Tag>
      ),
    },
    {
      title: '重试次数',
      dataIndex: 'retry',
      width: 80,
      render: (retry) => (
        <span style={{ color: retry > 0 ? '#ff7875' : '#52c41a' }}>
          {retry}
        </span>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate',
      width: 180,
      render: (text) => formatDateTime(text),
    },
    {
      title: '修改时间',
      dataIndex: 'gmtModified',
      width: 180,
      render: (text) => formatDateTime(text),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<RedoOutlined />}
            loading={retryLoading[record.id]}
            disabled={retryLoading[record.id]}
            onClick={() => handleRetry(record.id)}
          >
            重试
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <Card bordered={false}>
      {/* 搜索表单 */}
      <Form
        form={form}
        layout="inline"
        onFinish={handleSearch}
        style={{ marginBottom: 16 }}
      >
        <Form.Item name="labelCode" label="标签代码">
          <Input placeholder="请输入标签代码" allowClear style={{ width: 200 }} />
        </Form.Item>
        <Form.Item name="pass" label="是否通过">
          <Select placeholder="请选择" allowClear style={{ width: 120 }}>
            <Option value={1}>通过</Option>
            <Option value={0}>未通过</Option>
          </Select>
        </Form.Item>
        <Form.Item name="timeRange" label="时间范围">
          <RangePicker
            showTime={{
              format: 'HH:mm:ss',
            }}
            format="YYYY-MM-DD HH:mm:ss"
            placeholder={['开始时间', '结束时间']}
            style={{ width: 400 }}
          />
        </Form.Item>
        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
              查询
            </Button>
            <Button onClick={handleReset} icon={<ReloadOutlined />}>
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>

      {/* 表格 */}
      <Table
        loading={loading}
        dataSource={dataSource}
        columns={columns}
        rowKey="id"
        pagination={pagination}
        onChange={handleTableChange}
        scroll={{ x: 1210 }}
        size="middle"
      />
    </Card>
  );
};

export default CheckResultsContent;