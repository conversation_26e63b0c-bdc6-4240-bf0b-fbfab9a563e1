import React from 'react';
import Editor from '@monaco-editor/react';
import classNames from 'classnames';
import styles from '../../index.less';

const CodeEditor = ({ value, onChange, placeholder, height = 200, disabled = false }) => {
  const handleEditorChange = (val) => {
    onChange(val || '');
  };

  const handleEditorWillMount = (monaco) => {
    // 注册Groovy语言
    monaco.languages.register({ id: 'groovy' });

    // 设置Groovy语法高亮规则（基于Java语法扩展）
    monaco.languages.setMonarchTokensProvider('groovy', {
      tokenizer: {
        root: [
          // 关键字
          [/\b(def|class|interface|enum|trait|package|import|extends|implements|throws|return|if|else|while|for|do|break|continue|switch|case|default|try|catch|finally|throw|new|this|super|null|true|false|void|boolean|byte|char|short|int|long|float|double|String|Object|List|Map|Set|Collection|as|in|instanceof|assert)\b/, 'keyword'],

          // Groovy特有关键字
          [/\b(it|delegate|owner|property|metaClass|each|collect|find|findAll|grep|sort|unique|reverse|size|empty|contains|any|every|inject|with|use|mixin)\b/, 'keyword.groovy'],

          // 字符串
          [/"([^"\\]|\\.)*$/, 'string.invalid'],
          [/"/, 'string', '@string_double'],
          [/'([^'\\]|\\.)*$/, 'string.invalid'],
          [/'/, 'string', '@string_single'],
          [/"""/, 'string', '@string_triple'],
          [/\$\/.*?\/\$/, 'string.regex'],

          // 数字
          [/\d*\.\d+([eE][\-+]?\d+)?[fFdD]?/, 'number.float'],
          [/0[xX][0-9a-fA-F]+[Ll]?/, 'number.hex'],
          [/0[0-7]+[Ll]?/, 'number.octal'],
          [/\d+[lL]?/, 'number'],

          // 注释
          [/\/\*/, 'comment', '@comment'],
          [/\/\/.*$/, 'comment'],

          // 操作符
          [/[{}()\[\]]/, '@brackets'],
          [/[<>](?!@symbols)/, '@brackets'],
          [/@symbols/, 'delimiter'],

          // 标识符
          [/[a-zA-Z_$][\w$]*/, 'identifier'],
        ],

        string_double: [
          [/[^\\"$]+/, 'string'],
          [/\$\{/, 'string.interpolation', '@interpolation'],
          [/\$[a-zA-Z_]\w*/, 'string.interpolation'],
          [/\\./, 'string.escape'],
          [/"/, 'string', '@pop']
        ],

        string_single: [
          [/[^\\']+/, 'string'],
          [/\\./, 'string.escape'],
          [/'/, 'string', '@pop']
        ],

        string_triple: [
          [/[^"]+/, 'string'],
          [/"""/, 'string', '@pop'],
          [/"/, 'string']
        ],

        interpolation: [
          [/[^}]+/, 'string.interpolation'],
          [/}/, 'string.interpolation', '@pop']
        ],

        comment: [
          [/[^\/*]+/, 'comment'],
          [/\*\//, 'comment', '@pop'],
          [/[\/*]/, 'comment']
        ],
      },

      symbols: /[=><!~?:&|+\-*\/\^%]+/,
    });

    // 设置语言配置
    monaco.languages.setLanguageConfiguration('groovy', {
      comments: {
        lineComment: '//',
        blockComment: ['/*', '*/']
      },
      brackets: [
        ['{', '}'],
        ['[', ']'],
        ['(', ')']
      ],
      autoClosingPairs: [
        { open: '{', close: '}' },
        { open: '[', close: ']' },
        { open: '(', close: ')' },
        { open: '"', close: '"' },
        { open: "'", close: "'" }
      ],
      surroundingPairs: [
        { open: '{', close: '}' },
        { open: '[', close: ']' },
        { open: '(', close: ')' },
        { open: '"', close: '"' },
        { open: "'", close: "'" }
      ]
    });
  };

  const editorOptions = {
    selectOnLineNumbers: true,
    roundedSelection: false,
    readOnly: disabled,
    cursorStyle: 'line',
    automaticLayout: true,
    minimap: { enabled: false },
    scrollBeyondLastLine: false,
    fontSize: 13,
    lineHeight: 20,
    wordWrap: 'on',
    folding: true,
    lineNumbers: 'on',
    glyphMargin: false,
    lineDecorationsWidth: 0,
    lineNumbersMinChars: 3,
    renderLineHighlight: 'line',
    contextmenu: true,
    mouseWheelZoom: true,
    tabSize: 2,
    insertSpaces: true,
  };

  return (
    <div className={classNames(styles.codeEditor, { [styles.disabled]: disabled })}>
      <Editor
        height={height}
        language="groovy"
        theme="vs"
        value={value}
        onChange={handleEditorChange}
        options={editorOptions}
        beforeMount={handleEditorWillMount}
        loading={<div style={{ padding: '20px', textAlign: 'center' }}>加载编辑器中...</div>}
      />
      {!value && placeholder && (
        <div
          style={{
            position: 'absolute',
            top: '20px',
            left: '60px',
            color: '#999',
            pointerEvents: 'none',
            fontSize: '13px',
            zIndex: 1,
          }}
        >
          {placeholder}
        </div>
      )}
    </div>
  );
};

export default CodeEditor;
