import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Switch,
  Tabs,
  message,
  Button,
  Space,
  InputNumber,
} from 'antd';
import { createOrUpdateLabelCheckScript, getLabelCheckScript } from '@/services/api';
import CodeEditor from '../CodeEditor';
import styles from '../../index.less';

const { TabPane } = Tabs;
const { TextArea } = Input;

// 将采样率转换为中文说明
const getSampleRateDescription = (rate) => {
  if (!rate || rate <= 0) return '';

  if (rate >= 1) return '全量';
  if (rate >= 0.5) return '一半';
  if (rate >= 0.1) return '十分之' + Math.round(rate * 10);
  if (rate >= 0.01) return '百分之' + Math.round(rate * 100);
  if (rate >= 0.001) return '千分之' + Math.round(rate * 1000);
  if (rate >= 0.0001) return '万分之' + Math.round(rate * 10000);
  if (rate >= 0.00001) return '十万分之' + Math.round(rate * 100000);
  if (rate >= 0.000001) return '百万分之' + Math.round(rate * 1000000);

  return '极小比例';
};

const CreateOrEditModal = ({ visible, onCancel, onSuccess, record, title }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [preScript, setPreScript] = useState('');
  const [onlineScript, setOnlineScript] = useState('');
  const [sampleRateValue, setSampleRateValue] = useState(1);

  useEffect(() => {
    if (visible && record) {
      // 编辑模式，获取详细信息
      fetchScriptDetail();
    } else if (visible && !record) {
      // 新增模式，重置表单
      form.resetFields();
      setPreScript('');
      setOnlineScript('');
      setSampleRateValue(1);
    }
  }, [visible, record]);

  const fetchScriptDetail = async () => {
    try {
      const response = await getLabelCheckScript({ labelCode: record.labelCode });
      if (response.success) {
        const data = response.data;
        form.setFieldsValue({
          labelCode: data.labelCode,
          enable: data.enable === 1,
          sampleRate: data.sampleRate,
        });
        setPreScript(data.preScript || '');
        setOnlineScript(data.onlineScript || '');
        setSampleRateValue(data.sampleRate || 1);
      } else {
        message.error(response.msg || '获取脚本详情失败');
      }
    } catch (error) {
      message.error('获取脚本详情失败');
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const requestData = {
        labelCode: values.labelCode,
        preScript,
        onlineScript,
        enable: values.enable,
        sampleRate: values.sampleRate,
      };

      const response = await createOrUpdateLabelCheckScript(requestData);
      if (response.success) {
        message.success(`${record ? '更新' : '创建'}成功`);
        onSuccess();
      } else {
        message.error(response.msg || `${record ? '更新' : '创建'}失败`);
      }
    } catch (error) {
      if (error.errorFields) {
        message.error('请检查表单输入');
      } else {
        message.error(`${record ? '更新' : '创建'}失败`);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setPreScript('');
    setOnlineScript('');
    setSampleRateValue(1);
    onCancel();
  };

  return (
    <Modal
      title={title}
      visible={visible}
      onCancel={handleCancel}
      width={800}
      footer={
        <Space>
          <Button onClick={handleCancel}>取消</Button>
          <Button type="primary" loading={loading} onClick={handleSubmit}>
            确定
          </Button>
        </Space>
      }
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          enable: true,
          sampleRate: 1,
        }}
      >
        <Form.Item
          name="labelCode"
          label="标签代码"
          rules={[
            { required: true, message: '请输入标签代码' },
            { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '标签代码必须以字母开头，只能包含字母、数字和下划线' },
          ]}
        >
          <Input
            placeholder="请输入标签代码"
            disabled={!!record}
            maxLength={100}
          />
        </Form.Item>

        <Form.Item
          name="sampleRate"
          label="采样率"
          rules={[
            { required: true, message: '请输入采样率' },
            { type: 'number', min: 0.000001, max: 1, message: '采样率必须在0.000001-1之间' },
          ]}
          extra={
            <div>
              <div>采样率范围：0.000001-1，支持最多6位小数，1表示全量</div>
              {sampleRateValue && (
                <div style={{ color: '#1890ff', marginTop: 4 }}>
                  当前比例：{getSampleRateDescription(sampleRateValue)}
                </div>
              )}
            </div>
          }
        >
          <InputNumber
            placeholder="请输入采样率"
            min={0.000001}
            max={1}
            precision={6}
            step={0.1}
            style={{ width: '100%' }}
            onChange={(value) => setSampleRateValue(value)}
          />
        </Form.Item>

        <Form.Item
          name="enable"
          label="是否启用"
          valuePropName="checked"
        >
          <Switch checkedChildren="启用" unCheckedChildren="禁用" />
        </Form.Item>

        <Form.Item label="脚本配置">
          <Tabs defaultActiveKey="online" className={styles.environmentTabs}>
            <TabPane tab="线上脚本" key="online">
              <div style={{ marginBottom: 8 }}>
                <span style={{ color: '#666', fontSize: 12 }}>
                  线上环境使用的脚本内容，支持 Groovy 语法
                </span>
              </div>
              <CodeEditor
                value={onlineScript}
                onChange={setOnlineScript}
                placeholder="请输入线上环境的 Groovy 脚本内容..."
                height={300}
              />
            </TabPane>
            <TabPane tab="预发脚本" key="pre">
              <div style={{ marginBottom: 8 }}>
                <span style={{ color: '#666', fontSize: 12 }}>
                  预发环境使用的脚本内容，如果为空则使用线上脚本
                </span>
              </div>
              <CodeEditor
                value={preScript}
                onChange={setPreScript}
                placeholder="请输入预发环境的 Groovy 脚本内容（可选）..."
                height={300}
              />
            </TabPane>
          </Tabs>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateOrEditModal;
