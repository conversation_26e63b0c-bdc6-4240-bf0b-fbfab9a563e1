import React, { useState } from 'react';
import { Card, Tabs } from 'antd';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import LabelConfigContent from './components/LabelConfigContent';
import CheckResultsContent from './components/CheckResultsContent';

const { TabPane } = Tabs;

const LabelReconciliation = () => {
  const [activeTab, setActiveTab] = useState('labelConfig');

  const handleTabChange = (key) => {
    setActiveTab(key);
  };

    return (
    <PageHeaderWrapper title="标签对账">
      <Card bordered={false}>
        <Tabs
          activeKey={activeTab}
          onChange={handleTabChange}
          size="large"
          tabBarStyle={{ marginBottom: 0 }}
        >
          <TabPane tab="标签配置" key="labelConfig">
            <div style={{ marginTop: 16 }}>
              <LabelConfigContent />
            </div>
          </TabPane>
          <TabPane tab="对账结果" key="checkResults">
            <div style={{ marginTop: 16 }}>
              <CheckResultsContent />
            </div>
          </TabPane>
          <TabPane tab="有结果率" key="resultRate">
            <div style={{ marginTop: 16, height: 'calc(100vh - 200px)' }}>
              <iframe
                src="https://x.alibaba-inc.com/custom/59/product/preview/spm/4222"
                style={{
                  width: '100%',
                  height: '100%',
                  border: 'none',
                  borderRadius: '6px',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
                }}
                title="RTUS请求有结果率监控"
              />
            </div>
          </TabPane>
        </Tabs>
      </Card>
    </PageHeaderWrapper>
  );
};

export default LabelReconciliation;
