import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import {
  Card,
  Input,
  Select,
  Tabs,
  Button,
  Row,
  Col,
  Tree,
  Empty,
  Table,
  message,
  Form,
  Tooltip,
  Tag,
  Spin,
} from 'antd';
import styles from './index.less';
import { useState, useEffect } from 'react';
import { Link } from 'umi';
import {
  EditOutlined,
  QuestionCircleOutlined,
  ExportOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';
import FormModal from './FormModal';
import SearchForm from './SearchForm';
import MarkingModal from './MarkingModal';
import { queryNewLabelList, queryBuildTree, updateConfig, isSuperAdmin } from '@/services/api';
import dayjs from 'dayjs';
import {
  TimeTypeEnum,
  LabelTypeEnum,
  LabelStatusEnum,
  ProfileCodeEnum,
  LabelTagEnumMapping,
} from './enum';
import get from 'lodash/get';
import { getBpmsUrl } from '@/utils/utils';
import { history } from 'umi';
import { transformTree } from '@/utils/utils'

const { Option } = Select;

const FORM_ITEM_LAYOUT = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const getUniqueNickNames = text => {
  if (!text || text.length === 0) return '';

  const uniqueNickNames = new Set(
    text.map(item => item.nickName).filter(name => name !== null && name !== undefined)
  );

  return Array.from(uniqueNickNames).join(', ');
};

const LabelManagement = () => {
  const [treeData, setTreeData] = useState([]);
  const [formVisible, setFormVisible] = useState(false);
  const [visible, setVisible] = useState(false);
  const [type, setType] = useState('publicScope');
  const [dataSource, setDataSource] = useState([]);
  const [tabCount, setTabCount] = useState({});
  const [tabKey, setTabKey] = useState('ALL');
  const [loading, setLoading] = useState(false);
  const [categoryId, setCategoryId] = useState('');
  const [tagConfig, setTagConfig] = useState({
    isOfficial: false,
    openHotTag: false,
  });
  const [record, setRecord] = useState({});
  const [searchParams, setSearchParams] = useState({
    pageNo: 1,
    pageSize: 10,
  });
  const [expandedKeys, setExpandedKeys] = useState(['-1']);
  const [isFold, setIsFold] = useState(false);
  const [isSuperAdminState, setIsSuperAdminState] = useState(false);

  const [modelForm] = Form.useForm();

    useEffect(() => {
    // 检查是否是页面刷新
    const isPageRefresh = () => {
      if (window.performance && window.performance.navigation) {
        return window.performance.navigation.type === 1;
      }
      return false;
    };

    // 从 localStorage 加载保存的筛选条件
    const loadSavedSearchParams = () => {
      try {
        const saved = localStorage.getItem('labelManagement_searchParams');
        return saved ? JSON.parse(saved) : {};
      } catch (error) {
        console.error('加载保存的筛选条件失败:', error);
        return {};
      }
    };

    Promise.all([
      queryBuildTree({ bizEntityName: 'TAOBAO_USER' }),
      isSuperAdmin()
    ]).then(
      ([res1, res3]) => {
        setTreeData([
          {
            id: -1,
            name: '全部标签',
            children: get(res1, 'data.root.children', []),
          },
        ]);
        setIsSuperAdminState(res3.data);

        // 如果是页面刷新，清除保存的筛选条件并使用默认查询
        if (isPageRefresh()) {
          try {
            localStorage.removeItem('labelManagement_searchParams');
          } catch (error) {
            console.error('清除筛选条件失败:', error);
          }
          fetchData({});
          return;
        }

        // 尝试恢复保存的筛选条件
        const savedParams = loadSavedSearchParams();
        if (Object.keys(savedParams).length > 0) {
          const savedTabKey = savedParams.classifyTab || savedParams.tabKey || 'ALL';

          // 恢复 tab 状态
          setTabKey(savedTabKey);

          // 如果有分类信息，恢复分类选中状态
          if (savedParams.categoryId) {
            setCategoryId(savedParams.categoryId);
          }

          // 使用保存的参数进行查询
          fetchData({
            ...savedParams,
            classifyTab: savedTabKey,
            pageNo: savedParams.pageNo || 1,
            pageSize: savedParams.pageSize || 10,
          });
        } else {
          // 没有保存的条件，使用默认查询
          fetchData({});
        }
      }
    );
  }, []);

  // 是否存在树的数据
  const isExistTreeData = treeData && treeData.length > 0;

  const columns = [
    {
      title: '标签id',
      dataIndex: 'id',
      fixed: 'left',
    },
    {
      title: '标签code',
      dataIndex: 'code',
    },
    {
      title: '标签名称',
      dataIndex: 'name',
    },
    {
      title: '标签描述',
      dataIndex: 'description',
      width: 150,
      render: (text, record) => {
        return (
          <Tooltip title={text && text.length > 9 ? text : ''}>
            <div className={styles.description}>{text}</div>
          </Tooltip>
        );
      },
    },
    {
      title: () => {
        return (
          <div className={styles.columnTitle}>
            公开范围
            <Tooltip
              title={
                <span>
                  公开标签：按照“安全等级”字段，控制全员是否可用
                  <br />
                  私有标签：仅限“标签负责人/管理员”和“标签分享者”进行使用
                </span>
              }
            >
              <QuestionCircleOutlined />
            </Tooltip>
          </div>
        );
      },
      dataIndex: 'openScope',
      render: (text, record) => {
        return (
          text && (
            <Tooltip
              title={
                !record.editPermission ||
                record.status === 'APPROVING' ||
                record.status === 'APPROVE_FAILED'
                  ? '无操作权限'
                  : ''
              }
            >
              <div
                onClick={() => {
                  if (
                    !record.editPermission ||
                    record.sttaus === 'APPROVING' ||
                    record.status === 'APPROVE_FAILED'
                  ) {
                    return;
                  }
                  setFormVisible(true);
                  setRecord(record);
                  modelForm.setFieldsValue({ openScope: text, sharers: record.sharers });
                }}
                className={styles.columnEdit}
                style={{ cursor: record.editPermission ? 'pointer' : 'not-allowed' }}
              >
                {text === 'PUBLIC' ? '公开标签' : '私有标签'}
                <EditOutlined twoToneColor={'#000'} />
              </div>
            </Tooltip>
          )
        );
      },
    },
    {
      title: () => {
        return (
          <div className={styles.columnTitle}>
            打标
            <Tooltip
              title={
                <span>
                  官方：由诸葛平台官方出品和维护的标签
                  <br />
                  热门：根据标签圈人、导出、分析和查询使用综合得出的高频使用标签
                </span>
              }
            >
              <QuestionCircleOutlined />
            </Tooltip>
          </div>
        );
      },
      dataIndex: 'tagConfig',
      render: (text, record) => {
        return (
          <Tooltip
            title={
              !record.editPermission ||
              record.status === 'APPROVING' ||
              record.status === 'APPROVE_FAILED'
                ? '无操作权限'
                : ''
            }
          >
            <div
              onClick={() => {
                if (
                  !record.editPermission ||
                  record.status === 'APPROVING' ||
                  record.status === 'APPROVE_FAILED'
                )
                  return;
                setVisible(true);
                setTagConfig(text);
                setRecord(record);
              }}
              className={styles.columnEdit}
              style={{ cursor: record.editPermission ? 'pointer' : 'not-allowed' }}
            >
              {(record.tagList || []).map(item => {
                if (item === null || item === 'GOOD' || item === 'OUTPUT_LATE') {
                  return;
                }

                if (item === 'BLACK_LIST') {
                  return '黑名单';
                }

                const { color, name, textColor } = LabelTagEnumMapping[item];
                return (
                  <Tag color={color} style={{ color: textColor }}>
                    {name}
                  </Tag>
                );
              })}
              <EditOutlined twoToneColor={'#000'} />
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '用户主键',
      dataIndex: 'profileCode',
      render: text => {
        return ProfileCodeEnum[text] || text;
      },
    },
    {
      title: '标签时效',
      dataIndex: 'timeType',
      render: text => {
        return TimeTypeEnum[text] || text;
      },
    },
    {
      title: '标签类型',
      dataIndex: 'labelType',
      render: text => {
        return LabelTypeEnum[text] || text;
      },
    },
    {
      title: '标签状态',
      dataIndex: 'status',
      render: (text, record) => {
        return (
          <Tooltip title={!record.editPermission || text === 'APPROVE_FAILED' || text === 'ONLINE_ING' ? '无操作权限' : ''}>
            <div
              onClick={() => {
                if (text === 'APPROVING') {
                  window.open(getBpmsUrl(record.approveId));
                }
                if (!record.editPermission || text === 'APPROVING' || text === 'APPROVE_FAILED' || text === 'ONLINE_ING')
                  return;
                setFormVisible(true);
                setType('status');
                if (
                  text === 'WAIT_ONLINE' ||
                  text === 'APPROVING' ||
                  text === 'ONLINE_ING' ||
                  text === 'APPROVE_FAILED'
                ) {
                  modelForm.setFieldsValue({ status: LabelStatusEnum[text] });
                } else {
                  modelForm.setFieldsValue({ status: text });
                }
                setRecord(record);
              }}
              className={styles.columnEdit}
              style={{
                cursor: !record.editPermission || text === 'APPROVING' || text === 'APPROVE_FAILED' || text === 'ONLINE_ING' ? 'not-allowed' : 'pointer',
              }}
            >
              {LabelStatusEnum[text] || text}
              {text === 'APPROVING' ? <ExportOutlined /> : <EditOutlined twoToneColor={'#000'} />}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '健康分',
      dataIndex: 'healthScore',
    },
    {
      title: () => {
        return (
          <div className={styles.columnTitle}>
            负责人
            <Tooltip title="标签的唯一负责人">
              <QuestionCircleOutlined />
            </Tooltip>
          </div>
        );
      },
      dataIndex: 'owner',
      render: (text, record) => {
        return (
          <Tooltip
            title={
              !record.managePermission ||
              record.status === 'APPROVING' ||
              record.status === 'APPROVE_FAILED'
                ? '无操作权限'
                : ''
            }
          >
            <div
              onClick={() => {
                if (
                  !record.managePermission ||
                  record.status === 'APPROVING' ||
                  record.status === 'APPROVE_FAILED'
                )
                  return;
                setFormVisible(true);
                setType('owner');
                setRecord(record);
                modelForm.setFieldsValue({ owner: text });
              }}
              className={styles.columnEdit}
              style={{ cursor: record.managePermission ? 'pointer' : 'not-allowed' }}
            >
              {text?.nickName ?? ''}
              <EditOutlined twoToneColor={'#000'} />
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: () => {
        return (
          <div className={styles.columnTitle}>
            管理员
            <Tooltip title="标签的代理负责人，拥有和负责人相同的标签编辑权限（唯独不含修改“负责人”信息）">
              <QuestionCircleOutlined />
            </Tooltip>
          </div>
        );
      },
      dataIndex: 'managers',
      render: (text, record) => {
        return (
          <Tooltip
            title={
              !record.editPermission ||
              record.status === 'APPROVING' ||
              record.status === 'APPROVE_FAILED'
                ? '无操作权限'
                : ''
            }
          >
            <div
              onClick={() => {
                if (
                  !record.editPermission ||
                  record.status === 'APPROVING' ||
                  record.status === 'APPROVE_FAILED'
                )
                  return;
                setFormVisible(true);
                setType('managers');
                setRecord(record);
                modelForm.setFieldsValue({ managers: text });
              }}
              className={styles.columnEdit}
              style={{ cursor: record.editPermission ? 'pointer' : 'not-allowed' }}
            >
              {getUniqueNickNames(text)}
              <EditOutlined twoToneColor={'#000'} />
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '修改人',
      dataIndex: 'modifier',
      render: text => {
        return text?.nickName ?? text?.empId ?? '';
      },
    },
    {
      title: '上次修改时间',
      dataIndex: 'gmtModified',
      render: text => {
        return <span>{dayjs(text).format('YYYY-MM-DD HH:mm:ss')}</span>;
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
      render: (text, record) => {
        return (
          <>
            <Button
              onClick={() => {
                if (record.source === 'API') {
                  history.push(`/develop-tool/create-crowd-tag/TAOBAO_USER?id=${record.id}`);
                } else {
                  history.push(`/develop-tool/label-management/detail?id=${record.id}&type=view`);
                }
              }}
              type="link"
            >
              预览
            </Button>
            <Tooltip
              title={!record.editPermission || record.status === 'APPROVING' ? '无操作权限' : ''}
            >
              <Button
                disabled={!record.editPermission || record.status === 'APPROVING'}
                type="link"
                onClick={() => {
                  if (record.source === 'API') {
                    history.push(`/develop-tool/create-crowd-tag/TAOBAO_USER?id=${record.id}`);
                  } else {
                    history.push(
                      `/develop-tool/label-management/detail?id=${record.id}&type=update`
                    );
                  }
                }}
              >
                编辑
              </Button>
            </Tooltip>
          </>
        );
      },
    },
  ];

  const onSelect = (node, info) => {
    const {
      node: { key, children },
      selected,
    } = info;
    if (!key || (children && children.length > 0)) return;
    let categoryIdNew = selected ? key : '';

    // 保存分类选择状态到 localStorage
    try {
      const saved = localStorage.getItem('labelManagement_searchParams');
      const params = saved ? JSON.parse(saved) : {};
      params.categoryId = categoryIdNew;
      params.pageNo = 1; // 切换分类时重置页码
      localStorage.setItem('labelManagement_searchParams', JSON.stringify(params));
    } catch (error) {
      console.error('保存分类选择状态失败:', error);
    }

    setCategoryId(categoryIdNew);
    fetchData({ ...searchParams, categoryIdNew, pageNo: 1 });
  };

  let classifyTabList = [
    {
      label: '全部',
      value: 'ALL',
    },
    {
      label: '我有权限',
      value: 'AUTHORIZED',
    },
  ];

  const fetchData = params => {
    setLoading(true);
    setDataSource([]);
    setTabCount({});
    const data = {
      pageNo: params.pageNo ?? 1,
      pageSize: params.pageSize ?? 10,
      classifyTab: params.classifyTab ?? 'ALL',
      ...params,
    };
    if (!data.statusList) {
      data['statusList'] = ['ACTIVATE', 'WAIT_ONLINE', 'APPROVING', 'ONLINE_ING'];
    }
    if (!data.bizRegionList) {
      data['bizRegionList'] = ['PUBLIC_REGION'];
    }

    queryNewLabelList(data).then(res => {
      if (res.success) {
        setSearchParams(data);
        setDataSource(res.data.labelInfoList ?? []);
        setTabCount(res.data.tabCntMap);
        setLoading(false);
      } else {
        message.error(res.msg || '列表获取失败，请重试!');
      }
    });
  };

  const handleSearchForm = values => {
    if (values.isReset) {
      setCategoryId('');
      setTabKey('ALL'); // 重置时也重置tab
    }
    fetchData(values.isReset ? { pageNo: 1 } : { ...searchParams, ...values, pageNo: 1 });
  };

  const handleSetConfig = (params, callBack) => {
    let data = {};
    let messageText = '';
    if (record.id) {
      data['id'] = record.id;
    }
    if (params === 'tag') {
      data['tagConfig'] = tagConfig;
      messageText = '打标配置';
    }
    if (type === 'publicScope') {
      const value = modelForm.getFieldsValue();
      data['openScope'] = value.openScope;
      data['sharers'] = value.sharers;
      messageText = '公开范围';
    }
    if (type === 'status') {
      const value = modelForm.getFieldsValue();
      data['status'] = value.status;
      messageText = '标签状态';
    }
    if (type === 'owner') {
      const value = modelForm.getFieldsValue();
      data['owner'] = value.owner;
      messageText = '负责人';
    }
    if (type === 'managers') {
      const value = modelForm.getFieldsValue();
      data['managers'] = value.managers;
      messageText = '管理员';
    }
    updateConfig(data).then(res => {
      if (res.success) {
        message.success('编辑' + messageText + '成功');
        setVisible(false);
        fetchData(searchParams);
        setFormVisible(false);
        data = {};
        messageText = '';
        modelForm.resetFields();
        //修改状态二次确认弹窗
        if (callBack) {
          callBack();
        }
      } else {
        message.error(res.msg ?? '编辑' + messageText + '失败');
      }
    });
  };

  const onExpand = expandedKeys => {
    setExpandedKeys(expandedKeys);
  };

  const onFold = () => {
    setIsFold(true);
  };

  const onUnFold = () => {
    setIsFold(false);
  };

  return (
    <PageHeaderWrapper title="标签管理" toDocumentLink="https://aliyuque.antfin.com/qnwrq9/kzl68s/ks23g48klvwvbdnh?singleDoc#">
      <div className={styles.labelManagementPage}>
        <Card bordered={false} className={styles.searchCard}>
          <SearchForm onSearchSubmit={handleSearchForm} tabKey={tabKey} />
        </Card>
        <Spin tip="数据加载中..." spinning={loading}>
          <Card>
            <Tabs
              tabBarExtraContent={
                <Link to="/develop-tool/label-management/detail">
                  <Button type="primary">新增标签</Button>
                </Link>
              }
              activeKey={tabKey}
              onChange={key => {
                // 保存 tabKey 到 localStorage，切换 tab 时重置为第1页
                try {
                  const saved = localStorage.getItem('labelManagement_searchParams');
                  const params = saved ? JSON.parse(saved) : {};
                  params.classifyTab = key;
                  params.tabKey = key;
                  params.pageNo = 1; // 切换 tab 时重置页码
                  localStorage.setItem('labelManagement_searchParams', JSON.stringify(params));
                } catch (error) {
                  console.error('保存 tabKey 失败:', error);
                }

                setTabKey(key);
                fetchData({ ...searchParams, classifyTab: key, pageNo: 1 });
              }}
            >
              {classifyTabList.map(ele => (
                <Tabs.TabPane
                  tab={`${ele?.label}（${tabCount[ele?.value] ?? 0}）`}
                  key={ele?.value}
                />
              ))}
            </Tabs>
            <Row>
              {isExistTreeData ? (
                <Col
                  span={isFold ? 1 : 4}
                  style={{
                    transition: 'all 0.2s',
                  }}
                >
                  <div className={styles.fold}>
                    {isFold ? (
                      <MenuUnfoldOutlined className={styles.foldIcon} onClick={onUnFold} />
                    ) : (
                      <MenuFoldOutlined className={styles.foldIcon} onClick={onFold} />
                    )}
                  </div>
                  <Tree
                    treeData={transformTree(treeData)}
                    style={{ display: isFold ? 'none' : 'block' }}
                    onSelect={onSelect}
                    onExpand={onExpand}
                    expandedKeys={expandedKeys}
                    selectedKeys={[categoryId]}
                  />
                </Col>
              ) : (
                <Col span={5}></Col>
              )}
              <Col span={isFold ? 23 : 20} style={{ transition: 'all 0.2s' }}>
                <Table
                  dataSource={dataSource}
                  scroll={{ x: 'max-content' }}
                  rowKey={'id'}
                  columns={columns}
                  pagination={{
                    showSizeChanger: true,
                    showQuickJumper: true,
                    current: searchParams.pageNo ?? 1,
                    pageSize: searchParams.pageSize ?? 10,
                    total: tabCount[tabKey],
                    onChange: (pageNo, pageSize) => {
                      // 保存分页状态到 localStorage
                      try {
                        const saved = localStorage.getItem('labelManagement_searchParams');
                        const params = saved ? JSON.parse(saved) : {};
                        params.pageNo = pageNo;
                        params.pageSize = pageSize;
                        localStorage.setItem('labelManagement_searchParams', JSON.stringify(params));
                      } catch (error) {
                        console.error('保存分页状态失败:', error);
                      }

                      fetchData({ ...searchParams, pageNo, pageSize });
                    },
                  }}
                />
              </Col>
            </Row>
          </Card>
        </Spin>
      </div>
      <FormModal
        type={type}
        visible={formVisible}
        onCancel={() => {
          setFormVisible(false);
          setType('publicScope');
          setRecord({});
        }}
        modelForm={modelForm}
        onOk={handleSetConfig}
        record={record}
      />
      <MarkingModal
        onOk={() => handleSetConfig('tag')}
        visible={visible}
        tagConfig={tagConfig}
        setTagConfig={setTagConfig}
        record={record}
        isSuperAdmin={isSuperAdminState}
        onCancel={() => {
          setVisible(false);
          setTagConfig({
            isOfficial: false,
            openHotTag: false,
          });
          setRecord({});
        }}
      />
    </PageHeaderWrapper>
  );
};

export default LabelManagement;
