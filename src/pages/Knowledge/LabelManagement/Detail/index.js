import React, { useState, useEffect } from 'react';
import { Steps, Card, Radio, Button, Tooltip, Form, Select, Modal, Spin, message } from 'antd';
import { QuestionCircleOutlined, InfoCircleTwoTone, ExclamationCircleOutlined } from '@ant-design/icons';
import style from './index.less';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { Link } from 'umi';
import { debounce } from 'lodash';
import { parseOdpsTableName } from '@/utils/utils';
import {
  queryColumnInfos,
  queryDwsTableWithDesc,
  queryBuildTree,
  queryLabelDetail,
  createLabel,
  updateLabel,
  queryLabelEnumValueObj,
  checkOdpsAuth,
  sqlCheck,
  checkCrowdCircleSqlAuth,
} from '@/services/api';
import get from 'lodash/get';
import { getUrlParams } from '@/pages/LabelMarket/common/utils';
import { getParentKey, getTreeLevel } from '@/utils/tree';
import { getBpmsUrl } from '@/utils/utils';
import { connect } from 'dva';
import CreateTagEnum from '../components/CreateTagEnum';
import BasicConfig from '../components/BasicConfig';
import DataSourceConfig from '../components/DataSourceConfig';
import LabelConfig from '../components/LabelConfig';
import ValueConfig from '../components/ValueConfig';

const { Option } = Select;

function convertObjectToFormattedString(obj) {
  if (!obj || typeof obj !== 'object' || Object.keys(obj).length === 0) {
    return '';
  }

  const entries = Object.entries(obj);
  const formattedPairs = entries.map(([key, value]) => `${key}:${value}`);

  return formattedPairs.join('\n');
}

let items = [
  {
    title: '填写基础信息',
    description: '',
  },
  {
    title: '配置标签信息',
    description: '',
  },
  {
    title: '提交审批',
    description: '',
  },
];

const getCategoryInitialValue = (value, treeData) => {
  if (!value) {
    return undefined;
  }

  const ret = [value];
  let curKey = value;
  const level = getTreeLevel(0, treeData);
  for (let i = level - 1; i > 0; i--) {
    curKey = getParentKey(curKey, treeData);
    if (curKey) {
      ret.unshift(parseInt(curKey, 10));
    }
  }

  return ret;
};

function convertStringToObject(input) {
  if (!input) {
    return null;
  }

  const regex = /^(?!.*[ \t])([^\n:]+:[^\n:]+)(\n(?!.*[ \t])([^\n:]+:[^\n:]+))*$/;

  if (!regex.test(input)) {
    return {
      type: 'error',
      message: '存在非法字符',
    };
  }

  const parts = input.split(':');

  if (parts.length - 1 > 20) {
    return {
      type: 'error',
      message: '该方式适用于枚举值较少的场景，不得超过20个',
    };
  }

  const result = {};
  const pairs = input.split('\n');

  for (const pair of pairs) {
    const [key, value] = pair.split(':').map(s => s.trim());
    if (key && value) {
      result[key] = value;
    }
  }

  return result;
}

const Detail = props => {
  const { currentUser, isSuperAdmin } = props;
  const [current, setCurrent] = useState(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [odpsLoading, setOdpsLoading] = useState(false);
  const [columnDataSource, setColumnDataSource] = useState([]);
  const [notAuthOdpsConfig, setNotAuthOdpsConfig] = useState({
    partitionOptions: [],
    primaryKeyOptions: [],
    loading: false,
  });
  const [treeData, setTreeData] = useState([]);
  const [quickTemplateOpen, setQuickTemplateOpen] = useState(false);
  const [quickTemplateValue, setQuickTemplateValue] = useState('1:是 0:否');
  const [sqlParams, setSqlParams] = useState([]);
  const [labelEnumMapping, setLabelEnumMapping] = useState({});
  const [needApprovalConfig, setNeedApprovalConfig] = useState({
    needApproval: true,
    id: '',
  });
  const [checkLoading, setCheckLoading] = useState(false);
  const [sqlCheckLoading, setSqlCheckLoading] = useState(false);
  const [enumMappingVisible, setEnumMappingVisible] = useState(false);
  const [odpsIsAuth, setOdpsIsAuth] = useState(false);
  const [authCheckPassed, setAuthCheckPassed] = useState(false);
  const { id, type } = getUrlParams();
  const [isBehaviorLabel, setIsBehaviorLabel] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [slaModalVisible, setSlaModalVisible] = useState(false);
  const [pendingSubmitData, setPendingSubmitData] = useState(null);
  const [showL1Warning, setShowL1Warning] = useState(false);

  const [form] = Form.useForm();

  useEffect(() => {
    queryLabelEnumValueObj({ deleted: 0 }).then(res => {
      if (res.success && res.data) {
        setLabelEnumMapping(res.data);
      }
    });
  }, []);

  useEffect(() => {
    if (currentUser && !id) {
      form.setFieldsValue({
        owner: { empId: currentUser.workId, nickName: currentUser.name },
      });
    }
  }, [currentUser, id]);

  useEffect(() => {
    const fetchDetailsAndTree = async () => {
      if (id) {
        setSubmitLoading(true);
      }

      Promise.all([
        id ? queryLabelDetail(id) : Promise.resolve(null),
        queryBuildTree({ bizEntityName: 'TAOBAO_USER' }),
      ])
        .then(([detailRes, treeRes]) => {
          const treeDatas = get(treeRes, 'data.root.children', []);

          setTreeData(treeDatas);

          //详情
          if (detailRes && detailRes.data) {
            const data = {
              ...detailRes.data,
            };

            setIsBehaviorLabel(false);

            if (
              data.sourceConfig.project &&
              data.sourceConfig.table &&
              data.timeType === 'OFFLINE'
            ) {
              let odpsTable = `${data.sourceConfig.project}.${data.sourceConfig.table}`;
              handleChange(odpsTable);
              data['odpsTable'] = odpsTable;
            }

            if (data.sourceConfig.project && data.timeType === 'REALTIME') {
              let project = data.sourceConfig.project ? data.sourceConfig.project + '.' : '';
              let table = data.sourceConfig.table ?? '';
              let dataTable = `${project}${table}`;
              data['dataTable'] = dataTable;
            }

            if (
              data &&
              data.sourceConfig &&
              data.sourceConfig.sqlConfig &&
              data.sourceConfig.sqlConfig.sqlParams
            ) {
              setSqlParams(data.sourceConfig.sqlConfig.sqlParams);
            }

            if (data.sourceConfig.field && type === 'view') {
              data['selectedRowKeys'] = data.sourceConfig.field;
            } else {
              setSelectedRowKeys([
                {
                  columnName: data.sourceConfig.field,
                  columnType: data.sourceConfig.type ?? '',
                },
              ]);
            }

            if (data['expiredType'] !== 'USER_DEFINED') {
              data['ttl'] = null;
            }

            if (data['dataType'] === 'MULTI_VALUE') {
              data['constraintSep'] = 'MULTI_VALUE';
              data['dataConfig']['multiEnumSep'] = 'COMMA';
              data['dataType'] = 'ENUM';
            } else {
              data['constraintSep'] = 'SINGLE_ENUM';
            }

            if (data['dataConfig']) {
              if (data['dataConfig']['dataValue']) {
                const result = convertObjectToFormattedString(data['dataConfig']['dataValue']);
                data['dataConfig']['dataValue'] = result;
              }

              if (data['dataConfig']['kvWeightRange']) {
                data['step'] = {
                  minValue: data['dataConfig']['kvWeightRange'][0],
                  maxValue: data['dataConfig']['kvWeightRange'][1],
                  value: data['dataConfig']['kvWeightRange'][2],
                };
              }
            }

            if (data['scope']) {
              data['scopeList'] = data.scope;
            }

            if (data['ttl']) {
              data['ttl'] = Number(data['ttl'] ?? 0) / (60 * 60);
            }

            if (data['profileCode']) {
              data['accountType'] = data['profileCode'];
            }

            if (data['labelType'] === 'BEHAVIOR') {
              setIsBehaviorLabel(true);
            }

            // 设置分区字段和用户主键字段的值
            if (data.sourceConfig && data.sourceConfig.partitionField) {
              data['partitionField'] = data.sourceConfig.partitionField;
            }
            if (data.sourceConfig && data.sourceConfig.primaryKey) {
              data['primaryKey'] = data.sourceConfig.primaryKey;
            }

            // 如果是编辑模式且有完整的配置信息，设置授权状态为通过
            if (data.sourceConfig && data.sourceConfig.partitionField && data.sourceConfig.primaryKey) {
              setAuthCheckPassed(true);
              setOdpsIsAuth(true);
            }

            delete data['scope'];
            delete data['profileCode'];

            const value = getCategoryInitialValue(data.categoryIdNew, treeDatas);

            form.setFieldsValue({
              ...data,
              categoryIdNew: value,
            });

            // 如果是编辑模式且有表格数据，更新字段标识
            if (data.odpsTable && (data.partitionField || data.primaryKey)) {
              setTimeout(() => {
                updateColumnDataSource();
              }, 100);
            }
          }

          setSubmitLoading(false);
        })
        .catch(error => {
          console.error('Error fetching details or tree:', error);
          setSubmitLoading(false);
        });
    };

    fetchDetailsAndTree();
  }, [id]);

    const handleFinish = values => {
    // 任何情况都显示SLA弹窗
    const data = {
      ...values,
    };

    if (id) {
      data['id'] = id;
    }

    // 检查是否是公开+L1，决定是否显示红字警告
    const isPublicL1 = data.openScope === 'PUBLIC' && data.securityLevel === 1;

    setPendingSubmitData(values);
    setShowL1Warning(isPublicL1);
    setSlaModalVisible(true);
  };

  // 处理表单数据并提交（原有的 handleFinish 逻辑）
  const processSubmitData = values => {
    const data = {
      ...values,
    };

    if (id) {
      data['id'] = id;
    }

    if (data.odpsTable) {
      if (!data.hasOwnProperty('sourceConfig')) {
        data['sourceConfig'] = {};
      }

      const { project, table } = parseOdpsTableName(data.odpsTable);
      data['sourceConfig']['project'] = project;
      data['sourceConfig']['table'] = table;

      // 添加分区字段和用户主键字段
      if (data.partitionField) {
        data['sourceConfig']['partitionField'] = data.partitionField;
      }
      if (data.primaryKey) {
        data['sourceConfig']['primaryKey'] = data.primaryKey;
      }

      if (selectedRowKeys && selectedRowKeys.length > 0) {
        data['sourceConfig']['field'] = selectedRowKeys[0].columnName;
        data['sourceConfig']['type'] = selectedRowKeys[0].columnType;
      }
    }

    if (data.dataTable) {
      if (!data.hasOwnProperty('sourceConfig')) {
        data['sourceConfig'] = {};
      }
      const { project, table } = parseOdpsTableName(data.dataTable);

      data['sourceConfig']['project'] = project;
      data['sourceConfig']['table'] = table;
    }

    if (data.categoryIdNew) {
      data['categoryIdNew'] = data.categoryIdNew[data.categoryIdNew.length - 1];
    }

    if (data['constraintSep'] === 'MULTI_VALUE') {
      data['dataType'] = 'MULTI_VALUE';
    }

    //如果没传dataType，默认传
    if (!data['dataType']) {
      data['dataType'] = 'NONE_TYPE';
    }

    if (data['dataConfig']) {
      if (data['dataConfig']['dataValue']) {
        const result = convertStringToObject(data['dataConfig']['dataValue']);
        if (result && result.type === 'error') {
          message.error(result.message);
          return;
        }
        data['dataConfig']['dataValue'] = result;
      }
    }

    if (!data['source']) {
      data['source'] = 'ODPS';
    }

    if (data['step']) {
      data['dataConfig']['kvWeightRange'] = [
        data['step'].minValue,
        data['step'].maxValue,
        data['step'].value,
      ];
    }

    if (data['ttl']) {
      data['ttl'] = Number(data['ttl'] ?? 0) * 60 * 60;
    }

    delete data['constraintSep'];
    delete data['selectedRowKeys'];
    delete data['step'];
    delete data['partitionField'];
    delete data['primaryKey'];

    const request = id ? updateLabel : createLabel;

    setSubmitLoading(true);

    request(data).then(res => {
      if (res.success) {
        message.success(id ? '更新成功' : '新增成功');
        setCurrent(current + 1);
        setNeedApprovalConfig({
          needApproval: res.data.needApproval,
          id: res.data.approvalId,
        });
      } else {
        message.error(res.msg || '新增失败');
      }
      setSubmitLoading(false);
    });
  };

    // 处理 SLA 弹窗确认
  const handleSlaConfirm = () => {
    setSlaModalVisible(false);
    if (pendingSubmitData) {
      // 用户确认后，继续执行原有提交逻辑
      processSubmitData(pendingSubmitData);
      setPendingSubmitData(null);
      setShowL1Warning(false);
    }
  };

  // 处理 SLA 弹窗取消
  const handleSlaCancel = () => {
    setSlaModalVisible(false);
    setPendingSubmitData(null);
    setShowL1Warning(false);
  };

  // 检查授权校验按钮是否应该禁用
  const isAuthCheckDisabled = () => {
    const values = form.getFieldsValue();
    const timeType = values.timeType;
    const odpsTable = values.odpsTable;
    const partitionField = values.partitionField;
    const primaryKey = values.primaryKey;
    const labelType = values.labelType;

    // 只在离线模式下进行校验
    if (timeType !== 'OFFLINE') {
      return false;
    }

    // 基本条件：必须选择odps表、日期分区、用户主键
    if (!odpsTable || !partitionField || !primaryKey) {
      return true;
    }

    // 如果是单属性标签，还需要选择字段
    if (labelType === 'SINGLE_ATTRIBUTE' && selectedRowKeys.length === 0) {
      return true;
    }

    return false;
  };

    //选择odps表
  const handleChange = (value, option) => {
    // 清空之前的配置
    setNotAuthOdpsConfig({
      partitionOptions: [],
      primaryKeyOptions: [],
    });

    // 重置授权状态
    setAuthCheckPassed(false);
    setOdpsIsAuth(false);

    // 同时处理原有的 handleChangeOdpsTable 逻辑
    setOdpsLoading(true);

    queryColumnInfos({ tableGuid: value }).then(res => {
      if (res.success && res.data && res.data.columns) {
        // 处理非分区字段（用于原有的表格显示）
        const result = (res.data.columns || []).filter(item => {
          return !item.isPartitionCol;
        });
        setColumnDataSource(result);

        // 处理分区字段
        const partitionOptions = res.data.columns
          .filter(item => {
            return item.isPartitionCol;
          })
          .map(itx => {
            return {
              label: itx.columnName,
              value: itx.columnName,
            };
          });

        // 处理用户主键字段选项
        const primaryKeyOptions = (res.data.columns || []).map(item => {
          return {
            label: item.columnName,
            desc: item.columnDesc,
            value: item.columnName,
          };
        });

        setNotAuthOdpsConfig({
          partitionOptions,
          primaryKeyOptions,
        });

                // 如果选择的表包含授权信息，自动设置字段
        if (option && option.primaryKey && option.partitionField) {
          console.log('从选择的表中自动设置字段:', {
            primaryKey: option.primaryKey,
            partitionField: option.partitionField
          });

          // 检查字段是否在选项中存在
          const hasPartitionField = partitionOptions.some(opt => opt.value === option.partitionField);
          const hasPrimaryKey = primaryKeyOptions.some(opt => opt.value === option.primaryKey);

          if (hasPartitionField && hasPrimaryKey) {
            form.setFieldsValue({
              primaryKey: option.primaryKey,
              partitionField: option.partitionField,
            });

            // 延迟更新columnDataSource，确保表单值已设置
            setTimeout(() => {
              updateColumnDataSource();
            }, 100);
          } else {
            console.log('授权表中的字段在当前表结构中不存在，跳过自动设置');
          }
        }
      }
      setOdpsLoading(false);
    });
  };

  // 处理分区字段或用户主键字段变化
  const handleFieldChange = () => {
    // 重置授权状态
    setAuthCheckPassed(false);
    setOdpsIsAuth(false);

    // 更新columnDataSource，标识分区字段和主键字段
    updateColumnDataSource();
  };

  // 更新表格数据源，标识分区字段和主键字段
  const updateColumnDataSource = () => {
    const currentValues = form.getFieldsValue();
    const selectedPartitionField = currentValues.partitionField;
    const selectedPrimaryKey = currentValues.primaryKey;

    setColumnDataSource(prevData =>
      prevData.map(item => ({
        ...item,
        isPartitionField: item.columnName === selectedPartitionField,
        isPrimaryKey: item.columnName === selectedPrimaryKey,
      }))
    );
  };

  const editorChange = value => {
    form.setFieldsValue({
      sourceConfig: {
        sqlConfig: {
          sqlTemplate: value,
        },
      },
    });
    let sqlParams = Array.from(new Set(value.match(/\$\{(.*?)\}/g)));
    if (Array.isArray(sqlParams)) {
      sqlParams = sqlParams
        .filter(item => item.toLocaleLowerCase() !== '${date}')
        .map(ele => ele.slice(2, -1))
        .filter(item => {
          if (item) {
            return item;
          }
        })
        .map((e, i) => {
          let obj = {};
          if (e) {
            obj = {
              name: e,
              description: '',
              paramType: undefined,
            };
            return obj;
          }
        });
    }
    setSqlParams(sqlParams);
  };

  //枚举映射新增
  const handleAddEnumMapping = () => {
    setEnumMappingVisible(true);
  };

  const handleCheckSql = isCurrent => {
    const sqlValue = form.getFieldValue(['sourceConfig', 'sqlConfig', 'sqlTemplate']);
    const odpsTableValue = form.getFieldValue('odpsTable');

    if (!sqlValue) {
      message.error('SQL不能为空');
      return;
    }

    if (!odpsTableValue) {
      message.error('odps表不能为空');
      return;
    }

    setSqlCheckLoading(true);
    checkCrowdCircleSqlAuth({ sql: sqlValue, tableName: odpsTableValue }).then(res => {
      if (res.success) {
        // SQL权限校验成功，设置授权状态
        setAuthCheckPassed(true);
        if (!isCurrent) {
          message.success('SQL权限校验成功');
        }
        if (isCurrent) {
          setCurrent(current + 1);
        }
      } else {
        // SQL权限校验失败，重置授权状态
        setAuthCheckPassed(false);
        message.error(res.msg || 'SQL权限校验失败');
      }
      setSqlCheckLoading(false);
    }).catch(error => {
      console.error('SQL权限校验出错:', error);
      setAuthCheckPassed(false);
      message.error('SQL权限校验失败');
      setSqlCheckLoading(false);
    });
  };

  return (
    <PageHeaderWrapper
      title="标签配置"
      toDocumentLink="https://aliyuque.antfin.com/qnwrq9/kzl68s/ks23g48klvwvbdnh?singleDoc#"
    >
      <div className={style.configPage}>
        <Spin tip={id ? '加载中，请稍等...' : '提交中，请稍等...'} spinning={submitLoading}>
          <Card bordered={false}>
            <Steps current={current}>
              {items.map(ele => {
                if (ele.title === '提交审批' && !needApprovalConfig.needApproval) {
                  ele.title = '提交配置';
                }
                return (
                  <Steps.Step title={ele.title} key={ele.title} description={ele.description} />
                );
              })}
            </Steps>
          </Card>
          <Form
            labelCol={{ span: 4 }}
            wrapperCol={{ span: current === 1 ? 14 : 16 }}
            onFinish={handleFinish}
            form={form}
            disabled={type === 'view'}
            initialValues={{
              bizRegionList: ['PUBLIC_REGION'],
              accountType: 'USER',
              timeType: 'OFFLINE',
              labelType: 'SINGLE_ATTRIBUTE',
              updatePeriod: 'DAY',
              source: 'LINDORM',
              expiredType: 'NEVER_EXPIRED',
              openScope: 'PUBLIC',
              sourceConfig: {
                sqlConfig: {
                  sqlTemplate: 'select distinct xxx as user_id from ...',
                },
              },
              dataSourceConfig: {
                dataSource: 'FLIGGY',
              },
              dataConfig: {
                kvDisplayKey: '目标',
                kvDisplayValue: '权重',
              },
              processType: 'FACT',
            }}
          >
            <BasicConfig
              current={current}
              type={type}
              isSuperAdmin={isSuperAdmin}
              setSelectedRowKeys={setSelectedRowKeys}
              isBehaviorLabel={isBehaviorLabel}
            />
            <DataSourceConfig
              current={current}
              type={type}
              form={form}
              handleCheckSql={handleCheckSql}
              sqlCheckLoading={sqlCheckLoading}
              editorChange={editorChange}
              setSelectedRowKeys={setSelectedRowKeys}
              columnDataSource={columnDataSource}
              odpsLoading={odpsLoading}
              selectedRowKeys={selectedRowKeys}
              notAuthOdpsConfig={notAuthOdpsConfig}
              handleChange={handleChange}
              handleAuthCheck={() => {
                const values = form.getFieldsValue();
                const odpsTable = values.odpsTable;
                const partitionField = values.partitionField;
                const primaryKey = values.primaryKey;

                if (!odpsTable) {
                  message.warning('请先选择odps表');
                  return;
                }
                if (!partitionField) {
                  message.warning('请先选择日期分区');
                  return;
                }
                if (!primaryKey) {
                  message.warning('请先选择用户主键');
                  return;
                }

                setCheckLoading(true);
                setAuthCheckPassed(false);

                // 获取用户选择的字段，用逗号拼接
                const authorizedKeys = selectedRowKeys.map(item => item.columnName).join(',');

                console.log('授权校验参数:', {
                  tableName: odpsTable,
                  partitionField: partitionField,
                  primaryKey: primaryKey,
                  authorizedKeys: authorizedKeys,
                });

                checkOdpsAuth({
                  tableName: odpsTable,
                  partitionField: partitionField,
                  primaryKey: primaryKey,
                  authorizedKeys: authorizedKeys,
                }).then(res => {
                  if (res.data && res.success) {
                    const fieldsText = authorizedKeys ? '、选择字段' : '';
                    message.success(`授权校验通过！表、分区字段、用户主键${fieldsText}均具有查询权限`);
                    setOdpsIsAuth(true);
                    setAuthCheckPassed(true);
                  } else if (!res.data && res.success) {
                    const fieldsText = authorizedKeys ? '、选择字段' : '';
                    message.warning(`该表暂未授权诸葛平台，或分区字段、用户主键${fieldsText}缺少查询权限，请先进行表授权操作`);
                    setOdpsIsAuth(false);
                    setAuthCheckPassed(false);
                  } else if (!res.success) {
                    message.error(res.msg || '授权校验失败');
                    setOdpsIsAuth(false);
                    setAuthCheckPassed(false);
                  }
                  setCheckLoading(false);
                });
              }}
              checkLoading={checkLoading}
              isAuthCheckDisabled={isAuthCheckDisabled}
              handleFieldChange={handleFieldChange}
            />
            <LabelConfig current={current} form={form} type={type} treeData={treeData} id={id} />
            <ValueConfig
              current={current}
              form={form}
              type={type}
              labelEnumMapping={labelEnumMapping}
              handleAddEnumMapping={handleAddEnumMapping}
              setQuickTemplateOpen={setQuickTemplateOpen}
              sqlParams={sqlParams}
            />
            <Card hidden={current !== 2}>
              <div className={style.inApproval}>
                <InfoCircleTwoTone style={{ fontSize: 48 }} />
                <p style={{ marginTop: 20 }}>
                  {needApprovalConfig.needApproval ? '提交成功，审批中' : '提交成功'}
                </p>
              </div>
            </Card>
          </Form>
          <Card className={style.footerCard} bordered={false}>
            {current === 2 && (
              <Button
                disabled={type === 'view'}
                onClick={() => {
                  if (needApprovalConfig.needApproval) {
                    if (needApprovalConfig.id) {
                      window.open(getBpmsUrl(needApprovalConfig.id));
                    } else {
                      message.warning('未查询到审核ID，请返回列表查看!');
                    }
                  } else {
                    window.history.back();
                  }
                }}
                style={{ marginRight: 20 }}
                type="primary"
              >
                {needApprovalConfig.needApproval ? '查看审批流' : '返回列表页'}
              </Button>
            )}
            {current === 1 && (
              <>
                <Button onClick={() => setCurrent(current - 1)} style={{ marginRight: 20 }}>
                  返回上一步
                </Button>
                <Button
                  disabled={type === 'view'}
                  onClick={() => form.submit()}
                  style={{ marginRight: 20 }}
                  type="primary"
                  loading={submitLoading}
                >
                  提交
                </Button>
              </>
            )}
            {current === 0 && (
              <Button
                onClick={() => {
                                                      const values = form.getFieldsValue();
                  if (values.timeType === 'OFFLINE' && current === 0) {
                    if (!values.odpsTable) {
                      message.warning('请先选择odps表');
                      return;
                    }

                    if (!values.partitionField) {
                      message.warning('请先选择日期分区');
                      return;
                    }

                    if (!values.primaryKey) {
                      message.warning('请先选择用户主键');
                      return;
                    }

                    if (
                      values.labelType === 'SINGLE_ATTRIBUTE' &&
                      selectedRowKeys.length <= 0 &&
                      type !== 'view'
                    ) {
                      message.warning('请先选择字段');
                      return;
                    }

                    if (values.labelType === 'SELF_DEFINED_SQL') {
                      if (!values.sourceConfig.sqlConfig.sqlTemplate) {
                        message.warning('请先输入SQL');
                        return;
                      }
                      // view模式下跳过SQL校验
                      if (type === 'view') {
                        setCurrent(current + 1);
                        return;
                      }
                      handleCheckSql(true);
                      return;
                    }

                    // 单属性标签自动执行授权校验
                    if (values.labelType === 'SINGLE_ATTRIBUTE' && !authCheckPassed && type !== 'view') {
                      // 直接调用现有的授权校验逻辑
                      const odpsTable = values.odpsTable;
                      const partitionField = values.partitionField;
                      const primaryKey = values.primaryKey;
                      const authorizedKeys = selectedRowKeys.map(item => item.columnName).join(',');

                      setCheckLoading(true);
                      checkOdpsAuth({
                        tableName: odpsTable,
                        partitionField: partitionField,
                        primaryKey: primaryKey,
                        authorizedKeys: authorizedKeys,
                      }).then(res => {
                        if (res.data && res.success) {
                          setAuthCheckPassed(true);
                          setCurrent(current + 1);
                        } else {
                          message.warning('授权校验未通过，请手动点击授权校验按钮');
                        }
                        setCheckLoading(false);
                      });
                      return;
                    }
                  } else if (values.timeType === 'REALTIME' && current === 0) {
                    // 实时模式的校验逻辑
                    if (!values.dataTable) {
                      message.warning('请先输入数据表');
                      return;
                    }
                  }

                  setCurrent(current + 1);
                }}
                style={{ marginRight: 20 }}
                type="primary"
                loading={checkLoading}
              >
                下一步
              </Button>
            )}
            <Link to="/develop-tool/label-management">
              <Button>取消</Button>
            </Link>
          </Card>
        </Spin>
      </div>

      <Modal
        title="快速模版"
        visible={quickTemplateOpen}
        onCancel={() => setQuickTemplateOpen(false)}
        onOk={() => {
          //将quickTemplateValue中的空格替换成换行
          const value = quickTemplateValue.replace(/\s/g, '\n');
          form.setFieldsValue({
            dataConfig: {
              dataValue: value,
            },
          });
          setQuickTemplateOpen(false);
          setQuickTemplateValue('1:是 0:否');
        }}
      >
        <Radio.Group
          onChange={e => {
            setQuickTemplateValue(e.target.value);
          }}
          value={quickTemplateValue}
        >
          <Radio value="1:是 0:否">1:是 0:否</Radio>
          <Radio value="Y:是 N:否">Y:是 N:否</Radio>
        </Radio.Group>
      </Modal>
      <CreateTagEnum
        visible={enumMappingVisible}
        onCancel={() => {
          setEnumMappingVisible(false);
        }}
      />

      {/* SLA 确认弹窗 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <ExclamationCircleOutlined style={{ color: '#faad14', marginRight: 8 }} />
            <span>标签保障SLA提醒</span>
          </div>
        }
        visible={slaModalVisible}
        onCancel={handleSlaCancel}
        onOk={handleSlaConfirm}
        okText="我已阅读"
        cancelText="取消"
        maskClosable={false}
        width={500}
      >
                <div style={{ padding: '16px 0' }}>
          <p style={{ marginBottom: 16, fontSize: 14 }}>
            请仔细阅读{' '}
            <a
              href="https://aliyuque.antfin.com/qnwrq9/kzl68s/fq5pyz48egpzfzn4"
              target="_blank"
              rel="noopener noreferrer"
              style={{ color: '#1890ff' }}
            >
              诸葛标签保障SLA
            </a>
          </p>

          {showL1Warning && (
            <div style={{
              backgroundColor: '#fff2f0',
              border: '1px solid #ffccc7',
              borderRadius: 4,
              padding: 12,
              marginBottom: 16
            }}>
              <p style={{
                color: '#ff4d4f',
                fontSize: 14,
                fontWeight: 500,
                margin: 0,
                lineHeight: '1.6'
              }}>
                安全等级 L1 的标签无需审批，会被飞猪所有运营使用。创建人需对标签口径、数据质量和可能导致的故障负责。原则上仅有使用了数仓底表的公共底层标签，如会员等级、领券等需要设置为 L1。
              </p>
            </div>
          )}

          <p style={{ fontSize: 12, color: '#666', margin: 0 }}>
            点击"我已阅读"按钮确认您已阅读并了解相关内容后，才能继续提交标签更新。
          </p>
        </div>
      </Modal>
    </PageHeaderWrapper>
  );
};

export default connect(({ user }) => ({
  currentUser: user.currentUser,
  isSuperAdmin: user.isSuperAdmin,
}))(Detail);
