import { Button, Form, Radio, Select, Row, Col, Modal, Tooltip } from 'antd';
import { useCallback, useMemo, useState } from 'react';
import { WarningFilled, QuestionCircleOutlined } from '@ant-design/icons';
import { offlineLabel } from '@/services/api';
import GetUser from '@/components/GetUser';

const FormModal = ({ visible, onCancel, onOk, type, modelForm, record }) => {
  const [againConfirmVisible, setAgainConfirmVisible] = useState(false);
  const [crowdMap, setCrowdMap] = useState([]);
  const [statusValue, setStatusValue] = useState('');
  const renderContent = useCallback(() => {
    switch (type) {
      case 'publicScope':
        return (
          <>
            <Form.Item
              rules={[{ required: true, message: '请选择公开范围' }]}
              label="公开范围"
              name="openScope"
            >
              <Radio.Group>
                <Radio value="PUBLIC">
                  公开标签
                  <Tooltip title="按照“安全等级”字段，控制全员是否可用">
                    <QuestionCircleOutlined />
                  </Tooltip>
                </Radio>
                <Radio value="PRIVATE">
                  私有标签
                  <Tooltip title="仅限“标签负责人/管理员”和“标签分享者”进行使用">
                    <QuestionCircleOutlined />
                  </Tooltip>
                </Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, curValues) => prevValues.openScope !== curValues.openScope}
            >
              {({ getFieldValue }) => {
                if (getFieldValue('openScope') === 'PRIVATE') {
                  return (
                    <Form.Item name="sharers" label="授权使用">
                      <GetUser mode="multiple" />
                    </Form.Item>
                  );
                }
              }}
            </Form.Item>
          </>
        );
      case 'status':
        return (
          <Form.Item
            rules={[{ required: true, message: '请选择标签状态' }]}
            label="标签状态"
            name="status"
          >
            <Select
              options={[
                {
                  label: '使用中',
                  value: 'ACTIVATE',
                },
                {
                  label: '已下线',
                  value: 'OFFLINE',
                },
                {
                  label: '待下线',
                  value: 'WAIT_OFFLINE',
                },
              ]}
              placeholder="请选择标签状态"
            />
          </Form.Item>
        );
      case 'owner':
        return (
          <Form.Item
            rules={[{ required: true, message: '请选择标签负责人' }]}
            name="owner"
            label={
              <span>
                标签负责人&nbsp;
                <Tooltip title="标签的唯一负责人">
                  <QuestionCircleOutlined />
                </Tooltip>
              </span>
            }
          >
            <GetUser mode="default" />
          </Form.Item>
        );
      case 'managers':
        return (
          <Form.Item
            rules={[{ required: true, message: '请选择标签管理员' }]}
            name="managers"
            label={
              <span>
                标签管理员&nbsp;
                <Tooltip title="标签的代理负责人，拥有和负责人相同的标签编辑权限（唯独不含修改“负责人”信息）">
                  <QuestionCircleOutlined />
                </Tooltip>
              </span>
            }
          >
            <GetUser mode="multiple" />
          </Form.Item>
        );
      default:
        return null;
    }
  }, [type, visible]);

  const title = useMemo(() => {
    switch (type) {
      case 'publicScope':
        return '公开范围';
      case 'status':
        return '标签状态';
      case 'owner':
        return '负责人';
      case 'managers':
        return '管理员';
      default:
        return '';
    }
  }, [type, visible]);

  return (
    <>
      <Modal
        title={title}
        visible={visible}
        onCancel={onCancel}
        onOk={() => {
          if (type === 'status') {
            const result = modelForm.getFieldsValue();
            if (result.status === 'OFFLINE' || result.status === 'WAIT_OFFLINE') {
              setStatusValue(result.status);
              setAgainConfirmVisible(true);
              offlineLabel(record.code).then(res => {
                if (res.success) {
                  if (res.data) {
                    let crowdList = [];
                    Object.keys(res.data).forEach(key => {
                      crowdList.push({
                        crowdId: key,
                        crowdName: res.data[key],
                      });
                    });
                    setCrowdMap(crowdList);
                  }
                } else {
                  message.error(res.msg);
                }
              });
              return;
            }
          }
          onOk && onOk();
        }}
      >
        <Form form={modelForm} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
          {renderContent()}
        </Form>
      </Modal>
      <Modal
        visible={againConfirmVisible}
        onCancel={() => setAgainConfirmVisible(false)}
        onOk={() => {
          onOk &&
            onOk('', () => {
              setAgainConfirmVisible(false);
            });
        }}
        title="二次确认"
      >
        <div style={{ display: 'flex', alignItems: 'center', padding: 8, background: '#e5e5e5' }}>
          <WarningFilled style={{ color: 'red', fontSize: 20 }} />
          <span style={{ marginLeft: 8, fontSize: 12 }}>
            {statusValue === 'WAIT_OFFLINE'
              ? '“待下线”标签：不会影响存量“圈人/匹配/分析”使用，但是无法再增量操作'
              : '“已下线“标签：存量“圈人/匹配/分析”任务无法生产最新数据，且也无法再增量操作'}
          </span>
        </div>
        <p style={{ marginTop: 12, fontSize: 12 }}>
          ·影响人群圈选（当前有效未过期状态）：
          {(crowdMap || []).length > 0 ? crowdMap.map(item => `${item.crowdId}:(${item.crowdName})`).join(',') : '无'}
        </p>
      </Modal>
    </>
  );
};

export default FormModal;
