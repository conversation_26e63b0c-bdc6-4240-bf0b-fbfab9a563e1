import { Form, Modal, Input, Radio, Row, Col, Button, Select, Spin } from 'antd';
import GetUser from '@/components/GetUser';
import { Fragment, useEffect, useState } from 'react';
import ParamSetting from '../ParamSetting';
import {
  queryOdpsGrantedTable,
  queryOdpsTableColumn,
  queryLabelEnumValueObjByCode,
  editLabelEnumValueObj,
} from '@/services/api';

const { Option } = Select;

const CreateTagEnum = props => {
  const { visible, onCancel } = props;
  const [form] = Form.useForm();
  const [odpsOptions, setOdpsOptions] = useState([]);
  const [labelEnumColumns, setLabelEnumColumns] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible && odpsOptions.length < 1) {
      setLoading(true);
      queryOdpsGrantedTable({ sceneType: 'LABEL_ENUM_VALUE_SCENE' }).then(res => {
        if (res.data && res.data.length) {
          const result = res.data.map(item => {
            return {
              label: item,
              value: item,
            };
          });
          setOdpsOptions(result);
        }
        setLoading(false);
      });
    }
  }, [visible, odpsOptions]);

  const onLabelEnumValueTableChange = e => {
    queryOdpsTableColumn({ tableGuid: e }).then(res => {
      if (res.data) {
        setLabelEnumColumns(res.data);
      }
    });
  };

  const onSubmit = value => {
    const { metaType, args = [] } = value;
    const data = { ...value };
    if (metaType === 'CUSTOMIZE') {
      data['args'] = value.args.map(item => {
        return {
          enumCode: item.code,
          enumDesc: item.desc,
          leaf: 1,
          level: 'LEVEL_1',
          parentId: 0,
        };
      });
    }
    editLabelEnumValueObj(data).then(res => {
      if (res.success) {
        message.success('新增枚举映射成功');
        onCancel();
      } else {
        message.error(res.msg || '新增枚举映射失败');
      }
    });
    console.log(data, '数据');
  };

  return (
    visible && (
      <Modal visible={visible} onCancel={onCancel} width={800} footer={null} title="新增枚举映射">
        <Form form={form} onFinish={onSubmit} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <Form.Item
            label="枚举code"
            name="metaCode"
            required
            rules={[
              ({ getFieldValue }) => ({
                async validator(_, value) {
                  if (!value) {
                    return Promise.reject(new Error('请输入枚举code'));
                  }
                  const res = await queryLabelEnumValueObjByCode({ code: value.trim() });
                  if (res.data) {
                    return Promise.reject(new Error('该枚举code已存在'));
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <Input placeholder="请输入枚举code" />
          </Form.Item>
          <Form.Item
            label="枚举名称"
            name="metaName"
            rules={[{ required: true, message: '请输入枚举名称' }]}
          >
            <Input placeholder="请输入枚举名称" />
          </Form.Item>
          <Form.Item
            label="管理员"
            name="operator"
            rules={[{ required: true, message: '请输入管理员' }]}
          >
            <GetUser placeholder="请选择管理员" />
          </Form.Item>
          <Form.Item
            label="参数配置"
            name="metaType"
            rules={[{ required: true, message: '请选择参数配置' }]}
          >
            <Radio.Group>
              <Radio key="CUSTOMIZE" value="CUSTOMIZE">
                逻辑定义
              </Radio>
              <Radio key="ODPS" value="ODPS">
                ODPS表
              </Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, curValues) => prevValues.metaType !== curValues.metaType}
          >
            {({ getFieldValue }) => {
              const metaType = getFieldValue('metaType');
              if (metaType === 'CUSTOMIZE') {
                return (
                  <Form.Item
                    label="参数配置"
                    name="args"
                    rules={[{ required: true, message: '请输入参数配置' }]}
                  >
                    <ParamSetting />
                  </Form.Item>
                );
              }
              if (metaType === 'ODPS') {
                return (
                  <Fragment>
                    <Form.Item
                      label="ODPS表"
                      name={['odpsArgs', 'odpsGuid']}
                      rules={[{ required: true, message: '请选择ODPS表' }]}
                    >
                      <Select
                        placeholder="请选择ODPS表"
                        onChange={onLabelEnumValueTableChange}
                        showSearch
                        options={odpsOptions}
                        notFoundContent={loading ? <Spin size="small" /> : null}
                      />
                    </Form.Item>
                    <Form.Item
                      label="枚举字段"
                      name={['odpsArgs', 'enumColumn']}
                      rules={[{ required: true, message: '请选择选择枚举字段' }]}
                    >
                      <Select placeholder="请选择选择枚举字段" showSearch>
                        {labelEnumColumns.length &&
                          labelEnumColumns.map(c => (
                            <Option key={c.columnName}>{c.columnName}</Option>
                          ))}
                      </Select>
                    </Form.Item>
                    <Form.Item
                      label="枚举字段描述"
                      name={['odpsArgs', 'enumDescColumn']}
                      rules={[{ required: true, message: '请输入选择枚举字段描述' }]}
                    >
                      <Select placeholder="请输入选择枚举字段描述" showSearch>
                        {labelEnumColumns.length &&
                          labelEnumColumns.map(c => (
                            <Option key={c.columnName}>{c.columnName}</Option>
                          ))}
                      </Select>
                    </Form.Item>
                  </Fragment>
                );
              }
              return null;
            }}
          </Form.Item>
          <Row gutter={24}>
            <Col span={4} />
            <Col span={14} style={{ textAlign: 'center' }}>
              <Button onClick={() => onCancel()} style={{ marginRight: 15 }}>
                取消
              </Button>
              <Button type="primary" onClick={() => form.submit()}>
                保存
              </Button>
            </Col>
          </Row>
        </Form>
      </Modal>
    )
  );
};

export default CreateTagEnum;
