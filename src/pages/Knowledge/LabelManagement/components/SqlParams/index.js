import { useMemo } from 'react';
import { Input, Select, Button, Row, Col, Form } from 'antd';
import styles from './index.less';

const FormItem = Form.Item;
const { Option } = Select;

const columnFlex = {
  flexDirection: 'column',
  flex: 1,
};

const wrap = {
  whiteSpace: 'nowrap',
};

const PARAM_TYPE_ENUM = {
  NUMBER: '数值',
  DATE: '日期',
  TEXT: '文本',
  ENUM: '枚举',
  TIME: '时间',
  SINGLE_ENUM: '单值枚举',
};

const SQL_PARAM_TYPE = ['NUMBER', 'DATE', 'TEXT', 'ENUM']; // sql展示类型

const SqlParams = params => {
    const { labelEnumMapping = [], sqlData, onChange, value, type, disabled, handleAdd } = params;
    
  const newValue = useMemo(() => {
    let sqlValue = value ?? {};
    if (!sqlValue.name) {
      sqlValue['name'] = sqlData.name;
    }
    return sqlValue;
  }, [value, sqlData]);

  const triggerChange = changedValue => {
    if (onChange) {
      onChange({
        ...newValue,
        ...changedValue,
      });
    }
  };

  const onDisChange = values => {
    triggerChange({ description: values });
  };

   const onEnumChange = (values) => {
    const enumId = values
    triggerChange({ enumId });
  }

  const onParamChange = values => {
    const paramType = values;
    if (values === 'ENUM') {
      newValue.enumId = '';
      triggerChange({ ...newValue });
    } else {
      if (newValue.hasOwnProperty('enumId')) {
        delete newValue.enumId;
      }
    }
    triggerChange({ paramType });
  };

  const setParamType = () => {
    return SQL_PARAM_TYPE?.map(ele => ({
      label: PARAM_TYPE_ENUM[ele],
      value: ele,
    }));
  };

  return (
    <div style={{ flex: 1 }}>
      <Row gutter={{ md: 24, sm: 24 }} className={styles.paramRow}>
        <Col className={styles.paramSettingItem} span={4}>
          <span className={styles.paramSettingItemLabel}>{newValue.name}：</span>
        </Col>
        <Col span={20} style={{ padding: 0 }}>
          <div gutter={{ md: 24, sm: 24 }} style={columnFlex}>
            <Row>
              <Col className={styles.paramFlex}>
                <span style={wrap}>显示名称：</span>
                <Input
                  disabled={disabled}
                  type="text"
                  onChange={e => onDisChange(e.target.value)}
                  value={newValue.description}
                  style={{ marginRight: '10px' }}
                />
              </Col>
              <Col className={styles.paramFlex}>
                <span style={wrap}>参数类型：</span>
                <Select disabled={disabled} value={newValue.paramType} onChange={onParamChange}>
                  {setParamType()?.map(ele => (
                    <Option key={ele?.value} value={ele?.value}>{ele?.label}</Option>
                  ))}
                </Select>
              </Col>
            </Row>

            {['ENUM', 'SINGLE_ENUM']?.includes(newValue.paramType) ? (
              <Row className={styles.paramFlex}>
                <span style={wrap}>枚举映射：</span>
                <div className={styles.paramEnumId}>
                  <Select
                    showSearch
                    placeholder="请选择枚举映射"
                    filterOption={(input, option) =>
                      option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                    disabled={disabled}
                    value={newValue.enumId}
                    onChange={onEnumChange}
                    style={{ marginRight: '10px', maxWidth: 500 }}
                  >
                    {labelEnumMapping.length &&
                      labelEnumMapping.map(c => (
                        <Option key={c.id} value={c.id}>
                          {`${c.name}（${c.code})`}
                        </Option>
                      ))}
                  </Select>
                  <Button disabled={disabled} onClick={handleAdd}>新增</Button>
                </div>
              </Row>
            ) : null}
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default SqlParams;
