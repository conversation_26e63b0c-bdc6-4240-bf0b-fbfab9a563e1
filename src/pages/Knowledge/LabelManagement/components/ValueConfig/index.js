import {
  Card,
  Form,
  Checkbox,
  Radio,
  Tooltip,
  Select,
  Button,
  Input,
  Popover,
  InputNumber,
  Row,
  Col,
} from 'antd';
import { QuestionCircleOutlined, QuestionCircleFilled } from '@ant-design/icons';
import MyCascader from '@/components/Cascader';
import SqlParams from '../SqlParams';
import styles from './index.less';
import { zhugeUrl } from '@/utils/utils';

const ValueConfig = props => {
  const {
    form,
    type,
    current,
    labelEnumMapping,
    handleAddEnumMapping,
    sqlParams,
    setQuickTemplateOpen,
  } = props;
  return (
    <Card hidden={current !== 1} title="取值配置">
      <Form.Item
        noStyle
        shouldUpdate={(prevValues, curValues) => prevValues.labelType !== curValues.labelType}
      >
        {({ getFieldValue }) => {
          const labelTypeValue = getFieldValue('labelType');
          if (!type) {
            form.setFieldsValue({
              dataType: 'ENUM',
              dataConfig: {
                dataValueType: 'STRING',
                dataDesc: 'MANAL_INPUT',
                timeType: 'YMDHMS',
                kvSep: 'COLON',
                kvGroupSep: 'COMMA',
              },
              constraintSep: 'SINGLE_ENUM',
            });
          }
          // 单一属性标签
          if (labelTypeValue === 'SINGLE_ATTRIBUTE') {
            return (
              <>
                <Form.Item label="取值类型" name="dataType">
                  <Radio.Group disabled={type}>
                    <Radio value="ENUM">
                      枚举&nbsp;
                      <Tooltip title={'离散型数据，比如男、女，是、否，一级类目名称，商家 ID 等'}>
                        <QuestionCircleFilled />
                      </Tooltip>
                    </Radio>
                    <Radio value="NUMBER">
                      数值&nbsp;
                      <Tooltip title={'连续型数据，比如成交金额，成交件数等'}>
                        <QuestionCircleFilled />
                      </Tooltip>
                    </Radio>
                    <Radio value="DATE">
                      日期&nbsp;
                      <Tooltip title={'连续型数据，时间格式的数据，比如最近一次访问时间'}>
                        <QuestionCircleFilled />
                      </Tooltip>
                    </Radio>
                    <Radio value="KV">
                      KV&nbsp;
                      <Tooltip
                        title={
                          '离散型数据，一般用于偏好分、权重分类型标签，KV之间用冒号分隔，不同KV之间用逗号分隔，value只支持数值类型，如目的地偏好分“阿联酋:0.09005481597494128,泰国:0.22490211433046203,马尔代夫:0.606734534064213”'
                        }
                      >
                        <QuestionCircleFilled />
                      </Tooltip>
                    </Radio>
                  </Radio.Group>
                </Form.Item>
                <Form.Item
                  noStyle
                  shouldUpdate={(prevValues, curValues) =>
                    prevValues.dataType !== curValues.dataType ||
                    prevValues.constraintSep !== curValues.constraintSep
                  }
                >
                  {({ getFieldValue }) => {
                    const dataTypeValue = getFieldValue('dataType');
                    const valueConstraintValue = getFieldValue('constraintSep');
                    if (dataTypeValue === 'ENUM') {
                      return (
                        <Form.Item label="取值约束" name="constraintSep">
                          <Radio.Group
                            onChange={e => {
                              if (e.target.value === 'MULTI_VALUE') {
                                form.setFieldsValue({ dataConfig: { multiEnumSep: 'COMMA' } });
                              } else {
                                form.setFieldsValue({ dataConfig: { multiEnumSep: '' } });
                              }
                            }}
                            disabled={type}
                          >
                            <Radio value="SINGLE_ENUM">
                              单值&nbsp;
                              <Tooltip
                                title={
                                  <>
                                    <p style={{ marginBottom: 4 }}>
                                      ●
                                      示例：飞猪用户会员等级，用户同一时刻只能是一种等级，所以是单值枚举值（F1）
                                    </p>
                                    <p style={{ marginBottom: 0 }}>● 注意：枚举值数量最多1000</p>
                                  </>
                                }
                              >
                                <QuestionCircleFilled />
                              </Tooltip>
                            </Radio>
                            <Radio value="MULTI_VALUE">
                              多值 &nbsp;
                              <Tooltip
                                title={
                                  <>
                                    <p style={{ marginBottom: 4 }}>
                                      ●
                                      示例：用户感兴趣的目的地，可以是多个目的地，需要以逗号分隔多个枚举值（哈尔滨,三亚）
                                    </p>
                                    <p style={{ marginBottom: 0 }}>● 注意：枚举值数量最多1000</p>
                                  </>
                                }
                              >
                                <QuestionCircleFilled />
                              </Tooltip>
                              &nbsp;
                              <Form.Item noStyle name={['dataConfig', 'multiEnumSep']}>
                                <Select
                                  disabled={valueConstraintValue !== 'MULTI_VALUE' || type}
                                  style={{ width: 130 }}
                                  options={[
                                    {
                                      label: '英文逗号","',
                                      value: 'COMMA',
                                    },
                                    // {
                                    //   label: '英文冒号":"',
                                    //   value: 'COLON',
                                    // },
                                  ]}
                                />
                              </Form.Item>{' '}
                              字符串分隔符
                            </Radio>
                          </Radio.Group>
                        </Form.Item>
                      );
                    }
                  }}
                </Form.Item>
                <Form.Item
                  noStyle
                  shouldUpdate={(prevValues, curValues) =>
                    prevValues.dataType !== curValues.dataType
                  }
                >
                  {({ getFieldValue }) => {
                    const dataTypeValue = getFieldValue('dataType');
                    if (dataTypeValue === 'NUMBER') {
                      form.setFieldsValue({ dataConfig: { dataValueType: 'BIGINT' } });
                    } else {
                      form.setFieldsValue({ dataConfig: { dataValueType: 'STRING' } });
                    }
                    return (
                      <>
                        <Form.Item label="数值类型" name={['dataConfig', 'dataValueType']}>
                          <Radio.Group disabled={type}>
                            {(dataTypeValue === 'ENUM' ||
                              dataTypeValue === 'DATE' ||
                              dataTypeValue === 'KV') && <Radio value="STRING">string</Radio>}
                            {dataTypeValue === 'NUMBER' && <Radio value="BIGINT">bigint</Radio>}
                          </Radio.Group>
                        </Form.Item>
                        {dataTypeValue === 'DATE' && (
                          <Form.Item label="日期格式" name={['dataConfig', 'timeType']}>
                            <Radio.Group disabled={type === 'view'}>
                              <Radio value={'YMDHMS'}>yyyy-MM-dd HH:mm:ss</Radio>
                            </Radio.Group>
                          </Form.Item>
                        )}
                        {dataTypeValue === 'KV' && (
                          <>
                            <Form.Item name={['dataConfig', 'kvSep']} label="K与V之间的分隔符">
                              <Radio.Group disabled={type === 'view'}>
                                <Radio value={'COLON'}>英文冒号:</Radio>
                              </Radio.Group>
                            </Form.Item>
                            <Form.Item name={['dataConfig', 'kvGroupSep']} label="KV组之间的分隔符">
                              <Radio.Group disabled={type === 'view'}>
                                <Radio value={'COMMA'}>英文逗号,</Radio>
                              </Radio.Group>
                            </Form.Item>
                            <Form.Item
                              rules={[{ required: true, message: '请输入' }]}
                              name={['dataConfig', 'kvDisplayKey']}
                              label="Key值展示值"
                            >
                              <Input disabled={type === 'view'} placeholder="请输入" />
                            </Form.Item>
                          </>
                        )}
                        {/* 如果是枚举或者是kv才展示下面的 */}
                        {(dataTypeValue === 'ENUM' || dataTypeValue === 'KV') && (
                          <>
                            <Form.Item label="取值描述" name={['dataConfig', 'dataDesc']}>
                              <Radio.Group
                                onChange={e => {
                                  if (e.target.value === 'MANAL_INPUT') {
                                    form.setFieldsValue({ dataConfig: { dataValue: '' } });
                                  }
                                }}
                                disabled={type === 'view'}
                              >
                                <Radio value="MANAL_INPUT">
                                  手动输入&nbsp;
                                  <Tooltip
                                    title={'当枚举值数量不多时（<=20个），可手动进行取值配置'}
                                  >
                                    <QuestionCircleFilled />
                                  </Tooltip>
                                </Radio>
                                {dataTypeValue !== 'KV' && (
                                  <Radio value="AUTO_CONFIG">
                                    自动配置&nbsp;
                                    <Tooltip
                                      title={
                                        '当枚举值数量较多时，可由系统自动执行取值分布计算任务来获取取值信息'
                                      }
                                    >
                                      <QuestionCircleFilled />
                                    </Tooltip>
                                  </Radio>
                                )}
                                <Radio value="COMMON_ENUM">
                                  枚举维表&nbsp;
                                  <Tooltip
                                    title={
                                      '当枚举值数量较多/定期更新/较为通用时，可通过配置一个枚举维表进行取值映射'
                                    }
                                  >
                                    <QuestionCircleFilled />
                                  </Tooltip>
                                </Radio>
                              </Radio.Group>
                            </Form.Item>
                            <Form.Item
                              noStyle
                              shouldUpdate={(prevValues, curValues) =>
                                prevValues.dataConfig.dataDesc !== curValues.dataConfig.dataDesc
                              }
                            >
                              {({ getFieldValue }) => {
                                const dataDescValue = getFieldValue(['dataConfig', 'dataDesc']);

                                if (dataDescValue === 'MANAL_INPUT') {
                                  return (
                                    <>
                                      <Form.Item
                                        extra="不能出现空格、非英文冒号、多余回车、制表符等非法字符"
                                        label="取值范围"
                                      >
                                        <Form.Item noStyle name={['dataConfig', 'dataValue']}>
                                          <Input.TextArea
                                            disabled={type === 'view'}
                                            placeholder={'例如：\nF:女\nM:男'}
                                            style={{ height: 100, width: 380 }}
                                          />
                                        </Form.Item>
                                        <Button
                                          disabled={type === 'view'}
                                          onClick={() => setQuickTemplateOpen(true)}
                                          type="link"
                                        >
                                          快速模版
                                        </Button>
                                      </Form.Item>
                                      <Row style={{ marginBottom: 20 }}>
                                        <Col span={4}></Col>
                                        <Col span={10}>
                                          <div className={styles.valueNote}>
                                            请输入“字段取值”（数据源字段）和“展示值”（前端展示字段），格式为“字段取值:字段展示值”，多个值请换行分隔，示例如下：
                                            <br />
                                            F:女
                                            <br />
                                            M:男
                                          </div>
                                        </Col>
                                      </Row>
                                    </>
                                  );
                                }
                                if (dataDescValue === 'AUTO_CONFIG') {
                                  return (
                                    <Row style={{ marginBottom: 20 }}>
                                      <Col span={4}></Col>
                                      <Col span={10}>
                                        <div className={styles.valueNote}>
                                          标签上架后由系统自动执行取值分布计算任务来获取，此时人群圈选时会自动展示【字段取值】=【展示值】。
                                          <br />
                                          注意需依赖取值分布任务执行成功后才可在圈人界面展示出枚举值（默认取最大分区的取值）。如果希望上架后可尽快圈人，请改为【手动指定】或者【枚举维表】，
                                          <span style={{ color: 'red' }}>
                                            仅枚举值较多时推荐使用
                                          </span>
                                          。
                                        </div>
                                      </Col>
                                    </Row>
                                  );
                                }
                                if (dataDescValue === 'COMMON_ENUM') {
                                  return (
                                    <Form.Item required label="枚举维表">
                                      <Form.Item
                                        noStyle
                                        rules={[{ required: true, message: '请选择' }]}
                                        name={['dataConfig', 'dimEnumId']}
                                      >
                                        <Select
                                          showSearch
                                          placeholder="请选择枚举维表"
                                          filterOption={(input, option) =>
                                            option.props.children
                                              .toLowerCase()
                                              .indexOf(input.toLowerCase()) >= 0
                                          }
                                          style={{ width: 400 }}
                                          disabled={type === 'view'}
                                        >
                                          {labelEnumMapping.length &&
                                            labelEnumMapping.map(c => (
                                              <Option key={c.id} value={c.id}>
                                                {`${c.name}（${c.code})`}
                                              </Option>
                                            ))}
                                        </Select>
                                      </Form.Item>
                                      <Button
                                        onClick={() => {
                                          window.open(`${zhugeUrl}/develop-tool/enumValue`);
                                        }}
                                        style={{ marginLeft: 20 }}
                                      >
                                        添加
                                      </Button>
                                    </Form.Item>
                                  );
                                }
                              }}
                            </Form.Item>
                          </>
                        )}
                        {dataTypeValue === 'KV' && (
                          <>
                            <Form.Item
                              rules={[{ required: true, message: '请输入' }]}
                              name={['dataConfig', 'kvDisplayValue']}
                              label="Value值展示值"
                            >
                              <Input disabled={type === 'view'} placeholder="请输入" />
                            </Form.Item>
                            <Form.Item label="数据范围">
                              <Input.Group compact>
                                <Form.Item
                                  rules={[{ required: true, message: '请输入' }]}
                                  noStyle
                                  name={['step', 'minValue']}
                                >
                                  <InputNumber
                                    style={{ width: 100, textAlign: 'center' }}
                                    placeholder="请输入数值"
                                    disabled={type === 'view'}
                                  />
                                </Form.Item>
                                <Input
                                  style={{
                                    width: 30,
                                    borderLeft: 0,
                                    borderRight: 0,
                                    pointerEvents: 'none',
                                  }}
                                  placeholder="~"
                                  disabled
                                />
                                <Form.Item
                                  rules={[{ required: true, message: '请输入' }]}
                                  noStyle
                                  name={['step', 'maxValue']}
                                >
                                  <InputNumber
                                    style={{
                                      width: 100,
                                      textAlign: 'center',
                                    }}
                                    placeholder="请输入数值"
                                    disabled={type === 'view'}
                                  />
                                </Form.Item>
                              </Input.Group>
                            </Form.Item>
                            <Form.Item
                              rules={[{ required: true, message: '请输入' }]}
                              name={['step', 'value']}
                              label="步长"
                            >
                              <InputNumber
                                style={{ width: 300 }}
                                placeholder="请输入"
                                disabled={type === 'view'}
                              />
                            </Form.Item>
                          </>
                        )}
                      </>
                    );
                  }}
                </Form.Item>
              </>
            );
          }
          if (labelTypeValue === 'SELF_DEFINED_SQL') {
            return (
              sqlParams &&
              sqlParams.length > 0 && (
                <Form.Item required={true} label="sql参数">
                  {sqlParams.map((e, index) => {
                    return (
                      <Form.Item
                        key={index}
                        name={['sourceConfig', 'sqlConfig', 'sqlParams', index]}
                        rules={[{ required: true, message: '请输入sql参数' }]}
                      >
                        <SqlParams
                          sqlData={e}
                          style={{ flex: 1 }}
                          disabled={type === 'view'}
                          labelEnumMapping={labelEnumMapping}
                          handleAdd={handleAddEnumMapping}
                        />
                      </Form.Item>
                    );
                  })}
                </Form.Item>
              )
            );
          }
        }}
      </Form.Item>
    </Card>
  );
};

export default ValueConfig;
