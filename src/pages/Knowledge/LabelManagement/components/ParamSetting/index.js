/* eslint-disable no-plusplus */
import React from 'react';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { Row, Col, Input, Button } from 'antd';
import styles from './index.less';

class ParamSetting extends React.Component {
  constructor(props) {
    super(props);

    const value = props.value || {};
    this.id = 1;
    this.state = {
      ...value,
      keys: [{ id: 0 }],
    };
  }

  remove = index => {
    const { keys } = this.state;

    if (keys.length === 1) {
      return;
    }

    this.setState({
      keys: keys.filter((k, i) => i !== index),
    });
  };

  add = () => {
    const { keys } = this.state;

    const nextKeys = keys.concat({ id: this.id++ });

    this.setState({
      keys: nextKeys,
    });
  };

  onCodeChange = (id, value) => {
    const { keys } = this.state;

    const item = keys.find(i => i.id === id);
    item.code = value;

    this.setState({
      keys,
    });

    this.triggerChange(keys);
  };

  onDescChange = (id, value) => {
    const { keys } = this.state;

    const item = keys.find(i => i.id === id);
    item.desc = value;

    this.setState({
      keys,
    });

    this.triggerChange(keys);
  };

  triggerChange = changedValue => {
    // Should provide an event to pass value to Form.
    const { onChange } = this.props;
    if (onChange) {
      onChange(changedValue);
    }
  };

  render() {
    // console.log('触发更新', curTimeType);
    const { keys = [] } = this.state;

    const circleStyle = {
      cursor: 'pointer',
      position: 'relative',
      marginLeft: 5,
      fontSize: 24,
      color: '#999',
      transition: 'all 0.3s',
    };

    return (
      <div className={styles.paramSetting}>
        {!!keys.length &&
          keys.map((k, i) => (
            <Row key={`param_${k.id}`} gutter={{ md: 24, sm: 24 }}>
              <Col md={10} sm={24} className={styles.paramSettingItem}>
                <span className={styles.paramSettingItemLabel}>显示名：</span>
                <Input onChange={e => this.onDescChange(k.id, e.target.value)} allowClear />
              </Col>
              <Col md={10} sm={24} className={styles.paramSettingItem}>
                <span className={styles.paramSettingItemLabel}>实际值：</span>
                <Input onChange={e => this.onCodeChange(k.id, e.target.value)} allowClear />
              </Col>
              {i > 0 && (
                <Col md={4} sm={24}>
                  <MinusCircleOutlined style={circleStyle} onClick={() => this.remove(i)} />
                </Col>
              )}
            </Row>
          ))}
        <div>
          <Button type="dashed" onClick={this.add} style={{ width: '20%' }}>
            <PlusOutlined /> 增加
          </Button>
        </div>
      </div>
    );
  }
}

export default ParamSetting;
