import {
  Card,
  Form,
  Checkbox,
  Radio,
  Tooltip,
  Select,
  Button,
  Input,
  Popover,
} from 'antd';
import { QuestionCircleOutlined, QuestionCircleFilled } from '@ant-design/icons';
import MyCascader from '@/components/Cascader';
import GetUser from '@/components/GetUser';

const SAFE_TIP = (
  <div>
    <p>L1：该标签对所有人公开权限，不需要申请</p>
    <p>L2：低风险等级，需要申请，最长3年</p>
    <p>L3：中风险等级，需要申请，最长3年</p>
    <p>L4：高风险等级，需要申请，最长1年</p>
  </div>
);

const { Option } = Select;

const LabelConfig = props => {
  const { current, type, treeData, id, form } = props;

  return (
    <Card hidden={current !== 1} title="标签配置">
      <Form.Item
        required
        rules={[
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (!value) {
                return Promise.reject(new Error('标签名称不能为空'));
              }
              if (value && value.length > 27 && !id) {
                return Promise.reject(new Error('标签名称不能超过27个字符'));
              }
              return Promise.resolve();
            },
          }),
        ]}
        label="标签名称"
        name="name"
      >
        <Input disabled={type === 'view'} placeholder="请输入标签名称，尽量通俗易懂" />
      </Form.Item>
      <Form.Item
        rules={[{ required: true, message: '请输入标签描述' }]}
        label="标签描述"
        name="description"
      >
        <Input.TextArea
          disabled={type === 'view'}
          placeholder="请详细并准确地描述标签口径定义，是降低沟通成本的利器"
        />
      </Form.Item>
      <Form.Item
        rules={[{ required: true, message: '请选择公开范围' }]}
        label="公开范围"
        name="openScope"
      >
        <Radio.Group disabled={type === 'view'}>
          <Radio value="PUBLIC">
            公开标签&nbsp;
            <Tooltip title={'按照“安全等级”字段，控制全员是否可用'}>
              <QuestionCircleFilled />
            </Tooltip>
          </Radio>
          <Radio value="PRIVATE">
            私有标签&nbsp;
            <Tooltip title={'仅限“标签管理员”和“标签分享者”进行使用'}>
              <QuestionCircleFilled />
            </Tooltip>
          </Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item
        noStyle
        shouldUpdate={(prevValues, curValues) => prevValues.openScope !== curValues.openScope}
      >
        {({ getFieldValue }) => {
          const openScopeValue = getFieldValue('openScope');
          if (openScopeValue === 'PUBLIC') {
            return (
              <Form.Item required label="安全等级">
                <Form.Item
                  noStyle
                  rules={[{ required: true, message: '请选择安全等级' }]}
                  name="securityLevel"
                >
                  <Select
                    disabled={type === 'view'}
                    placeholder="请选择安全等级"
                    style={{ width: 240 }}
                  >
                    <Option value={1}>L1</Option>
                    <Option value={2}>L2</Option>
                    <Option value={3}>L3</Option>
                    <Option value={4}>L4</Option>
                  </Select>
                </Form.Item>
                <Popover content={SAFE_TIP}>
                  <Button type="link">教我选择</Button>
                </Popover>
              </Form.Item>
            );
          }
        }}
      </Form.Item>

      <Form.Item
        rules={[{ required: true, message: '请选择标签分类' }]}
        label="标签分类"
        name="categoryIdNew"
      >
        <MyCascader disabled={type === 'view'} treeData={treeData} />
      </Form.Item>
      <Form.Item
        rules={[{ required: true, message: '请选择标签负责人' }]}
        label="标签负责人"
        name="owner"
      >
        <GetUser disabled={type === 'view'} mode="default" />
      </Form.Item>
      <Form.Item
        rules={[{ required: true, message: '请选择加工方式' }]}
        label="加工方式"
        name="processType"
      >
        <Radio.Group disabled={type === 'view'}>
          <Radio value={'FACT'}>
            事实类&nbsp;
            <Tooltip
              title={
                '定义：基于原始数据清理后的归类，用于描述客观事实，如会员等级、终端类型、购买次数/金额等'
              }
            >
              <QuestionCircleFilled />
            </Tooltip>
          </Radio>
          <Radio value={'RULE'}>
            规则类&nbsp;
            <Tooltip
              title={
                '定义：基于确定的规则而产生的标签，拥有更多的业务属性，如将“活跃用户”标签可定义为，“过去30天发生a行为x次”&“过去30天发生b行为x次”，进行综合评定'
              }
            >
              <QuestionCircleFilled />
            </Tooltip>
          </Radio>
          <Radio value={'ALGORITHM'}>
            算法类&nbsp;
            <Tooltip
              title={
                '定义：基于现有事实及规则无法得出，需要运用决策树算法、贝叶斯算法等进行数据挖掘与训练，得出标签预测结果，如预测用户未来30天的目的地兴趣偏好'
              }
            >
              <QuestionCircleFilled />
            </Tooltip>
          </Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item
        rules={[{ required: true, message: '请选择标签来源' }]}
        label="标签来源"
        name={['dataSourceConfig', 'dataSource']}
      >
        <Radio.Group disabled={type === 'view'}>
          <Radio value={'FLIGGY'}>
            飞猪自有&nbsp;
            <Tooltip title={'基于飞猪数据资产加工而成的标签'}>
              <QuestionCircleFilled />
            </Tooltip>
          </Radio>
          <Radio value={'TAOBAO'}>
            淘宝回流&nbsp;
            <Tooltip title={'由淘宝侧加工而成的标签，并数据隔离后回流至飞猪'}>
              <QuestionCircleFilled />
            </Tooltip>
          </Radio>
          <Radio value={'ADEX'}>
            流通中心&nbsp;
            <Tooltip title={'由流通中心（数据中台）加工而成的标签，并数据隔离后回流至飞猪'}>
              <QuestionCircleFilled />
            </Tooltip>
          </Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item
        noStyle
        shouldUpdate={(prevValues, curValues) =>
          prevValues.dataSourceConfig !== curValues.dataSourceConfig
        }
      >
        {({ getFieldValue }) => {
          const labelSourceValue = getFieldValue(['dataSourceConfig', 'dataSource']);
          if (labelSourceValue === 'ADEX') {
            form.setFieldsValue({ needApproval: '2' });
            return (
              <>
                <Form.Item
                  disabled={type === 'view'}
                  label="匹配审核"
                  name={['dataSourceConfig', 'needApproval']}
                >
                  <Radio.Group>
                    <Radio value={true}>是</Radio>
                    <Radio value={false}>否</Radio>
                  </Radio.Group>
                </Form.Item>
                <Form.Item
                  disabled={type === 'view'}
                  label="原标签ID"
                  name={['dataSourceConfig', 'adexLabelId']}
                >
                  <Input placeholder="请输入原标签ID" />
                </Form.Item>
                <Form.Item
                  disabled={type === 'view'}
                  label="原数据表"
                  name={['dataSourceConfig', 'adexTableName']}
                >
                  <Input placeholder="请输入原数据表" />
                </Form.Item>
              </>
            );
          }
        }}
      </Form.Item>
      <Form.Item
        noStyle
        shouldUpdate={(prevValues, curValues) => prevValues.timeType !== curValues.timeType}
      >
        {({ getFieldValue }) => {
          const timeTypeValue = getFieldValue('timeType');
          return (
            <Form.Item label="标签支持项" name="scopeList">
              <Checkbox.Group disabled={type === 'view'}>
                {timeTypeValue !== 'REALTIME' && <Checkbox value="PUSH">PUSH</Checkbox>}
                <Checkbox value="MATCH">匹配</Checkbox>
                {timeTypeValue !== 'REALTIME' && <Checkbox value="ANALYSIS">分析</Checkbox>}
              </Checkbox.Group>
            </Form.Item>
          );
        }}
      </Form.Item>
    </Card>
  );
};

export default LabelConfig;
