import React from 'react';
import { InputNumber } from 'antd';

class MyInputNumber extends React.Component {
  static getDerivedStateFromProps(nextProps) {
    // Should be a controlled component.
    if ('value' in nextProps) {
      const { value } = nextProps;
      return {
        value,
      };
    }
    return null;
  }

  constructor(props) {
    super(props);

    this.state = {
      value: props.value,
    };
  }

  handleChange = value => {
    const num = parseInt(value, 10);
    // eslint-disable-next-line no-restricted-globals
    if (isNaN(num)) {
      return;
    }

    if (!('value' in this.props)) {
      this.setState(num);
    }
    this.triggerChange(num);
  };

  triggerChange = changedValue => {
    // Should provide an event to pass value to Form.
    const { onChange } = this.props;

    if (onChange) {
      onChange(changedValue);
    }
  };

  render() {
    const { value } = this.state;
    const { suffix, disabled } = this.props;
    return (
      <InputNumber
        min={0}
        value={value}
        disabled={disabled}
        onChange={this.handleChange}
        style={{ marginRight: 4, width: 150 }}
      />
    );
  }
}

export default MyInputNumber;
