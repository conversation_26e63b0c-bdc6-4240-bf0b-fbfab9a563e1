import {
  Card,
  Form,
  Checkbox,
  Radio,
  Tooltip,
  Select,
  Button,
  Spin,
  message,
  Table,
  Input,
} from 'antd';
import { QuestionCircleOutlined, CopyOutlined } from '@ant-design/icons';
import { queryGrantedTable, queryDwsTableWithDesc } from '@/services/api';
import { sortTablesByUserInput } from '@/utils/tableSort';
import { useState, useEffect, useRef } from 'react';
import { debounce } from 'lodash';
import { createPortal } from 'react-dom';
import CodeMirror from '@/components/CodeMirror/CodeMirrorInput';
import { Link } from 'umi';
import styles from './index.less';
import MyInputNumber from '../MyInputNumber';

const DataSourceConfig = props => {
  const {
    current,
    type,
    form,
    editorChange,
    setSelectedRowKeys,
    columnDataSource,
    odpsLoading,
    selectedRowKeys,
    handleCheckSql,
    sqlCheckLoading,
    notAuthOdpsConfig,
    handleChange,
    handleAuthCheck,
    checkLoading,
    isAuthCheckDisabled,
    handleFieldChange
  } = props;
    const [loading, setLoading] = useState(false);
  const [odpsList, setOdpsList] = useState([]);
  // 新增：用于防止重复查询的标记
  const hasTriggeredQuery = useRef(false);
  // 用于Portal的容器引用
  const copyButtonRef = useRef(null);

  // 初始化时不加载任何表列表，等待用户输入搜索关键词
  useEffect(() => {
    // 初始化时清空列表，等待用户搜索
    if (current === 0) {
      setOdpsList([]);
      // 重置查询标记
      hasTriggeredQuery.current = false;
    }
  }, [current]);

    // 新增：处理编辑模式下的自动查询
  useEffect(() => {
    // 只在当前步骤为0时执行，且确保已有handleChange函数，且未触发过查询
    if (current === 0 && handleChange && !hasTriggeredQuery.current) {
      // 检查表单中是否已有odpsTable值
      const odpsTable = form.getFieldValue('odpsTable');
      // 如果有表名但没有字段选项，说明是编辑模式进入但未触发查询
      if (odpsTable &&
          (!notAuthOdpsConfig?.partitionOptions?.length && !notAuthOdpsConfig?.primaryKeyOptions?.length)) {
        console.log('编辑模式下自动查询表字段信息:', odpsTable);
        // 设置已触发标记，防止重复查询
        hasTriggeredQuery.current = true;

        // 先尝试查询授权表信息，获取primaryKey和partitionField
        queryGrantedTable({ pageNo: 1, pageSize: 10, tableName: odpsTable }).then(res => {
          let option = { value: odpsTable, label: odpsTable };

          // 如果查询到授权表信息，添加primaryKey和partitionField
          if (res.success && res.data && res.data.length > 0) {
            const grantedTable = res.data.find(item => item.tableName === odpsTable);
            if (grantedTable && grantedTable.primaryKey && grantedTable.partitionField) {
              option.primaryKey = grantedTable.primaryKey;
              option.partitionField = grantedTable.partitionField;
              console.log('找到授权表信息，将自动设置字段:', {
                primaryKey: grantedTable.primaryKey,
                partitionField: grantedTable.partitionField
              });
            }
          }

          // 调用父组件的handleChange方法
          handleChange(odpsTable, option);
        }).catch(error => {
          console.error('查询授权表失败:', error);
          // 即使查询失败，也要调用handleChange来获取表结构信息
          handleChange(odpsTable, { value: odpsTable, label: odpsTable });
        });
      }
    }
  }, [current, form, notAuthOdpsConfig, handleChange]);

  // 表格列配置
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 80,
      render: (text, record, index) => index + 1,
    },
    {
      title: '字段名称',
      dataIndex: 'columnName',
    },
    {
      title: '类型',
      dataIndex: 'columnType',
    },
    {
      title: '描述',
      dataIndex: 'columnDesc',
    },
    {
      title: '备注',
      dataIndex: 'relatedLabel',
      render: (text, record) => {
        if (record.isPrimaryKey) {
          return '用户主键字段，不可配置标签';
        }
        if (record.isPartitionField) {
          return '日期分区字段，不可配置标签';
        }
        if (!record.isPrimaryKey && record.relatedLabel) {
          let code;
          let result;
          Object.keys(record.relatedLabel).forEach(key => {
            code = key;
            result = record.relatedLabel[key];
          });
          return (
            <>
              已上架标签 (<Link to={`/label-market/detail?labelCode=${code}`}>{result}</Link>)
              不可重复配置
            </>
          );
        }
        return '';
      },
    },
  ];

  const getGrantedTable = ({ pageNo = 1, pageSize = 10, tableName = '' }) => {
    // 如果tableName为空，不调用grantedTable接口，直接返回空列表
    if (!tableName) {
      setOdpsList([]);
      return;
    }

    setLoading(true);
    queryGrantedTable({ pageNo, pageSize, tableName }).then(res => {
      if (res.success && res.data && res.data.length > 0) {
        // 如果授权表有数据，使用授权表结果
        const result = res.data.map(item => {
          return {
            label: item.tableName,
            value: item.tableName,
            primaryKey: item.primaryKey,
            partitionField: item.partitionField,
          };
        });
        setOdpsList(result);

        // 如果只有一个结果且包含primaryKey和partitionField，自动选择该表并设置字段
        if (result.length === 1 && result[0].primaryKey && result[0].partitionField) {
          const grantedTable = result[0];
          console.log('搜索到唯一授权表，自动选择并设置字段:', {
            tableName: grantedTable.value,
            primaryKey: grantedTable.primaryKey,
            partitionField: grantedTable.partitionField
          });

          // 自动选择该表
          form.setFieldsValue({
            odpsTable: grantedTable.value,
          });

          // 触发表选择的处理逻辑
          handleChange(grantedTable.value, grantedTable);
        }

        setLoading(false);
      } else {
        // 如果授权表为空，切换到全量表搜索
        console.log('授权表为空，切换到全量表搜索，关键词:', tableName);
        queryDwsTableWithDesc({ keyword: tableName }).then(dwsRes => {
          if (dwsRes.success && dwsRes.data) {
            let result = [];
            Object.keys(dwsRes.data).forEach(key => {
              result.push({
                label: key,
                value: key,
                desc: dwsRes.data[key],
              });
            });

            // 智能排序：将用户输入能完整匹配的结果放到最前面
            result = sortTablesByUserInput(result, tableName);

            console.log('全量表搜索结果:', result.length, '条');
            setOdpsList(result);
          } else {
            console.log('全量表搜索失败或无数据');
            setOdpsList([]);
          }
          setLoading(false);
        }).catch(error => {
          console.error('全量表搜索出错:', error);
          setOdpsList([]);
          setLoading(false);
        });
      }
    }).catch(error => {
      console.error('授权表查询出错:', error);
      // 如果授权表查询失败，也尝试全量表搜索
      queryDwsTableWithDesc({ keyword: tableName }).then(dwsRes => {
        if (dwsRes.success && dwsRes.data) {
          let result = [];
          Object.keys(dwsRes.data).forEach(key => {
            result.push({
              label: key,
              value: key,
              desc: dwsRes.data[key],
            });
          });

          // 智能排序：将用户输入能完整匹配的结果放到最前面
          result = sortTablesByUserInput(result, tableName);

          setOdpsList(result);
        } else {
          setOdpsList([]);
        }
        setLoading(false);
      }).catch(() => {
        setOdpsList([]);
        setLoading(false);
      });
    });
  };

  //查询授权表
  const handleSearchOdpsTable = value => {
    // 只有当value有值时才调用搜索
    if (value && value.trim()) {
      getGrantedTable({ tableName: value.trim() });
    } else {
      // 清空搜索结果
      setOdpsList([]);
    }
  };

  // 复制odps表名
  const handleCopyOdpsTable = () => {
    const odpsTable = form.getFieldValue('odpsTable');
    if (odpsTable) {
      navigator.clipboard.writeText(odpsTable).then(() => {
        message.success('表名已复制到剪贴板');
      }).catch(() => {
        message.error('复制失败');
      });
    } else {
      message.warning('请先选择odps表');
    }
  };

  return (
    <>
      {/* 使用Portal将复制按钮渲染到Form外部，避免被Form的disabled属性影响 */}
      {copyButtonRef.current && createPortal(
        <Button
          icon={<CopyOutlined />}
          onClick={handleCopyOdpsTable}
          title="复制表名"
          disabled={false}
        />,
        copyButtonRef.current
      )}
      <Card hidden={current !== 0} title="数据源配置">
      <Form.Item
        noStyle
        shouldUpdate={(prevValues, curValues) =>
          prevValues.timeType !== curValues.timeType ||
          prevValues.source !== curValues.source ||
          prevValues.expiredType !== curValues.expiredType
        }
      >
        {({ getFieldValue }) => {
          const timeTypeValue = getFieldValue('timeType');
          const sourceValue = getFieldValue('source');
          const expiredTypeValue = getFieldValue('expiredType');
          if (sourceValue === 'LINDORM') {
            form.setFieldsValue({
              dataTable: 'tripODS.trip_picasso_user_profile',
            });
          } else if (sourceValue === 'HSF') {
            form.setFieldsValue({
              dataTable: 'TPP_ads_trip_rtus_usr_state',
            });
          }
          if(!type) {
            form.setFieldsValue({
              scopeList: ['MATCH'],
            });
          }
          if (timeTypeValue === 'OFFLINE') {
            return (
              <>
                <Form.Item
                  required
                  label={
                    <span>
                      odps表&nbsp;
                      <Tooltip title={'请选择odps表，优先显示已授权表，如无授权表则显示全量表'}>
                        <QuestionCircleOutlined />
                      </Tooltip>
                    </span>
                  }
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <Form.Item
                      rules={[{ required: true, message: '请输入表名' }]}
                      noStyle
                      name={'odpsTable'}
                    >
                      <Select
                        style={{ width: 400 }}
                        placeholder="请输入表名"
                        disabled={type === 'view'}
                        showSearch
                        filterOption={false}
                        notFoundContent={loading ? <Spin size="small" /> : null}
                        onSearch={debounce(handleSearchOdpsTable, 500)}
                        options={odpsList}
                        onChange={(value, option) => handleChange(value, option)}
                        optionRender={({ data: item }) => (
                          item.desc ? (
                            <Tooltip
                              title={
                                <>
                                  {item.label}
                                  <br />
                                  <span style={{ color: '#999999' }}>描述：{item.desc}</span>
                                </>
                              }
                            >
                              {item.label}
                              <br />
                              <span style={{ color: '#999999' }}>描述：{item.desc}</span>
                            </Tooltip>
                          ) : (
                            item.label
                          )
                        )}
                      />
                    </Form.Item>
                    <div ref={copyButtonRef} style={{ position: 'absolute', left: '416px' }}>
                      {/* 占位容器，实际按钮通过Portal渲染 */}
                    </div>
                  </div>
                </Form.Item>
                <Form.Item
                  rules={[{ required: true, message: '请选择日期分区' }]}
                  name="partitionField"
                  label={
                    <span>
                      日期分区&nbsp;
                      <Tooltip title={'请选择odps表的分区字段'}>
                        <QuestionCircleOutlined />
                      </Tooltip>
                    </span>
                  }
                >
                  <Select
                    style={{ width: 400 }}
                    options={notAuthOdpsConfig?.partitionOptions || []}
                    placeholder="请选择odps表的分区字段"
                    disabled={type === 'view'}
                    onChange={handleFieldChange}
                  />
                </Form.Item>
                <Form.Item
                  rules={[{ required: true, message: '请选择用户主键' }]}
                  name="primaryKey"
                  label={
                    <span>
                      用户主键&nbsp;
                      <Tooltip title={'请选择odps表的用户主键字段'}>
                        <QuestionCircleOutlined />
                      </Tooltip>
                    </span>
                  }
                >
                  <Select
                    style={{ width: 400 }}
                    placeholder="请选择odps表的用户主键字段"
                    options={notAuthOdpsConfig?.primaryKeyOptions || []}
                    disabled={type === 'view'}
                    onChange={handleFieldChange}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label?.toLowerCase() ?? '').includes(input.toLowerCase()) ||
                      (option?.desc?.toLowerCase() ?? '').includes(input.toLowerCase())
                    }
                    optionRender={({ data: item }) => (
                      <Tooltip
                        title={
                          <>
                            {item.label}
                            <br />
                            <span style={{ color: '#999999' }}>描述：{item.desc ?? '空'}</span>
                          </>
                        }
                      >
                        {item.label}
                        <br />
                        <span style={{ color: '#999999' }}>描述：{item.desc ?? '空'}</span>
                      </Tooltip>
                    )}
                  />
                </Form.Item>
                <Form.Item
                  noStyle
                  shouldUpdate={(prevValues, curValues) =>
                    prevValues.timeType !== curValues.timeType ||
                    prevValues.labelType !== curValues.labelType
                  }
                >
                  {({ getFieldValue }) => {
                    const labelTypeValue = getFieldValue('labelType');
                    if (timeTypeValue === 'OFFLINE' && labelTypeValue === 'SELF_DEFINED_SQL') {
                      if(!type) {
                        form.setFieldsValue({
                          scopeList: ['PUSH', 'MATCH'],
                        });
                      }

                      return (
                        <Form.Item label="SQL">
                          <Spin spinning={sqlCheckLoading} tip="校验中，请稍等...">
                            <Form.Item
                              noStyle
                              rules={[{ required: true, message: '请输入SQL' }]}
                              name={['sourceConfig', 'sqlConfig', 'sqlTemplate']}
                            >
                              <CodeMirror
                                disabled={type === 'view'}
                                mode="sql"
                                style={{
                                  display: 'inline-table',
                                  lineHeight: '20px',
                                  height: '300px',
                                  width: 800,
                                }}
                                editorChange={editorChange}
                              />
                            </Form.Item>
                            <Button
                              onClick={() => handleCheckSql(false)}
                              style={{ marginLeft: 20 }}
                              type="primary"
                              loading={sqlCheckLoading}
                            >
                              授权校验
                            </Button>
                          </Spin>
                        </Form.Item>
                      );
                    }
                    if (timeTypeValue === 'OFFLINE' && labelTypeValue === 'SINGLE_ATTRIBUTE') {
                      if(!type) {
                        form.setFieldsValue({
                          scopeList: ['PUSH', 'MATCH', 'ANALYSIS'],
                        });
                      }

                      return (
                        <>
                          <Form.Item
                            label={
                              <span>
                                更新频率&nbsp;
                                <Tooltip title={'请选择标签数据的更新调度频率'}>
                                  <QuestionCircleOutlined />
                                </Tooltip>
                              </span>
                            }
                            name="updatePeriod"
                          >
                            <Radio.Group disabled={type === 'view'}>
                              <Radio value="DAY">日更新</Radio>
                              <Radio value="WEEK">周更新</Radio>
                            </Radio.Group>
                          </Form.Item>
                          <Form.Item
                            name="selectedRowKeys"
                            label={
                              <span>
                                选择字段&nbsp;
                                <Tooltip title={'请选择数据字段进行标签配置和上架操作'}>
                                  <QuestionCircleOutlined />
                                </Tooltip>
                              </span>
                            }
                          >
                            {type === 'view' ? (
                              <Input disabled />
                            ) : (
                              <>
                                <div>
                                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '16px' }}>
                                    <div className={styles.fieldNote}>
                                      当前只支持配置单个标签进行上架（后续会支持批量上架上架能力）
                                    </div>
                                    <Button
                                      onClick={handleAuthCheck}
                                      type="primary"
                                      disabled={type === 'view' || isAuthCheckDisabled()}
                                      loading={checkLoading}
                                    >
                                      授权校验
                                    </Button>
                                  </div>
                                  <Table
                                    columns={columns}
                                    dataSource={columnDataSource}
                                    loading={odpsLoading}
                                    rowKey={'columnName'}
                                    pagination={{
                                      pageSize: 10,
                                    }}
                                    rowSelection={{
                                      selectedRowKeys: selectedRowKeys.map(item => item.columnName),
                                      disabled: type === 'view',
                                      type: 'radio',
                                      onChange: (selectedRowKeys, selectedRows) => {
                                        setSelectedRowKeys(selectedRows);
                                        handleFieldChange();
                                      },
                                      getCheckboxProps: record => ({
                                        disabled:
                                          record.isPrimaryKey ||
                                          record.isPartitionField ||
                                          (!record.isPrimaryKey && record.relatedLabel),
                                      }),
                                    }}
                                  />
                                </div>
                              </>
                            )}
                          </Form.Item>
                        </>
                      );
                    }
                  }}
                </Form.Item>
              </>
            );
          } else {
            return (
              <>
                <Form.Item
                  name="source"
                  rules={[{ required: true, message: '请选择实时类型' }]}
                  label="实时类型"
                >
                  <Radio.Group disabled={type === 'view'}>
                    <Radio value="LINDORM">MetaQ</Radio>
                    <Radio value="HSF">IGraph</Radio>
                  </Radio.Group>
                </Form.Item>
                <Form.Item name="expiredType" label="标签过期时间">
                  <Radio.Group disabled={type === 'view'}>
                    <Radio value={'NEVER_EXPIRED'}>不过期</Radio>
                    <Radio value={'NOWADAYS'}>当日过期（0-24点）</Radio>
                    <Radio value={'USER_DEFINED'}>
                      自定义设置{' '}
                      <Form.Item noStyle name="ttl">
                        <MyInputNumber
                          disabled={expiredTypeValue !== 'USER_DEFINED' || type === 'view'}
                        />
                      </Form.Item>{' '}
                      H（小时）
                    </Radio>
                  </Radio.Group>
                </Form.Item>
                <Form.Item
                  name="dataTable"
                  rules={[{ required: true, message: '请输入数据表' }]}
                  label="数据表"
                >
                  <Input disabled={sourceValue === 'LINDORM' || type === 'view'} />
                </Form.Item>
              </>
            );
          }
        }}
      </Form.Item>
    </Card>
    </>
  );
};

export default DataSourceConfig;
