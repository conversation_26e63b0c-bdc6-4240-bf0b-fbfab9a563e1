import { QuestionCircleFilled, QuestionCircleOutlined } from '@ant-design/icons';
import { Card, Checkbox, Form, Radio, Tooltip } from 'antd';

const BasicConfig = props => {
  const { current, type, isSuperAdmin, setSelectedRowKeys, isBehaviorLabel } = props;
  return (
    <Card hidden={current !== 0} title="基础配置">
      <Form.Item
        label={
          <span>
            所属租户&nbsp;
            <Tooltip title="请选择标签可见的租户范围，“小二租户”即为诸葛系统所用租户，面相内部同学">
              <QuestionCircleOutlined />
            </Tooltip>
          </span>
        }
        name="bizRegionList"
      >
        <Checkbox.Group disabled={type}>
          <Checkbox value="PUBLIC_REGION">小二租户</Checkbox>
          <Checkbox value="MERCHANT_REGION">商家租户</Checkbox>
          <Checkbox value="COMMERCIALIZATION_REGION">商业化租户</Checkbox>
        </Checkbox.Group>
      </Form.Item>
      <Form.Item
        label={
          <span>
            用户主键&nbsp;
            <Tooltip
              title={
                <>
                  请选择用户标签的实体主键类型：
                  <p style={{ marginBottom: 4 }}>● “淘宝id”指淘宝账户uid</p>
                  <p style={{ marginBottom: 0 }}>
                    ● “设备id”指用户手机的唯一设备码（如ios的idfa，Android的imei等）
                  </p>
                </>
              }
            >
              <QuestionCircleOutlined />
            </Tooltip>
          </span>
        }
        name="accountType"
      >
        <Radio.Group disabled={type}>
          <Radio value="USER">淘宝id</Radio>
          <Radio value="DEVICE">设备id</Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item
        label={
          <span>
            标签时效&nbsp;
            <Tooltip
              title={
                <>
                  请选择标签数据的更新时效：
                  <p style={{ marginBottom: 4 }}>● “离线标签”是天/周/月进行更新</p>
                  <p style={{ marginBottom: 0 }}>● “实时标签”是分钟级或者秒级进行更新</p>
                </>
              }
            >
              <QuestionCircleOutlined />
            </Tooltip>
          </span>
        }
        name="timeType"
      >
        <Radio.Group
          onChange={() => {
            setSelectedRowKeys([]);
          }}
          disabled={type}
        >
          <Radio value="OFFLINE">离线</Radio>
          <Radio value="REALTIME">实时</Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item
        noStyle
        shouldUpdate={(prevValues, curValues) => 
          prevValues.timeType !== curValues.timeType || 
          prevValues.bizRegionList !== curValues.bizRegionList
        }
      >
        {({ getFieldValue }) => {
          const timeTypeValue = getFieldValue('timeType');
          const bizRegionList = getFieldValue('bizRegionList');
          
          // 检查是否只选择了商业化租户
          const isOnlyCommercializationRegion = bizRegionList && 
            bizRegionList.length === 1 && 
            bizRegionList.includes('COMMERCIALIZATION_REGION');
          
          return (
            <Form.Item label="标签类型" name="labelType">
              <Radio.Group
                onChange={() => {
                  setSelectedRowKeys([]);
                }}
                disabled={type}
              >
                <Radio value="SINGLE_ATTRIBUTE">
                  单一属性标签&nbsp;
                  <Tooltip
                    title={
                      <>
                        <p style={{ marginBottom: 4 }}>
                          ●
                          定义：以用户唯一标识作为主键存储各类画像标签数据，且仅通过但一个的属性值，即可定义成一个标签
                        </p>
                        <p style={{ marginBottom: 4 }}>● 示例：</p>
                        <p style={{ marginBottom: 4 }}>&nbsp;&nbsp;○ 圈人场景：性别为 男 的人群</p>
                        <p style={{ marginBottom: 4 }}>&nbsp;&nbsp;○ 数据结构：</p>
                        <p style={{ marginBottom: 4 }}>
                          &nbsp;&nbsp;&nbsp;&nbsp;■ 用户实体主键-淘宝id：123
                        </p>
                        <p style={{ marginBottom: 4 }}>&nbsp;&nbsp;&nbsp;&nbsp;■ 属性-性别：男</p>
                      </>
                    }
                  >
                    <QuestionCircleFilled />
                  </Tooltip>
                </Radio>
                {(((timeTypeValue === 'OFFLINE' && isSuperAdmin) || type) || isOnlyCommercializationRegion) && (
                  <Radio value="SELF_DEFINED_SQL">
                    自定义SQL标签&nbsp;
                    <Tooltip
                      title={
                        <>
                          <p style={{ marginBottom: 4 }}>
                            ● 定义：自定义SQL标签多为标签逻辑复杂限制条件多，或为明细表sum汇总
                          </p>
                          <p style={{ marginBottom: 0 }}>
                            ● 注意：推荐大家将标签逻辑优化并生成结果表，这样能提升圈人速度和节约资源
                          </p>
                        </>
                      }
                    >
                      <QuestionCircleFilled />
                    </Tooltip>
                  </Radio>
                )}
                {
                  isBehaviorLabel && (
                    <Radio value="BEHAVIOR">
                      行为标签
                    </Radio>
                  )
                }
              </Radio.Group>
            </Form.Item>
          );
        }}
      </Form.Item>
    </Card>
  );
};

export default BasicConfig;
