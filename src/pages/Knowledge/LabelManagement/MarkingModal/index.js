import { Button, Form, Radio, Select, Row, Col, Checkbox, Tag, Switch, Modal } from 'antd';
import { WarningFilled } from '@ant-design/icons';
import styles from './index.less';

const MarkingModal = ({ visible, onCancel, onOk, tagConfig, setTagConfig, record, isSuperAdmin }) => {
  return (
    <Modal className={styles.markingModal} title="打标" visible={visible} onCancel={onCancel} onOk={onOk}>
      {isSuperAdmin && (
        <div className={styles.markingItem}>
          <Tag color='#e5cffa' style={{color: '#2a8189'}}>官方</Tag>
          <Checkbox disabled={record.openScope === 'PRIVATE'} onChange={e => setTagConfig({...tagConfig, isOfficial: e.target.checked})} checked={tagConfig.isOfficial}>官方维护标签</Checkbox>
        </div>
      )}
      <div className={styles.markingItem}>
        <Tag color='#e40d09' style={{color: '#fff'}}>热门</Tag>
        <Switch onChange={e => setTagConfig({...tagConfig, openHotTag: e})} checked={tagConfig.openHotTag} /> <WarningFilled style={{color: 'red'}} /> <span className={styles.markingItemText}>控制是否参与标签自动打标</span>
      </div>
    </Modal>
  );
};

export default MarkingModal;
