import React, { useState, useEffect } from 'react';
import { Form, Row, Col, Input, Button, Select } from 'antd';
const layout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const { Option } = Select;

const STORAGE_KEY = 'labelManagement_searchParams';

const SearchForm = props => {
  const { onSearchSubmit, tabKey } = props;
  const [form] = Form.useForm();

  // 保存筛选条件到 localStorage
  const saveSearchParams = (params) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(params));
    } catch (error) {
      console.error('保存筛选条件失败:', error);
    }
  };

  // 从 localStorage 恢复筛选条件
  const loadSearchParams = () => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      return saved ? JSON.parse(saved) : {};
    } catch (error) {
      console.error('加载筛选条件失败:', error);
      return {};
    }
  };

  // 组件初始化时恢复筛选条件
  useEffect(() => {
    // 检查是否是页面刷新
    const isPageRefresh = () => {
      if (window.performance && window.performance.navigation) {
        return window.performance.navigation.type === 1;
      }
      return false;
    };

    // 如果是页面刷新，不恢复筛选条件
    if (isPageRefresh()) {
      return;
    }

    const savedParams = loadSearchParams();
    if (Object.keys(savedParams).length > 0) {
      // 过滤掉 tabKey、分页相关的参数和分组相关参数，只恢复表单字段
      const { tabKey, pageNo, pageSize, classifyTab, categoryId, ...formParams } = savedParams;
      form.setFieldsValue(formParams);
    }
  }, []);

  const onSubmit = () => {
    form.submit();
  };

  const onReset = () => {
    form.resetFields();
    // 清除保存的筛选条件
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error('清除筛选条件失败:', error);
    }
    onSearchSubmit({
      isReset:true
    });
  };

  const onFinish = values => {
    // 保存筛选条件到 localStorage，包含 tabKey，但重置 pageNo 为 1
    const paramsToSave = { ...values, tabKey, pageNo: 1 };
    saveSearchParams(paramsToSave);

    onSearchSubmit(values);
  };

  return (
    <Form
      form={form}
      {...layout}
      onFinish={onFinish}
      onValuesChange={(value, allValues) => {
        // 表单值变化时，保存到 localStorage
        const paramsToSave = { ...allValues, tabKey };
        saveSearchParams(paramsToSave);
      }}
    >
      <Row gutter={24}>
        <Col span={8}>
          <Form.Item name="content" label="标签信息">
            <Input
              allowClear
              placeholder="请输入标签id,标签名称,负责人花名或工号"
              style={{ minWidth: 200 }}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item  name="bizRegionList" label="所属租户">
            <Select
              defaultValue={["PUBLIC_REGION"]}
              options={[
                {
                  label: '小二租户',
                  value: 'PUBLIC_REGION',
                },
                {
                  label: '商家租户',
                  value: 'MERCHANT_REGION',
                },
                {
                  label: '商业化租户',
                  value: 'COMMERCIALIZATION_REGION',
                },
              ]}
              mode="multiple"
              placeholder="请选择"
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item name="statusList" label="标签状态">
            <Select
              defaultValue={['ACTIVATE', 'WAIT_ONLINE','APPROVING','ONLINE_ING']}
              placeholder="请输入标签名称"
              mode="multiple"
              style={{ minWidth: 200 }}
            >
              <Option key="ACTIVATE">使用中</Option>
              <Option key="WAIT_ONLINE">待上线</Option>
              <Option key="WAIT_OFFLINE">待下线</Option>
              <Option key="OFFLINE">已下线</Option>
              <Option key="APPROVING">审批中</Option>
              <Option key="ONLINE_ING">上线中</Option>
              <Option key="APPROVE_FAILED">审批不通过</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24} style={{ marginBottom: 10 }}>
        <Col span={8}>
          <Form.Item name="openScopeList" label="公开范围">
            <Select
              allowClear
              options={[
                {
                  label: '公开标签',
                  value: 'PUBLIC',
                },
                {
                  label: '私有标签',
                  value: 'PRIVATE',
                },
              ]}
              mode='multiple'
              placeholder="请选择标签公开范围"
              style={{ minWidth: 200 }}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item name="tagList" label="打标">
            <Select
              allowClear
              options={[
                {
                  label: '官方标签',
                  value: 'OFFICIAL',
                },
                {
                  label: '热门标签',
                  value: 'HIGH_HOT',
                },
              ]}
              mode='multiple'
              placeholder="请选择打标标签"
              style={{ minWidth: 200 }}
            ></Select>
          </Form.Item>
        </Col>
        <Col span={8} style={{ textAlign: 'right' }}>
          <Button type="primary" onClick={onSubmit} style={{ marginRight: 10 }}>
            查询
          </Button>
          <Button htmlType="button" onClick={onReset} style={{ marginRight: 10 }}>
            重置
          </Button>
        </Col>
      </Row>
    </Form>
  );
};

export default SearchForm;
