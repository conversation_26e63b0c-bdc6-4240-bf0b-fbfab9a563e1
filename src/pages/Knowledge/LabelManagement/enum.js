export const TimeTypeEnum = {
    "OFFLINE": "离线标签",
    "REALTIME": "实时标签"
}

export const ProfileCodeEnum = {
    "USER": "淘宝id",
    "DEVICE": "设备id",
    "SCRM_USER": "scrm用户画像",
    "GALAXY": "星辰物料画像"
}

export const LabelTypeEnum = {
    "SINGLE_ATTRIBUTE": "单一属性标签",
    "COMBINE_DIMENSION": "组合维度标签",
    "BEHAVIOR": "行为标签",
    "SELF_DEFINED_SQL": "自定义SQL标签"
}

export const LabelStatusEnum = {
    "WAIT_ONLINE": "待上线",
    "ACTIVATE": "使用中",
    "WAIT_OFFLINE": "待下线",
    "OFFLINE": "已下线",
    "APPROVING": "审批中",
    "ONLINE_ING": "上线中",
    "APPROVE_FAILED":"审批不通过"
}

export const LabelTagEnumMapping = {
    "OFFICIAL": { bg:"#e5cffa", name:"官方", textColor:"#2a8189" },
    "HIGH_QUALITY": { color: "#096dd9", name: "质保", textColor: "#fff" },
    "HIGH_HOT": { color: "#e40d09", name: "热门", textColor: "#fff" },
}