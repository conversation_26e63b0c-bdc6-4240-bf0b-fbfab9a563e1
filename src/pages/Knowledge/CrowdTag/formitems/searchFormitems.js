import React from 'react';
import GetUser from '@/components/GetUser';

const searchFormItems = [
  {
    type: 'INPUT',
    label: '标签ID',
    field: 'id',
    forItemOptions: {
      initialValue: '',
    },
    componentOptions: {
      placeholder: '请输入标签ID',
    },
  },
  {
    type: 'INPUT',
    label: '标签名称',
    field: 'propertyName',
    forItemOptions: {
      initialValue: '',
    },
    componentOptions: {
      placeholder: '请输入标签名',
    },
  },
  {
    type: 'SELECT',
    label: '标签状态',
    field: 'status',
    forItemOptions: {
      initialValue: '',
    },
    componentOptions: {
      placeholder: '请选择标签状态',
    },
    children: [
      {
        name: '全部',
        value: 'ALL',
      },
      {
        name: '使用中',
        value: 'ACTIVATE',
      },
      {
        name: '待下线',
        value: 'WAIT_OFFLINE',
      },
      {
        name: '已下线',
        value: 'OFFLINE',
      },
    ],
  },
  {
    type: 'INPUT',
    label: '业务负责人',
    field: 'bizOwner',
    forItemOptions: {
      initialValue: '',
    },
    componentOptions: {
      placeholder: '请输入业务负责人',
    },
    component: <GetUser />,
  },
  {
    type: 'INPUT',
    label: '数据表',
    field: 'dataTable',
    forItemOptions: {
      initialValue: '',
    },
    componentOptions: {
      placeholder: '请输入数据表',
    },
  },
  {
    type: 'RADIO',
    label: '',
    field: 'onlysee',
    forItemOptions: {
      initialValue: '',
    },
    componentOptions: {
      placeholder: '请输入数据表',
    },
    children: [
      {
        name: '只看精华标签',
        value: 'onlysee',
      },
    ],
  },
];

export default searchFormItems;
