import React from 'react';
import TagClassfy from '../components/Category';
import ParamSetting from '../components/ParamSetting';
import GetUser from '@/components/GetUser';
import Stable from '../components/table';

// 处理联动的情形，createFormItems为一个接受editFormData参数方法
const getCreateFormItems = editFormData => {
  console.log('editFormData', editFormData);
  return [
    {
      type: 'INPUT',
      label: '英文名称',
      field: 'propertyName',
      forItemOptions: {
        initialValue: '',
        rules: [{ required: true, message: '请输入标签英文名称' }],
      },
      componentOptions: {
        placeholder: '请输入标签英文名称',
      },
    },
    {
      label: '管理员',
      field: 'bizOwner',
      forItemOptions: {
        initialValue: [],
        rules: [{ required: true, message: '请选择管理员' }],
      },
      componentOptions: {
        placeholder: '请输入英文名称',
      },
      component: <GetUser />,
    },
    {
      type: 'INPUT',
      label: '中文名称',
      field: 'chineseName',
      forItemOptions: {
        initialValue: '',
        rules: [{ required: true, message: '请输入中文名称' }],
      },
      componentOptions: {
        placeholder: '请输入中文名称',
      },
    },
    {
      type: 'RADIO',
      label: '标签时效',
      field: 'realTime2',
      forItemOptions: {
        initialValue: '',
        rules: [{ required: true, message: '请选择标签时效' }],
      },
      componentOptions: {
        placeholder: '请输入中文名称',
      },
      children: [
        {
          name: '非实时',
          value: 'noRealTime',
        },
        {
          name: '实时',
          value: 'realTime',
        },
      ],
    },
    {
      type: 'TEXTAREA',
      label: '标签描述',
      field: 'propertyDescription',
      forItemOptions: {
        initialValue: '',
        rules: [{ required: true, message: '请输入标签描述' }],
      },
      componentOptions: {
        placeholder: '请输入标签描述',
      },
    },
    {
      label: '业务负责人',
      field: 'bizOwners',
      forItemOptions: {
        initialValue: [],
        rules: [{ required: true, message: '请选择业务负责人' }],
      },
      componentOptions: {
        placeholder: '请选择业务负责人',
      },
      component: <GetUser />,
    },
    {
      label: '质量责任人',
      field: 'qualityOwner',
      forItemOptions: {
        initialValue: '',
        rules: [{ required: true, message: '请输入质量责任人' }],
      },
      componentOptions: {
        placeholder: '请输入质量责任人',
      },
      component: <GetUser />,
    },
    {
      label: '数据责任人',
      field: 'dataOwner',
      forItemOptions: {
        initialValue: '',
        rules: [{ required: true, message: '请输入数据责任人' }],
      },
      componentOptions: {
        placeholder: '请输入数据责任人',
      },
      component: <GetUser />,
    },
    {
      label: '标签值类型',
      field: 'categoryId',
      forItemOptions: {
        initialValue: '',
        // rules: [{ required: true, message: '请输入标签值类型' }],
      },
      component: <TagClassfy />,
    },
    {
      field: 'sourceGuid',
      forItemOptions: {
        initialValue: '',
        rules: [{ required: true, message: '请输入数据表' }],
      },
      componentOptions: {
        placeholder: '请输入数据表',
      },
      component: <Stable />,
      extra: (
        <a target="_blank" rel="noopener noreferrer">
          授权说明
        </a>
      ),
    },
    {
      type: 'INPUT',
      label: '标签字段',
      field: 'tagField',
      forItemOptions: {
        initialValue: '',
        rules: [{ required: true, message: '请输入数据表' }],
      },
      componentOptions: {
        placeholder: '请输入数据表',
      },
    },
    {
      type: 'RADIO',
      label: '标签值类型',
      field: 'realTime1',
      forItemOptions: {
        initialValue: '',
        rules: [{ required: true, message: '请选择是标签值类型' }],
      },
      children: [
        {
          name: '枚举型',
          value: 'noRealTime',
        },
        {
          name: '数字型',
          value: 'realTime3',
        },
        {
          name: '日期型',
          value: 'realTime5',
        },
        {
          name: 'KV型',
          value: 'realTime4',
        },
      ],
    },
    {
      label: '参数配置',
      field: 'tagValueType',
      forItemOptions: {
        initialValue: '',
        // rules: [{ required: true, message: '请输入参数配置' }],
      },
      component: <ParamSetting />,
    },
    {
      type: 'CHECKBOX',
      label: '同步范围',
      field: 'syncScope',
      forItemOptions: {
        initialValue: [],
        rules: [{ required: true, message: '请选择标签时效' }],
      },
      componentOptions: {
        placeholder: '请输入中文名称',
      },
      children: [
        {
          name: 'AUGE',
          value: 'AUGE',
        },
        {
          name: 'DMP',
          value: 'DMP',
        },
        {
          name: 'NOMO',
          value: 'NOMO',
        },
      ],
    },
  ];
};

export default getCreateFormItems;
