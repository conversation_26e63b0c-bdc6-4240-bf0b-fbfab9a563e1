import { message } from 'antd';
import pathToRegexp from 'path-to-regexp';
import { queryBuildTree, queryLabel, operateLabel, CheckIsAdmin,editDeleteSQL, isCommercializationManager } from '@/services/api';

export default {
  namespace: 'crowdTag',
  state: {
    loading: false,
    treeLoading: false,
    treeData: [],
    labels: [],
    searchFormData: {
      status: 'ALL',
      bizRegionCode: 'public_region'
      // tag: 'all',
    },
    selectNode: 0, // 选择的叶子结点ID，用来分页过滤
    /**
     * 编辑
     */
    editFormData: {},
    modalVisible: false,
    pagination: {},
    selectedRowKeys: [],
    entityName: null,
    isFold: false,
    drawerVisible: false,
    drawerGenehmigung: false,
    displayRows: [],
    genehmigungFormData: {},
    genehmigungOwers:[],
    isAdmin: false, // 是否是管理员
    isCommercializationManager: false, // 是否展示商业化选项
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(({ pathname }) => {
        if (pathname.includes('/develop-tool/crowd-tag')) {
          const match = pathToRegexp('/develop-tool/crowd-tag/:id').exec(pathname);
          console.log('match--',match)
          if (match) {
            dispatch({
              type: 'updateState',
              payload: {
                entityName: match[1],
                treeData: [], // 每次需要清空此状态
                pagination: {
                  pageNum: 1,
                },
              },
            });
          }
          dispatch({
            type: 'query',
            payload: {
              entityName: match[1],
            },
          });
        }
      });
    },
  },
  effects: {
    *query({ payload }, { put, all }) {
      const arr = [
        put.resolve({
          type: 'queryLabel',
          payload: {
            // bizEntityName: payload.entityName,
          },
        }),
        put.resolve({
          type: 'queryBuildTree',
          payload: {
            bizEntityName: payload.entityName,
          },
        }),
      ];

      yield all(arr);
    },
    *queryBuildTree({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: {
          treeLoading: true,
        },
      });
      const result = yield call(queryBuildTree, payload);

      if (!result.success) {
        message.error(result.msg);
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          treeLoading: false,
          treeData: (result.data && result.data.root && result.data.root.children) || [],
        },
      });
    },
    *queryLabel({ payload = {} }, { call, put, select }) {
      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const { pagination, searchFormData, selectNode } = yield select(state => state.crowdTag);

      const sfd = Object.assign({}, searchFormData, {
        bizOwner: searchFormData.bizOwner && JSON.stringify(searchFormData.bizOwner),
      });

      // 删除空值筛选条件
      Object.keys(sfd).forEach(k => !sfd[k] && delete sfd[k]);
      if (sfd.official) {
        if (sfd.official === 'official') {
          sfd.official = true
        } else if (sfd.official === 'new') {
          sfd.official = false
        } else if (sfd.official === 'all') { 
          delete sfd.official 
        }
      }

      const res = yield call(queryLabel, {
        deleted: 0,
        pageNum: pagination.current || 1,
        pageSize: pagination.pageSize || 10,
        categoryId: selectNode || undefined,
        ...sfd,
        ...payload,
      });

      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }

      const { rows, totalNum } = res.data;

      pagination.total = totalNum;

      yield put({
        type: 'updateState',
        payload: {
          labels: rows,
          loading: false,
          pagination,
        },
      });
    },
    *excuteAction({ payload }, { call, select, put }) {
      const { selectedRowKeys } = yield select(state => state.crowdTag);

      if (!payload) {
        return;
      }

      if (!payload.batchOperateAction) {
        message.error('请选择要执行的操作');
        return;
      }

      if (!selectedRowKeys || !selectedRowKeys.length) {
        message.error('请选择要操作的标签');
        return;
      }

      const data = {
        ...payload,
        labelIdList: selectedRowKeys,
      };

      const res = yield call(operateLabel, data);

      if (!res.success) {
        message.error(res.msg || '操作失败');
        return;
      }

      message.success('操作成功');

      yield put({
        type: 'queryLabel',
      });
    },
    *delete({ payload }, { put, call }) {
      if (!payload.id) {
        return;
      }

      const res = yield call(editDeleteSQL, payload);

      if (!res.success) {
        message.error(res.msg || '删除失败');
        return;
      }

      message.success('删除成功');

      yield put({
        type: 'queryLabel',
      });
    },
    *onCheckIsAdmin({ payload }, { put, call }){
      const res = yield call(CheckIsAdmin)

      if (!res.success) {
        console.log('查询是否是管理员失败！');
        return;
      }

      yield put({
        type: 'updateState',
        payload: {isAdmin: res.data}
      })
    },
    *onCommercializationManager({ payload }, { put, call }){
      const res = yield call(isCommercializationManager)

      if (!res.success) {
        console.log(res?.msg || '查询失败');
        return;
      }

      yield put({
        type: 'updateState',
        payload: {isCommercializationManager: res.data}
      })
    }
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    updateSearchFormData(state, { payload }) {
      return {
        ...state,
        searchFormData: {
          ...state.searchFormData,
          ...payload,
        },
      };
    },
  },
};
