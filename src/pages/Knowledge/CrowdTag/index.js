import React, { PureComponent, Fragment } from 'react';
import {
  Row,
  Col,
  Card,
  Table,
  Button,
  Input,
  Select,
  Radio,
  Tag,
  Popconfirm,
  Menu,
  Dropdown,
  Popover,
  Divider
} from 'antd';
import dayjs from 'dayjs';
import { Link }  from 'umi';
import { history } from 'umi';
import { connect } from 'dva';
import MsgDrawer from '@/components/MsgDrawer';
import SearchTree from '@/components/SearchTree';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import SearchForm from '@/components/SearchForm';
import MyIcon from '@/components/MyIcon';
import ActionForm from './components/ActionForm';
import UserFeedBack from './components/UserFeedBack';
import { LABEL_STATUS, TAGS_ICON } from './constants';
import { QuestionCircleOutlined } from '@ant-design/icons';
import styles from './index.less';

const { Option } = Select;
const RadioGroup = Radio.Group;
const FORM_ITEM_LAYOUT = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

@connect(state => ({ crowdTag: state.crowdTag, user: state.user }))
class CrowdTag extends PureComponent {
  componentDidMount() {
    const { dispatch } = this.props;

    dispatch({
      type: 'crowdTag/onCheckIsAdmin',
    });

    dispatch({
      type: 'crowdTag/onCommercializationManager'
    })
  }

  // 创建表单values变化
  onValuesChange = values => {
    const { dispatch } = this.props;

    dispatch({
      type: 'crowdTag/onEditFormDataChange',
      payload: values,
    });
  };

  // 点击叶子节点
  onSelect = node => {
    if (!node) {
      return;
    }

    const {
      dispatch,
      crowdTag: { pagination },
    } = this.props;

    dispatch({
      type: 'crowdTag/updateState',
      payload: {
        selectNode: node.id,
        pagination: {
          ...pagination,
          current: 1,
        }
      },
    });

    dispatch({
      type: 'crowdTag/queryLabel',
      payload: {
        categoryId: node.id,
      },
    });
  };

  onModalVisible = visible => {
    const { dispatch } = this.props;

    dispatch({
      type: 'crowdTag/onModalVisible',
      payload: {
        visible,
      },
    });
  };

  handleTableChange = pagination => {
    const {
      dispatch,
      crowdTag: { pagination: pager, selectNode },
    } = this.props;

    dispatch({
      type: 'crowdTag/updateState',
      payload: {
        pagination: {
          ...pager,
          current: pagination.current,
          pageSize: pagination.pageSize,
        },
      },
    });

    dispatch({
      type: 'crowdTag/queryLabel',
      payload: {
        pageNum: pagination.current,
        categoryId: selectNode || null,
      },
    });
  };

  onAction = values => {
    const { dispatch } = this.props;

    dispatch({
      type: 'crowdTag/excuteAction',
      payload: values,
    });
  };

  toggleFold = isFold => {
    const { dispatch } = this.props;

    dispatch({
      type: 'crowdTag/updateState',
      payload: {
        isFold,
      },
    });
  };

  delete = id => {
    const { dispatch } = this.props;

    dispatch({
      type: 'crowdTag/delete',
      payload: {
        id,
        deleted: 1,
      },
    });
  };

  onShowRowDetailDrawer = record => {
    const { dispatch } = this.props;
    const {
      id,
      name,
      code,
      address,
      bizOwner,
      qualityOwner,
      dataOwner,
      dataUpdateTime,
      description,
      sourceConfig,
    } = record;

    const { project, table } = sourceConfig
    let sourceGuid = `${project}.${table}`

    dispatch({
      type: 'crowdTag/updateState',
      payload: {
        displayRows: [
          {
            label: '标签ID',
            value: id,
          },
          {
            label: '标签名称',
            value: name,
          },
          {
            label: '英文名称',
            value: code,
          },
          {
            label: '标签描述',
            value: description,
          },
          {
            label: '覆盖人数',
            value: address,
          },
          {
            label: '业务负责人',
            value: bizOwner && bizOwner.nickName,
          },
          {
            label: '质量负责人',
            value: qualityOwner && qualityOwner.nickName,
          },
          {
            label: '数据负责人',
            value: dataOwner && dataOwner.nickName,
          },
          {
            label: '数据表',
            value: (
              <Popover content={sourceGuid} trigger="hover">
                <a>查看表名</a>
              </Popover>
            ),
          },
          {
            label: '数据更新时间',
            value:
              dataUpdateTime === -28800000
                ? '无'
                : dayjs(dataUpdateTime).format('YYYY-MM-DD HH:mm:ss'),
          },
        ],
        drawerVisible: true,
      },
    });
  };

  onShowGenehmigungDrawer = record => {
    const { dispatch } = this.props;
    const { creator, dataOwner } = record
    dispatch({
      type: 'crowdTag/updateState',
      payload: {
        genehmigungFormData: record,
        drawerGenehmigung: true,
        genehmigungOwers: [creator, dataOwner]
      },
    });
  }

  onCloseGenehmigungDrawer = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'crowdTag/updateState',
      payload: {
        drawerGenehmigung: false,
        genehmigungOwers: []
      },
    });
  }

  onCloseRowDetailDrawer = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'crowdTag/updateState',
      payload: {
        drawerVisible: false,
      },
    });
  };

  render() {
    const {
      dispatch,
      crowdTag: {
        isFold,
        treeData,
        labels,
        loading,
        pagination,
        treeLoading,
        searchFormData,
        entityName,
        drawerVisible,
        displayRows,
        isAdmin,
        isCommercializationManager,
        drawerGenehmigung,
        genehmigungFormData,
        genehmigungOwers
      },
      user: { currentUser } 
    } = this.props;

    // 是否存在树的数据
    const isExistTreeData = treeData && treeData.length > 0;

    const columns = [
      {
        title: '英文名称',
        dataIndex: 'code',
        key: 'code',
        render: text => <Popover content={text}>{text}</Popover>,
      },
      {
        title: '标签名称',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        render: (text, record) => {
          const { extInfo } = record;

          return (
            <span style={{ display: 'flex', alignItems: 'center' }}>
              {extInfo && extInfo.tags && extInfo.tags.length > 0 && (
                <span style={{ position: 'absolute', top: 0, left: 0 }}>
                  {extInfo.tags.map(t => (
                    <Fragment key={t}>
                      <MyIcon
                        style={{ fontSize: 14, marginLeft: 5, paddingTop: 4 }}
                        type={TAGS_ICON[t]}
                      />
                    </Fragment>
                  ))}
                </span>
              )}
              <Popover content={text}>
                <span className={styles.propertyDescriptionText}>{text}</span>
              </Popover>
            </span>
          );
        },
      },
      {
        title: '人数',
        dataIndex: 'address',
        key: 'address',
        render: text => text || '无',
      },
      {
        title: <span>
          收费&nbsp;
          <Popover content={<a href='https://aliyuque.antfin.com/dl7v63/zwzgqr/subnu53efxtozyro?singleDoc# ' target='_blank'>查看收费规则</a>}>
            <QuestionCircleOutlined />
          </Popover>
        </span>,
        dataIndex: 'isPay',
        key: 'isPay',
        render: (text, record) => {
          return (
            <div>{record && record.extInfo && record.extInfo.isPay ? '付费' : '免费'}</div>
          )
        }
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: s => <Tag color={LABEL_STATUS[s] ? LABEL_STATUS[s].status : 'processing'}>{LABEL_STATUS[s] ? LABEL_STATUS[s].text : '处理中'}</Tag>,
      },
      {
        title: '标签健康分',
        dataIndex: 'healthScore',
        key: 'healthScore',
        render: text => text || '100%',
      },
      {
        title: '用户反馈',
        dataIndex: 'userFeedback',
        key: 'userFeedback',
        render: (text, record) => <UserFeedBack currentUser={currentUser} record={record} />
      },
      {
        title: '数据更新时间',
        dataIndex: 'dataUpdateTime',
        key: 'dataUpdateTime',
        render: dataUpdateTime =>
        (dataUpdateTime === -28800000
          ? '无'
          : dayjs(dataUpdateTime).format('YYYY-MM-DD HH:mm:ss')),
      },
      {
        title: '数据时间',
        dataIndex: 'olapDataRefluxedDs',
        key: 'olapDataRefluxedDs',
        render: text => text || '暂无数据'
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        fixed: 'right',
        render: (text, record) => {
          const menu = (
            <Menu>
              <Menu.Item
                key="1"
                onClick={() => {
                  this.onShowRowDetailDrawer(record);
                }}
              >
                更多信息
              </Menu.Item>
              <Menu.Item
                key="2"
                onClick={() => {
                  history.push(`/develop-tool/create-crowd-tag/${entityName}?id=${record.id}`);
                }}
              >
                编辑
              </Menu.Item>
              <Menu.Item key="3" disabled>
                标签详情
              </Menu.Item>
              <Menu.Item key="4" disabled>
                标签质量
              </Menu.Item>
            </Menu>
          );
          return (
            <div>
              <Popconfirm
                title="是否删除?"
                onConfirm={() => this.delete(record.id)}
                okText="确定"
                cancelText="取消"
              >
                <a>删除</a>
              </Popconfirm>
              <Divider type="vertical" />
              <Dropdown overlay={menu}>
                <a>更多</a>
              </Dropdown>
            </div>
          );
        },
      },
    ];

    const searchForm = {
      searchForm: searchFormData,
      setting: [
        {
          label: '标签名称',
          formItemLayout: FORM_ITEM_LAYOUT,
          target: <Input allowClear placeholder="请输入标签名称" style={{ minWidth: 200 }} />,
          name: 'name',
        },
        {
          label: '英文名称',
          target: <Input allowClear placeholder="请输入英文名称" />,
          name: 'code',
        },
        {
          label: '标签状态',
          formItemLayout: FORM_ITEM_LAYOUT,
          target: (
            <Select placeholder="请输入标签名称" style={{ minWidth: 200 }}>
              <Option key="ALL">全部</Option>
              <Option key="ACTIVATE">使用中</Option>
              <Option key="WAIT_ONLINE">待上线</Option>
              <Option key="WAIT_OFFLINE">待下线</Option>
              <Option key="OFFLINE">已下线</Option>
            </Select>
          ),
          name: 'status',
        },
        {
          label: '数据表名',
          formItemLayout: FORM_ITEM_LAYOUT,
          target: <Input allowClear placeholder="请输入数据表名" style={{ minWidth: 200 }} />,
          name: 'sourceGuid',
        },
        {
          target: (
            <RadioGroup style={{ minWidth: 250 }}>
              <Radio key="all" value="all">
                全部
              </Radio>
              <Radio key="official" value="official">
                精华标签
              </Radio>
              <Radio key="new" value="new">
                新标签
              </Radio>
            </RadioGroup>
          ),
          name: 'official',
        },
        {
          label: '租户类型',
          formItemLayout: FORM_ITEM_LAYOUT,
          target: (
            <Select placeholder="请选择租户类型" style={{ minWidth: 200 }} >
              <Option key="merchant_region">商家租户</Option>
              <Option key="public_region">小二租户</Option>
              {
                (isAdmin || isCommercializationManager) && <Option key="fliggy_commercialization">商业化租户</Option>
              }
            </Select>
          ),
          name: 'bizRegionCode',
        },
        {}, // 用于占位
        {}
      ],
      onValuesChange: values => {
        dispatch({
          type: 'crowdTag/updateSearchFormData',
          payload: values,
        });
      },
      handleSubmit: () => {
        // 置为第一页
        dispatch({
          type: 'crowdTag/updateState',
          payload: {
            pagination: {
              ...pagination,
              current: 1,
            },
          },
        });

        dispatch({
          type: 'crowdTag/queryLabel',
          payload: {
            pageNum: 1,
          },
        });
      },
    };

    const rowSelection = {
      onChange: selectedRowKeys => {
        dispatch({
          type: 'crowdTag/updateState',
          payload: {
            selectedRowKeys,
          },
        });
      },
    };

    return (
      <PageHeaderWrapper title="旧标签管理">
        <Card
          bordered={false}
          loading={treeLoading}
          style={{ minHeight: 800 }}
          className={styles.container}
        >
          <Row gutter={24}>
            {isExistTreeData ? (
              <Col
                span={isFold ? 1 : 5}
                style={{
                  transition: 'all 0.2s',
                }}
              >
                <Card className={styles.leftTree} bordered={false}>
                  <SearchTree
                    isFold={isFold}
                    toggleFold={this.toggleFold}
                    dataSource={treeData}
                    onSelect={this.onSelect}
                  />
                </Card>
              </Col>
            ) : (
              <Col span={5}></Col>
            )}
            <Col
              // eslint-disable-next-line no-nested-ternary
              span={isExistTreeData ? (isFold ? 23 : 19) : 24}
              style={{ transition: 'all 0.2s' }}
            >
              <SearchForm key="searchForm" {...searchForm} />

              <div style={{ position: 'relative', width: '100%', marginTop: 10, marginBottom: 20 }}>
                <ActionForm onAction={this.onAction} isShowBizRegionList={isAdmin || isCommercializationManager} />
                <Link
                  to={`/develop-tool/create-crowd-tag/${entityName}`}
                  style={{ position: 'absolute', right: 0, top: 0 }}
                >
                  <Button disabled={!isAdmin} type="primary">创建标签</Button>
                </Link>
              </div>
              <Card>
                <Table
                  rowSelection={rowSelection}
                  rowKey={record => record.id}
                  loading={loading}
                  dataSource={labels}
                  columns={columns}
                  scroll={{ x: 'max-content' }}
                  pagination={pagination}
                  onChange={this.handleTableChange}
                />
              </Card>
            </Col>
          </Row>
        </Card>
        <MsgDrawer
          title="标签详情"
          visible={drawerVisible}
          content={displayRows}
          onClose={this.onCloseRowDetailDrawer}
          width={400}
        />
      </PageHeaderWrapper>
    );
  }
}

export default CrowdTag;
