.container {
  :global {
    .ant-table-row .ant-table-cell {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .ant-table-row .ant-table-cell:nth-child(2) {
      max-width: 150px;
    }

    .ant-card-body {
      padding: 6px;
    }
  }

  .propertyDescriptionText {
    max-width: 200px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}

.leftTree {
  position: relative;
  min-height: 1000px;

  :global {
    .ant-card-body {
      padding-top: 8px;
    }
  }
}
