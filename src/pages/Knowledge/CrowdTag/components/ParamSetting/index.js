/* eslint-disable no-plusplus */
import React from 'react';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { Row, Col, Input, Button } from 'antd';
import styles from './index.less';

class ParamSetting extends React.Component {
  static getDerivedStateFromProps(nextProps) {
    // Should be a controlled component.
    if ('value' in nextProps) {
      return {
        ...(nextProps.value || {}),
      };
    }
    return null;
  }

  constructor(props) {
    super(props);

    const value = props.value || {};
    this.id = 0;
    this.state = {
      ...value,
      keys: [],
    };
  }

  remove = index => {
    const { keys } = this.state;

    if (keys.length === 1) {
      return;
    }

    this.setState({
      keys: keys.filter((k, i) => i !== index),
    });
  };

  add = () => {
    const { keys } = this.state;

    const nextKeys = keys.concat(this.id++);

    this.setState({
      keys: nextKeys,
    });
  };

  triggerChange = changedValue => {
    // Should provide an event to pass value to Form.
    const { onChange } = this.props;
    if (onChange) {
      onChange({
        ...this.state,
        ...changedValue,
      });
    }
  };

  render() {
    // console.log('触发更新', curTimeType);
    const { keys = [] } = this.state;

    const circleStyle = {
      cursor: 'pointer',
      position: 'relative',
      marginLeft: 5,
      fontSize: 24,
      color: '#999',
      transition: 'all 0.3s',
    };
    return (
      <div>
        {keys.length &&
          keys.map((k, i) => {
            return (
              <Row gutter={{ md: 12, lg: 24, xl: 48 }}>
                <Col md={12} sm={24} className={styles.paramSettingItem}>
                  <span className={styles.paramSettingItemLabel}>显示名：</span>
                  <Input style={{ width: 150 }} />
                </Col>
                <Col md={12} sm={24} className={styles.paramSettingItem}>
                  <span className={styles.paramSettingItemLabel}>实际值：</span>
                  <Input style={{ width: 150 }} />
                </Col>
                <Col>
                  <MinusCircleOutlined style={circleStyle} onClick={() => this.remove(i)} />
                </Col>
              </Row>
            );
          })}
        <div>
          <Button type="dashed" onClick={this.add} style={{ width: '20%' }}>
            <PlusOutlined /> 增加
          </Button>
        </div>
      </div>
    );
  }
}

export default ParamSetting;
