import React, { useEffect, useState } from 'react';
import { Tooltip, message } from 'antd';
import { DislikeFilled, DislikeOutlined, LikeFilled, LikeOutlined } from '@ant-design/icons';
import { userFeedback } from '@/services/api';
import classNames from 'classnames';
import styles from './index.less';


const UserFeedBack = (props) => {
  const [action, setAction] = useState(null);
  const { record, currentUser, refresh } = props

  useEffect(() => {
    setAction(record?.userFeedback)
  }, [record?.userFeedback])

  const onLike = async (upOrDown) => {
    if (action === upOrDown) return
    setAction(upOrDown)
    const data = {
      upOrDown,
      labelCode: record.code,
      operator: {
        empId: currentUser.workId,
        nickName: currentUser.name
      }
    }
    const res = await userFeedback(data)

    if (!res?.success) {
      message.error(res?.msg || '请求错误')
      return
    }

    message.success('反馈成功')
  }

  return (
    <div className={styles.feedback}>
      <span onClick={() => {onLike(1)}}>
        {action === 1 ? <LikeFilled style={{ color: '#e37c33' }} /> : <LikeOutlined />}
      </span>
      <span onClick={() => {onLike(-1)}}>
        {action === -1 ? <DislikeFilled style={{ color: '#e37c33' }} /> : <DislikeOutlined />}
      </span>
    </div>
  )
}

export default UserFeedBack
