import React, { useState, Fragment, useEffect } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, Button, Input, Row, Col, Radio, Select, Checkbox } from 'antd';
import GetUser from '@/components/GetUser';
import Category from '../Category';

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const CheckboxGroup = Checkbox.Group;
const { Option } = Select;
const NOOP = () => {};

const createModal = ({
  title = '',
  modalVisible = false,
  onCancel = NOOP,
  onSubmit = NOOP,
  editFormData,
  modalWidth = 900,
  form: { getFieldDecorator, setFieldsValue, validateFields, resetFields },
  grantedTables = [],
  odpsTables = [],
  tableColumns = [],
  onSearch = NOOP,
  onTableChange = NOOP, // 新增授权表change事件
  isAddGrant = false,
  setIsAddGrant = NOOP, // 新增授权按钮控制
  onAddGrantedTable = NOOP, // 新增表按钮click提交表单事件
  labelColumns = [],
  onGrantedTableChange = NOOP, // 数据表change事件
}) => {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const [keyword, setKeyword] = useState(null);
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const [isSelectedGrantedTable, setIsSelectedGrantedTable] = useState(false);

  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    if (!isAddGrant) {
      resetFields(['tableName', 'partitionField', 'primaryKey', 'isGranted']);
    }
  }, [isAddGrant]);

  const handleTableSearch = val => {
    setKeyword(val);
    onSearch(val);
  };

  // 添加授权表时表格选择联动
  const handleTableChange = val => {
    // eslint-disable-next-line react/no-this-in-sfc
    setFieldsValue({
      primaryKey: undefined,
    });
    onTableChange(val);
  };

  // 数据表联动事件
  const handleTableGuidChange = val => {
    setIsAddGrant(false);
    setIsSelectedGrantedTable(true);
    onGrantedTableChange(val);
    setFieldsValue({
      labelColumn: undefined,
    });
  };

  // 新增表
  const submitAddGrantedTable = () => {
    validateFields(['tableName', 'partitionField', 'primaryKey', 'isGranted'], (err, values) => {
      if (!err) {
        console.log(values);
        onAddGrantedTable(values);
      }
    });
  };

  const renderForm = (
    <Form {...FORM_ITEM_LAYOUT} onSubmit={onSubmit}>
      <FormItem label="英文名称">
        {getFieldDecorator('modelName', {
          initialValue: editFormData.propertyName,
          rules: [{ required: true, message: '请输入标签英文名称' }],
        })(<Input placeholder="请输入标签英文名称" />)}
      </FormItem>
      <FormItem label="中文名称">
        {getFieldDecorator('propertyDescription', {
          initialValue: editFormData.propertyDescription,
          rules: [{ required: true, message: '请输入标签中文名称' }],
        })(<Input placeholder="请输入标签中文名称" />)}
      </FormItem>
      <FormItem label="标签时效">
        {getFieldDecorator('modelName', {
          initialValue: editFormData.realTime,
          rules: [{ required: true, message: '请输入标签中文名称' }],
        })(
          <RadioGroup>
            <Radio key="1" value="real">
              实时
            </Radio>
            <Radio key="2" value="no">
              非实时
            </Radio>
          </RadioGroup>
        )}
      </FormItem>
      <FormItem label="业务负责人">
        {getFieldDecorator('bizOwners', {
          initialValue: editFormData.bizOwners,
          rules: [{ required: true, message: '请选择业务负责人' }],
        })(<GetUser />)}
      </FormItem>
      <FormItem label="质量负责人">
        {getFieldDecorator('qualityOwner', {
          initialValue: editFormData.qualityOwner,
          rules: [{ required: true, message: '请选择质量负责人' }],
        })(<GetUser />)}
      </FormItem>
      <FormItem label="数据负责人">
        {getFieldDecorator('dataOwner', {
          initialValue: editFormData.dataOwner,
          rules: [{ required: true, message: '请选择数据负责人' }],
        })(<GetUser />)}
      </FormItem>
      <FormItem label="标签分类">
        {getFieldDecorator('categoryId', {
          initialValue: editFormData.categoryId,
          rules: [{ required: true, message: '请选择标签分类' }],
        })(<Category />)}
      </FormItem>
      <FormItem label="数据表">
        {getFieldDecorator('data', {
          initialValue: editFormData.categoryId,
          rules: [{ required: true, message: '请选择标签分类' }],
        })(
          <Select placeholder="请选择数据表" onChange={handleTableGuidChange}>
            {grantedTables.length && grantedTables.map(t => <Option key={t}>{t}</Option>)}
          </Select>
        )}
      </FormItem>
      <FormItem label=" " colon={false}>
        <Button
          onClick={() => {
            setIsSelectedGrantedTable(false);
            setFieldsValue({
              data: undefined,
              labelColumn: undefined,
            });
            setIsAddGrant(!isAddGrant);
          }}
        >{`${isAddGrant ? '取消' : ''}新增授权`}</Button>
      </FormItem>
      {(isAddGrant || isSelectedGrantedTable) && (
        <Fragment>
          <FormItem label="表">
            {getFieldDecorator('tableName', {
              initialValue: editFormData.tableName,
              rules: [{ required: true, message: '请输入表' }],
            })(
              <Select
                showSearch
                onSearch={handleTableSearch}
                onChange={handleTableChange}
                placeholder="选择表"
                optionLabelProp="value"
                disabled={isSelectedGrantedTable}
              >
                {odpsTables.length && // 高亮显示
                  odpsTables.map(t => {
                    const index = t.indexOf(keyword);
                    const beforeStr = t.substr(0, index);
                    const afterStr = t.substr(index + keyword.length);
                    const content =
                      index > -1 ? (
                        <span>
                          {beforeStr}
                          <span style={{ color: '#f50' }}>{keyword}</span>
                          {afterStr}
                        </span>
                      ) : (
                        <span>{t}</span>
                      );
                    return <Option key={t}>{content}</Option>;
                  })}
              </Select>
            )}
          </FormItem>
          <FormItem label="分区分段名">
            {getFieldDecorator('partitionField', {
              initialValue: editFormData.fenduan,
              rules: [{ required: true, message: '请输入分区分段名' }],
            })(<Input disabled={isSelectedGrantedTable} placeholder="请输入分区分段名" />)}
          </FormItem>
          <FormItem label="用户id字段">
            {getFieldDecorator('primaryKey', {
              initialValue: editFormData.column,
              rules: [{ required: true, message: '请输入字段' }],
            })(
              <Select showSearch placeholder="请输入字段" disabled={isSelectedGrantedTable}>
                {tableColumns.length &&
                  tableColumns.map(c => <Option key={c.columnName}>{c.columnName}</Option>)}
              </Select>
            )}
          </FormItem>
          <FormItem
            label="确认授权"
            extra={
              !isSelectedGrantedTable ? (
                <span style={{ color: 'red' }}>
                  GRANT Describe,Select ON TABLE 表名称 TO USER
                  ALIYUN$<EMAIL>
                </span>
              ) : null
            }
          >
            {getFieldDecorator('isGranted', {
              initialValue: [],
              rules: [{ required: true, message: '请输入分区分段名' }],
            })(
              <CheckboxGroup disabled={isSelectedGrantedTable}>
                <Checkbox value="has">已经授权</Checkbox>
              </CheckboxGroup>
            )}
          </FormItem>
          {!isSelectedGrantedTable && (
            <FormItem label=" " colon={false}>
              <Button onClick={submitAddGrantedTable}>添加表</Button>
            </FormItem>
          )}
        </Fragment>
      )}
      <FormItem label="标签字段">
        {getFieldDecorator('labelColumn', {
          initialValue: editFormData.labelColumn,
          rules: [{ required: true, message: '请选择字段' }],
        })(
          <Select showSearch placeholder="请选择字段">
            {labelColumns.length &&
              labelColumns.map(c => <Option key={c.columnName}>{c.columnName}</Option>)}
          </Select>
        )}
      </FormItem>
      <Row gutter={24}>
        <Col span={4} />
        <Col span={14} style={{ textAlign: 'center' }}>
          <Button onClick={() => onCancel()} style={{ marginRight: 15 }}>
            取消
          </Button>
          <Button type="primary" htmlType="submit">
            保存
          </Button>
        </Col>
      </Row>
    </Form>
  );

  return (
    <Modal
      width={modalWidth}
      title={title}
      footer={null}
      visible={modalVisible}
      onCancel={onCancel}
    >
      {renderForm}
    </Modal>
  );
};

export default Form.create({
  onValuesChange: (props, values) => props.onValuesChange && props.onValuesChange(values),
})(createModal);
