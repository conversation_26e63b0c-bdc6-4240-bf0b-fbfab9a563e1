import React, { PureComponent } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Select, Row, Col, Button, Checkbox, message} from 'antd';
import GetUser from '@/components/GetUser';

import { BATCH_ACTION } from './constants';
import './index.less';

const { Option } = Select;
const FormItem = Form.Item;
const CheckboxGroup = Checkbox.Group;
const FORM_ITEM_LAYOUT = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};

const FORM_ITEM_TENANT = {
  labelCol: { span: 8 },
  wrapperCol: { span: 17 },
};
const NOOP = () => {};

@Form.create()
class Action extends PureComponent {
  state = {
    action: null,
  };

  excute = () => {
    const {
      onAction = NOOP,
      form: { getFieldsValue },
    } = this.props;
    if (getFieldsValue().hasOwnProperty('bizRegionList') && getFieldsValue().bizRegionList === undefined) {
      message.error('请选择所属租户');
      return
    }
    if (getFieldsValue().hasOwnProperty('securityLevel') && getFieldsValue().securityLevel === undefined) {
      message.error('请选择安全等级');
      return
    }
    onAction(getFieldsValue());

  };

  handleActionChange = value => {
    this.setState({
      action: value,
    });
  };

  render() {
    const {
      form: { getFieldDecorator },
      isShowBizRegionList,
    } = this.props;

    const { action } = this.state;

    return (
      <Form layout="inline" className="fl-search-form-wrap">
        <Row gutter={{ md: 8, lg: 24, xl: 48 }} style={{ marginRight: 0, marginLeft: 0 }}>
          <FormItem label="操作分类" {...FORM_ITEM_LAYOUT}>
            {getFieldDecorator('batchOperateAction')(
              <Select
                placeholder="请选择要执行的操作"
                style={{ minWidth: 202 }}
                onChange={this.handleActionChange}
              >
                {
                  BATCH_ACTION.map(item => <Option key={item.val} value={item.val}>
                    {item.name}
                </Option>)
                }
              </Select>
            )}
          </FormItem>
          {((!!action && action === 'BATCH_SET_BIZ_OWNER') ||
            action === 'BATCH_SET_DATA_OWNER' ||
            action === 'BATCH_SET_QUALITY_OWNER') && (
            <Col span={8}>
              <FormItem label="责任人" {...FORM_ITEM_LAYOUT}>
                {getFieldDecorator('employee')(
                  <GetUser
                    placeholder="请选择责任人"
                    mode="default"
                    style={{ minWidth: 199 }}
                  />
                )}
              </FormItem>
            </Col>
          )}

          {action === 'BATCH_SET_BIZ_REGION_ID' && (
            <Col span={8} style={{paddingRight:0}}>
              <FormItem label="所属租户" {...FORM_ITEM_TENANT}>
                {getFieldDecorator('bizRegionList')(
                  <Select placeholder="请选择租户类型" style={{ minWidth: 200 }} mode="multiple">
                  <Option key="merchant_region">商家租户</Option>
                  <Option key="public_region">小二租户</Option>
                  {
                    isShowBizRegionList && <Option key="fliggy_commercialization">商业化租户</Option>
                  }
                </Select>
                )}
              </FormItem>
            </Col>
          )}

          {action === 'BATCH_SET_SECURITY_LEVEL' && (
            <Col span={8}>
              <FormItem label="安全等级" {...FORM_ITEM_LAYOUT}>
                {getFieldDecorator('securityLevel')(
                  <Select placeholder="请选择安全等级" style={{ minWidth: 202 }}>
                    <Option value="1">L1</Option>
                    <Option value="2">L2</Option>
                    <Option value="3">L3</Option>
                    <Option value="4">L4</Option>
                  </Select>
                )}
              </FormItem>
            </Col>
          )}

          <Col span={4}>
            <Button type="primary" onClick={this.excute}>
              执行
            </Button>
          </Col>
        </Row>
      </Form>
    );
  }
}

export default Action;
