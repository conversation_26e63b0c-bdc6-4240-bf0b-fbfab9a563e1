import { message } from 'antd';
import pathToRegexp from 'path-to-regexp';
import { history } from 'umi'
import { get } from 'lodash';
import {
  queryOdpsGrantedTable,
  queryOdpsTable,
  queryOdpsTableColumn,
  queryBuildTree,
  editGrantedTable,
  editLabel,
  editLabelEnumValueObj,
  queryBizEntity,
  queryLabelEnumValueObj,
  queryLabelEnum,
  queryLabelById,
  queryActCalendarOdpsGrantedTableInfo,
  queryLabelByName,
} from '@/services/api';
import { setServiceParamsKey, getServiceParamsValueSource, isArrayEmptyOrHasObject } from '../../CreateCrowdTag/common/utils';

export default {
  namespace: 'createCrowdTag',
  state: {
    loading: false,
    editFormData: {},
    grantedTables: [],
    odpsTables: [],
    tableColumns: [], // 用于添加授权表
    labelColumns: [], // 用于标签分类
    labelEnumColumns: [], // 用于枚举值
    isAddGrant: false,
    keyword: null,
    treeData: [],
    modalVisible: false,
    metaType: '',
    entityName: null,
    bizEntities: [],
    labelEnumMapping: [],
    labelDataTypes: [],
    isSelectedGrantedTable: false,
    enumValueEditFormData: {},
    btnLoading: false
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(({ pathname, query }) => {
        if (pathname.includes('/develop-tool/create-crowd-tag')) {
          const match = pathToRegexp('/develop-tool/create-crowd-tag/:id').exec(pathname);
          if (match) {
            dispatch({
              type: 'query',
              payload: {
                entityName: match[1],
              },
            });

            if (query.id) {
              dispatch({
                type: 'queryLabelById',
                payload: {
                  labelId: query.id,
                },
              });
            } else {
              dispatch({
                type: 'updateState',
                payload: {
                  editFormData: {
                    sqlTemplate:'select distinct xxx as user_id from ...',
                    securityLevel: 1,
                    // scope:['PUSH'],
                    isGranted:[],
                  },
                  isSelectedGrantedTable: false, //优化创建自定义标签时新增授权变灰度问题
                }
                
              });
            }

            // 修复selet框显示问题
            dispatch({
              type: 'updateState',
              payload: {
                entityName: match[1],
              },
            });
          }
        }
      });
    },
  },
  effects: {
    *query({ payload }, { put, all }) {
      yield all([
        put.resolve({
          type: 'queryOdpsGrantedTable',
          payload: {
            sceneType: 'LABEL_SCENE',
          },
        }),
        put.resolve({
          type: 'queryOdpsGrantedTable',
          payload: {
            sceneType: 'LABEL_ENUM_VALUE_SCENE',
          },
        }),
        put.resolve({
          type: 'queryBizEntity',
        }),
        put.resolve({
          type: 'queryTreeData',
          payload: {
            entityName: payload.entityName,
          },
        }),
        put.resolve({
          type: 'queryLabelEnumValueObj',
        }),
        put.resolve({
          type: 'queryLabelEnum',
        }),
      ]);
    },
    *queryLabelByName({ payload }, { call }) {
      const res = yield call(queryLabelByName, { name: payload.name });
      if (res.success === false) {
        message.error(res.msg || '查询失败');
        return;
      }

      return !!res.data;
    },
    *queryLabelById({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const res = yield call(queryLabelById, { id: payload.labelId });

      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }


      const { data } = res;

      const { ttl, extInfo = {}, sourceConfig, physicalProfileCode, source, profileCode} = data;
      // const { odpsSourceColumn, odpsSourceJoinKey, odpsSourcePartitionField } =
      //   sourceExtInfo || {};
      const { weightRange, isPay } = extInfo || {};
      const [minValue, maxValue, stepLength] = weightRange || [];

      const {project,table,field,primaryKey,partitionField,sqlConfig,} = sourceConfig

      const {sqlParams,sqlTemplate} = sqlConfig || {}
      let sourceGuid = ''
      if (source === 'HSF') {
        sourceGuid = `${project}`
      } else {
        sourceGuid = project && table ? `${project}.${table}` : ''
      }
      let isGranted = ['hasGranted']

      let definition = ''
      let portraitType = ''

      if (physicalProfileCode === 'tbup' || physicalProfileCode === 'scrm_up' || physicalProfileCode === 'device_p') {
        definition = 6
      } else if (physicalProfileCode === 'tbus' || physicalProfileCode === 'scrm_ups' || physicalProfileCode === 'device_ps') {
        definition = 1
      }

      if (profileCode === 'tbu') {
        portraitType = 'tbup'
      } else if (profileCode === 'scrm_u') {
        portraitType = 'scrm_up'
      } else if (profileCode === 'device') {
        portraitType = 'device_p'
      }

      let sourceExtInfo = false
      if ((primaryKey && partitionField) || sourceGuid) {
        sourceExtInfo = true
      }

      let serviceParams = get(data, 'sourceConfig.apiSourceExtInfo.serviceParams', [])

      // 入参字段
      if (serviceParams.length > 0 && isArrayEmptyOrHasObject(serviceParams) === 'object') {
        setServiceParamsKey(serviceParams)
      }

      let editFormData = {
        ...data,
        primaryKey,
        partitionField,
        sourceGuid,
        isGranted,
        definition,
        field,
        sqlParams,
        sqlTemplate,
        minValue,
        maxValue,
        stepLength,
        isPay,
        portraitType,
        ttl: ttl / (60 * 60),
        hsfParams: get(data, 'sourceConfig.apiSourceExtInfo.hsfParams', []),
      };

      yield put({
        type: 'updateState',
        payload: {
          editFormData,
          isSelectedGrantedTable: !!sourceExtInfo,
          loading: false,
        },
      });

      if (editFormData.sourceGuid) {
        yield put({
          type: 'queryGrantedTableColumns',
          payload: {
            tableGuid: editFormData.sourceGuid,
          },
        });
      }
    },

    *queryLabelEnumValueObj(model, { call, put }) {
      const res = yield call(queryLabelEnumValueObj, {
        deleted: 0,
      });

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          labelEnumMapping: res.data,
        },
      });
    },
    *queryTreeData({ payload }, { call, put }) {
      const result = yield call(queryBuildTree, {
        bizEntityName: payload.entityName,
      });

      if (!result.success) {
        message.error(result.msg);
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          treeData: (result.data && result.data.root && result.data.root.children) || [],
        },
      });
    },
    *queryLabelEnum(model, { call, put }) {
      const res = yield call(queryLabelEnum);

      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }

      const { labelDataTypes } = res.data;

      yield put({
        type: 'updateState',
        payload: {
          labelDataTypes,
        },
      });
    },
    *queryOdpsGrantedTable({ payload }, { call, put }) {
      const res = yield call(queryOdpsGrantedTable, payload);
      if (!res.success) {
        message.error(res.msg);
        return;
      }

      const data = {};
      const key = payload.sceneType === 'LABEL_SCENE' ? 'grantedTables' : 'grantedTablesEnumValue';
      data[key] = res.data;

      yield put({
        type: 'updateState',
        payload: data,
      });
    },
    *queryOdpsGrantedTableByTableName({ payload }, { call, put, select }) {
      const { editFormData } = yield select(state => state.createCrowdTag);

      const res = yield call(queryActCalendarOdpsGrantedTableInfo, {
        ...payload,
        sceneType: 'LABEL_SCENE',
      });

      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }

      const { primaryKey, partitionField } = res.data[0];

      yield put({
        type: 'updateState',
        payload: {
          editFormData: {
            ...editFormData,
            primaryKey,
            partitionField,
          },
        },
      });
    },
    *queryOdpsTable({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: {
          keyword: payload.keyword,
        },
      });

      const res = yield call(queryOdpsTable, payload);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          odpsTables: res.data,
        },
      });
    },

    *queryTableColumns({ payload }, { call, put }) {
      const res = yield call(queryOdpsTableColumn, payload);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          tableColumns: res.data,
        },
      });
    },
    // 授权表的列
    *queryGrantedTableColumns({ payload }, { call, put }) {
      const res = yield call(queryOdpsTableColumn, payload);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          labelColumns: res.data,
        },
      });
    },
    *queryBizEntity({ payload }, { call, put }) {
      const res = yield call(queryBizEntity, {
        pageNum: 1,
        pageSize: 500, // 返回全部
        ...payload,
      });

      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }

      const { data } = res;

      yield put({
        type: 'updateState',
        payload: {
          bizEntities: data
            ? data.map(d => ({
                name: d.name,
                desc: d.description,
              }))
            : [],
        },
      });
    },
    // 授权表的枚举值联动出的列
    *queryGrantedTableEnumColumns({ payload }, { call, put }) {
      const res = yield call(queryOdpsTableColumn, payload);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          labelEnumColumns: res.data,
        },
      });
    },
    // 添加授权表
    *editGrantedTable({ payload }, { call, put }) {
      const res = yield call(editGrantedTable, payload);

      if (!res.success) {
        message.error(res.msg || '添加失败');
        return;
      }

      message.success('添加成功');

      yield put({
        type: 'updateState',
        payload: {
          isAddGrant: false,
        },
      });

      yield put({
        type: 'queryOdpsGrantedTable',
        payload: {
          sceneType: 'LABEL_SCENE',
        },
      });
    },
    // 添加枚举值
    *editLabelEnumValueObj({ payload }, { call, put }) {
      const res = yield call(editLabelEnumValueObj, payload);

      if (!res.success) {
        message.error(res.msg || '新增失败');
        return;
      }

      message.success('新增成功');

      yield put({
        type: 'onModalCancel',
      });

      // 枚举值对象查询
      yield put({
        type: 'queryLabelEnumValueObj',
      });
    },
    *editLabel({ payload }, { call, select, put }) {
      const data = payload;

      if (!data) return

      let ext = {}
      yield put({
        type: 'updateState',
        payload: {
          btnLoading: true,
        },
      });

      const { editFormData, entityName } = yield select(state => state.createCrowdTag);
      const isUpdate = !!(editFormData && editFormData.id);
      if (isUpdate) {
        data.id = editFormData.id;
        delete data.creator
        if (data.weightRange) {
          ext = {
            weightRange:data.weightRange
          }
        }
      } else {
        if (data.weightRange) {
          ext = {
            tags:["new"],
            weightRange:data.weightRange
          }
        } else {
          ext = {
            tags:["new"],
          }
        }
      }

      ext.isPay = data.isPay
      data.extInfo = {...ext, ...payload.extInfo}
      delete data.isPay
      delete data.weightRange

      const res = yield call(editLabel, data);
      if (!res.success) {
        message.error(res.msg || '请求失败');
        yield put({
          type: 'updateState',
          payload: {
            btnLoading: false,
          },
        });
        return;
      }

      if (res.success) {
        yield put({
          type: 'updateState',
          payload: {
            btnLoading: false,
          },
        });
      }

      message.success(isUpdate ? '更新成功' : '创建成功');

      // 跳转到标签页
      history.push(`/develop-tool/crowd-tag/${entityName}`);
    },
    *editorVariableList({ payload }, { call, put , select}) {
      const { editFormData } = yield select(state => state.createCrowdTag);
      let sqlParams = Array.from(new Set(payload.match(/\$\{(.*?)\}/g)))
      if(Array.isArray(sqlParams)){
        sqlParams = sqlParams
          .filter(item=> item.toLocaleLowerCase() !== '${date}')
          .map(ele=>ele.slice(2,-1))
          .filter(item => {
            if(item) {
              return item
            }
          }).map((e,i) => {
            let obj ={}
            if (e) {
              obj = {
                "name":e,
                "description":'',
                "paramType":undefined
              }
              return  obj
            } 
          })
      }

      yield put({
        type: 'updateState',
        payload: {
          editFormData: {
            ...editFormData,
            sqlParams,
          },
        },
      });
    },

    *editorHsfList({ payload }, { call, put, select }) {
      const { editFormData } = yield select(state => state.createCrowdTag);
      let hsfParams = getServiceParamsValueSource(payload, [])
      hsfParams = Array.from(new Set(hsfParams))
      if (Array.isArray(hsfParams)) {
        hsfParams = hsfParams
          .filter(item => {
            if (item) {
              return item
            }
          }).map((e, i) => {
            let obj = {}
            if (e) {
              obj = {
                "name": e,
                "description": '',
                "paramType": undefined
              }
              return obj
            }
          })
      }

      yield put({
        type: 'updateState',
        payload: {
          editFormData: {
            ...editFormData,
            hsfParams,
          },
        },
      });
    },

  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    onEditFormDataChange(state, { payload }) {
      return {
        ...state,
        editFormData: {
          ...state.editFormData,
          ...payload,
        },
      };
    },
    onModalCancel(state) {
      return {
        ...state,
        modalVisible: false,
      };
    },
    onModalOpen(state) {
      return {
        ...state,
        modalVisible: true,
      };
    },
  },
};
