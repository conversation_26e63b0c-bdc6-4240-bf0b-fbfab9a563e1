import React, { Fragment } from 'react';
import { Form, Input, Select, Button, Row, Card, message } from 'antd';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import styles from './index.less';

const { Option } = Select;
const { TextArea } = Input;
let id = 0;

class ServiceParams extends React.Component {
  static getDerivedStateFromProps(nextProps) {
    // Should be a controlled component.
    if ('value' in nextProps) {
      const { value } = nextProps;
      return {
        value,
      };
    }
    return null;
  }

  constructor(props) {
    super(props);
    const value = props.value || [];
    this.state = {
      value,
    }
  }
  componentDidMount() {
    id = this.props.value.length
  }
  remove = k => {
    const { form } = this.props;
    // can use data-binding to get
    const keys = form.getFieldValue('keys');
    // We need at least one passenger
    if (keys.length === 1) {
      return;
    }

    // can use data-binding to set
    form.setFieldsValue({
      keys: keys.filter(key => key !== k),
    });
  };



  add = () => {
    const { form } = this.props;
    // can use data-binding to get
    const keys = form.getFieldValue('keys');
    const nextKeys = keys.concat(id++);
    // can use data-binding to set
    // important! notify form to detect changes
    form.setFieldsValue({
      keys: nextKeys,
    });
  };


  handleIsExist = (rule, val, callback) => {
    if (!val) {
      if (val !== 0) {
        callback();
      }
      callback('请将数据填写完整')
    } else {
      callback();
    }
  }

  // 判断是否为json数据
  isJsonString = (str) => {
    try {
      if (typeof str === "object") {
        return true;
      }
    } catch(e) {}
    return false;
  }

  getinitialValue = (str) => {
    if(this.isJsonString(str)) {
      return JSON.stringify(str)
    }
    return str
  }

  render() {
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const initData = [];
    this.state.value.forEach((item, index) => {//根据真实数据，设置默认keys数组
      initData.push(index);
    });

    const formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
    };
    const formItemLayoutWithOutLabel = {
      wrapperCol: {
        xs: { span: 16, offset: 5 },
        sm: { span: 16, offset: 5 },
      },
    };

    getFieldDecorator('keys', { initialValue: initData });
    const keys = getFieldValue('keys');
    const formItems = keys.map((k, index) => (
      <Form.Item
        {...(index === 0 ? formItemLayout : formItemLayoutWithOutLabel)}
        label={index === 0 ? '入参' : ''}
        required={true}
        key={k}
        className={styles.cardBorder}
      >
        {getFieldDecorator(`serviceParams[${k}]`, {
          initialValue: this.state.value[k] ? this.getinitialValue(this.state.value[k]) : '',
          // rules: [
          //   {
          //     required: true,
          //     message: "Please input passenger's name or delete this field.",
          //   },
          // ],
        })(<TextArea rows={5} style={{width:'80%'}}/>)}
        {keys.length > 1 ? (
          <Button onClick={() => this.remove(k)} icon={<MinusCircleOutlined />} type="link"/>
        ) : null}
      </Form.Item>
    ));
    return (
      <>
        {formItems}
        <Form.Item {...formItemLayoutWithOutLabel}>
          <Button type="dashed" onClick={this.add} style={{width:"80%"}}>
            <PlusOutlined /> 增加
          </Button>
        </Form.Item>
      </>
    );
  }
}

export default ServiceParams
