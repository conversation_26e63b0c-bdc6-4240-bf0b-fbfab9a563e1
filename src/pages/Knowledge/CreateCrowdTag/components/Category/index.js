import React from 'react';
import { Row, Col, Select } from 'antd';
import { getTreeLevel } from '@/utils/tree';

const { Option } = Select;

class Category extends React.Component {
  static getDerivedStateFromProps(nextProps) {
    // Should be a controlled component.
    if ('value' in nextProps) {
      return {
        ...(nextProps.value || {}),
        level: getTreeLevel(0, nextProps.treeData),
      };
    }
    return null;
  }

  constructor(props) {
    super(props);

    const value = props.value || {};
    this.state = {
      ...value,
      // eslint-disable-next-line react/no-unused-state
      firstTag: undefined,
      secondTag: undefined,
      thirdTag: undefined,
      secondTags: [],
      thirdTags: [],
      level: 0,
    };
  }

  firstChange = value => {
    const { treeData } = this.props;
    const { level } = this.state;

    if (level === 1) {
      this.triggerChange(value);
      return;
    }

    const ret = [];

    treeData.forEach(item => {
      if (item.id === value && item.children && item.children.length > 0) {
        ret.push(...item.children);
      }
    });

    const secondTag = ret[0].id;

    if (level === 2) {
      // 二级联动
      this.triggerChange(secondTag);
      return;
    }

    let thirdTags = [];

    if (secondTag && secondTag.children && secondTag.children.length > 0) {
      thirdTags = secondTag.children;
    }

    this.setState({
      // eslint-disable-next-line react/no-unused-state
      firstTag: value,
      secondTags: ret,
      secondTag,
      thirdTag: thirdTags[0].id,
      thirdTags,
    });

    this.triggerChange(thirdTags[0].id);
  };

  secondChange = value => {
    const { secondTags } = this.state;

    const ret = [];

    secondTags.forEach(item => {
      if (item.id === value && item.children && item.children.length > 0) {
        ret.push(...item.children);
      }
    });

    this.setState({
      secondTag: value,
      thirdTags: ret,
      thirdTag: ret[0].id,
    });

    this.triggerChange(ret[0].id);
  };

  thirdChange = value => {
    this.setState({
      thirdTag: value,
    });

    this.triggerChange(value);
  };

  triggerChange = changedValue => {
    // Should provide an event to pass value to Form.
    const { onChange } = this.props;
    if (onChange) {
      onChange(changedValue || this.state.thirdTag);
    }
  };

  render() {
    // console.log('触发更新', curTimeType);

    const { treeData } = this.props;

    const { secondTags, thirdTags, secondTag, thirdTag, level } = this.state;

    return (
      <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
        {level > 0 && (
          <Col md={8} sm={24}>
            <Select placeholder="请选择分类" onChange={this.firstChange}>
              {treeData &&
                treeData.length > 0 &&
                treeData.map(item => (
                  <Option key={`tagClassfy_1_${item.id}`} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Col>
        )}
        {level > 1 && (
          <Col md={8} sm={24}>
            <Select placeholder="请选择分类" onChange={this.secondChange} value={secondTag}>
              {secondTags.length > 0 &&
                secondTags.map(item => (
                  <Option key={`tagClassfy_2_${item.id}`} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Col>
        )}
        {level > 2 && (
          <Col md={8} sm={24}>
            <Select placeholder="请选择分类" onChange={this.thirdChange} value={thirdTag}>
              {thirdTags.length > 0 &&
                thirdTags.map(item => (
                  <Option key={`tagClassfy_3_${item.id}`} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Col>
        )}
      </Row>
    );
  }
}

export default Category;
