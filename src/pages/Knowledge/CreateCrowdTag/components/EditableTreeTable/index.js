import React, { useState, useEffect, useRef, Fragment } from 'react';
import { Input, Button, Table, Tooltip, Select, message } from 'antd';
import { PlusCircleOutlined, FileAddOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { cloneDeep, get } from 'lodash';
import { v4 as uuidv4 } from 'uuid';
import { getHsfLabelValueSource } from '@/services/api';

const { TextArea } = Input;

import { getNode } from '../../../CreateCrowdTag/common/utils';

// 如果subParams为空数组转为null
const changeSubParams = treeData => {
  treeData?.forEach(ele => {
    if (ele?.subParams && ele?.subParams.length === 0) {
      ele.subParams = null;
    }
    if (ele?.subParams?.length > 0) {
      changeSubParams(ele?.subParams);
    }
  });
};

// 过滤参数
const filterNode = (treeData, key) => {
  return treeData.reduce((acc, node) => {
    const subParams = node.subParams ? filterNode(node.subParams, key) : null;
    if (node.key !== key) {
      acc.push(Object.assign({}, node, { subParams }));
    }

    return acc;
  }, []);
};

// 添加节点
const addNode = (treeData, selectNode, node) => {
  const nodes = getNode(treeData, selectNode); // 获取到节点
  const { parentNode, presentNode, presentNodeIndex } = nodes;

  if (parentNode && presentNode && presentNodeIndex !== '') {
    parentNode.splice(presentNodeIndex + 1, 0, node);
  }
  return treeData;
};

// 插入子节点
const insertNode = (treeData, selectNode, node) => {
  const nodes = getNode(treeData, selectNode); // 获取到节点
  const { parentNode, presentNode } = nodes;
  if (parentNode && presentNode) {
    if (presentNode.subParams) {
      presentNode.subParams.push(node);
    } else {
      presentNode.subParams = [node];
    }
  }
  return treeData;
};

// 设置全部展开
const setExpandedRowAllKeys = (treeData, keyList) => {
  treeData?.forEach(ele => {
    if (ele.subParams && ele.subParams.length) {
      keyList.push(ele.key);
      setExpandedRowAllKeys(ele.subParams, keyList);
    }
  });

  return keyList;
};

const EditableTreeTable = props => {
  const [expandedRowKeys, setExpandedRowKeys] = useState([]);
  const [valueSourceOpts, setValueSourceOpts] = useState([])
  const { value, onChange } = props;
  const count = useRef(0); // 用于计数

  useEffect(() => {
    getHsfLabelValueSource().then(res => {
      if (res?.success) {
        const data = get(res,'data',[])?.map(ele => ({label: ele, value: ele}))
        setValueSourceOpts(data || [])
      } else {
        message.error(res?.msg)
      }
    })
  }, [])

  useEffect(() => {
    if (value.length && !count.current) {
      const keyList = setExpandedRowAllKeys(value, []);
      setExpandedRowKeys(keyList);
      count.current++;
    }
  }, [JSON.stringify(value)]);

  const columns = [
    {
      title: '参数名称',
      dataIndex: 'paramName',
      render: (text, record) => (
        <Input
          value={text}
          onChange={e => onNodeValueChange(e.target.value, record.key, 'paramName')}
          style={{ width: 170 }}
        />
      ),
    },
    {
      title: '参数类型',
      dataIndex: 'paramType',
      width: 200,
      render: (text, record) => (
        <Input
          value={text}
          onChange={e => onNodeValueChange(e.target.value, record.key, 'paramType')}
          style={{ width: '80%' }}
        />
      ),
    },
    {
      title: '属性名称',
      dataIndex: 'propertyKey',
      width: 200,
      render: (text, record) => (
        <Input
          value={text}
          onChange={e => onNodeValueChange(e.target.value, record.key, 'propertyKey')}
          style={{ width: '80%' }}
        />
      ),
    },
    {
      title: '默认值',
      dataIndex: 'defaultValue',
      width: 200,
      render: (text, record) => {
        return (
          <Input
            value={Array.isArray(text) ? text?.join(',') : text}
            onChange={e => {
              const value =
                record?.paramType === 'java.util.List'
                  ? e.target.value?.split(',')
                  : e.target.value;
              onNodeValueChange(value, record.key, 'defaultValue');
            }}
            style={{ width: '80%' }}
          />
        );
      },
    },
    {
      title: '数据源',
      dataIndex: 'valueSource',
      width: 200,
      render: (text, record) => (
        <Select
          allowClear
          value={text}
          onChange={value => onNodeValueChange(value, record.key, 'valueSource')}
          options={valueSourceOpts}
          style={{ width: '80%' }}
        />
      ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      fixed: 'right',
      width: 140,
      render: (text, record) => (
        <Fragment>
          <Tooltip title="增加子节点">
            <Button
              type="link"
              size="middle"
              onClick={() => {
                onChildNode(record.key);
              }}
              icon={<PlusCircleOutlined />}
            />
          </Tooltip>
          <Tooltip title="增加兄弟节点">
            <Button
              type="link"
              size="middle"
              onClick={() => {
                onSiblingNode(record.key);
              }}
              icon={<FileAddOutlined />}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="link"
              size="middle"
              danger
              onClick={() => {
                onDeleteNode(record.key);
              }}
              icon={<CloseCircleOutlined />}
            />
          </Tooltip>
        </Fragment>
      ),
    },
  ];

  /**
   *
   * @param {*} values 输入的值
   * @param {*} nodeKey 当前的key，用于查询是哪个参数
   * @param {*} name 当前改动值的name
   */
  const onNodeValueChange = (values, nodeKey, name) => {
    const newTreeData = cloneDeep(value);
    const { parentNode, presentNode } = getNode(newTreeData, nodeKey);
    if (presentNode) {
      presentNode[name] = values;
      onChange(newTreeData);
    }
  };

  // 删除参数
  const onDeleteNode = nodeKey => {
    const newTreeData = cloneDeep(value);
    const filterData = filterNode(newTreeData, nodeKey);
    changeSubParams(filterData);
    onChange(filterData);
  };

  // 插入兄弟节点
  const onSiblingNode = nodeKey => {
    const initData = {
      paramType: undefined,
      propertyKey: undefined,
      paramName: undefined,
      key: uuidv4(),
    };
    let newTreeData = cloneDeep(value);
    onChange(addNode(newTreeData, nodeKey, initData));
  };

  // 插入子节点
  const onChildNode = nodeKey => {
    const initData = {
      paramType: undefined,
      propertyKey: undefined,
      paramName: undefined,
      key: uuidv4(),
    };
    onChangeExpandedRowKeys(true, nodeKey);
    const newTreeData = cloneDeep(value);
    onChange(insertNode(newTreeData, nodeKey, initData));
  };

  // 插入根节点
  const addRootNode = () => {
    const initData = {
      paramType: undefined,
      propertyKey: undefined,
      paramName: undefined,
      key: uuidv4(),
    };
    const newTreeData = cloneDeep(value);
    newTreeData.push(initData);
    onChange(newTreeData);
  };

  const onExpand = (expanded, record) => {
    onChangeExpandedRowKeys(expanded, record?.key);
  };

  const onChangeExpandedRowKeys = (expanded, nodeKey) => {
    if (expanded && !expandedRowKeys?.includes(nodeKey)) {
      setExpandedRowKeys([...expandedRowKeys, nodeKey]);
    }

    if (!expanded && expandedRowKeys?.includes(nodeKey)) {
      setExpandedRowKeys(pre => pre?.filter(ele => ele !== nodeKey));
    }
  };

  return (
    <div style={{ width: '100%' }}>
      <Button onClick={addRootNode} type="primary" style={{ marginBottom: 10 }}>
        添加参数
      </Button>
      <div style={{ width: '100%' }}>
        <Table
          bordered
          rowKey="key"
          scroll={{ x: 'max-content', y: 400 }}
          columns={columns}
          dataSource={value}
          childrenColumnName="subParams"
          expandedRowKeys={expandedRowKeys}
          onExpand={onExpand}
          indentSize={20}
          pagination={false}
        />
      </div>
    </div>
  );
};

export default EditableTreeTable;
