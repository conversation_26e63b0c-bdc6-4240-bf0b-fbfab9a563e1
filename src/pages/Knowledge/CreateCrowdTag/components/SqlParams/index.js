import React from 'react';
import { Form } from '@ant-design/compatible';
import { Input, Select, Button, Row, Col } from 'antd'
import styles from './index.less';

const FormItem = Form.Item;
const { Option } = Select;
const wrap = {
  whiteSpace: "nowrap"
};

const columnFlex = {
  flexDirection: "column",
  flex: 1
}

const FORM_ITEM_LAB = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
}

const FORM_ITEM_LABF = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 3 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 21 },
  },
}

// 参数类型枚举
const PARAM_TYPE_ENUM = {
  'NUMBER': '数值',
  'DATE': '日期',
  'TEXT': '文本',
  'ENUM': '枚举',
  'TIME': '时间',
  'SINGLE_ENUM': '单值枚举',
}

const SQL_PARAM_TYPE = ['NUMBER', 'DATE', 'TEXT', 'ENUM', ] // sql展示类型
const HSF_PARAM_TYPE = ['NUMBER', 'TIME', 'TEXT', 'SINGLE_ENUM', 'ENUM'] // hsf展示类型


class SqlParams extends React.Component {
  static getDerivedStateFromProps(nextProps) {
    // Should be a controlled component.
    if ('value' in nextProps) {
      const { value } = nextProps;
      return {
        value,
      };
    }
    return null;
  }

  constructor(props) {
    super(props);
    const value = props.value || {};
    this.state = {
      ...value
    }
  }

  componentDidMount() {
    const { onChange, value } = this.props;
    onChange({
      ...value
    });
  }



  openModal = () => {
    this.props.onModalOpen()
  }

  onDisChange = (values) => {
    const description = values
    this.triggerChange({ description });
  }

  onParamChange = (values) => {
    const { value } = this.props;
    const paramType = values
    if (values === 'ENUM') {
      value.enumId = ''
      this.triggerChange({ ...value });
    } else {
      if (value.hasOwnProperty('enumId')) {
        delete value.enumId
      }
    }
    this.triggerChange({ paramType });
  }

  onEnumChange = (values) => {
    const enumId = values
    this.triggerChange({ enumId });
  }

  triggerChange = changedValue => {
    const { onChange, value } = this.props;
    if (onChange) {
      onChange({
        ...value,
        ...changedValue
      });
    }
  };

  // hsf与sql展示不同类型
  setParamType = () => {
    const { type } = this.props
    if (type && type === 'hsf') {
      return HSF_PARAM_TYPE?.map(ele => ({
        label: PARAM_TYPE_ENUM[ele],
        value: ele
      }))
    }

    return SQL_PARAM_TYPE?.map(ele => ({
      label: PARAM_TYPE_ENUM[ele],
      value: ele
    }))
  }
  
  render() {
    const { name, description } = this.state
    const { labelEnumMapping, sqlData } = this.props
    const sqlValue = this.props.value
    sqlValue.name = sqlData.name
    return (
      <div style={{ flex: 1 }}>
        <Row gutter={{ md: 24, sm: 24 }} className={styles.paramRow}>
          <Col className={styles.paramSettingItem} span={4}>
            <span className={styles.paramSettingItemLabel}>{sqlValue.name}：</span>
          </Col>
          <Col span={20} style={{ padding: 0 }}>
            <div gutter={{ md: 24, sm: 24 }} style={columnFlex}>
              <Row>
                <Col className={styles.paramFlex}>
                  <span style={wrap}>显示名称：</span>
                  <Input type="text" onChange={e => this.onDisChange(e.target.value)} value={sqlValue.description} style={{ marginRight: '10px' }} />
                </Col>
                <Col className={styles.paramFlex}>
                  <span style={wrap}>参数类型：</span>
                  <Select
                    value={sqlValue.paramType}
                    onChange={this.onParamChange}
                  >
                    {
                      this.setParamType()?.map(ele => (
                        <Option value={ele?.value}>{ele?.label}</Option>
                      ))
                    }
                  </Select>
                </Col>
              </Row>

              {['ENUM', 'SINGLE_ENUM']?.includes(sqlValue.paramType) ?
                (<Row className={styles.paramFlex}>
                  <span style={wrap}>枚举映射：</span>
                  <div className={styles.paramEnumId}>
                    <Select showSearch placeholder="请选择枚举映射"
                      filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                      value={sqlValue.enumId}
                      onChange={this.onEnumChange}
                      style={{ marginRight: '10px', maxWidth: 500 }}
                    >
                      {labelEnumMapping.length &&
                        labelEnumMapping.map(c => (
                          <Option key={c.id} value={c.id}>
                            {`${c.name}（${c.code})`}
                          </Option>
                        ))}
                    </Select>
                    <Button onClick={this.openModal}>新增</Button>
                  </div>
                </Row>) : null
              }
            </div>
          </Col>
        </Row>
      </div>
    )
  }
}

export default SqlParams