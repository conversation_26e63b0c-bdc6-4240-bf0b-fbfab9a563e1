import { PROFILE_TBUP,  PROFILE_SCRM_UP, PROFILE_DEVICE} from './constants'
import {  message } from 'antd';
import { v4 as uuidv4 } from 'uuid';
import { parseOdpsTableName } from '@/utils/utils';

const getPortraitType = (portraitType) => {
  switch (portraitType) {
    case 'tbup':
      return PROFILE_TBUP
    case 'scrm_up':
      return PROFILE_SCRM_UP
    case 'device_p':
      return PROFILE_DEVICE
    default:
      return PROFILE_TBUP
  }
}


// 判断是否为json数据
const isJsonString = (str) => {
  try {
    if (typeof JSON.parse(str) == "object") {
      return true;
    }
  } catch (e) { }
  return false;
}

// sql&标签参数校验
const isParamsCheck = (data) => {
  let sqlNames = data.map(value => value.name)
  let sqlNamesSet = new Set(sqlNames)
  let sqlDescs = data.map(value => value.description)
  let sqlDescsSet = new Set(sqlDescs)
  if ((sqlNamesSet.size != sqlNames.length) || (sqlDescsSet.size != sqlDescs.length)) {
    return true
  }
  return false
}

// 递归去除key
const removeKeyFromTree = (data, keyToRemove) => {
  if (Array.isArray(data)) {
    return data.map(item => removeKeyFromTree(item, keyToRemove));
  }

  if (typeof data === 'object' && data !== null) {
    const newObj = {};
    for (const key in data) {
      if (key !== keyToRemove) {
        newObj[key] = removeKeyFromTree(data[key], keyToRemove);
      }
    }
    return newObj;
  }

  return data;
}


/**
 * 提交数据处理
 */
export const formatSubmit = (values, getFieldValue, currentUser, longOverTime) => {
  const payload = values;
  const {
    partitionField,
    primaryKey,
    dimEnumMetaId,
    categoryIdNew,
    field,
    minValue,
    maxValue,
    stepLength,
    dataType,
    ttl,
    isPay,
    portraitType,
    dataSource,
    adexLabelId,
    adexTableName,
    needApproval
  } = payload;

  const extInfo = {}

  //提交时判断sql语句变量名是否有重复
  if (payload.sqlParams && isParamsCheck(payload.sqlParams)) {
    message.error('sql语句变量名或描述可能存在重复');
    return
  }

  if (payload.labelParams && isParamsCheck(payload.labelParams)) {
    message.error('标签参数变量名或描述可能存在重复');
    return
  }

  if (payload.securityLevel) {
    payload.securityLevel = Number(payload.securityLevel)
  }

  //提交时判断如果有入参有值
  if (getFieldValue('source') === 'API') {
    let isServiceParams = false
    if (payload.serviceParams.length > 0 && isArrayEmptyOrHasObject(payload?.serviceParams) === 'string') {
      payload.serviceParams.forEach(ele => {
        if (!ele && ele !== 0) {
          isServiceParams = true
        }
      })
    }

    if (isServiceParams) {
      message.error('请将入参填写完整')
      return
    }
  }



  // if (getFieldValue('realtime') === 1) {
  //   // 小时转化成秒数
  //   payload.sourceType = 'lindorm';
  // } else if (getFieldValue('realtime') === 2) {
  //   payload.sourceType = 'HSF';
  // }
  // 实时：REALTIME, 离线：OFFLINE
  if (getFieldValue('type') === 'REALTIME') {
    if (getFieldValue('overtimeType') === 'short') {
      payload.ttl = ttl * 60 * 60;
      payload.expiredType = 'USER_DEFINED'
    } else if (getFieldValue('overtimeType') === 'today') {
      payload.ttl = 24 * 60 * 60;
      payload.expiredType = 'NOWADAYS'
    } else {
      payload.ttl = longOverTime;
      payload.expiredType = 'NEVER_EXPIRED'
    }
  } else if (getFieldValue('type') === 'OFFLINE') {
    payload.ttl = longOverTime;
    payload.expiredType = 'NEVER_EXPIRED'
  }

  // 固定值
  payload.profileId = getPortraitType(portraitType).profileId
  payload.profileCode = getPortraitType(portraitType).profileCode
  payload.profileName = getPortraitType(portraitType).profileName
  // payload.securityLevel = 0
  payload.creator = {
    empId: currentUser.workId,
    workId: currentUser.name
  }

  payload.modifier = {
    empId: currentUser.workId,
    workId: currentUser.name
  }
 
  // 6: 快速, 1: 自定义, REALTIME: 实时
  if (getFieldValue('definition') && getFieldValue('definition') === 6) {
    payload.physicalProfileId = getPortraitType(portraitType).fastPhysicalProfileId
    payload.physicalProfileCode = getPortraitType(portraitType).fastPhysicalProfileCode
    payload.physicalProfileName = getPortraitType(portraitType).fastPhysicalProfileName
  } else if (getFieldValue('definition') && getFieldValue('definition') === 1) {
    payload.physicalProfileId = getPortraitType(portraitType).customPhysicalProfileId
    payload.physicalProfileCode = getPortraitType(portraitType).customPhysicalProfileCode
    payload.physicalProfileName = getPortraitType(portraitType).customPhysicalProfileName
    payload.dataType = 'NONE_TYPE'
  } else if (getFieldValue('type') && getFieldValue('type') === 'REALTIME') {
    payload.physicalProfileId = getPortraitType(portraitType).realPhysicalProfileId
    payload.physicalProfileCode = getPortraitType(portraitType).realPhysicalProfileCode
    payload.physicalProfileName = getPortraitType(portraitType).realPhysicalProfileName
  }

  if (payload.dataType === 'KV') {
    payload.subDataType = 'STRING'
  }

  if (getFieldValue('type') === 'REALTIME' && getFieldValue('source') !== 'API') {
    const { project, table } = parseOdpsTableName(payload.sourceGuid);
    payload.sourceConfig = {
      project,
      table,
      type: payload.dataType,
    }
  } else if (getFieldValue('type') === 'REALTIME' && getFieldValue('source') === 'API') {

    let serviceParams = payload.serviceParams?.map(ele => {
      if (isJsonString(ele)) {
        return JSON.parse(ele)
      }
      return ele
    })

    payload.sourceConfig = {
      apiSourceExtInfo: {
        proServiceVersion: payload.proServiceVersion,
        methodName: payload.methodName,
        resultKey: payload.resultKey,
        serviceName: payload.serviceName,
        timeout: payload.timeout,
        hsfParams: payload.hsfParams || undefined,
        group: 'HSF',
        serviceParams: removeKeyFromTree(serviceParams, "key"),
      }
    }
  } else if (getFieldValue('type') === 'OFFLINE') {
    if (getFieldValue('definition') && getFieldValue('definition') === 1) {
      const { project, table } = parseOdpsTableName(payload.sourceGuid);
      payload.sourceConfig = {
        project,
        table,
        type: payload.dataType,
        field: payload.field,
        primaryKey: payload.primaryKey,
        partitionField: payload.partitionField,
        sqlConfig: {
          sqlTemplate: payload.sqlTemplate,
          sqlParams: payload.sqlParams
        }
      }
    } else if (getFieldValue('definition') && getFieldValue('definition') === 6) {
      const { project, table } = parseOdpsTableName(payload.sourceGuid);
      payload.sourceConfig = {
        project,
        table,
        type: payload.dataType,
        field: payload.field,
        primaryKey: payload.primaryKey,
        partitionField: payload.partitionField,
      }
    }
  }

  Object.assign(extInfo, {needApproval})

  // 数据来源
  if (dataSource || adexLabelId || adexTableName) {
    Object.assign(extInfo, {
      dataSourceConfig: {
        dataSource,
        adexLabelId,
        adexTableName
      }
    })
  }

  if (JSON.stringify(extInfo) !== '{}') {
    payload.extInfo = extInfo
  }

  delete payload.dataSource;
  delete payload.adexLabelId;
  delete payload.adexTableName;
  delete payload.sqlParams;
  delete payload.sqlTemplate;
  delete payload.hsfParams;


  // payload.sourceExtInfo = {
  //   odpsSourceColumn: field,
  //   odpsSourceJoinKey: primaryKey,
  //   odpsSourcePartitionField: partitionField,
  // };

  // payload.dimEnumMetaId = BIGINT(dimEnumMetaId);
  // 如果类目存在
  if (payload.categoryIdNew) {
    payload.categoryIdNew = categoryIdNew.length && categoryIdNew[categoryIdNew.length - 1];
  }

  // 保持整洁
  delete payload.primaryKey;
  delete payload.partitionField;
  delete payload.field;
  delete payload.sourceGuid
  delete payload.overtimeType
  delete payload.isGranted
  delete payload.definition
  delete payload.serviceName
  delete payload.methodName
  delete payload.proServiceVersion
  delete payload.timeout
  delete payload.serviceParams
  delete payload.resultKey
  delete payload.keys
  delete payload.portraitType
  // if (dataType === 'KV') {
  //   payload.extInfo = {
  //     ...(editFormData.extInfo || {}),
  //     weightRange: [minValue, maxValue, stepLength], // 最小 最大 步长
  //   };
  // }

  if (dataType === 'KV') {
    payload.weightRange = [minValue, maxValue, stepLength]
    delete payload.minValue
    delete payload.maxValue
    delete payload.stepLength
  }

  return payload
}


// 深度优先遍历
const loop = (treeData, selectNode, returnValue) => {
  treeData.forEach((item, index) => {
    if (item.key === selectNode) {
      returnValue = {
        parentNode: treeData,
        presentNode: item,
        presentNodeIndex: index
      };
    } else if (item.subParams && item.subParams.length > 0) {
      returnValue = loop(item.subParams, selectNode, returnValue) || {};
    }
  });
  // 返回值
  if (returnValue.parentNode && returnValue.presentNode) {
    return returnValue;
  }
};

// 获取参数
export const getNode = (treeData, selectNode) => {
  const returnValue = {
    parentNode: '',
    presentNode: '',
    presentNodeIndex: ''
  };
  return loop(treeData, selectNode, returnValue) || returnValue;
};


// 递归添加key
export const setServiceParamsKey = (treeData) => {
  treeData.forEach(ele => {
    ele.key = uuidv4()
    if (ele.subParams) {
      setServiceParamsKey(ele.subParams)
    }
  })
}

// 递归获取需要动态配置的值
export const getServiceParamsValueSource = (treeData, paramNameList) => {
  treeData.forEach(ele => {
    if (ele.valueSource === 'manual_config' && ele.paramName) {
      paramNameList.push(ele.paramName)
    }
    if (ele.subParams) {
      getServiceParamsValueSource(ele.subParams, paramNameList)
    }
  })

  return paramNameList
}


// 判断serviceParams返回值为对象数组还是数组
export const isArrayEmptyOrHasObject = (aArray) => {
  if (typeof aArray?.[0] === 'string') {
    return 'string';
  } 
  if (typeof aArray[0] === 'object' && aArray[0] !== null) {
    return 'object';
  }

  return ''
}

