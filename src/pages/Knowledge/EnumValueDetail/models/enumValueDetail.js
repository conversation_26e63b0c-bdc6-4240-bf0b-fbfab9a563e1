import { message } from 'antd';
import {
  queryLabelEnumValueBuildTree,
  editLabelEnumValue,
  deleteLabelEnumValue,
} from '@/services/api';

export default {
  namespace: 'enumValueDetail',
  state: {
    loading: false,
    enumValues: [],
    pagination: {},
    treeData: [],
    isFold: false,
    treeLoading: false,
    dimMetaId: -1,
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(({ pathname, query }) => {
        if (pathname.includes('/knowledge/enumValueDetail')) {
          dispatch({
            type: 'updateState',
            payload: {
              dimMetaId: query.id,
            },
          });

          dispatch({
            type: 'query',
            payload: {
              dimMetaId: query.id,
            },
          });
        }
      });
    },
  },
  effects: {
    *query({ payload }, { put, all }) {
      yield all([
        put.resolve({
          type: 'queryLabelEnumValueBuildTree',
          payload,
        }),
      ]);
    },
    *queryLabelEnumValueBuildTree({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: {
          treeLoading: true,
        },
      });

      const res = yield call(queryLabelEnumValueBuildTree, payload);

      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          treeLoading: false,
          treeData: (res.data && res.data.root && res.data.root.children) || [],
        },
      });
    },
    *editLabelEnumValue({ payload }, { call, select }) {
      const isUpdate = !!payload.id;
      const { parentId } = payload;

      const { dimMetaId } = yield select(state => state.enumValueDetail);

      const res = yield call(editLabelEnumValue, {
        ...payload,
        index: payload.id,
        // 根节点处理
        parentIndex: parentId === 0 ? `${dimMetaId}_${parentId}` : parentId,
      });

      if (!res.success || !res.data) {
        message.error(res.msg || '保存失败');
        return {
          success: false,
          msg: '保存失败',
        };
      }

      message.success(isUpdate ? '更新成功' : '创建成功');

      return {
        success: true,
        msg: '',
        data: {
          id: res.data,
          ...payload,
          parentId,
          isUpdate,
        },
      };
    },
    *deleteLabelEnumValue({ payload }, { call }) {
      const res = yield call(deleteLabelEnumValue, payload);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success('删除成功');

      // eslint-disable-next-line consistent-return
      return {
        success: true,
        msg: '',
        data: {
          id: payload.id,
        },
      };
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
