import React from 'react';
import { Card, Row, Col, Table } from 'antd';
import { connect } from 'dva';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import EditableTree from '@/components/EditableTree';
import styles from './index.less';

@connect(state => ({ enumValueDetail: state.enumValueDetail }))
class EnumValueDetail extends React.PureComponent {
  componentWillMount() {
    this.props.dispatch({
      type: 'enumValueDetail/updateState',
      payload: {
        enumValues: [],
      },
    });
  }

  onSelect = node => {
    const { dispatch } = this.props;

    // 由于服务端返回的数据中会存在children为空的数组，
    // 导致嵌套列表展示的时候出现加号但是子元素并不存在，
    // 于是遍历节点去除children为空的children属性
    const curNode = Object.assign({}, node);
    const loop = n => {
      if (!n.children) {
        return;
      }

      if (n.children) {
        if (n.children.length === 0) {
          // eslint-disable-next-line no-param-reassign
          delete n.children;
          return;
        }

        n.children.forEach(item => {
          loop(item);
        });
      }
    };

    loop(curNode);
    dispatch({
      type: 'enumValueDetail/updateState',
      payload: {
        enumValues: [curNode],
      },
    });
  };

  toggleFold = isFold => {
    const { dispatch } = this.props;

    dispatch({
      type: 'enumValueDetail/updateState',
      payload: {
        isFold,
      },
    });
  };

  onEdit = values => {
    const { dispatch } = this.props;

    return dispatch({
      type: 'enumValueDetail/editLabelEnumValue',
      payload: values,
    });
  };

  onDelete = values => {
    const { dispatch } = this.props;

    return dispatch({
      type: 'enumValueDetail/deleteLabelEnumValue',
      payload: values,
    });
  };

  onRefresh = () => {
    const {
      dispatch,
      enumValueDetail: { dimMetaId },
    } = this.props;

    dispatch({
      type: 'enumValueDetail/queryLabelEnumValueBuildTree',
      payload: {
        dimMetaId,
      },
    });
  };

  render() {
    const {
      enumValueDetail: { treeData, treeLoading, isFold, enumValues },
    } = this.props;

    const columns = [
      {
        title: '枚举值ID',
        dataIndex: 'id',
        width: 80,
        key: 'id',
        align: 'center',
        render: text => {
          const [, enumId] = text.split('_');
          return enumId;
        },
      },
      {
        title: '枚举值名称',
        dataIndex: 'name',
        width: 80,
        key: 'name',
        align: 'center',
      },
      {
        title: '枚举值CODE',
        dataIndex: 'enumCode',
        width: 80,
        key: 'enumCode',
        align: 'center',
      },
    ];

    return (
      <PageHeaderWrapper title="枚举值详情">
        <Card bordered={false}>
          <Row gutter={24}>
            <Col span={isFold ? 1 : 7} style={{ transition: 'all 0.2s' }}>
              <Card className={styles.leftTree} bordered={false} loading={treeLoading}>
                <EditableTree
                  isFold={isFold}
                  toggleFold={this.toggleFold}
                  dataSource={treeData}
                  onSelect={this.onSelect}
                  onEdit={this.onEdit}
                  onRefresh={this.onRefresh}
                  onDelete={this.onDelete}
                />
              </Card>
            </Col>
            <Col span={isFold ? 23 : 17} style={{ transition: 'all 0.2s' }}>
              <Card bordered={false}>
                <Table rowKey={record => record.id} dataSource={enumValues} columns={columns} />
              </Card>
            </Col>
          </Row>
        </Card>
      </PageHeaderWrapper>
    );
  }
}

export default EnumValueDetail;
