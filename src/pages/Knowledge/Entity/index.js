import React from 'react';
import { Card, Table, Tag, Row, <PERSON>, <PERSON><PERSON>, Di<PERSON><PERSON>, Popconfirm } from 'antd';
import { connect } from 'dva';
import { Link }  from 'umi';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import EditModal from './components/EditModal';

@connect(state => ({ entity: state.entity, user: state.user }))
class Entity extends React.PureComponent {
  handleTableChange = pagination => {
    const {
      dispatch,
      entity: { pagination: pager },
    } = this.props;

    dispatch({
      type: 'entity/updateState',
      payload: {
        pagination: {
          ...pager,
          current: pagination.current,
          pageSize: pagination.pageSize,
        },
      },
    });

    dispatch({
      type: 'entity/queryBizEntity',
      payload: {
        pageNum: pagination.current,
      },
    });
  };

  onEdit = record => {
    const { dispatch } = this.props;

    dispatch({
      type: 'entity/updateState',
      payload: {
        editFormData: record,
        modalVisible: true,
      },
    });
  };

  onOpenEditModal = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'entity/onModalOpen',
    });
  };

  onCancelkEditModal = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'entity/onModalCancel',
    });
  };

  onSubmit = values => {
    const { dispatch } = this.props;

    dispatch({
      type: 'entity/editBizEntity',
      payload: values,
    });
  };

  onValuesChange = values => {
    const { dispatch } = this.props;

    dispatch({
      type: 'entity/onEditFormDataChange',
      payload: values,
    });
  };

  delete = bizEntityName => {
    const { dispatch } = this.props;

    dispatch({
      type: 'entity/deleteBizEntity',
      payload: {
        bizEntityName,
      },
    });
  };

  render() {
    const {
      entity: { bizEntities, pagination, loading, modalVisible, editFormData },
    } = this.props;
    
    const columns = [
      {
        title: '实体ID',
        dataIndex: 'id',
        key: 'id',
        align: 'center',
      },
      {
        title: '实体名称',
        dataIndex: 'name',
        key: 'name',
        align: 'center',
      },
      {
        title: '实体描述',
        dataIndex: 'description',
        key: 'description',
        align: 'center',
      },

      {
        title: '管理员',
        dataIndex: 'operator',
        key: 'operator',
        align: 'center',
        render: users => (
          <span>
            {users &&
              users.length > 0 &&
              users.map(u => (
                <Tag color="green" key={u.empId}>
                  {u.nickName}
                </Tag>
              ))}
          </span>
        ),
      },
      {
        title: '创建人',
        dataIndex: 'creator',
        key: 'creator',
        align: 'center',
        render: u => (
          <Tag color="green" key={u.empId}>
            {u.nickName}
          </Tag>
        ),
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        align: 'left',
        render: (text, record) => {
          const { creator = {}, operator = [] } = record;
          const owners = [...operator, creator];

          const {
            user: { currentUser },
          } = this.props;

          const isGranted = owners.map(o => o.nickName).includes(currentUser.name);

          return (
            <span>
              <Link to={`/develop-tool/crowd-tag/${encodeURIComponent(record.name)}`}>查看标签</Link>
              {isGranted && (
                <>
                  <Divider type="vertical" />
                  <a onClick={() => this.onEdit(record)}>编辑</a>
                  <Divider type="vertical" />
                  <Popconfirm
                    title="是否删除？"
                    okText="确定"
                    cancelText="取消"
                    onConfirm={() => this.delete(record.name)}
                  >
                    <a>删除</a>
                  </Popconfirm>
                </>
              )}
            </span>
          );
        },
      },
    ];

    return (
      <PageHeaderWrapper title="实体管理">
        <Card bordered={false}>
          <Row gutter={24} style={{ marginBottom: 10 }}>
            <Col span={24} style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button type="primary" onClick={this.onOpenEditModal}>
                创建实体
              </Button>
            </Col>
          </Row>
          <Table
            loading={loading}
            rowKey={record => record.id}
            columns={columns}
            dataSource={bizEntities}
            pagination={pagination}
            onChange={this.handleTableChange}
          />
        </Card>

        <EditModal
          modalVisible={modalVisible}
          onCancel={this.onCancelkEditModal}
          onSubmit={this.onSubmit}
          onValuesChange={this.onValuesChange}
          editFormData={editFormData}
        />
      </PageHeaderWrapper>
    );
  }
}

export default Entity;
