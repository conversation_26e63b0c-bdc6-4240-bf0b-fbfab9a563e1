import React from 'react';
import { Modal, Input, Row, Col, Button } from 'antd';
import SubmitForm from '@/components/SubmitForm';
import GetUser from '@/components/GetUser';
import { queryBizEntity } from '@/services/api';

const { TextArea } = Input;
const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
const NOOP = () => {};

class EditModal extends React.PureComponent {
  handleEntityNameIsExist = (rule, val, callback) => {
    const { editFormData } = this.props;
    if (!val) {
      callback();
    }

    queryBizEntity({ pageNum: 1, pageSize: 10, name: val }).then(res => {
      if (res.data && res.data.length > 0 && editFormData.name !== val) {
        callback('该实体名称已存在');
      }

      callback();
    });
  };

  render() {
    const {
      modalVisible,
      onSubmit = NOOP,
      onCancel = NOOP,
      editFormData,
      onValuesChange = NOOP,
    } = this.props;

    const dataSource = [
      {
        title: '实体名称',
        dataKey: 'name',
        component: <Input placeholder="请输入实体名称" disabled={editFormData.id} />,
        formItemLayout: FORM_ITEM_LAYOUT,
        option: {
          rules: [
            { required: true, message: '请输入实体名称' },
            {
              validator: this.handleEntityNameIsExist,
            },
          ],
        },
        extra: !editFormData.id && <span style={{ color: 'green' }}>实体名称创建之后无法修改</span>,
      },
      {
        title: '实体描述',
        dataKey: 'description',
        component: <TextArea placeholder="请输入实体描述" />,
        formItemLayout: FORM_ITEM_LAYOUT,
        option: {
          rules: [{ required: true, message: '请输入实体描述' }],
        },
      },
      {
        title: '管理员',
        dataKey: 'operator',
        component: <GetUser placeholder="请选择管理员" />,
        formItemLayout: FORM_ITEM_LAYOUT,
        option: {
          rules: [{ required: true, message: '请选择管理员' }],
        },
      },
    ];

    const dataSetEditProps = {
      onValuesChange: values => onValuesChange(values),
      handleSubmit: values => {
        onSubmit(values);
      },
      formData: editFormData,
      dataSource,
      submit: {
        component: (
          <Row gutter={24}>
            <Col span={5} />
            <Col span={14} style={{ textAlign: 'center' }}>
              <Button onClick={onCancel} style={{ marginRight: 15 }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                保存
              </Button>
            </Col>
          </Row>
        ),
      },
    };

    return (
      <Modal
        visible={modalVisible}
        footer={null}
        title={editFormData.id ? '编辑实体' : '新建实体'}
        onCancel={onCancel}
        width={600}
      >
        <SubmitForm {...dataSetEditProps} />
      </Modal>
    );
  }
}

export default EditModal;
