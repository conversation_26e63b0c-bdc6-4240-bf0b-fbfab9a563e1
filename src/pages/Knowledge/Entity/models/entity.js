import { message } from 'antd';
import { queryBizEntity, editBizEntity, deleteBizEntity } from '@/services/api';

export default {
  namespace: 'entity',
  state: {
    loading: false,
    bizEntities: [],
    pagination: {},
    modalVisible: false,
    editFormData: {},
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(({ pathname }) => {
        if (pathname === '/develop-tool/entity') {
          dispatch({ type: 'query' });
        }
      });
    },
  },
  effects: {
    *query(model, { put, all }) {
      yield all([
        put.resolve({
          type: 'queryBizEntity',
        }),
      ]);
    },
    *queryBizEntity({ payload }, { call, put, select }) {
      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const { pagination } = yield select(state => state.entity);

      const res = yield call(queryBizEntity, {
        pageNum: pagination.curent || 1,
        pageSize: pagination.pageSize || 10,
        ...payload,
      });

      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          bizEntities: res.data,
          loading: false,
        },
      });
    },
    *editBizEntity({ payload }, { call, put, select }) {
      const data = payload;
      const { editFormData } = yield select(state => state.entity);

      const isUpdate = !!(editFormData && editFormData.id);
      if (isUpdate) {
        data.id = editFormData.id;
      }

      const res = yield call(editBizEntity, data);

      if (!res.success) {
        message.error(res.msg || '保存失败');
        return;
      }

      message.success(isUpdate ? '更新成功' : '创建成功');

      yield put({
        type: 'updateState',
        payload: {
          modalVisible: false,
          editFormData: {},
        },
      });
      yield put({
        type: 'queryBizEntity',
      });
    },
    *deleteBizEntity({ payload }, { call, put }) {
      const res = yield call(deleteBizEntity, payload);

      if (!res.success) {
        message.error(res.msg || '删除失败');
        return;
      }

      message.success('删除成功');

      yield put({
        type: 'queryBizEntity',
      });
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    onModalOpen(state) {
      return {
        ...state,
        modalVisible: true,
      };
    },
    onModalCancel(state) {
      return {
        ...state,
        modalVisible: false,
        editFormData: {},
      };
    },
    onEditFormDataChange(state, { payload }) {
      return {
        ...state,
        editFormData: {
          ...state.editFormData,
          ...payload,
        },
      };
    },
  },
};
