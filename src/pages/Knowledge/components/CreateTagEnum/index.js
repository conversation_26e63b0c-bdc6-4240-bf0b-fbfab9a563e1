import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, Input, Radio, Select, Row, Col, Button } from 'antd';
import { queryLabelEnumValueObjByCode } from '@/services/api';
import GetUser from '@/components/GetUser';
import ParamSetting from '../ParamSetting';

const NOOP = () => {};
const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const { Option } = Select;

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

@Form.create()
class CreateTagEum extends React.PureComponent {
  componentWillReceiveProps(nextProps) {
    if (nextProps !== this.props && nextProps.editFormData !== this.props.editFormData) {
      this.props.form.resetFields();
    }
  }

  onSubmit = () => {
    const { onSubmit = NOOP, editFormData } = this.props;
    this.props.form.validateFields((err, values) => {
      if (!err) {
        const payload = values;
        const { args, metaType, odpsGuid, enumColumn, enumDescColumn } = payload;
        if (metaType === 'CUSTOMIZE') {
          payload.args =
            args &&
            args.map(i => ({
              enumCode: i.code,
              enumDesc: i.desc,
              leaf: 1,
              level: 'LEVEL_1',
              parentId: 0,
            }));
        } else if (metaType === 'ODPS') {
          payload.odpsArgs = {
            odpsGuid,
            enumColumn,
            enumDescColumn,
          };

          delete payload.odpsGuid;
          delete payload.enumColumn;
          delete payload.enumDescColumn;
        }

        onSubmit({ ...editFormData, ...payload });
      }
    });
  };

  handleEnumValueCodeIsExist = (rule, val, callback) => {
    const { editFormData } = this.props;
    if (!val) {
      callback();
    }

    queryLabelEnumValueObjByCode({ code: val.trim() }).then(res => {
      if (res.data && editFormData.code !== val) {
        callback('该枚举code已存在');
      }

      callback();
    });
  };

  onSyncOdps = () => {
    const { editFormData, onSyncOdps = NOOP } = this.props;

    if (!editFormData.id) {
      return;
    }

    onSyncOdps(editFormData.id);
  };

  render() {
    const {
      modalVisible = NOOP,
      onCancel = NOOP,
      form: { getFieldDecorator },
      onMetaTypeChange,
      metaType,
      grantedTables = [],
      labelEnumColumns = [],
      onLabelEnumValueTableChange = NOOP,
      editFormData,
      syncLoading = false,
    } = this.props;

    return (
      modalVisible && (
        <Modal visible={modalVisible} onCancel={onCancel} width={800} footer={null}>
          <Form {...FORM_ITEM_LAYOUT}>
            <FormItem label="枚举code">
              {getFieldDecorator('metaCode', {
                initialValue: editFormData.code,
                rules: [
                  { required: true, message: '请输入枚举名称' },
                  {
                    validator: this.handleEnumValueCodeIsExist,
                  },
                ],
              })(<Input placeholder="请输入枚举code" allowClear disabled={!!editFormData.code} />)}
            </FormItem>
            <FormItem label="枚举名称">
              {getFieldDecorator('metaName', {
                initialValue: editFormData.name,
                rules: [{ required: true, message: '请输入枚举名称' }],
              })(<Input placeholder="请输入枚举名称" allowClear />)}
            </FormItem>
            <FormItem label="管理员">
              {getFieldDecorator('operator', {
                initialValue: editFormData.operator,
                rules: [{ required: true, message: '请选择管理员' }],
              })(<GetUser placeholder="请选择管理员" mode="multiple" />)}
            </FormItem>
            <FormItem label="参数配置">
              {getFieldDecorator('metaType', {
                initialValue: editFormData.type,
                rules: [{ required: true, message: '请输入参数配置' }],
              })(
                <RadioGroup onChange={onMetaTypeChange}>
                  <Radio key="CUSTOMIZE" value="CUSTOMIZE" disabled={!!editFormData.type}>
                    逻辑定义
                  </Radio>
                  <Radio key="ODPS" value="ODPS" disabled={!!editFormData.type}>
                    ODPS表
                  </Radio>
                </RadioGroup>
              )}
            </FormItem>
            {!editFormData.code && metaType === 'CUSTOMIZE' && (
              <FormItem label="参数配置">
                {getFieldDecorator('args', {
                  rules: [{ required: true, message: '请输入参数配置' }],
                })(<ParamSetting />)}
              </FormItem>
            )}
            {editFormData.type && editFormData.type === 'ODPS' && (
              <FormItem label="同步odps">
                <Button type="primary" loading={syncLoading} onClick={this.onSyncOdps}>
                  同步
                </Button>
              </FormItem>
            )}
            {!editFormData.code && metaType === 'ODPS' && (
              <Fragment>
                <FormItem label="ODPS表">
                  {getFieldDecorator('odpsGuid', {
                    rules: [{ required: true, message: '请选择ODPS表' }],
                  })(
                    <Select
                      placeholder="请选择ODPS表"
                      onChange={onLabelEnumValueTableChange}
                      showSearch
                    >
                      {grantedTables.length && grantedTables.map(t => <Option key={t}>{t}</Option>)}
                    </Select>
                  )}
                </FormItem>
                <FormItem label="枚举字段">
                  {getFieldDecorator('enumColumn', {
                    rules: [{ required: true, message: '请输入枚举字段' }],
                  })(
                    <Select placeholder="请输入枚举字段" showSearch>
                      {labelEnumColumns.length &&
                        labelEnumColumns.map(c => (
                          <Option key={c.columnName}>{c.columnName}</Option>
                        ))}
                    </Select>
                  )}
                </FormItem>
                <FormItem label="枚举字段描述">
                  {getFieldDecorator('enumDescColumn', {
                    rules: [{ required: true, message: '请输入枚举描述字段' }],
                  })(
                    <Select placeholder="请输入枚举描述字段" showSearch>
                      {labelEnumColumns.length &&
                        labelEnumColumns.map(c => (
                          <Option key={c.columnName}>{c.columnName}</Option>
                        ))}
                    </Select>
                  )}
                </FormItem>
              </Fragment>
            )}
            <Row gutter={24}>
              <Col span={4} />
              <Col span={14} style={{ textAlign: 'center' }}>
                <Button onClick={() => onCancel()} style={{ marginRight: 15 }}>
                  取消
                </Button>
                <Button type="primary" onClick={this.onSubmit}>
                  保存
                </Button>
              </Col>
            </Row>
          </Form>
        </Modal>
      )
    );
  }
}

export default CreateTagEum;
