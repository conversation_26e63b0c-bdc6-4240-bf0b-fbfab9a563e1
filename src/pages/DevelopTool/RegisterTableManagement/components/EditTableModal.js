import React, { useEffect } from 'react';
import { Modal, Form, Input, message } from 'antd';

const EditTableModal = ({ visible, record, onCancel, onSave }) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible && record) {
      // 编辑模式，填充表单
      form.setFieldsValue({
        projectName: record.projectName,
        tableName: record.tableName,
        primaryKey: record.primaryKey,
        partitionKey: record.partitionKey,
      });
    } else if (visible) {
      // 新增模式，清空表单
      form.resetFields();
    }
  }, [visible, record, form]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      await onSave(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };


  return (
    <Modal
      title={record ? '编辑注册表' : '新增注册表'}
      visible={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      width={600}
      okText="保存"
      cancelText="取消"
      className="edit-table-modal"
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          projectName: '',
          tableName: '',
          primaryKey: '',
          partitionKey: '',
        }}
      >
        <Form.Item
          name="projectName"
          label="项目名称"
          rules={[
            { required: true, message: '请输入项目名称' },
            { max: 100, message: '项目名称不能超过100个字符' },
          ]}
        >
          <Input placeholder="请输入项目名称" />
        </Form.Item>

        <Form.Item
          name="tableName"
          label="表名"
          rules={[
            { required: true, message: '请输入表名' },
            { max: 200, message: '表名不能超过200个字符' },
          ]}
        >
          <Input placeholder="请输入表名" />
        </Form.Item>

        <Form.Item
          name="primaryKey"
          label="主键"
          rules={[
            { required: true, message: '请输入主键' },
            { max: 100, message: '主键不能超过100个字符' },
          ]}
        >
          <Input placeholder="请输入主键" />
        </Form.Item>

        <Form.Item
          name="partitionKey"
          label="分区键"
          rules={[
            { max: 100, message: '分区键不能超过100个字符' },
          ]}
        >
          <Input placeholder="请输入分区键（可选）" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default EditTableModal;
