.register-table-management {
  background: #f0f2f5;
  padding: 24px;
  min-height: calc(100vh - 64px);

  .search-form {
    margin-bottom: 16px;

    .ant-form {
      .ant-form-item {
        margin-bottom: 16px;
      }
    }
  }

  .table-card {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
      }
    }

    .table-container {
      .ant-table {
        .ant-table-thead > tr > th {
          background: #fafafa;
          font-weight: 500;
        }

        .ant-table-tbody > tr {
          &:hover > td {
            background: #f5f5f5;
          }
        }

      }

      .ant-pagination {
        margin-top: 16px;
        text-align: right;
      }
    }
  }
}

.edit-table-modal {
  .ant-modal-body {
    .ant-form {
      .ant-form-item {
        margin-bottom: 24px;

        .ant-form-item-label {
          padding-bottom: 8px;

          label {
            font-weight: 500;
          }
        }

      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .register-table-management {
    padding: 16px;

    .search-form {
      .ant-form {
        .ant-form-item {
          margin-bottom: 12px;
        }
      }
    }

    .table-card {
      .table-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .ant-btn {
          width: 100%;
        }
      }

      .table-container {
        .ant-table {
          font-size: 12px;
        }
      }
    }
  }

  .edit-table-modal {
    .ant-modal {
      margin: 16px;
      max-width: calc(100vw - 32px);
    }
  }
}
