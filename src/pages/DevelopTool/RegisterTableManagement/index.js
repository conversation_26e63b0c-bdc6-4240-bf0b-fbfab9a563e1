import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Form,
  Modal,
  Input,
  message,
  Pagination,
  Row,
  Col,
  Popconfirm,
  Space,
  Tooltip,
} from 'antd';
import { EditOutlined, DeleteOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { connect } from 'dva';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import EditTableModal from './components/EditTableModal';
import {
  queryOdpsRegisterProfileTableList,
  updateOdpsRegisterProfileTable,
  createOdpsRegisterProfileTable,
} from '@/services/api';
import Exception403 from '@/pages/Exception/403';
import styles from './index.less';

const RegisterTableManagement = props => {
  const [form] = Form.useForm();
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchParams, setSearchParams] = useState({});

  const { isSuperAdmin } = props;

  useEffect(() => {
    if (isSuperAdmin) {
      fetchData();
    }
  }, [isSuperAdmin]);

  // 获取数据
  const fetchData = async (params = {}) => {
    setLoading(true);
    try {
      const queryParams = {
        ...searchParams,
        ...params,
      };
      const response = await queryOdpsRegisterProfileTableList(queryParams);

      if (Array.isArray(response)) {
        setData(response);
        setPagination(prev => ({
          ...prev,
          total: response.length,
        }));
      } else {
        message.error('获取数据失败');
        setData([]);
      }
    } catch (error) {
      console.error('获取数据失败:', error);
      message.error('获取数据失败: ' + (error.message || '未知错误'));
      setData([]);
    } finally {
      setLoading(false);
    }
  };

  // 搜索
  const handleSearch = values => {
    const params = {};
    Object.keys(values).forEach(key => {
      if (values[key]) {
        params[key] = values[key];
      }
    });
    setSearchParams(params);
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchData(params);
  };

  // 重置搜索
  const handleReset = () => {
    form.resetFields();
    setSearchParams({});
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchData();
  };

  // 新增
  const handleAdd = () => {
    setEditingRecord(null);
    setEditModalVisible(true);
  };

  // 编辑
  const handleEdit = record => {
    setEditingRecord(record);
    setEditModalVisible(true);
  };

  // 保存
  const handleSave = async values => {
    try {
      if (editingRecord) {
        // 编辑
        const result = await updateOdpsRegisterProfileTable({
          ...values,
          id: editingRecord.id,
        });
        if (result !== false) {
          message.success('更新成功');
        } else {
          message.error('更新失败');
          return;
        }
      } else {
        // 新增
        const result = await createOdpsRegisterProfileTable(values);
        if (result === true) {
          message.success('创建成功');
        } else {
          message.error('创建失败');
          return;
        }
      }
      setEditModalVisible(false);
      fetchData();
    } catch (error) {
      console.error('保存失败:', error);
      message.error(editingRecord ? '更新失败' : '创建失败');
    }
  };


  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      key: 'projectName',
      width: 150,
    },
    {
      title: '表名',
      dataIndex: 'tableName',
      key: 'tableName',
      width: 200,
      render: text => (
        <Tooltip title={text}>
          <span>{text}</span>
        </Tooltip>
      ),
    },
    {
      title: '主键',
      dataIndex: 'primaryKey',
      key: 'primaryKey',
      width: 120,
    },
    {
      title: '分区键',
      dataIndex: 'partitionKey',
      key: 'partitionKey',
      width: 120,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Button
          size="small"
          icon={<EditOutlined />}
          onClick={() => handleEdit(record)}
        >
          编辑
        </Button>
      ),
    },
  ];

  const searchForm = (
    <Card className={styles.searchForm}>
      <Form form={form} layout="inline" onFinish={handleSearch}>
        <Form.Item name="projectName" label="项目名称">
          <Input placeholder="请输入项目名称" style={{ width: 200 }} />
        </Form.Item>
        <Form.Item name="tableName" label="表名">
          <Input placeholder="请输入表名" style={{ width: 200 }} />
        </Form.Item>
        <Form.Item name="primaryKey" label="主键">
          <Input placeholder="请输入主键" style={{ width: 200 }} />
        </Form.Item>
        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
              搜索
            </Button>
            <Button onClick={handleReset}>重置</Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );

  if (!isSuperAdmin) {
    return <Exception403 />;
  }

  return (
    <PageHeaderWrapper title="注册表管理">
      <div className={styles.registerTableManagement}>
        {searchForm}
        <Card className={styles.tableCard}>
          <div className={styles.tableHeader}>
            <h3>注册表列表</h3>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              新增注册表
            </Button>
          </div>
          <div className={styles.tableContainer}>
            <Table
              columns={columns}
              dataSource={data}
              loading={loading}
              rowKey="id"
              scroll={{ x: 1000 }}
              pagination={{
                ...pagination,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: total => `共 ${total} 条记录`,
                onChange: (page, pageSize) => {
                  setPagination(prev => ({ ...prev, current: page, pageSize }));
                  fetchData({ ...searchParams, page, pageSize });
                },
              }}
            />
          </div>
        </Card>
      </div>

      <EditTableModal
        visible={editModalVisible}
        record={editingRecord}
        onCancel={() => setEditModalVisible(false)}
        onSave={handleSave}
      />
    </PageHeaderWrapper>
  );
};

export default connect(({ user }) => ({
  isSuperAdmin: user.isSuperAdmin,
}))(RegisterTableManagement);
