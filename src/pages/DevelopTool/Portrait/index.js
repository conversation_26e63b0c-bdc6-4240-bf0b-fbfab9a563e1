import React, { useState, useEffect } from 'react';
import { message, Table, Divider, Card, Button, Row, Col, Badge, Checkbox, Modal } from 'antd';
import { connect } from 'dva';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import PortraitCreate from './components/PortraitCreate';
import PortraitView from './components/PortraitView';
import { STATUS_MAP, CONFIG_TYPE } from './constants';
import { queryProfileList, profilePhysicalDelete } from '@/services/api';
import Exception403 from '@/pages/Exception/403';

const Portrait = props => {
  const [data, setData] = useState([]);
  const [configType, setConfigType] = useState(CONFIG_TYPE.ADD);
  const [isOpenModal, setIsOpenModal] = useState(false);
  const [isViewOpenModal, setIsViewOpenModal] = useState(false);
  const [detailData, setDetailData] = useState({});
  const [tableLoading, setTableLoading] = useState(false);
  const [checked, setChecked] = useState(false);
  const { isSuperAdmin, currentUser } = props;

  useEffect(() => {
    onRefresh({});
  }, []);

  const onRefresh = data => {
    setTableLoading(true);
    queryProfileList(data).then(res => {
      setTableLoading(false);
      if (res?.success) {
        setData(res?.data);
      } else {
        message.error(res?.msg);
      }
    });
  };

  const onChekBoxChange = e => {
    setChecked(e.target.checked);
    if (e.target.checked) {
      onRefresh({ deleted: 0 });
      return;
    }
    onRefresh({});
  };

  const onEdit = record => {
    setIsOpenModal(true);
    setDetailData(record);
    setConfigType(CONFIG_TYPE.EDIT);
  };

  const onView = record => {
    setIsViewOpenModal(true);
    setDetailData(record);
    setConfigType(CONFIG_TYPE.VIEW);
  };

  const onDel = record => {
    Modal.confirm({
      icon: false,
      closable: true,
      maskClosable: true,
      title: <span style={{ color: 'red' }}>警告</span>,
      width: 500,
      cancelText: '取消操作',
      okText: '继续作废',
      centered: true,
      autoFocusButton: null,
      content: (
        <span style={{ color: 'red' }}>
          作废画像可能会引起线上问题，请先确认相关标签和人群不再使用后再作废
        </span>
      ),
      onOk: () =>
        new Promise((resolve, reject) => {
          profilePhysicalDelete({ id: record?.id }).then(res => {
            if (res.success) {
              resolve();
              message.success('删除成功');
              checked ? onRefresh({ deleted: 0 }) : onRefresh({});
            } else {
              message.error(res.msg || '删除失败');
            }
          });
        }),
    });
  };

  const close = () => {
    setIsOpenModal(false);
    setIsViewOpenModal(false);
    setDetailData({});
  };

  const columns = [
    {
      title: '画像id',
      dataIndex: 'profileId',
      key: 'profileId',
    },
    {
      title: '一级画像',
      dataIndex: 'profileName',
      key: 'profileName',
    },
    {
      title: '二级画像',
      dataIndex: 'physicalProfileName',
      key: 'physicalProfileName',
    },
    {
      title: '主键id',
      dataIndex: 'primaryKey',
      key: 'primaryKey',
    },
    {
      title: '状态',
      dataIndex: 'deleted',
      render: (text, record) => (
        <Badge status={STATUS_MAP[text]?.status} text={STATUS_MAP[text]?.text} />
      ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 170,
      render: (text, record) => {
        return (
          <span>
            <a type="link" onClick={() => onView(record)}>
              查看
            </a>
            {record?.deleted === 0 && (
              <span>
                <Divider type="vertical" style={{ marginLeft: '4px', marginRight: '4px' }} />
                <a type="link" onClick={() => onEdit(record)}>
                  编辑
                </a>
                <Divider type="vertical" style={{ marginLeft: '4px', marginRight: '4px' }} />
                <a type="link" onClick={() => onDel(record)} style={{ color: 'red' }}>
                  作废
                </a>
              </span>
            )}
          </span>
        );
      },
    },
  ];

  return (
    <div>
      {isSuperAdmin ? (
        <PageHeaderWrapper inner title="画像管理">
          <Card>
            <Row gutter={24}>
              <Col span={6}>
                <Checkbox onChange={onChekBoxChange} checked={checked}>
                  仅展示生效数据
                </Checkbox>
              </Col>
              <Col span={18} style={{ textAlign: 'right', marginBottom: '20px' }}>
                <Button
                  type="primary"
                  onClick={() => {
                    setIsOpenModal(true);
                    setConfigType(CONFIG_TYPE.ADD);
                  }}
                >
                  画像创建
                </Button>
              </Col>
            </Row>
            <Table dataSource={data} columns={columns} rowKey="id" loading={tableLoading} />
          </Card>
          <PortraitCreate
            isOpenModal={isOpenModal}
            detailData={detailData}
            close={close}
            type={configType}
            currentUser={currentUser}
            refresh={() => {
              checked ? onRefresh({ deleted: 0 }) : onRefresh({});
            }}
          />
          <PortraitView isOpenModal={isViewOpenModal} detailData={detailData} close={close} />
        </PageHeaderWrapper>
      ) : (
        <Exception403 />
      )}
    </div>
  );
};

export default connect(({ user }) => ({
  isSuperAdmin: user.isSuperAdmin,
  currentUser: user.currentUser,
}))(Portrait);
