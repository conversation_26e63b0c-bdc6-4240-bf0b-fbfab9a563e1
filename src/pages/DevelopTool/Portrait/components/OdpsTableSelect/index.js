import React, { useState, useMemo } from 'react';
import { Select, Spin, Avatar } from 'antd';
import debounce from 'lodash/debounce';
import { queryOdpsTable } from '@/services/api';

const OdpsTableSelect = ({ value, onChange, placeholder, disabled }) => {
  const [odpsTablesOptions, setOdpsTablesOptions] = useState([]);
  const [fetching, setFetching] = useState(false);

  // 填充花名
  useMemo(() => {
    if (!value) return;
    queryOdpsTable({ keyword: value }).then(res => {
      if (!res.success) {
        message.error(res.msg);
        return;
      }
      setOdpsTablesOptions(res.data);
      setFetching(false);
    });
  }, [value]);

  // 搜索用户
  const fetchUser = debounce(val => {
    if (!val) return;
    setOdpsTablesOptions([]);
    setFetching(true);
    queryOdpsTable({ keyword: val }).then(res => {
      if (!res.success) {
        message.error(res.msg);
        return;
      }
      setOdpsTablesOptions(res.data);
      setFetching(false);
    });
  }, 800);

  // 选择花名
  const handleChange = val => {
    setFetching(false);
    onChange?.(val);
  };

  return (
    <Select
      disabled={disabled}
      value={value}
      showSearch
      onChange={handleChange}
      notFoundContent={fetching ? <Spin size="small" /> : null}
      onSearch={fetchUser}
      placeholder={placeholder}
      allowClear
    >
      {odpsTablesOptions &&
        odpsTablesOptions.length > 0 &&
        odpsTablesOptions.map(ele => (
          <Option value={ele} key={ele}>
            {ele}
          </Option>
        ))}
    </Select>
  );
};

export default OdpsTableSelect;
