import React, { useState, useCallback, useEffect } from 'react';
import {
  Modal,
  Select,
  Form,
  Row,
  Col,
  Button,
  Radio,
  DatePicker,
  message,
  Tag,
  Input,
  Drawer,
  Badge,
} from 'antd';
import dayjs from 'dayjs';
import { STATUS_MAP, CONFIG_TYPE, SCENE_ENUM, REALTIME_OR_NOT } from '../../constants';

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

const FormItem = Form.Item;

const PortraitView = props => {
  const [form] = Form.useForm();
  const { isOpenModal, detailData, close } = props;

  const onClose = () => {
    close();
  };

  return (
    <Drawer
      title="画像查看"
      width={600}
      visible={isOpenModal}
      destroyOnClose
      onClose={onClose}
      footer={
        <div
          style={{
            textAlign: 'right',
          }}
        >
          <Button
            onClick={() => {
              onClose();
            }}
            style={{ marginRight: 15 }}
          >
            取消
          </Button>
        </div>
      }
    >
      <Form {...FORM_ITEM_LAYOUT} form={form}>
        <Form.Item label="画像ID">{detailData?.profileId}</Form.Item>
        <Form.Item label="画像状态">
          <Badge
            status={STATUS_MAP[detailData.deleted]?.status}
            text={STATUS_MAP[detailData.deleted]?.text}
          />
        </Form.Item>
        <Form.Item label="一级画像code">{detailData?.profileCode}</Form.Item>
        <Form.Item label="一级画像名称">{detailData?.profileName}</Form.Item>
        <Form.Item label="二级画像code">{detailData?.physicalProfileCode}</Form.Item>
        <Form.Item label="二级画像名称">{detailData?.physicalProfileName}</Form.Item>
        <Form.Item label="主键列">{detailData?.primaryKey}</Form.Item>
        <Form.Item label="标签时效">{REALTIME_OR_NOT[detailData?.type]}</Form.Item>
        <Form.Item label="宽表名称">
          {`odps.${detailData?.odpsProject}.${detailData?.odpsTable}`}
        </Form.Item>
        <Form.Item label="宽表注释">{detailData?.odpsTableComment}</Form.Item>
        <Form.Item label="宽表生命周期">{detailData?.odpsTableLifecycle}</Form.Item>
        <Form.Item label="holo表名">{detailData?.olapTable}</Form.Item>
        <Form.Item label="底表表名">
          {`odps.${detailData?.odpsMasterConfig?.project}.${detailData?.odpsMasterConfig?.table}`}
        </Form.Item>
        <Form.Item label="时间分区">{detailData?.odpsTablePtKey}</Form.Item>
        
        <Form.Item label="场景">{SCENE_ENUM[detailData?.physicalProfileSceneConfigList?.[0]?.physicalProfileSceneName]}</Form.Item>
        <Form.Item label="创建时间">{dayjs(detailData?.gmtCreate).format('YYYY-MM-DD HH:mm:ss')}</Form.Item>
      </Form>
    </Drawer>
  );
};

export default PortraitView;
