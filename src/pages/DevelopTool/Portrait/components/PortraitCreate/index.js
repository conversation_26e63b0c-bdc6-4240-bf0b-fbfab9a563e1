import React, { useState, useCallback, useEffect } from 'react';
import {
  Modal,
  Select,
  Form,
  Row,
  Col,
  Button,
  Radio,
  DatePicker,
  message,
  Tag,
  Input,
  Drawer,
  Badge,
  InputNumber
} from 'antd';
import dayjs from 'dayjs';
import { STATUS_MAP, CONFIG_TYPE, SCENE_ENUM, REALTIME_OR_NOT } from '../../constants';
import OdpsTableSelect from '../OdpsTableSelect';
import { parseOdpsTableName } from '@/utils/utils';
import {
  profilePhysicalCreate,
  profilePhysicalUpdate
} from '@/services/api';

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

const FormItem = Form.Item;

const PortraitCreate = props => {
  const [form] = Form.useForm();
  const [btnLoading, setBtnLoading] = useState(false);
  const { isOpenModal, detailData, close, type, refresh, currentUser } = props;

  useEffect(() => {
    if (!isOpenModal) return;
    if (type === CONFIG_TYPE.EDIT) {
      const {
        profileId,
        profileCode,
        profileName,
        physicalProfileCode,
        physicalProfileName,
        primaryKey,
        odpsProject,
        odpsTable,
        odpsTableComment,
        odpsTableLifecycle,
        olapTable,
        odpsMasterConfig,
        odpsTablePtKey,
        physicalProfileSceneConfigList,
        type,
      } = detailData;
      form.setFieldsValue({
        primaryKey,
        odpsTableComment,
        odpsTableLifecycle,
        olapTable,
        odpsTablePtKey,
        type,
        profileId,
        profileCode,
        profileName,
        physicalProfileCode,
        physicalProfileName,
        physicalProfileSceneName: physicalProfileSceneConfigList?.[0]?.physicalProfileSceneName,
        odpsTableName: `odps.${odpsProject}.${odpsTable}`,
        bottomTableName: `odps.${odpsMasterConfig?.project}.${odpsMasterConfig?.table}`,
      });
    }
  }, [isOpenModal]);

  const onFinish = values => {
    const { odpsTableName, bottomTableName, physicalProfileSceneName, ...other } = values;
    const { project: odpsProject, table: odpsTable } = parseOdpsTableName(odpsTableName);
    const { project, table } = parseOdpsTableName(bottomTableName);
    const odpsMasterConfig = {
      project,
      table,
    };
    const physicalProfileSceneConfigList = [{physicalProfileSceneName}]
    let data = {
      ...other,
      odpsProject,
      odpsTable,
      odpsMasterConfig,
      physicalProfileSceneConfigList
    };

    if (type === CONFIG_TYPE.ADD) {
      data.creator = currentUser?.workId
      data.modifier = currentUser?.workId
      setBtnLoading(true);
      profilePhysicalCreate(data).then(res => {
        setBtnLoading(false);
        if (res?.success) {
          onClose();
          refresh();
          message.success('创建成功');
        } else {
          message.error(res?.msg);
        }
      });

      return;
    }

    if (type === CONFIG_TYPE.EDIT) {
      data.id = detailData?.id;
      data.creator = detailData?.creator
      data.modifier = currentUser?.workId
      Modal.confirm({
        icon: false,
        closable: true,
        maskClosable: true,
        title: <span style={{ color: 'red' }}>提示</span>,
        width: 500,
        cancelText: '取消操作',
        okText: '继续保存',
        centered: true,
        autoFocusButton: null,
        content: '变更画像数据可能会引起向上数据问题，请确认影响后再继续',
        onOk: () =>
          new Promise((resolve, reject) => {
            profilePhysicalUpdate(data).then(res => {
              resolve();
              if (res?.success) {
                onClose();
                refresh();
                message.success('编辑成功');
              } else {
                message.error(res?.msg);
              }
            });
          }),
      });
    }
  };

  const onClose = () => {
    form.resetFields();
    close();
  };

  return (
    <Drawer
      title={type === CONFIG_TYPE.EDIT ? '画像编辑' : '画像新增'}
      width={800}
      visible={isOpenModal}
      destroyOnClose
      onClose={onClose}
      footer={
        <div
          style={{
            textAlign: 'right',
          }}
        >
          <Button
            onClick={() => {
              onClose();
            }}
            style={{ marginRight: 15 }}
          >
            取消
          </Button>
          <Button
            type="primary"
            loading={btnLoading}
            onClick={() => {
              form.submit();
            }}
          >
            保存
          </Button>
        </div>
      }
    >
      <Form {...FORM_ITEM_LAYOUT} onFinish={onFinish} form={form} initialValues={{type: 'REALTIME'}}>
        {type === CONFIG_TYPE.EDIT && (
          <span>
            <Form.Item label="画像状态">
              <Badge
                status={STATUS_MAP[detailData.deleted]?.status}
                text={STATUS_MAP[detailData.deleted]?.text}
              />
            </Form.Item>
          </span>
        )}
        <Form.Item
          label="一级画像id"
          name="profileId"
          rules={[{ required: true, message: '请输入一级画像id' }]}
        >
          <InputNumber placeholder="请输入一级画像id" disabled={type === CONFIG_TYPE.EDIT} style={{width: '100%'}}/>
        </Form.Item>
        <Form.Item
          label="一级画像code"
          name="profileCode"
          rules={[{ required: true, message: '请输入一级画像code' }]}
        >
          <Input placeholder="请输入一级画像code" disabled={type === CONFIG_TYPE.EDIT} />
        </Form.Item>
        <Form.Item
          label="一级画像名称"
          name="profileName"
          rules={[{ required: true, message: '请输入一级画像名称' }]}
        >
          <Input placeholder="请输入一级画像名称" disabled={type === CONFIG_TYPE.EDIT} />
        </Form.Item>
        <Form.Item
          label="二级画像code"
          name="physicalProfileCode"
          rules={[{ required: true, message: '请输入二级画像code' }]}
        >
          <Input placeholder="请输入二级画像code" disabled={type === CONFIG_TYPE.EDIT} />
        </Form.Item>
        <Form.Item
          label="二级画像名称"
          name="physicalProfileName"
          rules={[{ required: true, message: '请输入二级画像名称' }]}
        >
          <Input placeholder="请输入二级画像名称" disabled={type === CONFIG_TYPE.EDIT} />
        </Form.Item>
        <Form.Item
          label="主键列"
          name="primaryKey"
          rules={[{ required: true, message: '请输入主键列' }]}
        >
          <Input placeholder="请输入主键列" disabled={type === CONFIG_TYPE.EDIT} />
        </Form.Item>
        <Form.Item
          label="标签时效"
          name="type"
          rules={[{ required: true, message: '请选择标签时效' }]}
        >
          <Radio.Group disabled={type === CONFIG_TYPE.EDIT}>
            {Object.keys(REALTIME_OR_NOT).map(ele => (
              <Radio value={ele}>{REALTIME_OR_NOT[ele]}</Radio>
            ))}
          </Radio.Group>
        </Form.Item>
        <Form.Item
          label="宽表名称"
          name="odpsTableName"
        >
          <OdpsTableSelect placeholder="请选择宽表名称" />
        </Form.Item>
        <Form.Item
          label="宽表注释"
          name="odpsTableComment"
        >
          <Input placeholder="请输入宽表注释" />
        </Form.Item>
        <Form.Item
          label="宽表生命周期"
          name="odpsTableLifecycle"
        >
          <InputNumber placeholder="请输入宽表生命周期" style={{width: '100%'}}/>
        </Form.Item>
        <Form.Item
          label="holo表名"
          name="olapTable"
        >
          <Input placeholder="请输入holo表名" />
        </Form.Item>
        <Form.Item
          label="底表表名"
          name="bottomTableName"
        >
          <OdpsTableSelect placeholder="请选择底表表名" />
        </Form.Item>
        <Form.Item
          label="时间分区"
          name="odpsTablePtKey"
          rules={[{ required: true, message: '请输入底表时间分区' }]}
        >
          <Input placeholder="请输入底表时间分区" />
        </Form.Item>
        <Form.Item
          label="场景"
          name="physicalProfileSceneName"
          rules={[{ required: true, message: '请选择场景' }]}
        >
          <Select placeholder="请选择场景">
            {Object.keys(SCENE_ENUM).map(ele => (
              <Option value={ele}>{SCENE_ENUM[ele]}</Option>
            ))}
          </Select>
        </Form.Item>
        {type === CONFIG_TYPE.EDIT && (
          <Form.Item label="创建时间">{dayjs(detailData?.gmtCreate).format('YYYY-MM-DD HH:mm:ss')}</Form.Item>
        )}
      </Form>
    </Drawer>
  );
};

export default PortraitCreate;
