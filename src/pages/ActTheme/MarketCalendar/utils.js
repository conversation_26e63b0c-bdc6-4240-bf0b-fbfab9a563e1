import dayjs from 'dayjs';
import { BADGE_MAP, HOT_MAP } from './constants';

// function format(date) {
//   const time = new Date(date)
//   let s = '';
//   const month = (time.getMonth() + 1) >= 10 ? (time.getMonth() + 1) : `0${time.getMonth() + 1}`;
//   const day = time.getDate() >= 10 ? time.getDate() : `0${time.getDate()}`;
//   s = `${time.getFullYear()}-${month}-${day} 00:00:00`
//   console.log(date, s)
//   return (s); // 返回日期。
// }
// 获取2个日期之间的所有日期
function getAll(startTime, endTime) {
  const result = []
  const begin = dayjs(startTime).format('YYYY-MM-DD HH:mm:ss').split(' ')[0]
  const end = dayjs(endTime).format('YYYY-MM-DD HH:mm:ss').split(' ')[0]
  const ab = begin.split('-');
  const ae = end.split('-');
  const db = new Date();
  db.setUTCFullYear(ab[0], ab[1] - 1, ab[2]);
  const de = new Date();
  de.setUTCFullYear(ae[0], ae[1] - 1, ae[2]);
  const unixDb = db.getTime();
  const unixDe = de.getTime();
  for (let k = unixDb; k <= unixDe;) {
    // result.push(format(new Date(k / 1)))
    result.push(dayjs(k).format('YYYY-MM-DD HH:mm:ss'))
    k += 24 * 60 * 60 * 1000;
  }

  return result;
}

export function formatData(data) {
  const calenderData = []
  data.forEach(item => {
    if (item.starttime === item.endtime) {
      calenderData.push({
        ...item,
        time: dayjs(item.starttime).format('YYYY-MM-DD HH:mm:ss'),
      })
    } else {
      const times = getAll(item.starttime, item.endtime)
      times.forEach(time => {
        calenderData.push({
          ...item,
          time,
        })
      });
    }
  });

  const calenderResult = {}
  calenderData.forEach(item => {
    const time = item.time.split(' ')[0]
    calenderResult[time] = calenderResult[time] || []
    calenderResult[time].push({
      ...item,
      status: BADGE_MAP[item.hot] || 'default',
      hotLevel: HOT_MAP[item.hot] || 1,
    })
  });

  return calenderResult;
}
