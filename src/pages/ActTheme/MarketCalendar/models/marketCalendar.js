import dayjs from 'dayjs'
import get from 'lodash/get';
import { message } from 'antd'
import { queryActCalendar, queryActType, queryActList, queryType, createNewAct, updateAct, deleteAct, queryActAdmin, queryActTypeConst } from '@/services/api'
import { queryCurrent } from '@/services/user'
import { formatData } from '../utils'

export default {
  namespace: 'marketCalendar',
  state: {
    loading: false,

    calendarValue: dayjs(),
    // 全部活动
    popoverVisible: {},
    // 活动详情
    modalVisible: false,
    modalItem: {},
    // 筛选项取值
    actType: ['全部'],
    actCountry: ['全部'],
    actProvince: ['全部'],
    actCity: ['全部'],
    // 活动数据
    data: [],
    dataCache: {},

    calendarData: {},

    formData: {
      dateRange: [
        dayjs(dayjs().startOf('month').subtract(7, 'days').format('YYYYMMDD')),
        dayjs(dayjs().endOf('month').subtract(-7, 'days').format('YYYYMMDD')),
      ],
      area: '全部',
      hot: '全部',
      actType: '全部',
      actCountry: '全部',
      actProvince: '全部',
      actCity: '全部',
    },

    newModelVisible: false,
    actFormData: {
      name: '',
    },

    showShortTitle: false,
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(({ pathname }) => {
        if (pathname === '/act-theme/market-calendar') {
          dispatch({ type: 'query' })
        }
      })
    },
  },
  effects: {
    *query(model, { put, all }) {
      yield all([
        put.resolve({
          type: 'queryData',
        }),
        put.resolve({
          type: 'queryActType',
        }),
        put.resolve({
          type: 'fetchCurrent',
        }),
        put.resolve({
          type: 'actType',
        }),
      ])
    },
    // *fetchCurrent(_, { call, put }) {
    //   const response = yield call(queryCurrent);
    //   if (!response.success) {
    //     message.error('请求用户信息失败');
    //     return;
    //   }
    //   const { name, workId, avatar } = response.data;
    //   const response2 = yield call(queryActAdmin);
    //   yield put({
    //     type: 'updateState',
    //     payload: {
    //       name,
    //       workId,
    //       avatar,
    //       admin: response2.data,
    //       isAdmin: response2.data.indexOf(name) !== -1,
    //     },
    //   })
    // },
    *actType(model, { call, put }) {
      const response = yield call(queryActTypeConst);
      yield put({
        type: 'updateState',
        payload: {
          actTypeConst: response.data
        },
      })
    },
    // 走MySQL查询
    *queryActType(model, { select, call, put }) {
      const { formData } = yield select(state => state.marketCalendar)
      const formParam = {}
      // 日期区间
      if (formData.dateRange[0]) {
        formParam.startTime = formData.dateRange[0].format('YYYYMMDD')
      }
      if (formData.dateRange[1]) {
        formParam.endTime = formData.dateRange[1].format('YYYYMMDD')
      }
      const result = yield call(queryType, formParam)
      // const actType = get(result, 'actType.data', []).map(item => item.type)
      // console.log('queryType-result', result)
      const actType = get(result, 'actType.actType', [])
      const actCountry = get(result, 'actCountry.actCountry', [])
      const actProvince = get(result, 'actProvince.actProvince', [])
      const actCity = get(result, 'actCity.actCity', [])
      yield put({
        type: 'updateState',
        payload: {
          actType: ['全部'].concat(actType),
          actCountry: ['全部'].concat(actCountry),
          actProvince: ['全部'].concat(actProvince),
          actCity: ['全部'].concat(actCity),
        },
      })
    },
    // 走ODPS表查询
    // *queryActType2(model, { select, call, put }) {
    //   const { formData } = yield select(state => state.marketCalendar)
    //   const formParam = {}
    //   // 日期区间
    //   if (formData.dateRange[0]) {
    //     formParam.startTime = formData.dateRange[0].format('YYYYMMDD')
    //   }
    //   if (formData.dateRange[1]) {
    //     formParam.endTime = formData.dateRange[1].format('YYYYMMDD')
    //   }
    //   const result = yield call(queryActType, formParam)
    //   const actType = get(result, 'actType.data', []).map(item => item.type)
    //   // console.log('actType', actType)
    //   yield put({
    //     type: 'updateState',
    //     payload: {
    //       actType: ['全部'].concat(actType),
    //     },
    //   })
    //   const actCountry = [...new Set(get(result, 'actCountry.data', []).map(item => item.country.split('，')).reduce((prev, curr) => prev.concat(curr)))];
    //   // console.log('actCountry', actCountry)
    //   yield put({
    //     type: 'updateState',
    //     payload: {
    //       actCountry: ['全部'].concat(actCountry),
    //     },
    //   })
    //   const actProvince = [...new Set(get(result, 'actProvince.data', []).map(item => item.province && item.province.split('，')).reduce((prev, curr) => prev.concat(curr)))];
    //   // console.log('actProvince', actProvince)
    //   yield put({
    //     type: 'updateState',
    //     payload: {
    //       actProvince: ['全部'].concat(actProvince),
    //     },
    //   })
    //   const actCity = [...new Set(get(result, 'actCity.data', []).filter(item => item && item.city).map(item => item.city && item.city.split('，')).reduce((prev, curr) => prev.concat(curr)))];
    //   // console.log('actCity', actCity)
    //   yield put({
    //     type: 'updateState',
    //     payload: {
    //       actCity: ['全部'].concat(actCity),
    //     },
    //   })
    // },
    // 走MySQL查询
    *queryData({ payload }, { select, call, put }) {
      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      })
      const { formData, dataCache } = yield select(state => state.marketCalendar)
      const formParam = {
        ...formData,
      }
      // 日期区间
      if (formData.dateRange[0]) {
        formParam.startTime = formData.dateRange[0].format('YYYY-MM-DD HH:mm:ss')
      }
      if (formData.dateRange[1]) {
        formParam.endTime = formData.dateRange[1].format('YYYY-MM-DD HH:mm:ss')
      }
      delete formParam.dateRange;
      // console.log('queryAct-formData', formParam)
      const key = Object.keys(formParam).map(item => formParam[item]).join('-');
      if (dataCache[key] && !payload.force) {
        yield put({
          type: 'updateState',
          payload: {
            loading: false,
            data: dataCache[key],
            calendarData: formatData(dataCache[key]),
          },
        })
        return;
      }
      const result = yield call(queryActList, formParam)
      // 切换到MySQL
      // console.log('queryActList', result)
      // const result1 = yield call(queryActCalendar, formParam);
      dataCache[key] = result.data;
      // console.log('queryActList111', result1)

      yield put({
        type: 'updateState',
        payload: {
          loading: false,
          data: result.data,
          dataCache,
          calendarData: result.data ? formatData(result.data) : {}
        },
      })
    },
    // 走ODPS表
    // *queryData2(model, { select, call, put }) {
    //   yield put({
    //     type: 'updateState',
    //     payload: {
    //       loading: true,
    //     },
    //   })
    //   const { formData, dataCache } = yield select(state => state.marketCalendar)
    //   const formParam = {
    //     ...formData,
    //   }
    //   // 日期区间
    //   if (formData.dateRange[0]) {
    //     formParam.startTime = formData.dateRange[0].format('YYYYMMDD')
    //   }
    //   if (formData.dateRange[1]) {
    //     formParam.endTime = formData.dateRange[1].format('YYYYMMDD')
    //   }
    //   delete formParam.dateRange;
    //   const key = Object.keys(formParam).map(item => formParam[item]).join('-');
    //   if (dataCache[key]) {
    //     yield put({
    //       type: 'updateState',
    //       payload: {
    //         loading: false,
    //         data: dataCache[key],
    //         calendarData: formatData(dataCache[key]),
    //       },
    //     })
    //     return;
    //   }
    //   // {
    //   //   startTime: '20191201',
    //   //   endTime: '20191231'
    //   // }
    //   const result = yield call(queryActCalendar, formParam);
    //   dataCache[key] = result.data;

    //   yield put({
    //     type: 'updateState',
    //     payload: {
    //       loading: false,
    //       data: result.data,
    //       dataCache,
    //       calendarData: result.data ? formatData(result.data) : {}
    //     },
    //   })
    // },
    *submitNewAct({ payload }, { put, call }) {
      // const { actFormData } = yield select(state => state.marketCalendar)
      // console.log('submit-actFormData-payload', payload)
      const actFormData = payload
      let result
      if (actFormData.id) {
        const options = {
          name: actFormData.name,
          subtitle: actFormData.subtitle || '',
          starttime: actFormData.starttime,
          endtime: actFormData.endtime,
          type: actFormData.type,
          country: actFormData.country,
          province: actFormData.province,
          city: actFormData.city,
          address: actFormData.address,
          hot: actFormData.hot,
          description: actFormData.description,
          area: actFormData.area,
          actStatus: actFormData.actStatus,
        }

        if (actFormData.tickettime) {
          options.tickettime = actFormData.tickettime
        }

        result = yield call(updateAct, {
          id: actFormData.id,
          options,
        })
      } else {
        delete actFormData.status
        if (!actFormData.tickettime) {
          delete actFormData.tickettime
        }
        result = yield call(createNewAct, {
          ...actFormData,
          ds: '2019-12-02',
        })
      }

      if (result.status === 'success') {
        // 成功
        message.success('操作成功！', 2)
      } else {
        message.error('操作失败！', 2)
      }

      yield put({
        type: 'updateState',
        payload: {
          newModelVisible: false,
        },
      })

      yield put({
        type: 'queryData',
        payload: {
          force: true,
        }
      })
    },
    *editAct({ payload }, { put }) {
      const temp = {
        ...payload
      }
      const actFormData = {}
      actFormData.name = payload.name
      delete temp.name

      yield put({
        type: 'updateState',
        payload: {
          newModelVisible: true,
          actFormData: {
            ...actFormData,
            ...temp,
          },
        },
      })
    },
    *deleteAct({ payload }, { call, put }) {
      const result = yield call(deleteAct, {
        id: payload.id,
      })
      if (result.status === 'success') {
        // 成功
        message.success('操作成功！', 2)
      } else {
        message.error('操作失败！', 2)
      }
      yield put({
        type: 'updateState',
        payload: {
          newModelVisible: false,
          modalVisible: false,
          actFormData: {},
        },
      })
      yield put({
        type: 'queryData',
        payload: {
          force: true,
        }
      })
    },
    *calendarPanelChange({ payload }, { select, put }) {
      const { formData } = yield select(state => state.marketCalendar)
      const {
        value,
      } = payload

      yield put({
        type: 'updateState',
        payload: {
          formData: {
            ...formData,
            dateRange: [
              dayjs(dayjs(value.startOf('month').format('YYYY-MM-DD')).subtract(7, 'days').format('YYYYMMDD')),
              dayjs(dayjs(value.endOf('month').format('YYYY-MM-DD hh:mm')).subtract(-7, 'days').format('YYYYMMDD')),
            ],
          },
          calendarValue: value,
        },
      })
      yield put({
        type: 'queryData',
      })
    },
    *popoverVisibleChange({ payload }, { put }) {
      const popoverVisible = {}
      popoverVisible[payload.cellDate] = payload.visible
      yield put({
        type: 'updateState',
        payload: {
          popoverVisible,
        },
      })
    },
    *showPopover({ payload }, { put }) {
      const popoverVisible = {}
      popoverVisible[payload] = true
      yield put({
        type: 'updateState',
        payload: {
          popoverVisible,
        },
      })
    },
    *closePopover({ payload }, { select, put }) {
      const { popoverVisible } = yield select(state => state.marketCalendar)
      popoverVisible[payload] = false
      yield put({
        type: 'updateState',
        payload: {
          popoverVisible,
        },
      })
    },
  },

  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      }
    },
    updateFormData(state, { payload }) {
      return {
        ...state,
        formData: {
          ...state.formData,
          ...payload,
        },
      }
    },
    hideDetail(state) {
      return {
        ...state,
        modalVisible: false,
      }
    },
    showDetail(state, { payload }) {
      return {
        ...state,
        modalVisible: true,
        modalItem: payload,
      }
    },
    closeDetail(state) {
      return {
        ...state,
        modalVisible: false,
      }
    },
    showNewAct(state) {
      return {
        ...state,
        newModelVisible: true,
      }
    },
    hideNewAct(state) {
      return {
        ...state,
        newModelVisible: false,
        actFormData: {},
      }
    },
  },
}
