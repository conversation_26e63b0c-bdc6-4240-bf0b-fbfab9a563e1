import React, { PureComponent } from 'react';
import { DownOutlined } from '@ant-design/icons';
import {
  Calendar,
  Badge,
  Card,
  Popover,
  Table,
  Modal,
  Select,
  Col,
  Row,
  Spin,
  Button,
  Popconfirm,
  Switch,
} from 'antd';
import { connect } from 'dva';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import solarLunar from 'solarlunar';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import SearchForm from '@/components/SearchForm';
import NewAct from './components/new-act';
import styles from './index.less';
import { CAL_KEY_MAP } from './constants';

const { Option } = Select
// const { RangePicker } = DatePicker;
dayjs.locale('zh-cn');

@connect(state => ({ marketCalendar: state.marketCalendar }))
class SubMenu extends PureComponent {
  // constructor(props) {
  //   super(props)
  //   this.state = {
  //     showShortTitle: false,
  //   }
  // }

  renderLoading = () => {
    return (
      <div style={{ textAlign: 'center', margin: '390px auto' }}>
        <Spin tip="数据加载中..." />
      </div>
    )
  }

  switchCheck = e => {
    const { dispatch } = this.props;
    dispatch({
      type: 'marketCalendar/updateState',
      payload: {
        showShortTitle: e,
      },
    })
  }

  headerRender = ({ value, onChange }, isAdmin, showShortTitle) => {
    const start = 0;
    const end = 12;
    const monthOptions = [];

    const current = value.clone();
    const localeData = value.localeData();
    const months = [];
    for (let i = 0; i < 12; i += 1) {
      current.month(i);
      months.push(localeData.monthsShort(current));
    }

    for (let index = start; index < end; index += 1) {
      monthOptions.push(
        <Option className="month-item" key={`${index}`}>
          {months[index]}
        </Option>,
      );
    }
    const month = value.month();

    const year = value.year();
    const options = [];
    for (let i = year; i < year + 2; i += 1) {
      options.push(
        <Option key={i} value={i} className="year-item">
          {i}
        </Option>,
      );
    }
    return (
      <div style={{ padding: 10 }}>
        <Row type="flex" justify="end" gutter={8}>
          {
            isAdmin ? <Col style={{ paddingTop: '2px' }}>
              <Switch checkedChildren="短标题" unCheckedChildren="短标题" defaultChecked={showShortTitle} onClick={e => this.switchCheck(e)} />
            </Col> : null
          }
          <Col style={{ paddingTop: '2px' }}>
            热度说明：
          </Col>
          <Col style={{ paddingTop: '4px' }}>
            <Badge status="error" text="高" />
          </Col>
          <Col style={{ paddingTop: '4px' }}>
            <Badge status="warning" text="中" />
          </Col>
          <Col style={{ marginRight: '24px', paddingTop: '4px' }}>
            <Badge status="default" text="低" />
          </Col>
          <Col>
            <Select
              size="small"
              dropdownMatchSelectWidth={false}
              className="my-year-select"
              onChange={newYear => {
                const now = value.clone().year(newYear);
                onChange(now);
              }}
              value={String(year)}
            >
              {options}
            </Select>
          </Col>
          <Col>
            <Select
              size="small"
              dropdownMatchSelectWidth={false}
              value={String(month)}
              onChange={selectedMonth => {
                const newValue = value.clone();
                newValue.month(parseInt(selectedMonth, 10));
                onChange(newValue);
              }}
            >
              {monthOptions}
            </Select>
          </Col>
          {/* <Col>
            <Group size="small" onChange={e => onTypeChange(e.target.value)} value={type}>
              <Button value="month">月</Button>
              <Button value="year">年</Button>
            </Group>
          </Col> */}
        </Row>
      </div>
    );
  }

  renderDetail = (item, isAdmin) => {
    const dataSource = []
    // 产品要求要按产品要求的字段顺序，不能随便
    dataSource.push({
      name: CAL_KEY_MAP.name,
      value: item.name
    })
    // 短标题
    if (item.subtitle) {
      dataSource.push({
        name: CAL_KEY_MAP.subtitle,
        value: item.subtitle
      })
    }
    dataSource.push({
      name: CAL_KEY_MAP.type,
      value: item.type
    })
    dataSource.push({
      name: CAL_KEY_MAP.starttime,
      value: item.starttime ? dayjs(item.starttime).format('YYYY-MM-DD HH:mm:ss') : ''
    })
    dataSource.push({
      name: CAL_KEY_MAP.endtime,
      value: item.endtime ? dayjs(item.endtime).format('YYYY-MM-DD HH:mm:ss') : ''
    })
    let status = ''
    if (new Date() / 1 > new Date(item.endtime) / 1) {
      status = '已结束'
    } else if (new Date() / 1 < new Date(item.starttime) / 1) {
      status = '未开始'
    } else if (new Date() / 1 >= new Date(item.starttime) / 1
      && new Date() / 1 <= new Date(item.endtime) / 1) {
      status = '进行中'
    }
    dataSource.push({
      name: CAL_KEY_MAP.actStatus,
      value: (item.actStatus === '已延期' || item.actStatus === '0') ? item.actStatus : status,
    })
    // dataSource.push({
    //   name: CAL_KEY_MAP.area,
    //   value: item.area
    // })
    dataSource.push({
      name: CAL_KEY_MAP.country,
      value: item.country || '无'
    })
    dataSource.push({
      name: CAL_KEY_MAP.province,
      value: item.province || '无'
    })
    dataSource.push({
      name: CAL_KEY_MAP.city,
      value: item.city || '无'
    })
    dataSource.push({
      name: CAL_KEY_MAP.address,
      value: item.address || '无'
    })
    dataSource.push({
      name: CAL_KEY_MAP.hot,
      value: item.hot
    })
    dataSource.push({
      name: CAL_KEY_MAP.description,
      value: item.description
    })
    if (item.tickettime) {
      dataSource.push({
        name: CAL_KEY_MAP.tickettime,
        value: item.tickettime ? dayjs(item.tickettime).format('YYYY-MM-DD HH:mm:ss') : ''
      })
    }

    const columns = [
      {
        title: '选项',
        dataIndex: 'name',
        key: 'name',
        width: 150,
      },
      {
        title: '取值',
        dataIndex: 'value',
        key: 'value',
        width: 450,
      },
    ];

    return (
      <div>
        {
          isAdmin ? <div>
          <Button
            type="primary"
            onClick={() => {
              this.props.dispatch({
                type: 'marketCalendar/editAct',
                payload: item,
              });
              this.props.dispatch({
                type: 'marketCalendar/hideDetail',
              });
            }}
            style={{ marginBottom: 10, marginRight: 20 }}
          >编辑活动</Button>
          <Popconfirm
            type="primary"
            title="确认是否删除活动？"
            onConfirm={() => this.props.dispatch({
              type: 'marketCalendar/deleteAct',
              payload: item,
            })}
          >
            <Button type="primary">删除活动</Button>
          </Popconfirm>
          </div> : null
        }
        <Table
          dataSource={dataSource}
          columns={columns}
          showHeader={ false }
          pagination={ false }
        />
      </div>
    )
  }

  dateCellRender = (value, showShortTitle) => {
    const { marketCalendar, dispatch } = this.props;
    const { calendarData, popoverVisible } = marketCalendar;
    const cellDate = dayjs(value).format('YYYY-MM-DD');
    const temp = calendarData[cellDate] || [];
    // 按热度降序排序
    const cellData = temp.sort((a, b) => b.hotLevel - a.hotLevel)
    // 取农历值
    const [Y, M, D] = cellDate.split('-')
     // 输入的日子为公历
    const solar2lunarData = solarLunar.solar2lunar(Y, M, D);
    return (
      <div className={styles.cellAct}>
        {
          cellData.map((item, index) => {
            if (index <= 4) {
              return (
                <Badge status={item.status} text={showShortTitle ? item.subtitle : item.name} key={`${cellDate}${item.starttime}-${index}`} onClick={() => dispatch({ type: 'marketCalendar/showDetail', payload: item })} />
              )
            }
            return null
          })
        }
        {
          cellData.length ? <Popover
            content={<div>
              {
                cellData.map((item, index) => (
                  <Badge status={item.status} text={showShortTitle ? item.subtitle : item.name} key={`${cellDate}${item.starttime}-${index}`} onClick={() => dispatch({ type: 'marketCalendar/showDetail', payload: item })} />
                ))
              }
              <a onClick={() => dispatch({
                  type: 'marketCalendar/closePopover',
                  payload: cellDate,
                })}>关闭</a>
              </div>
            }
            title={cellDate}
            trigger="click"
            visible={popoverVisible[cellDate]}
            onVisibleChange={visible => dispatch({
              type: 'marketCalendar/popoverVisibleChange',
              payload: {
                visible,
                cellDate,
              },
            })}
          >
            <div
              className={styles.more}
              onClick={() => {
                dispatch({
                  type: 'marketCalendar/showPopover',
                  payload: cellDate,
                });
                return false;
              }}
            >
              <DownOutlined />
            </div>
          </Popover> : null
        }
        {
          solar2lunarData.lDay === 1 ? <div className={styles.lunarFirst}>{solar2lunarData.monthCn}{solar2lunarData.dayCn}</div> : <div className={styles.lunar}>{solar2lunarData.dayCn}</div>
        }
      </div>
    );
  };

  render() {
    const { marketCalendar, dispatch } = this.props;
    const {
      formData, modalVisible, modalItem, calendarValue,
      actType, actCountry, actProvince, actCity, loading,
      newModelVisible, actFormData,
      isAdmin,
      showShortTitle,
    } = marketCalendar;

    const form = {
      searchForm: formData,
      setting: [
        {
          label: '活动类型',
          target: (
            <Select>
              {
                actType.map((item, index) => <Option key={`${item}-${index}`} value={item}>{item}</Option>)
              }
            </Select>
          ),
          name: 'actType',
        },
        {
          label: '境内/境外',
          target: (
            <Select>
              <Option value="全部">全部</Option>
              <Option value="境外（含港澳台）">境外（含港澳台）</Option>
              <Option value="境内">境内</Option>
            </Select>
          ),
          name: 'area',
        },
        {
          label: '国家/地区',
          target: (
            <Select>
              {
                actCountry.map(item => <Option key={item} value={item}>{item}</Option>)
              }
            </Select>
          ),
          name: 'actCountry',
        },
        {
          label: '省份/区域',
          target: (
            <Select>
              {
                actProvince.map((item, index) => <Option key={`${item}-${index}`} value={item}>{item}</Option>)
              }
            </Select>
          ),
          name: 'actProvince',
        },
        {
          label: '城市',
          target: (
            <Select>
              {
                actCity.map((item, index) => <Option key={`${item}-${index}`} value={item}>{item}</Option>)
              }
            </Select>
          ),
          name: 'actCity',
        },
        {
          label: '热度',
          target: (
            <Select>
              <Option key="全部" value="全部">全部</Option>
              <Option key="高" value="高">高</Option>
              <Option key="中" value="中">中</Option>
              <Option key="低" value="低">低</Option>
            </Select>
          ),
          name: 'hot',
        },
      ],
      onValuesChange: values => {
        dispatch({
          type: 'marketCalendar/updateFormData',
          payload: values,
        });
      },
      handleSubmit: () => {
        dispatch({
          type: 'marketCalendar/queryData',
        });
      },
    };
    console.log('showShortTitle', showShortTitle)

    return (
      <PageHeaderWrapper inner title="活动日历">
        <Card bordered>
        <SearchForm key="spmForm" {...form} />
        {
          isAdmin ? <Button
            type="primary"
            onClick={() =>
              dispatch({
                type: 'marketCalendar/showNewAct',
              })
            }
          >+ 新建活动</Button> : null
        }
        {
          loading ? this.renderLoading() : <Calendar
            value={calendarValue}
            onPanelChange={(value, mode) => dispatch({
              type: 'marketCalendar/calendarPanelChange',
              payload: {
                value,
                mode,
              }
            })}
            dateCellRender={e => this.dateCellRender(e, showShortTitle)}
            headerRender={e => this.headerRender(e, isAdmin, showShortTitle)}
          />
        }
        </Card>
        <Modal
          width={600}
          title={modalItem.name}
          visible={modalVisible}
          footer={null}
          onOk={() => dispatch({ type: 'marketCalendar/hideDetail' })}
          onCancel={() => dispatch({ type: 'marketCalendar/hideDetail' })}
        >
          {this.renderDetail(modalItem, isAdmin)}
        </Modal>

        <Modal
          width={600}
          title={actFormData.id ? '编辑活动' : '新建活动'}
          visible={newModelVisible}
          footer={null}
          onOk={() => dispatch({ type: 'marketCalendar/hideNewAct' })}
          onCancel={() => dispatch({ type: 'marketCalendar/hideNewAct' })}
        >
          <NewAct
            onSave={() => dispatch({ type: 'marketCalendar/hideNewAct' })}
            // readonly={this.state.readonly}
          />
        </Modal>
      </PageHeaderWrapper>
    );
  }
}

export default SubMenu;
