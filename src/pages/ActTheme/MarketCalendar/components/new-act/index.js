import React, { PureComponent } from 'react'
import dayjs from 'dayjs'
import PropTypes from 'prop-types'
import { connect } from 'dva'
import FormRender from 'form-render'
import { Layout, Popconfirm, Button, message } from 'antd'
import styles from './index.less'
import { ACT_TYPE } from '../../constants'

const uiSchema = {}

@connect(({ marketCalendar }) => {
  return {
    ...marketCalendar,
  }
})
class ActManager extends PureComponent {
  static propTypes = {
    onSave: PropTypes.func,
  }

  static defaultProps = {
    onSave: () => {},
  }

  constructor(props) {
    super(props)
    this.state = {
      isValidate: true,
      formData: {
        ...props.actFormData,
        starttime: dayjs(props.actFormData.starttime).format('YYYY-MM-DD HH:mm:ss'),
        endtime: dayjs(props.actFormData.endtime).format('YYYY-MM-DD HH:mm:ss'),
        tickettime: props.actFormData.tickettime ? dayjs(props.actFormData.tickettime).format('YYYY-MM-DD HH:mm:ss') : '',
      },
      readonly: props.readonly,
    }
  }

  componentWillReceiveProps(nextProps) {
    this.setState({
      formData: {
        ...nextProps.actFormData,
        starttime: dayjs(nextProps.actFormData.starttime).format('YYYY-MM-DD HH:mm:ss'),
        endtime: dayjs(nextProps.actFormData.endtime).format('YYYY-MM-DD HH:mm:ss'),
        tickettime: nextProps.actFormData.tickettime ? dayjs(nextProps.actFormData.tickettime).format('YYYY-MM-DD HH:mm:ss') : '',
      },
      readonly: nextProps.readonly,
    })
  }

  // 数据变化回调
  onChange = value => {
    const {
      formData
    } = this.state;
    this.setState({
      formData: {
        ...formData,
        ...value,
      },
    })
  }

  // 数据格式校验回调
  onValidate = list => {
    if (list.length > 0) {
      this.setState({ isValidate: false })
    } else {
      this.setState({ isValidate: true })
    }
  }

  onSave = () => {
    const { dispatch, onSave } = this.props

    if (!this.state.isValidate) {
      message.error('请正确填写数据', 2)
      return
    }

    const { formData } = this.state
    dispatch({
      type: 'marketCalendar/submitNewAct',
      payload: formData,
    })

    onSave()
  }

  render() {
    const { isLoading, actTypeConst } = this.props
    const propsSchema = {
      type: 'object',
      properties: {
        name: {
          title: '活动名称',
          type: 'string',
          maxLength: 100,
        },
        subtitle: {
          title: '短标题',
          type: 'string',
          description: '不超过11个字',
          maxLength: 20,
        },
        starttime: {
          title: '开始时间',
          type: 'string',
          format: 'date',
          'ui:options': {
            format: 'YYYY-MM-DD HH:mm:ss',
          },
        },
        endtime: {
          title: '结束时间',
          type: 'string',
          format: 'date',
          'ui:options': {
            format: 'YYYY-MM-DD HH:mm:ss',
          },
        },
        type: {
          title: '活动类型',
          type: 'string',
          enum: actTypeConst.length ? actTypeConst : ACT_TYPE,
          enumNames: actTypeConst.length ? actTypeConst : ACT_TYPE,
          'ui:widget': 'select',
          maxItems: 1,
        },
        area: {
          title: '地区',
          type: 'string',
          enum: ['境内', '境外（含港澳台）'],
          enumNames: ['境内', '境外（含港澳台）'],
          'ui:widget': 'radio',
        },
        country: {
          title: '国家/地区',
          type: 'string',
        },
        province: {
          title: '省份/区域',
          type: 'string',
        },
        city: {
          title: '城市',
          type: 'string',
        },
        address: {
          title: '地址',
          type: 'string',
        },
        hot: {
          title: '热度',
          type: 'string',
          enum: ['高', '中', '低'],
          enumNames: ['高', '中', '低'],
          'ui:widget': 'radio',
        },
        description: {
          title: '描述',
          type: 'string',
        },
        tickettime: {
          title: '抢票/打印准考证时间',
          type: 'string',
          format: 'date',
          'ui:options': {
            format: 'YYYY-MM-DD HH:mm:ss',
          },
        },
        actStatus: {
          title: '状态',
          type: 'string',
          enum: ['正常', '已延期'],
          enumNames: ['正常', '已延期'],
          'ui:widget': 'radio',
        },
      },
      required: ['name', 'starttime', 'endtime'],
    }
    // console.log('this.state.formData', this.state.formData, this.state.formData.starttime, dayjs(this.state.formData.starttime).format('YYYY-MM-DD HH:mm:ss'))
    return (
      <Layout style={{ background: '#fff' }}>
        <FormRender
          className="titan-page"
          key={`FormRender${!!isLoading}`}
          propsSchema={propsSchema}
          uiSchema={uiSchema}
          formData={this.state.formData}
          onChange={this.onChange}
          onValidate={this.onValidate}
          displayType="row"
          labelWidth={120}
          readOnly={this.state.readonly}
        />
        {this.state.readonly ? null : (
          <Popconfirm
            type="primary"
            title="确认是否保存？"
            onConfirm={() => this.onSave()}
          >
            <Button type="primary">保存</Button>
          </Popconfirm>
        )}
      </Layout>
    )
  }
}

export default ActManager
