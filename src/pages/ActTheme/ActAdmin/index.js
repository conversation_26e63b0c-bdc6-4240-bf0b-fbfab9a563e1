import React, { PureComponent } from 'react';
import { Card, Table, Spin, Modal } from 'antd';
import { connect } from 'dva';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';

// const { RangePicker } = DatePicker;
dayjs.locale('zh-cn');
const { confirm } = Modal

@connect(state => ({ actAdmin: state.actAdmin }))
class SubMenu extends PureComponent {
  renderLoading = () => {
    return (
      <div style={{ textAlign: 'center', margin: '390px auto' }}>
        <Spin tip="数据加载中..." />
      </div>
    )
  }

  showConfirm = record => {
    const { dispatch } = this.props
    confirm({
      title: '确认要恢复吗',
      content: '这个是已删除的活动，恢复后可到活动日历中查看或修改!',
      onOk() {
        dispatch({
          type: 'actAdmin/unDelete',
          payload: record,
        })
      },
      onCancel() {},
    })
  }

  render() {
    const { actAdmin } = this.props;
    const {
      data, loading,
    } = actAdmin;

    const columns = [
      {
        title: '活动ID',
        dataIndex: 'id',
        key: 'id',
        width: 80,
      },
      {
        title: '活动名称',
        dataIndex: 'name',
        key: 'name',
        width: 180,
      },
      {
        title: '开始时间',
        dataIndex: 'starttime',
        key: 'starttime',
        width: 120,
        render: text => <span>{dayjs(text).format('YYYY-MM-DD HH:mm:ss')}</span>
      },
      {
        title: '结束时间',
        dataIndex: 'endtime',
        key: 'endtime',
        width: 120,
        render: text => <span>{dayjs(text).format('YYYY-MM-DD HH:mm:ss')}</span>
      },
      {
        title: '活动类型',
        dataIndex: 'type',
        key: 'type',
        width: 100,
      },
      {
        title: '境内/境外',
        dataIndex: 'area',
        key: 'area',
        width: 100,
      },
      {
        title: '国家/地区',
        dataIndex: 'country',
        key: 'country',
        width: 100,
      },
      {
        title: '省份/区域',
        dataIndex: 'province',
        key: 'province',
        width: 100,
      },
      {
        title: '城市',
        dataIndex: 'city',
        key: 'city',
        width: 100,
      },
      {
        title: '地址',
        dataIndex: 'address',
        key: 'address',
        width: 200,
      },
      {
        title: '热度',
        dataIndex: 'hot',
        key: 'hot',
        width: 80,
      },
      {
        title: '购票时间',
        dataIndex: 'tickettime',
        key: 'tickettime',
        width: 120,
        render: text => <span>{text && dayjs(text).format('YYYY-MM-DD HH:mm:ss')}</span>
      },
      {
        title: '状态',
        dataIndex: 'actStatus',
        key: 'actStatus',
        width: 80,
      },
      {
        title: '操作',
        dataIndex: 'action',
        fixed: 'right',
        render: (text, record) => (<span><a onClick={() => this.showConfirm(record)}>恢复</a></span>)
      },
    ]

    return (
      <PageHeaderWrapper inner title="活动管理">
        <Card bordered title="已删除活动列表">
        {
          loading ? this.renderLoading() : <Table
            bordered
            dataSource={data}
            columns={columns}
            rowKey={(record, key) => key}
            scroll={{ x: 1400 }}
          />
        }
        </Card>
      </PageHeaderWrapper>
    );
  }
}

export default SubMenu;
