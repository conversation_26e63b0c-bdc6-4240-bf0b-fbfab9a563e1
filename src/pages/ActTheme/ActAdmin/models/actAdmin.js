import { message } from 'antd'
import { queryDeleteAct, unDeleteAct } from '@/services/api'

export default {
  namespace: 'actAdmin',
  state: {
    loading: true,

    // 活动数据
    data: [],
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(({ pathname }) => {
        if (pathname === '/act-theme/act-admin') {
          dispatch({ type: 'query' })
        }
      })
    },
  },
  effects: {
    *query(model, { put, all }) {
      yield all([
        put.resolve({
          type: 'queryData',
        }),
      ])
    },
    *queryData(_, { call, put }) {
      const response = yield call(queryDeleteAct);
      yield put({
        type: 'updateState',
        payload: {
          data: response.data,
          loading: false,
        },
      })
    },
    *unDelete({ payload }, { call, put }) {
      const {
        id
      } = payload
      const result = yield call(unDeleteAct, { id });
      if (result.status === 'success') {
        // 成功
        message.success('操作成功！', 2)
      } else {
        message.error('操作失败！', 2)
      }
      yield put({
        type: 'queryData'
      })
    },
  },

  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      }
    },
  },
}
