import { message } from 'antd';
import {
  queryEnclosedItems,
  createEnclosedItem,
  queryEnclosedEnum,
  queryAlgoModelTaskInstance,
  queryAlgoModel,
  queryAlgoModelTaskConfigEnum,
  queryAlgoModelEnum,
  excuteAlgoModelTaskConfigTask,
  stopAlgoModelTaskConfigTask,
  editCrowdByAlgo,
  delayEnclosed,
  deleteEnclosed,
} from '@/services/api';

export default {
  namespace: 'enclosedItem',
  state: {
    loading: false,
    tasks: [],
    type: 'primary', // primary: 我的， all: 全部
    pagination: {},
    algoModels: [],
    /**
     * 编辑
     */
    editChooseFormData: {},
    editCopFormData: {},
    // 圈人
    copModalVisible: false,
    // 选品
    chooseModalVisible: false,
    isEditChooseModal: true,
    fileContent: '',

    crowdDelayModalVisible: false,
    editCrowdDelayFormData: {},

    bizSceneType: [],
    algoModelName: [],
    itemType: [],

    businessTypes: [],
    drawerVisible: false,
    displayRows: [],
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(({ pathname }) => {
        if (pathname === '/choose-product/enclosed-item') {
          dispatch({ type: 'query' });
        }
      });
    },
  },
  effects: {
    *query(model, { put, all }) {
      yield all([
        put.resolve({
          type: 'queryEnclosedItems',
        }),
        put.resolve({
          type: 'queryEnclosedEnum',
        }),
        put.resolve({
          type: 'queryAlgoModel',
        }),
        put.resolve({
          type: 'queryAlgoModelTaskConfigEnum',
        }),
      ]);
    },
    *queryAlgoModel(model, { put, call }) {
      // 获取全部的算法模型，目前设为100
      const res = yield call(queryAlgoModel, { pageNum: 1, pageSize: 100 });

      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }

      const rows = res.data && res.data.rows;

      yield put({
        type: 'updateState',
        payload: {
          algoModels: rows,
        },
      });
    },
    *queryEnclosedItems({ payload }, { select, put, call }) {
      const { pagination, type } = yield select(state => state.enclosedItem);

      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const res = yield call(queryEnclosedItems, {
        pageNum: pagination.current || 1,
        pageSize: pagination.pageSize || 10,
        type,
        ...payload,
      });

      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }

      const { rows, totalNum } = res.data;

      pagination.total = totalNum;

      yield put({
        type: 'updateState',
        payload: {
          pagination,
          tasks: rows,
          loading: false,
        },
      });
    },
    *queryAlgoModelTaskConfigEnum(model, { call, put }) {
      const res = yield call(queryAlgoModelTaskConfigEnum);

      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }

      yield put({
        type: 'updateState',
      });
    },
    *queryAlgoModelEnum(model, { call, put }) {
      const res = yield call(queryAlgoModelEnum);

      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }

      const { statuses, businessTypes, platforms } = res.data;

      yield put({
        type: 'updateState',
        payload: {
          statuses,
          businessTypes,
          platforms,
        },
      });
    },
    *queryEnclosedEnum(model, { call, put }) {
      const res = yield call(queryEnclosedEnum);

      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }

      const { bizSceneType, algoModelName, itemType } = res.data;

      yield put({
        type: 'updateState',
        payload: {
          bizSceneType,
          algoModelName,
          itemType,
        },
      });
    },
    *editEnclosedItem({ payload = {} }, { put, call }) {
      const data = payload;

      const res = yield call(createEnclosedItem, data);

      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }

      message.success(data.id ? '更新成功' : '创建成功');

      yield put({
        type: 'queryEnclosedItems',
        payload: {},
      });

      yield put({
        type: 'onChooseModalCancel',
      });
    },
    *submitCreateChoosePerson({ payload }, { call, put, select }) {
      const data = payload;

      const { editCopFormData } = yield select(state => state.enclosedItem);

      const isUpdate = !!(editCopFormData && editCopFormData.id);
      if (isUpdate) {
        data.id = editCopFormData.id;
      }

      const res = yield call(editCrowdByAlgo, data);

      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }
      message.success(isUpdate ? '更新成功' : '创建成功');

      yield put({
        type: 'updateState',
        payload: {
          copModalVisible: false,
        },
      });
    },
    *queryAlgoModelTaskInstance({ payload }, { call }) {
      const res = yield call(queryAlgoModelTaskInstance, payload);

      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }

      // eslint-disable-next-line consistent-return
      return res.data;
    },
    *excuteAlgoModelTaskConfigTask({ payload }, { call }) {
      const res = yield call(excuteAlgoModelTaskConfigTask, payload);

      if (!res.success) {
        message.error(res.msg || '请求失败');
        return false;
      }

      message.success('任务开始执行');
      return true;
    },
    *stopAlgoModelTaskConfigTask({ payload }, { call }) {
      const res = yield call(stopAlgoModelTaskConfigTask, payload);

      if (!res.success) {
        message.error(res.msg || '请求失败');
        return false;
      }

      message.success('任务停止成功');
      return true;
    },
    *onEditEnclosedItem({ payload }, { put }) {
      yield put({
        type: 'onEditFormData',
        payload,
      });

      yield put({
        type: 'onOpenEnclosedItem',
      });
    },
    *editCrowdDelayTime({ payload }, { call, put }) {
      const data = payload;

      const res = yield call(delayEnclosed, data);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success('延期成功');

      yield put({
        type: 'queryEnclosedItems',
      });

      yield put({
        type: 'onCrowdDelayModalCancel',
      });
    },
    *delete({ payload }, { call, put }) {

      console.log('for delete payload', payload);
      const res = yield call(deleteEnclosed, payload);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success('删除成功');

      yield put({
        type: 'queryEnclosedItems',
      });
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    onOpenEnclosedItem(state) {
      return {
        ...state,
        chooseModalVisible: true,
      };
    },
    onEditFormData(state, { payload }) {
      return {
        ...state,
        editFormData: payload
      };
    },
    onChooseModalCancel(state) {
      return {
        ...state,
        chooseModalVisible: false,
        editChooseFormData: {},
      };
    },
    onCopModalCancel(state) {
      return {
        ...state,
        copModalVisible: false,
        editCopFormData: {},
      };
    },
    updateEditUploadFormData(state, { payload }) {
      return {
        ...state,
        editUploadFormData: {
          ...state.editUploadFormData,
          ...payload,
        },
      };
    },
    onEditCopFormDataChange(state, { payload }) {
      // TODO 根据选择的主场景修改 templateType 的值
      return {
        ...state,
        editCopFormData: {
          ...state.editCopFormData,
          ...payload,
        },
      };
    },
    onOpenCrowdDelayModal(state) {
      return {
        ...state,
        crowdDelayModalVisible: true,
      };
    },
    onCrowdDelayModalCancel(state) {
      return {
        ...state,
        crowdDelayModalVisible: false,
      };
    },
  },
};
