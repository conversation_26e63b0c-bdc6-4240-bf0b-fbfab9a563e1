import React, { PureComponent } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Badge,
  Popover,
  Tag,
} from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons'
import dayjs from 'dayjs';
import { connect } from 'dva';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import SearchTable from '@/components/SearchTable';
import CreateCopModal from '@/components/CreateChoosePersonModal';
import CreateEnclosedModal from '@/components/CreateEnclosedModal';
import CrowdDelayModal from '../GatherPerson/components/CrowdDelayModal';
import { STATUS_MAP } from './constants';
import styles from './index.less';

const { TabPane } = Tabs;

@connect(state => ({ enclosedItem: state.enclosedItem, user: state.user }))
class enclosedItem extends PureComponent {
  onSearch = values => {
    const {
      dispatch,
      enclosedItem: { pagination: pager },
    } = this.props;

    // 置为第一页
    dispatch({
      type: 'enclosedItem/updateState',
      payload: {
        pagination: {
          ...pager,
          current: 1,
        },
      },
    });

    dispatch({
      type: 'enclosedItem/queryAlgoModelTaskConfig',
      payload: {
        ...values,
      },
    });
  };

  onTabChange = value => {
    const {
      dispatch,
      enclosedItem: { pagination: pager },
    } = this.props;
    const type = value === '2' ? 'all' : 'primary';
    dispatch({
      type: 'enclosedItem/updateState',
      payload: {
        type,
        pagination: {
          ...pager,
          current: 1,
        },
      },
    });

    dispatch({
      type: 'enclosedItem/queryEnclosedItems',
      payload: {
        type,
      },
    });
  };

  onSubmit = values => {
    const { dispatch } = this.props;
    dispatch({
      type: 'enclosedItem/editEnclosedItem',
      payload: {
        ...values,
      },
    });
  };

  handleTableChange = pagination => {
    const {
      dispatch,
      enclosedItem: { pagination: pager },
    } = this.props;

    dispatch({
      type: 'enclosedItem/updateState',
      payload: {
        pagination: {
          ...pager,
          current: pagination.current,
          pageSize: pagination.pageSize,
        },
      },
    });
    dispatch({
      type: 'enclosedItem/queryAlgoModelTaskConfig',
      payload: {
        pageNum: pagination.current,
      },
    });
  };

  onLater = record => {
    this.onOpenCrowdDelayModal(record);
  }

  onDelete = id => {
    const { dispatch } = this.props;

    dispatch({
      type: 'enclosedItem/delete',
      payload: {
        id,
      },
    });
  }

  onEdit = record => {
    const {
      dispatch
    } = this.props;
    if (!record) {
      return;
    }

    dispatch({
      type: 'enclosedItem/onEditEnclosedItem',
      payload: record,
    });
  }

  onCrowdDelayModalCancel = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'enclosedItem/onCrowdDelayModalCancel',
    });
  };

  // 提交过期时间
  onSubmitDelayTime = values => {
    const { dispatch } = this.props;

    dispatch({
      type: 'enclosedItem/editCrowdDelayTime',
      payload: values,
    });
  };


  onOpenCrowdDelayModal = record => {
    const { dispatch } = this.props;

    dispatch({
      type: 'enclosedItem/updateState',
      payload: {
        editCrowdDelayFormData: record,
        crowdDelayModalVisible: true,
      },
    });
  };

  render() {
    const {
      enclosedItem: {
        chooseModalVisible,
        isEditChooseModal,
        editCopFormData,
        copModalVisible,
        loading,
        tasks,
        pagination,
        algoModels,
        type,
        editFormData,

        bizSceneType,
        algoModelName,
        itemType,

        editCrowdDelayFormData,
        crowdDelayModalVisible,
      },
      dispatch,
    } = this.props;

    const defaultActiveKey = type === 'all' ? '2' : '1';

    const dbname = <div>
      <div style={{ marginBlottom: 0 }}>结果表：trip_profile.dws_fliggy_push_recommendation_result</div>
      <div style={{ marginBlottom: 0 }}>一级分区ds=任务运行日期，二级分区task_id=素材id</div>
    </div>;
    const columns = [
      {
        title: '素材ID',
        dataIndex: 'id',
        align: 'center',
      },
      {
        title: '素材名称',
        dataIndex: 'itemTaskName',
        className: styles.taskName,
        render: text => (
          <Popover placement="topLeft" content={text}>
            {text}
          </Popover>
        ),
      },
      {
        title: '素材类型',
        dataIndex: 'itemType',
        render: text => {
          const obj = itemType && itemType.find(item => {
            return Object.keys(item).find(key => key === text);
          }) || { [text]: '暂无' };
          return obj[text];
        }
      },
      {
        title: '素材池ID',
        dataIndex: 'itemPoolId',
      },
      {
        title: '应用场景',
        dataIndex: 'bizSceneType',
        render: text => {
          const obj = bizSceneType && bizSceneType.find(item => {
            return Object.keys(item).find(key => key === text);
          }) || { [text]: '暂无' };
          return obj[text];
        }
      },
      {
        title: '数量',
        dataIndex: 'amount',
        render: text => text || '暂无'
      },
      {
        title: '创建人',
        dataIndex: 'creator',
        render: creator =>
          !!creator && (
            <span>
              <Tag color="green">{creator.nickName}</Tag>
            </span>
          ),
      },
      {
        title: '数据更新时间',
        dataIndex: 'gmtModified',
        render: gmtLastStart =>
          (gmtLastStart ? dayjs(gmtLastStart).format('YYYY-MM-DD HH:mm:ss') : '暂无'),
      },
      {
        title: '状态',
        width: 120,
        dataIndex: 'taskStatus',
        render: status => (
          <div>
            <Badge status={STATUS_MAP[status] && STATUS_MAP[status].status} text={STATUS_MAP[status].text} />
            {
              status === 'SUCCESS'
              ? (
                <Popover content={dbname}>
                  <QuestionCircleOutlined />
                </Popover>
              ) : null
            }
          </div>
        ),
      },
      {
        title: '操作',
        align: 'middle',
        width: 180,
        dataIndex: 'action',
        render: (text, record) => {
          const { creator, operator } = record;

          const owners = creator && operator && [creator, ...(operator || [])];

          const {
            user: { currentUser },
          } = this.props;

          const isGranted = owners.map(o => o.nickName).includes(currentUser.name);

          return (
            <div>
              <a disabled={!isGranted} type="link" onClick={() => this.onLater(record)}>
                延期
              </a>
              <Divider type="vertical" />
              <a disabled={!isGranted} type="link" onClick={() => this.onEdit(record)}>
                编辑
              </a>
              <Divider type="vertical" />
              <a disabled={!isGranted} type="link" onClick={() => this.onDelete(record.id)}>
                删除
              </a>
              {/* <a type="link">分析</a>
              <Divider type="vertical" />
              <a type="link">同步</a>
              <Divider type="vertical" />
              <a type="link">查看效果</a>
              <Divider type="vertical" /> */}
            </div>
          );
        },
      },
    ];

    const table = (
      <SearchTable
        rowKey={record => record.id}
        loading={loading}
        dataSource={tasks}
        onSearch={this.onSearch}
        columns={columns}
        pagination={pagination}
        onChange={this.handleTableChange}
        hideRowSelection={1}
      />
    );

    const buttons = (
      <div>
        <Button
          type="primary"
          style={{ marginRight: 20 }}
          onClick={() => {
            dispatch({
              type: 'enclosedItem/updateState',
              payload: {
                chooseModalVisible: true,
                isEditChooseModal: true,
                editFormData: {}
              },
            });
          }}
        >
          新增素材
        </Button>
      </div>
    );

    return (
      <PageHeaderWrapper title="人货素材匹配">
        <Card bordered={false}>
          <Tabs
            defaultActiveKey={defaultActiveKey}
            animated={false}
            tabBarExtraContent={buttons}
            onChange={this.onTabChange}
          >
            <TabPane tab="我的素材" key="1">
              {table}
            </TabPane>
            <TabPane tab="全部素材" key="2">
              {table}
            </TabPane>
          </Tabs>

          {/* 新增素材 */}
          <CreateEnclosedModal
            algoModels={algoModels}
            editFormData={editFormData}
            editModalVisible={chooseModalVisible}
            isEdit={isEditChooseModal}
            itemBizType={bizSceneType}
            algoModelName={algoModelName}
            itemType={itemType}
            onSubmit={this.onSubmit}
            onCancel={() =>
              dispatch({
                type: 'enclosedItem/onChooseModalCancel',
              })
            }/>

          {/**
           * 延时
           */}
          <CrowdDelayModal
            editFormData={editCrowdDelayFormData}
            editModalVisible={crowdDelayModalVisible}
            onCancel={this.onCrowdDelayModalCancel}
            onSubmit={this.onSubmitDelayTime}
          />
          <CreateCopModal
            algoModels={algoModels}
            editFormData={editCopFormData}
            editModalVisible={copModalVisible}
            onValuesChange={values =>
              dispatch({
                type: 'enclosedItem/onEditCopFormDataChange',
                payload: values,
              })
            }
            onSubmit={values =>
              dispatch({
                type: 'enclosedItem/submitCreateChoosePerson',
                payload: values,
              })
            }
            onCancel={() =>
              dispatch({
                type: 'enclosedItem/onCopModalCancel',
              })
            }
          />
        </Card>
      </PageHeaderWrapper>
    );
  }
}

export default enclosedItem;
