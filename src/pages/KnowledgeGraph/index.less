.htitle {

}
.title {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 3px 12px;
  background-color: #ccc;
}
.desc {
  position: relative;
  padding: 3px 12px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.desctableItem {
  background-color: #fff;
  padding: 3px 6px;
}
.white {
  background-color: #fff;
}
.block {
  .white;
}
.content {
  background-color: #fff;
  padding-bottom: 24px;
}
.btn {
  margin-top: 6px;
  margin-right: 6px;
  margin-left: 6px;
}
.concept {
  background-color: #fff;
}
.subtitle {
  margin-right: 12px;
}
.desctd {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}
.hasMore {
  position: absolute;
  right: 12px;
  bottom: 0;
  background-color: #fff;
}
.loading {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 22;
}
.mainImg {
  width: 100%;
  height: 100%;
}
.desctabletitle {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.kg_content {
  :global {
    .ant-table-tbody > tr > td {
      padding: 6px !important;
    }
  }
}