import {
  queryKnowledgeByName,
  queryKnowledgeById,
  queryKnowledgeTypeEnum,
} from '@/services/strategy';
import { message } from 'antd';
import { drawer } from '../draw';

export default {
  namespace: 'knwoledgeGraph',
  state: {
    loading: false,
    isModalShow: true,
    query: '',
    type: '3',
    typeEnum: [],
    pagination: {
      current: 1,
      pageSize: 10,
    },
    knwoledgegraphObj: {},
    detailModalVisible: false,
    refreshLoading: false,
    editCrowdDelayFormData: {},
    crowdDelayModalVisible: false,
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(({ pathname }) => {
        if (pathname.includes('/knowledge/knowledge-graph')) {
          dispatch({
            type: 'getEnum'
          })
          // dispatch({
          //   type: 'queryKnowledgeByName',
          //   payload: {
          //     name: '杭州西湖风景区'
          //   }
          // });
          dispatch({
            type: 'drawer',
          })
        }
      });
    },
  },
  effects: {
    *getEnum({ payload = {} }, { put, call }) {
      const res = yield call(queryKnowledgeTypeEnum);
      if (!res.success) {
        message.error(res.message);
        return;
      }
      const {
        data: {
          list = []
        } = {}
      } = res;
      const typeEnums = list.filter(i => i).map(a => {
        return {
          ...a,
          entityTypeId: a.entityTypeId.toString()
        }
      });
      yield put({
        type: 'updateState',
        payload: {
          typeEnum: typeEnums,
        }
      });
    },
    *drawer({ payload = {} }, { call }) {
      yield call(drawer, {
        ...payload,
        // onClick: function* getA(data) {
        //   // new Promise()
        //   yield put({
        //     type: 'queryKnowledgeById',
        //     payload: data,
        //   })
        // },
      });
    },
    *queryKnowledgeByName({ payload = {} }, { select, put, call }) {
      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });
      const { pagination, query, type } = yield select(state => state.knwoledgeGraph);

      const res = yield call(queryKnowledgeByName, {
        ...payload,
        name: payload.name || query,
        type: payload.type || type,
        pageNo: pagination.current || 1,
        pageSize: pagination.pageSize || 10,
      });

      if (!res.success) {
        message.error(res.message);
        yield put({
          type: 'updateState',
          payload: {
            loading: false,
          }
        });
        return;
      }

      if (res && (!res.data || (res.data && Object.keys(res.data).length === 0))) {
        message.warn('暂无结果,请尝试切换输入框左侧类型或关键词！');
        yield put({
          type: 'updateState',
          payload: {
            loading: false,
          }
        });
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          query: payload.name || query,
          type: payload.type || type,
          loading: false,
          isModalShow: true,
          knwoledgegraphObj: res.data,
          pagination: res.data && res.data.baseDescInfoVOPageInfo
        },
      });

      const {
        baseDescInfoVOPageInfo: {
          data: resultData
        } = {},
      } = (res.data || {});
      if (resultData && resultData.length === 1 && ((pagination.current || 1) === 1)) {
        yield put({
          type: 'drawer',
          payload: res.data
        });
      }
    },
    *queryKnowledgeById({ payload = {} }, { put, call }) {
      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });
      const res = yield call(queryKnowledgeById, payload);

      if (!res.success) {
        message.error(res.msg);
        yield put({
          type: 'updateState',
          payload: {
            loading: false,
          }
        });
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          loading: false,
          knwoledgegraphObj: res.data,
          isModalShow: false,
        },
      });

      yield put({
        type: 'drawer',
        payload: res.data
      });
    },
    *onOpenDetailModal({ payload }, { put }) {
      const { curCrowd } = payload;

      yield put({
        type: 'updateState',
        payload: {
          curCrowd,
          detailModalVisible: true,
        },
      });
    },
    *hiddenModal({ payload }, { put }) {
      yield put({
        type: 'updateState',
        payload: {
          isModalShow: payload
        },
      });
    }
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    onOpenMultMerchatsGpDrawer(state) {
      return {
        ...state,
        multMerchatsGpVisible: true,
      };
    },
    onCloseMultMerchatsGpDrawer(state) {
      return {
        ...state,
        multMerchatsGpVisible: false,
        editFormData: {},
      };
    },
    onOpenCrowdDelayModal(state) {
      return {
        ...state,
        crowdDelayModalVisible: true,
      };
    },
    onCrowdDelayModalCancel(state) {
      return {
        ...state,
        crowdDelayModalVisible: false,
      };
    },
  },
};
