import React, { PureComponent, useState } from 'react';
import { connect } from 'dva';
import { DownOutlined, UpOutlined } from '@ant-design/icons'
import { Modal, Card, Table, Button, Row, Col, Input, Select, Pagination, Spin, } from 'antd';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { destroy } from './draw';
import styles from './index.less';

const { Option } = Select;

function InfoTitle(props) {
  const {
    title = '基本信息',
    onClick,
    show,
  } = props;
  return (
    <div className={styles.title} onClick={onClick}>
      <span>{title}</span>
      {
        show ? <UpOutlined /> : <DownOutlined />
      }
    </div>
  );
}

function BaseInfo(props) {
  let info = {};
  try {
    info = JSON.parse(props.info);
  } catch (ex) {
    // console.error(ex.message, 'props info', props);
  }
  const {
    name = '杭州',
    typeDesc,
    longitudeAndLatitude,
    introduction,
    time,
    crowd,
    author,
    context,
  } = info;
  const [show, setShow] = useState(false);
  const [showMore, setShowMore] = useState(false);
  const onClickMore = () => {
    setShowMore(!showMore);
  }
  const renderText = (key, value, hiddenMore) => {
    if (!value) return null;
    return (
      <div
        className={styles.desc}
        style={showMore ? null : { maxHeight: 90 }}>
        <span>{key}: </span>
        <span> {value}</span>
        {
          hiddenMore ?
          (
            <a
              className={styles.hasMore}
              onClick={() => onClickMore()}>
              {
                showMore ? '收起' : '更多'
              }
            </a>
          )
          : null
        }
      </div>
    );
  };
  if (Object.keys(info) < 1) {
    return (
      <div style={{ padding: 12 }} >
        暂无信息
      </div>
    );
  }
  const onClick = () => {
    setShow(!show);
  }
  return (
    <div className={styles.block}>
      <h3 style={{ paddingLeft: 12 }}>{name}</h3>
      <InfoTitle onClick={onClick} show={show} />
      {show ?
        (
          <div className={styles.content}>
            {
              renderText('类型', typeDesc)
            }
            {
              renderText('经纬度', longitudeAndLatitude)
            }
            {
              renderText('描述', introduction, true)
            }
            {
              renderText('适宜时间', time)
            }
            {
              renderText('适宜人群', crowd)
            }
            {
              renderText('作者', author)
            }
            {
              renderText('内容正文', context, true)
            }
          </div>
        )
        : null
      }
    </div>
  );
}

function ThemeConcept(props) {
  const {
    hotThemeConcepts = [],
    onSearch,
  } = props;
  const [show, setShow] = useState(false);
  if (hotThemeConcepts.length === 0) {
    return null;
  }
  const onClick = () => {
    setShow(!show);
  }
  return (
    <div className={styles.concept}>
      <InfoTitle title="主题概念" onClick={onClick} show={show} />
      {
        show ?
        (
          <div className={styles.content}>
            {
              hotThemeConcepts.map(item => {
                const {
                  concept,
                  conceptId,
                  entityType,
                } = item;
                return (
                  <Button
                    className={styles.btn}
                    onClick={() => onSearch({
                      value: conceptId,
                      type: entityType,
                      name: concept,
                    })}>
                    {
                      concept
                    }
                  </Button>
                );
              })
            }
          </div>
        ) : null
      }
    </div>
  );
}

/**
 * 单个实体的表格
 * @param {*} props 
 */
function EntityItem(props) {
  const {
    atomicEntity = '著名景点',
    entityCount = '3',
    entityInfoList,
    onSearch,
  } = props;
  const [show, setShow] = useState(false);
  const onClick = () => {
    setShow(!show);
  }
  const columns = [
    {
      title: '名称',
      dataIndex: 'entityName',
      key: 'entityName',
      // width: 220,
    },
    {
      title: '类型',
      dataIndex: 'typeDesc',
      key: 'typeDesc',
      width: 50,
    },
    {
      title: '热度指数',
      dataIndex: 'hot',
      key: 'hot',
      width: 70,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 90,
      render: (text, record) => {
        return (
          <span>
            <a
              onClick={() => onSearch({
                value: record.entityId,
                type: record.entityType,
                name: record.entityName,
              })}>
                点击查看
              </a>
          </span>
        )
      }
    }
  ];
  return (
    <div className={styles.desctableItem}>
      <div className={styles.desctabletitle}>
        <div>
          <span className={styles.subtitle}>{atomicEntity} </span>
          <span className={styles.subtitle}>  {entityCount}</span>
        </div>
        <a onClick={() => onClick()}>{show ? '收起' : '展开'}</a>
      </div>
      {
        show ? (
          <Table
            bordered
            pagination={false}
            dataSource={entityInfoList}
            columns={columns}
          />
        ) : null
      }
    </div>
  );
}

function Entitys(props) {
  const {
    entities = [],
    onSearch,
  } = props;
  const [show, setShow] = useState(false);
  if (entities.length === 0) {
    return null;
  }
  const onClick = () => {
    setShow(!show);
  }
  return (
    <div style={{ backgroundColor: '#fff' }}>
      <InfoTitle title="关联实体" onClick={onClick} show={show} />
      {
        show ?
        (
          <div className={styles.content}>
          {
            entities.map(node => {
              return <EntityItem {...node} onSearch={onSearch} />
            })
          }
          </div>
        ) : null
      }
    </div>
  )
}

function Products(props) {
  const {
    associatedCount,
    products = [],
    onSearch,
  } = props;
  const [show, setShow] = useState(false);
  if (products.length === 0) {
    return null;
  }
  const onClick = () => {
    setShow(!show);
  }
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 240,
    },
    {
      title: '热度指数',
      dataIndex: 'hot',
      key: 'hot',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 90,
      render: (text, record) => {
        return (
          <span>
            <a onClick={() => {
              onSearch({
                value: record.productId,
                type: record.entityType,
                name: record.name,
              })
            }}>点击查看</a>
          </span>
        )
      }
    }
  ];
  return (
    <>
      <InfoTitle title="商品" onClick={onClick} show={show} />
      {
        show ?
        (
          <div className={styles.content}>
            <div className={styles.desctableItem}>
              <span className={styles.subtitle}>关联商品数量：</span>
              <span>{associatedCount}</span>
            </div>
            <div className={styles.desctableItem}>
              <span className={styles.subtitle}>热门商品:</span>
              <Table
                pagination={false}
                columns={columns}
                dataSource={products} />
            </div>
          </div>
        ) : null
      }
    </>
  );
}

@connect(state => ({ knwoledgeGraph: state.knwoledgeGraph, user: state.user }))
class knwoledgeGraph extends PureComponent {
  constructor(props) {
    super(props);
    this.defaultSelect = '3';
  }

  handleTableChange = (current, pageSize) => {
    const {
      dispatch,
      knwoledgeGraph: { pagination: pager },
    } = this.props;

    dispatch({
      type: 'knwoledgeGraph/updateState',
      payload: {
        pagination: {
          ...pager,
          current,
          pageSize,
        },
      },
    });

    dispatch({
      type: 'knwoledgeGraph/queryKnowledgeByName',
      payload: {
        pageNo: current,
      },
    });
  };

  onSelectChange = (keys, rows) => {
    const {
      dispatch,
      knwoledgeGraph: { selectedRows },
    } = this.props;
    const selectedRowKeys = keys;
    let sr = rows.filter(item => item);

    if (keys.length !== sr.length) {
      sr = keys.map(k => selectedRows.find(r => r.id === k) || sr.find(r => r.id === k));
    }

    dispatch({
      type: 'knwoledgeGraph/updateState',
      payload: {
        selectedRowKeys,
        selectedRows: sr,
      },
    });
  };

  onSearch = inputValue => {
    let input = inputValue;
    if (typeof inputValue === 'string') {
      input = {
        id: inputValue,
      };
    }
    const { value, type, name } = input;
    const { dispatch, knwoledgeGraph: { type: rawType } } = this.props;
    dispatch({
      type: 'knwoledgeGraph/updateState',
      payload: {
        query: name,
        type: type && type.toString() || rawType,
      },
    });

    dispatch({
      type: 'knwoledgeGraph/queryKnowledgeById',
      payload: {
        type: type && type.toString() || rawType,
        id: value,
      },
    });
  }

  onSearchByName = value => {
    const {
      dispatch,
      knwoledgeGraph: { pagination: pager },
    } = this.props;

    dispatch({
      type: 'knwoledgeGraph/updateState',
      payload: {
        pagination: {
          ...pager,
          current: 1,
          pageSize: 10,
        },
        query: value,
      },
    });
    dispatch({
      type: 'knwoledgeGraph/queryKnowledgeByName',
      payload: {
        name: value,
      },
    });
  }

  handleChange = value => {
    this.defaultSelect = value;
    const { dispatch } = this.props;
    dispatch({
      type: 'knwoledgeGraph/updateState',
      payload: {
        type: value.toString(),
      }
    });
  }

  onCancel = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'knwoledgeGraph/hiddenModal',
      payload: false
    });
  }

  onValueChange = e => {
    const { dispatch } = this.props;
    dispatch({
      type: 'knwoledgeGraph/updateState',
      payload: {
        query: e.target.value
      }
    });
  }

  render() {
    const {
      knwoledgeGraph: {
        query,
        type,
        typeEnum = [],
        loading,
        isModalShow,
        pagination,
        knwoledgegraphObj = {},
      },
    } = this.props;

    const columns = [
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        width: 170,
      },
      {
        title: '类型',
        dataIndex: 'typeDesc',
        key: 'typeDesc',
      },
      {
        title: '经纬度',
        dataIndex: 'longitudeAndLatitude',
        key: 'longitudeAndLatitude',
      },
      {
        title: '描述',
        dataIndex: 'desc',
        key: 'desc',
        width: 200,
        render: text => {
          return (
            <span className={styles.desctd}>
              {
                text
              }
            </span>
          )
        }
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 90,
        render: (text, record) => (
          <a
            onClick={
              () => this.onSearch({
                value: record.entityId,
                type: record.entityType,
                name: record.name,
              })
            }>
            进入图谱
          </a>
        ),
      },
    ];

    const {
      baseDescInfoVOPageInfo: {
        data = [],
      } = {},
      knowledgeGraphVO = {},
    } = knwoledgegraphObj;

    const hasVo = Object.keys(knowledgeGraphVO).length > 0;

    if (!hasVo) {
      destroy();
    }
    return (
      <PageHeaderWrapper
        inner
        title="知识图谱"
      >
        <Row style={{ marginBottom: 10 }}>
          <Input.Group compact>
            <Select
              style={{ width: '10%' }}
              value={type}
              // defaultValue={this.defaultSelect}
              onChange={this.handleChange}>
              {/* <Option value="3">POI</Option>
              <Option value="1">目的地</Option>
              <Option value="2">商圈</Option>
              <Option value="4">商品</Option> */}
              {
                typeEnum.map(typeObj => {
                  return (
                    <Option value={typeObj.entityTypeId}>{typeObj.entityName}</Option>
                  );
                })
              }
            </Select>
            <Input.Search
              style={{ width: '50%' }}
              defaultValue={query}
              value={query}
              onChange={e => this.onValueChange(e)}
              placeholder="请输入目的地/商圈/POI/概念等"
              onSearch={this.onSearchByName} />
          </Input.Group>
        </Row>
        <Row>
          <Modal
            width={840}
            footer={null}
            onCancel={this.onCancel}
            visible={(data.length > 1 || (data.length === 1 && ((pagination.current || pagination.pageNo) !== 1))) && isModalShow}>
            <Table
              columns={columns}
              dataSource={data}
              pagination={false}
            />
            <Row gutter={24} justify="end" align="right" style={{ marginTop: 24 }}>
              <Pagination
                {...pagination}
                current={pagination.pageNo}
                total={pagination.totalSize}
                showSizeChanger={false}
                onChange={this.handleTableChange} />
            </Row>
          </Modal>
          <Col xs={12} sm={14} md={14} lg={14} xl={16} xxl={18} style={{ position: 'relative' }}>
            {
              loading ? (
              <div className={styles.loading}>
                <Spin />
              </div>
              )
              : null
            }
            <Card bordered bodyStyle={{ padding: 0 }}>
              <div id="knowledge_container" style={{ overflow: 'hidden' }}>
                {
                  hasVo ?
                    null
                    : (
                      <img
                        alt="星空"
                        className={styles.mainImg}
                        src="https://img.alicdn.com/tfs/TB1yTgmQkT2gK0jSZFkXXcIQFXa-1024-576.jpg" />
                    )
                }
              </div>
            </Card>
          </Col>
          <Col xs={12} sm={10} md={10} lg={10} xl={8} xxl={6} style={{ height: 1000, overflow: 'scroll' }}>
            <div className={styles.kg_content}>
              <BaseInfo info={knowledgeGraphVO.baseInfo} />
              <ThemeConcept
                {...knowledgeGraphVO.themeConcept}
                onSearch={this.onSearch} />
              <Entitys
                {...knowledgeGraphVO.associatedEntity}
                onSearch={this.onSearch} />
              <Products
                {...knowledgeGraphVO.associatedProduct}
                onSearch={this.onSearch} />
            </div>
          </Col>
        </Row>
      </PageHeaderWrapper>
    );
  }
}

export default knwoledgeGraph;
