import G6 from '@antv/g6';
import deepclone from 'deepclone';

let globalGraphCache = null;
export function paint(renderData, onClick) {
  const width = document.getElementById('knowledge_container').scrollWidth || 600;
  const height = document.getElementById('knowledge_container').scrollHeight || 1000;
  const graph = new G6.Graph({
    container: 'knowledge_container',
    width,
    height,
    // fitView: true,
    // fitViewPadding: 50,
    layout: {
      type: 'force',
      preventOverlap: true,
      linkDistance: d => {
        if (d.source.id === 'node0') {
          return 260;
        }
        return 180;
      },
      nodeStrength: d => {
        if (d.isLeaf) {
          return -50;
        }
        return -10;
      },
      edgeStrength: d => {
        if (d.source.id === 'node1' || d.source.id === 'node2' || d.source.id === 'node3') {
          return 0.7;
        }
        return 0.1;
      },
    },
    defaultNode: {
      color: '#5B8FF9',
      style: {
        lineWidth: 1,
        fill: '#C6E5FF',
        fontSize: 10
      },
    },
    defaultEdge: {
      size: 1,
      color: '#e2e2e2',
    },
    modes: {
      default: [{
        type: 'tooltip',
        formatText: data => {
          // return ;
          return `<div style="background-color:#fff;padding:6px;border:1px solid #ccc;border-radius:3px;">${data.rawText || ''}</div>`;
        }
      }]
    }
  });
  const data = renderData;
  const {
    nodes
  } = data;
  graph.data({
    nodes,
    edges: data.edges.map((edge, i) => {
      edge.id = 'edge' + i;
      return Object.assign({}, edge);
    }),
  });
  graph.on('click', e => {
    const {
      item: {
        _cfg: {
          model: {
            id,
            typeId,
          } = {},
        } = {}
      } = {}
    } = e;
    console.log('clik id & type ', id, typeId);
    if (id && typeId && onClick) {
      const fff = onClick({
        id,
        type: typeId,
      });
      if (fff && fff.next) {
        const acs = fff.next({
          id,
          type: typeId,
        });
        const bcd = fff.next({
          id,
          type: typeId,
        });
        fff.next();
        // console.log('acs', acs, typeof acs, acs.value);
        // console.log('bcd', bcd, typeof bcd, bcd.value);
      }
    }
  });
  graph.render();
  graph.on('afterlayout', e => {
    if (nodes.length < 30) {
      graph.fitView(70);
    } else {
      graph.fitView(50);
    }
  })
  return graph;
}

function plattarnValues(raw = [], attr = {}, color = '#ccc') {
  let values = [];
  const {
    id = 'entityId',
    key,
    type,
    label = 'entityName',
  } = attr;
  if (key) {
    values = raw.reduce((arr, next, index) => {
      const list = next[key];
      const result = list.map((node, idx) => {
        const showText = node[label].length > 4 ? `${node[label].substring(0, 4)}..` : node[label];
        // {hot,isAtom}
        return {
          id: node[id] || `${index}-${idx}`,
          size: 60,
          isLeaf: true,
          typeId: node.entityType,
          rawText: `[${node.typeDesc || type}]${node[label]}`,
          label: showText,
          style: {
            fill: node.isAtom ? '#73D3F6' : 'orange',
            fontSize: 10
          }
        }
      });
      return arr.concat(result)
    }, [])
  } else {
    values = raw.map(node => {
      const showText = node[label].length > 4 ? `${node[label].substring(0, 4)}..` : node[label];
      return {
        id: node[id],
        size: 60,
        isLeaf: true,
        typeId: node.entityType,
        rawText: `[${node.typeDesc || type}]${node[label]}`,
        label: showText,
        style: {
          fill: node.isAtom ? '#73D3F6' : 'orange',
          fontSize: 10
        }
      }
    });
  }
  return values;
}

export function parseData(data) {
  const rawData = deepclone(data);
  const {
    // baseDescInfoVOList,
    knowledgeGraphVO: {
      associatedEntity: {
        entities = [],
      } = {},
      associatedProduct: {
        products = [],
      } = {},
      themeConcept: {
        hotThemeConcepts = [],
      } = {},
      baseInfo = '{}',
    } = {},
  } = rawData;

  const entitiesNodes = plattarnValues(entities, { key: 'entityInfoList', type: '实体' }, '#73D3F6');
  const themeNodes = plattarnValues(hotThemeConcepts, { label: 'concept', id: 'conceptId', type: '主题概念' }, 'orange');
  const productNodes = plattarnValues(products, { label: 'name', id: 'productId', type: '商品' });

  const nodes = entitiesNodes.concat(themeNodes).concat(productNodes);
  const edges = nodes.map(node => {
    return {
      source: 'root',
      target: node.id,
    };
  });
  const rootName = JSON.parse(baseInfo).name;
  nodes.push({ id: 'root', size: 70, label: rootName.substring(0, 5), rawText: rootName });

  return {
    nodes,
    edges,
  };
}

export function drawer({ onClick, ...data }) {
  console.log('onClick', onClick);
  return new Promise(resolve => {
    setTimeout(() => {
      if (globalGraphCache) {
        if (Object.keys(data).length > 0) {
          // globalGraphCache.changeData(parseData(data));
          globalGraphCache.destroy();
          globalGraphCache = null;
          globalGraphCache = paint(parseData(data), onClick);
        } else {
          globalGraphCache.destroy();
          globalGraphCache = null;
        }
      } else if (Object.keys(data).length > 0) {
        globalGraphCache = paint(parseData(data), onClick);
      }
      resolve();
    }, 20);
  });
}

export function destroy() {
  if (globalGraphCache) {
    globalGraphCache.destroy();
    globalGraphCache = null;
  }
}
