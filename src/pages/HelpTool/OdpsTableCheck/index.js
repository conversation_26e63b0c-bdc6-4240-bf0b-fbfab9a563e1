import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { Card, Form, Table, Button, Select, message, Spin } from 'antd';
import { odpsCheckTaskCreate, odpsCheckHistoryList, queryOdpsTable } from '@/services/api';
import { get, debounce } from 'lodash'

const FORM_ITEM_LAYOUT = {
  labelCol: {
    span: 5,
  },
  wrapperCol: {
    span: 10,
  },
};

const TASK_STATUS_TEXT = {
  'INIT': '初始化',
  'RUNNING': '运行中',
  'SUCCESS': '运行成功',
  'FAILED': '运行失败',
};

const OdpsTableCheck = (props) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [tableLoading, setTableLoading] = useState(false);
  const [selectLoading, setSelectLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [odpsTables, setOdpsTables] = useState([]);
  const [keyword, setKeyword] = useState('');

  useEffect(() => {
    query()
  }, [JSON.stringify(props?.currentUser)])

  const columns = [
    {
      title: 'odps表名',
      dataIndex: 'entity',
      key: 'entity',
    },
    {
      title: '任务状态',
      dataIndex: 'taskStatus',
      key: 'taskStatus',
      render: (text, record) => TASK_STATUS_TEXT[text],
    },
    {
      title: '备注',
      dataIndex: 'errMsg',
      key: 'errMsg'
    }
  ];

  const query = async () => {
    const { currentUser } = props
    if (JSON.stringify(currentUser) === '{}') return
    setTableLoading(true)
    const res = await odpsCheckHistoryList({
      operator: {
        empId: currentUser?.workId,
        nickName: currentUser?.name
      },
    })
    
    setTableLoading(false)

    if (!res?.success) {
      message.error(res?.errorMsg)
      return
    }

    setDataSource(res?.data || [])
  }

  const onFinish = async (values) => {
    const { currentUser } = props
    const { guid } = values;
    setLoading(true)
    const res = await odpsCheckTaskCreate({
      guid,
      operator: {
        empId: currentUser?.workId,
        nickName: currentUser?.name
      },
    })
    setLoading(false)
    
    if (!res?.success) {
      message.error(res?.msg)
      return
    }
    res?.data ? message.success('创建成功') : message.error('创建失败，请联系管理员')
    query()
  };

  const fetchOdpsTable = debounce((keyword) => {
    setSelectLoading(false)
    setKeyword(keyword)
    queryOdpsTable({ keyword }).then(res => {
      setSelectLoading(true)
      if (!res.success) {
        message.error(res.msg);
        return;
      }

      setOdpsTables(res?.data || [])
    });
  }, 800)

  return (
    <PageHeaderWrapper inner title="odps授权校验">
      <Card style={{ marginBottom: '20px' }} bordered={false}>
        <Form form={form} onFinish={onFinish} {...FORM_ITEM_LAYOUT}>
          <Form.Item label="odps表名" name="guid" rules={[{ required: true, message: 'odps表名必填' }]}>
            <Select
              showSearch
              onSearch={fetchOdpsTable}
              placeholder="选择ODPS表名称"
              optionLabelProp="value"
              showArrow={false}
              notFoundContent={selectLoading ? <Spin size="small" /> : null}
            >
              {odpsTables.length && // 高亮显示
                odpsTables.map(t => {
                  const index = t.indexOf(keyword);
                  const beforeStr = t.substr(0, index);
                  const afterStr = t.substr(index + keyword.length);
                  const content =
                    index > -1 ? (
                      <span>
                        {beforeStr}
                        <span style={{ color: '#f50' }}>{keyword}</span>
                        {afterStr}
                      </span>
                    ) : (
                      <span>{t}</span>
                    );
                  return (
                    <Option title={t} key={t}>
                      {content}
                    </Option>
                  );
                })}
            </Select>
          </Form.Item>
          <Form.Item wrapperCol={{ offset: 5, span: 16 }}>
            <Button type="primary" htmlType="submit" loading={loading}>
              提交
            </Button>
          </Form.Item>
        </Form>
      </Card>
      <Card>
        <h4>历史校验任务</h4>
        <Table
          scroll={{ y: 500 }}
          loading={tableLoading}
          dataSource={dataSource}
          columns={columns}
          rowKey={record => record.id}
          pagination={false}
          bordered
        />
      </Card>
    </PageHeaderWrapper>
  );
};

export default connect(({ user }) => ({
  currentUser: user.currentUser,
}))(OdpsTableCheck);
