import { Table, Empty, Tabs, But<PERSON>, Tooltip, Badge, Tag, message, Flex } from 'antd';
import { useState } from 'react';
import { DownloadOutlined } from '@ant-design/icons';
import { downloadCrowdBlood } from '@/services/api';
import dayjs from 'dayjs';
import { WarningFilled, ShareAltOutlined } from '@ant-design/icons';
import { zhugeUrl } from '@/utils/utils';
import { queryCrowdCircle } from '@/services/api';

const TableView = ({ crowdTable, crowdId }) => {
  //是否是上游人群
  const [crowdType, setCrowdType] = useState('upstream');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });

  const columns = [
    {
      title: '层级',
      dataIndex: 'level',
      key: 'level',
      render: text => {
        return (crowdType === 'upstream' ? '上游' : '下游') + 'L' + text;
      },
    },
    {
      title: '人群ID',
      dataIndex: 'crowdId',
      key: 'crowdId',
    },
    {
      title: '人群名称',
      dataIndex: 'crowdName',
      key: 'crowdName',
      width: 220,
      render: (text, record) => {
        return (
          <div>
            {record.timeType === 'ONLINE' ? <Tag color="volcano">实时</Tag> : null}
            <a
              onClick={() => {
                queryCrowdCircle(record.crowdId).then(res => {
                  if (res.data) {
                    const data = res.data;
                    if (data.crowdType === 'OPERATE_CROWD') {
                      window.open(zhugeUrl + `/crowd-stategy/gather-person?crowdId=${data.id}`);
                    }
                    window.open(
                      zhugeUrl +
                        `/crowd-stategy/gather-person/detail?crowdType=${data.circleType}&profileType=${data.physicalProfileCode}&id=${data.id}&type=view`
                    );
                    return;
                  }
                });
              }}
            >
              {text}
            </a>
          </div>
        );
      },
    },
    {
      title: '描述',
      dataIndex: 'crowdDesc',
      key: 'crowdDesc',
      ellipsis: {
        showTitle: false,
      },
      render: crowdDesc => (
        <Tooltip placement="topLeft" title={crowdDesc}>
          {crowdDesc}
        </Tooltip>
      ),
    },
    {
      title: '异常原因',
      dataIndex: "errorMsg",
      key: "errorMsg"
    },
    {
      title: '过期时间',
      dataIndex: 'expireTime',
      key: 'expireTime',
      render: text => {
        const isExpired = Date.now() > text;
        const isExpired7Day = Date.now() > text - 7 * 24 * 60 * 60 * 1000;
        return (
          <>
            {isExpired7Day && !isExpired ? <WarningFilled style={{ color: 'red' }} /> : null}
            {isExpired ? (
              <Badge status="error" text="已过期" />
            ) : (
              dayjs(text).format('YYYY-MM-DD HH:mm:ss')
            )}
          </>
        );
      },
    },
    {
      title: '负责人',
      dataIndex: 'owner',
      key: 'owner',
      render: text => text && text.nickName,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      render: text => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      render: (text, record) => {
        return (
          <Button
            icon={<ShareAltOutlined />}
            onClick={() => {
              window.open(
                zhugeUrl +
                  `/crowd-manage/human-relations?crowdId=${record.crowdId}&crowdName=${record.crowdName}`
              );
            }}
            type="link"
          >
            查看血缘关系
          </Button>
        );
      },
    },
  ];

  return (
    <>
      <Tabs
        activeKey={crowdType}
        onChange={key => {
          setCrowdType(key);
          setPagination({
            current: 1,
            pageSize: 10,
          });
        }}
        tabBarExtraContent={<Button
          type="primary"
          style={{ marginBottom: '10px' }}
          icon={<DownloadOutlined />}
          onClick={() => {
            if (!crowdId) {
              message.warning('请先选择人群');
              return;
            }
            if (crowdTable.childCrowds.length === 0 && crowdTable.parentCrowds.length === 0) {
              message.warning("人群关系为空，暂不导出");
              return;
            }
            downloadCrowdBlood({ crowdId }).then(res => {
              const url = window.URL.createObjectURL(new Blob([res]));
              const link = document.createElement('a');
              link.href = url;
              link.setAttribute('download', '人群血缘关系.xlsx');
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
            });
          }}
        >
          导出
        </Button>}
        items={[
          {
            key: 'upstream',
            label: '上游人群' + `(${crowdTable.childCrowds.length})`,
          },
          {
            key: 'downstream',
            label: '下游人群' + `(${crowdTable.parentCrowds.length})`,
          },
        ]}
      />
      <Table
        columns={columns}
        dataSource={crowdType === 'upstream' ? crowdTable.childCrowds : crowdTable.parentCrowds}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          onChange: (page, pageSize) => {
            setPagination({
              current: page,
              pageSize,
            });
          },
        }}
        locale={{
          emptyText: (
            <Empty description={`该人群没有${crowdType === 'upstream' ? '上' : '下'}游人群`} />
          ),
        }}
      />
    </>
  );
};

export default TableView;
