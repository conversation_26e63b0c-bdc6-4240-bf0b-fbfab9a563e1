import { Select, Spin } from 'antd';
import { debounce } from 'lodash';
import { useEffect, useState } from 'react';
import { queryCrowdBloodByKeyword } from '@/services/api';

const SearchCrowd = props => {
  const { value, onChange, options: crowdOptions = [] } = props;

  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState([]);

  useEffect(() => {
    if (crowdOptions) {
      setOptions(crowdOptions)
    }
  },[crowdOptions])

  const handleSearch = value => {
    if (!value) return;
    setLoading(true);
    setOptions([]);
    queryCrowdBloodByKeyword({ keyword: value }).then(res => {
      if (res.success && res.data) {
        let result = [];
        Object.keys(res.data).forEach(key => {
          result.push({
            label: res.data[key],
            value: key,
          });
        });
        setOptions(result);
        setLoading(false);
      }
    });
  };

  return (
    <Select
      value={value}
      onChange={onChange}
      placeholder="请输入人群ID或名称进行搜索"
      showSearch
      defaultActiveFirstOption={false}
      filterOption={false}
      notFoundContent={loading ? <Spin size="small" /> : null}
      onSearch={debounce(handleSearch, 500)}
      options={options}
      optionRender={({ data: item }) => (
        <>
          <p style={{ fontSize: 14, fontWeight: 500 }}>{item.label}</p>
          <p style={{ color: '#999999' }}>ID：{item.value}</p>
        </>
      )}
    />
  );
};

export default SearchCrowd;
