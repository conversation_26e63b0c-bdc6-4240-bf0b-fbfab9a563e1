import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { <PERSON>ton, Card, Tabs, Descriptions, Alert, Tag, Badge } from 'antd';
import { SearchForm, useForm } from 'form-render';
import styles from './index.less';
import GraphicsView from './GraphicsView';
import TableView from './TableView';
import SearchCrowd from './components/SearchCrowd';
import { queryCrowdBlood, queryCrowdBloodTable, queryCrowdCircle } from '@/services/api';
import { useEffect, useMemo, useState } from 'react';
import dayjs from 'dayjs';
import { zhugeUrl } from '@/utils/utils';
import { WarningFilled } from '@ant-design/icons';
import { useLocation, useParams, history } from 'umi';

const HumanRelations = () => {
  const form = useForm();
  const [crowdDetail, setCrowdDetail] = useState({});
  const [crowdTable, setCrowdTable] = useState({
    childCrowds: [],
    parentCrowds: [],
  });
  const [crowdId, setCrowdId] = useState();
  const [crowdOptions, setCrowdOptions] = useState([]);
  const [tabKey, setTabKey] = useState('1');
  const { query } = useLocation();

  useEffect(() => {
    if (query && query.crowdId && query.crowdName) {
      setCrowdId(query.crowdId)
      setCrowdOptions([{ label: query.crowdName, value: query.crowdId }]);
      form.setValues({ crowdId: query.crowdId });
      searchCrowdBlood(query.crowdId);
    }
  }, [query])

  const items = useMemo(() => {
    if (Object.keys(crowdDetail).length === 0) return [];
    const isExpired = Date.now() > crowdDetail.expireTime;
    const isExpired7Day = Date.now() > crowdDetail.expireTime - 7 * 24 * 60 * 60 * 1000;
    return [
      {
        key: '1',
        label: '人群ID',
        children: crowdDetail && crowdDetail.crowdId,
      },
      {
        key: '2',
        label: '描述',
        children: crowdDetail && crowdDetail.crowdDesc,
      },
      {
        key: '3',
        label: '过期时间',
        children: (
          <div>
            {isExpired7Day && !isExpired ? <WarningFilled style={{ color: 'red' }} /> : null}
            {isExpired ? (
              <Badge style={{marginTop:0}} status="error" text="已过期" />
            ) : (
              dayjs(crowdDetail.expireTime).format('YYYY-MM-DD HH:mm:ss')
            )}
          </div>
        ),
      },
      {
        key: '4',
        label: '负责人',
        children: crowdDetail && crowdDetail.owner && crowdDetail.owner.nickName,
      },
      {
        key: '5',
        label: '创建时间',
        children: crowdDetail && dayjs(crowdDetail.createTime).format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        key: '6',
        label: tabKey === "1" ? '直接上游人群数量' : "上游人群数量",
        children: tabKey === "1" ?  (crowdDetail && crowdDetail.childCnt) || 0 : crowdTable.childCrowds.length,
      },
      {
        key: '7',
        label: tabKey === "1" ?  '直接下游人群数量' : "下游人群数量",
        children: tabKey === "1" ?  (crowdDetail && crowdDetail.parentCnt) || 0 : crowdTable.parentCrowds.length,
      },
    ];
  }, [crowdDetail, tabKey]);

  const searchCrowdBlood = id => {
    setCrowdId(id);
    getCrowdBoold(id);
    getCrowdBooldTable(id);
  };

  const getCrowdBoold = id => {
    queryCrowdBlood({ crowdId: id }).then(res => {
      if (res.success && res.data) {
        setCrowdDetail(res.data);
      }
    });
  };

  const getCrowdBooldTable = id => {
    queryCrowdBloodTable({ crowdId: id }).then(res => {
      if (res.success && res.data) {
        setCrowdTable(res.data);
      }
    });
  };

  const handleCheckCrowdBlood = corwd => {
    if (corwd.crowdId) {
      setTabKey('1');
      setCrowdOptions([{ label: corwd.crowdName, value: corwd.crowdId }]);
      form.setValues({ crowdId: corwd.crowdId });
      searchCrowdBlood(corwd.crowdId);
    }
  };

  return (
    <PageHeaderWrapper title="人群血缘关系">
      <Alert message="该数据通过离线方式产出，当日新建人群请第二日再来查看" type="warning" />
      <Card style={{ marginTop: 16 }}>
        <SearchForm
          form={form}
          className={styles.searchForm}
          schema={{
            type: 'object',
            displayType: 'row',
            properties: {
              crowdId: {
                title: '人群信息',
                type: 'string',
                cellSpan: 4,
                widget: 'searchCrowd',
                labelWidth: 200,
                props: {
                  options: crowdOptions,
                },
              },
            },
          }}
          onMount={false}
          column={6}
          onSearch={formData => {
            if (!formData.crowdId) {
              setCrowdId('');
              return;
            }
            searchCrowdBlood(formData.crowdId);
          }}
          widgets={{ searchCrowd: SearchCrowd }}
          searchBtnRender={(refresh, clearSearch) => {
            return [
              <Button onClick={refresh} type="primary">
                查询
              </Button>,
              <Button
                onClick={() => {
                  setCrowdDetail({});
                  setCrowdId('');
                  setCrowdTable({
                    childCrowds: [],
                    parentCrowds: [],
                  });
                  if (query.crowdId) {
                    history.replace("/crowd-manage/human-relations")
                  }
                  clearSearch();
                }}
              >
                重置
              </Button>,
            ];
          }}
        />
      </Card>
      {crowdDetail && crowdDetail.crowdId && (
        <Card style={{ marginTop: 16 }}>
          <Descriptions
            title={
              <>
                {crowdDetail.timeType === 'ONLINE' ? <Tag color="volcano">实时</Tag> : null}
                {crowdDetail.crowdName}
              </>
            }
            extra={
              <Button
                type="primary"
                onClick={() => {
                  queryCrowdCircle(query.crowdId || crowdId).then(res => {
                    if (res.data) {
                      const data = res.data;
                      if (data.crowdType === 'OPERATE_CROWD') {
                        window.open(zhugeUrl + `/crowd-stategy/gather-person?crowdId=${query.crowdId || crowdId}`);
                      }
                      window.open(
                        zhugeUrl +
                          `/crowd-stategy/gather-person/detail?crowdType=${data.circleType}&profileType=${data.physicalProfileCode}&id=${data.id}&type=view`
                      );
                      return;
                    }
                  });
                }}
              >
                查看详情
              </Button>
            }
            items={items}
          />
        </Card>
      )}
      <Card style={{ marginTop: 16, minHeight: 500 }}>
        <Tabs
          defaultActiveKey="1"
          activeKey={tabKey}
          onChange={key => {
            setTabKey(key);
          }}
          type="card"
          style={{ marginBottom: 32 }}
          items={[
            {
              label: `图形视图`,
              key: '1',
              children: <GraphicsView crowdDetail={crowdDetail} />,
            },
            {
              label: `表格视图`,
              key: '2',
              children: (
                <TableView
                  crowdId={query.crowdId || crowdId}
                  crowdTable={crowdTable}
                  handleCheckCrowdBlood={handleCheckCrowdBlood}
                />
              ),
            },
          ]}
        />
      </Card>
    </PageHeaderWrapper>
  );
};
export default HumanRelations;
