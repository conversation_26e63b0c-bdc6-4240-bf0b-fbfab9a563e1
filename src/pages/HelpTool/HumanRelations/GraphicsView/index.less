.flow-container {
    width: 100%;
    height: 600px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.flow-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 4px;
    margin: 10px;
}

.flow-wrapper {
    flex: 1;
    position: relative;
    height: 100%;
    .ant-empty {
        margin-top: 100px;
        .ant-empty-description {
            color: rgba(0, 0, 0, 1);
        }
    }
}

.react-flow__attribution {
    display: none;
}

.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
}

.legend-item {
    display: flex;
    align-items: center;
    margin-right: 16px;
}

.legend-type.current {
    background-color: #1890ff;
}

.legend-type.upstream {
    background-color: #fa8c16;
}

.legend-type.downstream {
    background-color:#52c41a ;
}

.legend-type {
    margin-right: 8px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
}

.custom-node {
    padding: 10px;
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    color: white;
    font-weight: 500;
    text-align: center;
}

.custom-node.current {
    background-color: #1890ff;
}

.custom-node.upstream {
    background-color: #52c41a;
}

.custom-node.downstream {
    background-color: #fa8c16;
}

.node-content {
    width: 80px;
    font-size: 10px;
    text-align: center;
    word-break: break-all;
}

.node-details h3 {
    margin-top: 0;
    margin-bottom: 12px;
    font-size: 16px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 8px;
}

.node-details p {
    margin: 8px 0;
    font-size: 14px;
}