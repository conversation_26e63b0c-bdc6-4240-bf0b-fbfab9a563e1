import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  ReactFlow,
  Controls,
  Background,
  ReactFlowProvider,
  useReactFlow,
  useNodesState,
  useEdgesState,
  MarkerType,
  Panel,
  Handle,
  Position,
  getNodesBounds,
  getViewportForBounds,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { Button, Space, Flex, Spin, Popover, Empty, Tag, Badge } from 'antd';
import {
  ExportOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
  WarningFilled,
} from '@ant-design/icons';
import dagre from 'dagre';
import './index.less';
import { toPng } from 'html-to-image';
import dayjs from 'dayjs';

function transformData(crowd, nodes = [], edges = [], nodeType = 'current', color) {
  // 添加当前节点（如果尚未添加）
  if (!nodes.some(node => node.crowdId === crowd.crowdId)) {
    nodes.push({
      crowdId: crowd.crowdId,
      label: crowd.crowdName,
      type: nodeType,
      details: {
        title: crowd.crowdName,
        crowdId: crowd.crowdId,
        description: crowd.crowdDesc || '',
        expireTime: crowd.expireTime ? dayjs(crowd.expireTime).format('YYYY-MM-DD HH:mm:ss') : '',
        owner: crowd.owner || '',
        createTime: crowd.createTime ? dayjs(crowd.createTime).format('YYYY-MM-DD HH:mm:ss') : '',
        timeType: crowd.timeType,
        errorMsg: crowd.errorMsg || '',
      },
    });
  }

  // 处理子节点
  if (crowd.childCrowds && crowd.childCrowds.length > 0) {
    for (const child of crowd.childCrowds) {
      transformData(child, nodes, edges, 'downstream', '#1890ff');
      edges.push({
        crowdId: `${crowd.crowdId}-${child.crowdId}`,
        source: String(child.crowdId),
        target: String(crowd.crowdId),
        style: { stroke: color || '#1890ff' },
      });
    }
  }

  const parents = crowd.parentCrowds || [];
  if (parents.length > 0) {
    for (const parent of parents) {
      transformData(parent, nodes, edges, 'upstream', '#ccc');
      edges.push({
        crowdId: `${parent.crowdId}-${crowd.crowdId}`,
        source: String(crowd.crowdId),
        target: String(parent.crowdId),
        style: { stroke: color || '#ccc' },
      });
    }
  }

  return { nodes, edges };
}

// 自定义节点组件
const CustomNode = ({ data, isConnectable }) => {
  if (!data) return;
  const isExpired = Date.now() > dayjs(data.details.expireTime).valueOf();
  const isExpired7Day =
    Date.now() > dayjs(data.details.expireTime).valueOf() - 7 * 24 * 60 * 60 * 1000;

  return (
    <Popover
      arrow={false}
      placement="bottomRight"
      getPopupContainer={() => document.getElementById("fullscreen-node")}
      content={
        <div className="node-details">
          <h3>
            {data.details && data.details.timeType === 'ONLINE' ? (
              <Tag color="volcano">实时</Tag>
            ) : null}
            {data.details && data.details.title}
          </h3>
          <p>
            <strong>ID:</strong> {data.details && data.details.crowdId}
          </p>
          <p>
            <strong>描述:</strong> {data.details && data.details.description}
          </p>
          <p style={{ display: 'flex', alignItems: 'center' }}>
            <strong style={{ width: 70 }}>过期时间: </strong>
            {isExpired7Day && !isExpired ? <WarningFilled style={{ color: 'red' }} /> : null}
            {isExpired ? (
              <Badge style={{ width: '50%' }} status="error" text="已过期" />
            ) : (
              dayjs(data.details && data.details.expireTime).format('YYYY-MM-DD HH:mm:ss')
            )}
          </p>
          <p>
            <strong>负责人:</strong> {data.details && data.details.owner.nickName}
          </p>
          <p>
            <strong>创建时间:</strong> {data.details && data.details.createTime}
          </p>
          {data.details && data.details.errorMsg && (
            <p>
              <strong>异常原因:</strong> {data.details.errorMsg}
            </p>
          )}
        </div>
      }
    >
      <div style={{ height: 80 }}>
        <Handle type="target" position={Position.Left} isConnectable={isConnectable} />
        <div
          style={{
            // opacity: data.details.errorMsg ? 0.5 : 1,
            border: data.details.errorMsg ? '2px dashed #ff4d4f' : '',
            background: data.details.errorMsg
              ? data.type === 'downstream'
                ? 'rgb(250 140 22 / 50%)'
                : 'rgb(82 196 26 / 50%)'
              : '',
          }}
          className={`custom-node ${data.type}`}
        >
          <p className="node-content">
            {data.details && data.details.timeType === 'ONLINE' ? (
              <Tag color="volcano">实时</Tag>
            ) : null}
            {data.label}
          </p>
        </div>
        <Handle type="source" position={Position.Right} id="a" isConnectable={isConnectable} />
      </div>
    </Popover>
  );
};

function downloadImage(dataUrl, name) {
  const a = document.createElement('a');

  a.setAttribute('download', name + '人群血缘关系视图' + '.png');
  a.setAttribute('href', dataUrl);
  a.click();
}

// 导出按钮组件
const ExportButton = ({ text, name, nodes }) => {
  const imageWidth = Math.ceil(nodes.length / 10) * 600;
  const imageHeight = Math.ceil(nodes.length / 10) * 800;

  const { getNodes } = useReactFlow();

  const onExport = () => {
    const nodesBounds = getNodesBounds(getNodes());
    const viewport = getViewportForBounds(nodesBounds, imageWidth, imageHeight, 0.5, 2);

    toPng(document.querySelector('.react-flow__viewport'), {
      backgroundColor: '#fff',
      width: imageWidth,
      height: imageHeight,
      style: {
        width: imageWidth,
        height: imageHeight,
        transform: `translate(${viewport.x}px, ${viewport.y}px) scale(${viewport.zoom})`,
      },
    }).then(url => downloadImage(url, name));
  };

  return (
    <Button type="primary" icon={<ExportOutlined />} onClick={onExport}>
      {text}
    </Button>
  );
};

//全屏按钮组件
const FullScreenButton = () => {
  const nodeId = 'fullscreen-node'; // 定义要全屏的节点 ID
  const [isFullscreen, setIsFullscreen] = useState(false);

  const enterFullscreen = element => {
    if (element) {
      if (element.requestFullscreen) {
        element.requestFullscreen();
      } else if (element.webkitRequestFullscreen) {
        // Safari
        element.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) {
        // IE11
        element.msRequestFullscreen();
      }
    }
  };

  const exitFullscreen = () => {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if (document.webkitExitFullscreen) {
      // Safari
      document.webkitExitFullscreen();
    } else if (document.msExitFullscreen) {
      // IE11
      document.msExitFullscreen();
    }
  };

  const handleToggleFullscreen = () => {
    const element = document.getElementById(nodeId);
    if (document.fullscreenElement) {
      exitFullscreen();
      setIsFullscreen(false);
    } else {
      element.style.backgroundColor = '#fff';
      setIsFullscreen(true);
      enterFullscreen(element);
    }
  };
  return (
    <Button
      type="primary"
      icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
      onClick={handleToggleFullscreen}
      style={{ marginRight: 16 }}
    >
      {isFullscreen ? '退出全屏' : '全屏'}
    </Button>
  );
};

// 使用dagre库计算节点布局
const getLayoutedElements = (nodes, edges, direction = 'LR') => {
  const dagreGraph = new dagre.graphlib.Graph();
  dagreGraph.setDefaultEdgeLabel(() => ({}));

  // 设置图形布局方向和节点间距
  const nodeWidth = 80;
  const nodeHeight = 40;
  dagreGraph.setGraph({ rankdir: direction, nodesep: 60, ranksep: 60 });

  // 添加节点到dagre图
  nodes.forEach(node => {
    dagreGraph.setNode(node.id, { width: nodeWidth, height: nodeHeight });
  });

  // 添加边到dagre图
  edges.forEach(edge => {
    dagreGraph.setEdge(edge.source, edge.target);
  });

  // 计算布局
  dagre.layout(dagreGraph);

  // 获取计算后的节点位置
  const layoutedNodes = nodes.map(node => {
    const nodeWithPosition = dagreGraph.node(node.id);

    return {
      ...node,
      position: {
        x: nodeWithPosition.x - nodeWidth / 2,
        y: nodeWithPosition.y - nodeHeight / 2,
      },
    };
  });

  return { nodes: layoutedNodes, edges };
};

const FlowContent = ({ exportButtonText = '导出', serverNodes, serverEdges, crowdDetail }) => {
  // 定义节点类型
  const nodeTypes = {
    custom: CustomNode,
  };

  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [loading, setLoading] = useState(true);
  const reactFlowWrapper = useRef(null);
  const reactFlowInstance = useReactFlow();

  const transformServerNodesToFlowNodes = useCallback(serverNodes => {
    return serverNodes.map(node => ({
      id: String(node.crowdId),
      // 初始位置设为0,0，后面会用dagre计算
      position: { x: 0, y: 0 },
      data: {
        label: node.label,
        type: node.type,
        details: node.details,
      },
      type: 'custom',
      draggable: false,
    }));
  }, []);

  // 从服务端数据转换为ReactFlow边数据
  const transformServerEdgesToFlowEdges = useCallback(serverEdges => {
    return serverEdges.map(edge => ({
      id: String(edge.crowdId),
      source: edge.source,
      target: edge.target,
      animated: edge.animated || false,
      style: edge.style || { stroke: '#1890ff' },
      markerEnd: {
        type: MarkerType.ArrowClosed,
        color: edge.style?.stroke || '#1890ff',
      },
      draggable: false,
    }));
  }, []);

  // 获取数据并初始化图表
  useEffect(() => {
    const initializeFlow = async () => {
      setLoading(true);

      try {
        const data = transformData(crowdDetail);

        // 转换数据格式
        const flowNodes = transformServerNodesToFlowNodes(data.nodes);
        const flowEdges = transformServerEdgesToFlowEdges(data.edges);

        // 使用dagre计算布局
        const { nodes: layoutedNodes, edges: layoutedEdges } = getLayoutedElements(
          flowNodes,
          flowEdges
        );

        // 更新状态
        setNodes(layoutedNodes);
        setEdges(layoutedEdges);
      } catch (error) {
        console.error('Failed to initialize flow:', error);
      } finally {
        setLoading(false);
      }
    };

    initializeFlow();
  }, [crowdDetail]);

  // 当节点或边更新后，自动适应视图
  useEffect(() => {
    if (nodes.length > 0 && !loading && reactFlowInstance) {
      setTimeout(() => {
        reactFlowInstance.fitView({ padding: 0.2 });
      }, 100);
    }
  }, [nodes, loading, reactFlowInstance]);

  return (
    <div className="flow-wrapper" ref={reactFlowWrapper}>
      {loading ? (
        <div className="loading-container">
          <Spin tip="加载中..." size="large" />
        </div>
      ) : (
        <>
          {Object.keys(crowdDetail).length === 0 ? (
            <Empty
              description={
                <>
                  <h3>请在上方搜索并选择一个人群查看其血缘关系</h3>
                  <p>提示：输入人群ID或名称，选择后即可查看血缘关系图</p>
                </>
              }
            />
          ) : (
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              nodeTypes={nodeTypes}
              fitView
              fitViewOptions={{ padding: 0.2 }}
            >
              <Controls />
              <Background />

              {/* 使用Panel组件放置图例和导出按钮 */}
              <Panel position="top-left" className="flow-header">
                <Flex align="center">
                  <Space size="large">
                    <div className="legend-item">
                      <div className="legend-type current"></div>
                      <span>当前人群</span>
                    </div>
                    <div className="legend-item">
                      <div className="legend-type upstream" />
                      <span>上游人群</span>
                    </div>
                    <div className="legend-item">
                      <div className="legend-type downstream" />
                      <span>下游人群</span>
                    </div>
                    <div className="legend-item">
                      <div
                        style={{
                          marginRight: 8,
                          border: '1px dashed #ff4d4f',
                          background: 'rgb(250 140 22 / 50%)',
                        }}
                        className="legend-type"
                      />
                      /
                      <div
                        style={{
                          margin: "0 8px",
                          border: '1px dashed #ff4d4f',
                          background: "rgb(82 196 26 / 50%)",
                        }}
                        className="legend-type"
                      />
                      <span style={{ color: '#ff4d4f' }}>异常人群,请多关注!</span>
                    </div>
                  </Space>
                </Flex>
              </Panel>
              <Panel position="top-right">
                <FullScreenButton />
                <ExportButton text={exportButtonText} name={crowdDetail.crowdName} nodes={nodes} />
              </Panel>
            </ReactFlow>
          )}
        </>
      )}
    </div>
  );
};

// 主组件
const GraphicsView = props => {
  return (
    <div id="fullscreen-node" className="flow-container">
      <ReactFlowProvider>
        <FlowContent {...props} />
      </ReactFlowProvider>
    </div>
  );
};

export default GraphicsView;
