import React, { useState, useEffect } from 'react';
import { Tag, Radio, Button, Input, Card, Typography, message, Modal, Select } from 'antd';
import { SwapOutlined, ArrowRightOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { connect } from 'dva';
import { get } from 'lodash';
import styles from './index.less';
import { idToDeviceOneId, queryMappingIdTypes } from '@/services/api';
const { confirm } = Modal;

const { Paragraph } = Typography;

const DeviceOneid = props => {
  const [type, setType] = useState(1); // 1-实时查询
  const [inputVal, setInputVal] = useState(null);
  const [outputVal, setOutputVal] = useState(null);
  const [inputTypeOpts, setInputTypeOpts] = useState([]); // 输入类型
  const [inputTypeVal, setInputTypeVal] = useState(null);
  const [queryBtnLoading, setQueryBtnLoading] = useState(false);

  useEffect(() => {
    queryMappingIdTypes().then(res => {
      if (res.success) {
        const data = res.data && res.data.length > 0 ? res.data.map(ele => ({ label: ele, value: ele })) : []
        setInputTypeOpts(data)
        setInputTypeVal(get(data, '[0].value', null))
      } else {
        message.error(res.msg);
      }
    })
  }, [])

  const onTypeChange = e => {
    setInputVal(null);
    setOutputVal(null);
  };

  const onTaobaoIdChange = e => {
    setInputVal(e.target.value);
  };

  const onQuery = async () => {
    let res = {};
    if (!inputVal) {
      message.error('请输入Device Oneid')
      return
    }
    if (!inputTypeVal) {
      message.error('请选择输入类型')
      return
    }
    setQueryBtnLoading(true);
    res = await idToDeviceOneId({ idType: inputTypeVal, idValue: inputVal });
    setQueryBtnLoading(false);
    if (res.success && res.data) {
      setOutputVal(res.data && res.data.length ? res.data.join(',') : null);
      message.success('查询成功');
      return;
    }
    message.error(res.msg);

  };


  return (
    <Card bordered={false}>
      <div className={styles.toolRadio}>
        <h3>目标类型</h3>
        <Radio.Group onChange={onTypeChange} value={type}>
          <Radio value={1}>单个查询</Radio>
        </Radio.Group>
      </div>
      <div className={styles.toolContent}>
        <div className={styles.toolContentLeft}>
          <h3 style={{ display: 'flex', alignItems: 'center' }}>
            输入
            <Select
              style={{ width: 200, marginLeft: 10 }}
              options={inputTypeOpts}
              value={inputTypeVal}
              onChange={(val) => { setInputTypeVal(val) }}
            />
          </h3>
          <Input style={{ width: '400px' }} onChange={onTaobaoIdChange} value={inputVal} />
        </div>
        <div className={styles.toolContentCentre}>
          {type === 1 ? <SwapOutlined /> : <ArrowRightOutlined />}
        </div>
        <div className={styles.toolContentRight}>
          <h3>输出Device Oneid</h3>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Input disabled style={{ width: '400px' }} value={outputVal} />
            <Paragraph copyable={{ text: outputVal }} style={{ marginBottom: 0 }} />
          </div>
        </div>
      </div>
      <Button
        type="primary"
        shape="round"
        style={{ width: '100px' }}
        onClick={onQuery}
        loading={queryBtnLoading}
      >
        查询
      </Button>
    </Card>
  );
};

export default connect(({ user }) => ({
  currentUser: user.currentUser,
}))(DeviceOneid);
