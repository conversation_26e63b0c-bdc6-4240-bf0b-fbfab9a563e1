import React, { useState, useEffect } from 'react';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { Card, Form, Table, Button, Input, message } from 'antd';
import { queryLabelValue } from '@/services/api';

const FORM_ITEM_LAYOUT = {
  labelCol: {
    span: 5,
  },
  wrapperCol: {
    span: 10,
  },
};

const TagValueQuery = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [tagValue, setTagValue] = useState('');

  const onFinish = async (values) => {
    console.log('我在的热门', values);
    setLoading(true)
    const res = await queryLabelValue(values)
    setLoading(false)

    if (!res?.success) {
      message.error(res?.msg)
      return
    }
    
    setTagValue(res?.data)
  };

  return (
    <PageHeaderWrapper inner title="标签匹配校验">
      <Card style={{ marginBottom: '20px' }} bordered={false}>
        <Form form={form} onFinish={onFinish} {...FORM_ITEM_LAYOUT}>
          <Form.Item label="uid" name="entityId" rules={[{ required: true, message: 'uid必填' }]}>
            <Input placeholder="请输入uid" />
          </Form.Item>
          <Form.Item label="标签code" name="labelName" rules={[{ required: true, message: '标签code必填' }]}>
            <Input placeholder="请输入标签code" />
          </Form.Item>
          <Form.Item wrapperCol={{ offset: 5, span: 16 }}>
            <Button type="primary" htmlType="submit" loading={loading}>
              提交
            </Button>
          </Form.Item>
        </Form>
      </Card>
      <Card title="查询结果">
        <Form.Item label="标签值" {...FORM_ITEM_LAYOUT}>
          {tagValue || '无'}
        </Form.Item>
      </Card>
    </PageHeaderWrapper>
  );
};

export default TagValueQuery;
