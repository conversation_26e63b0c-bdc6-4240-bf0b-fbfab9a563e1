import React, { useState, useEffect } from 'react';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { Card, Form, Table, Button, Input, message } from 'antd';
import { crowdMatch } from '@/services/api';

const FORM_ITEM_LAYOUT = {
  labelCol: {
    span: 5,
  },
  wrapperCol: {
    span: 10,
  },
};

const MatchResult = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState({});

  const onFinish = async values => {
    setLoading(true);
    const res = await crowdMatch(values);
    setLoading(false);
    if (!res?.success) {
      message.error(res?.msg);
      return;
    }

    setData(res?.data || {});
  };

  const getMatchResultDetail = () => {
    if (data?.matchResultDetail) {
      return (
        <div style={{marginTop: 6}}>
          {Object.keys(data?.matchResultDetail || {}).map(ele => (
            <div>{`${ele}：${data?.matchResultDetail[ele]}`}</div>
          ))}
        </div>
      );
    }

    return '-';
  };

  return (
    <PageHeaderWrapper inner title="人群匹配校验">
      <Card style={{ marginBottom: '20px' }} bordered={false}>
        <Form form={form} onFinish={onFinish} {...FORM_ITEM_LAYOUT}>
          <Form.Item label="uid" name="uid" rules={[{ required: true, message: 'uid必填' }]}>
            <Input placeholder="请输入uid" />
          </Form.Item>
          <Form.Item
            label="人群id"
            name="crowdId"
            rules={[{ required: true, message: '人群id必填' }]}
          >
            <Input placeholder="请输入人群id" />
          </Form.Item>
          <Form.Item wrapperCol={{ offset: 5, span: 16 }}>
            <Button type="primary" htmlType="submit" loading={loading}>
              提交
            </Button>
          </Form.Item>
        </Form>
      </Card>
      <Card title="查询结果">
        <Form {...FORM_ITEM_LAYOUT}>
          <Form.Item label="匹配类型">{data?.matchType || '-'}</Form.Item>
          <Form.Item label="匹配结果">{data?.matchResult ? '是' : '否'}</Form.Item>
          <Form.Item label="匹配结果详情">{getMatchResultDetail()}</Form.Item>
        </Form>
      </Card>
    </PageHeaderWrapper>
  );
};

export default MatchResult;
