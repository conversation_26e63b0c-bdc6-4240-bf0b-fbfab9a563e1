import React, { useState, useEffect } from 'react';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { Card, Form, Table, Button, Input, Select, message } from 'antd';
import { queryOnlineStatisticVal } from '@/services/api';

const FORM_ITEM_LAYOUT = {
  labelCol: {
    span: 5,
  },
  wrapperCol: {
    span: 10,
  },
};

const OnlineTrafficStatistics = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [num, setNum] = useState('-');

  const onFinish = async (values) => {
    setLoading(true)
    const res = await queryOnlineStatisticVal(values)
    setLoading(false)
    if (!res?.success) {
      message.error(res?.msg)
      return
    }

    setNum(res?.data)
  };

  return (
    <PageHeaderWrapper inner title="人群流量分析">
      <Card style={{ marginBottom: '20px' }} bordered={false}>
        <Form form={form} onFinish={onFinish} {...FORM_ITEM_LAYOUT}>
          <Form.Item label="人群id" name="entityId" rules={[{ required: true, message: '人群id必填' }]}>
            <Input placeholder="请输入人群id" />
          </Form.Item>
          <Form.Item label="统计维度" name="statisticDim" rules={[{ required: true, message: '统计维度必填' }]}>
            <Select
              allowClear
              placeholder="请选择统计维度"
            >
              <Option value="week">近一周</Option>
              <Option value="month">近一个月</Option>
              <Option value="day">昨天</Option>
            </Select>
          </Form.Item>
          <Form.Item wrapperCol={{ offset: 5, span: 16 }}>
            <Button type="primary" htmlType="submit" loading={loading}>
              提交
            </Button>
          </Form.Item>
        </Form>
      </Card>
      <Card title="查询结果">
        <Form.Item label="线上流量统计日均值" {...FORM_ITEM_LAYOUT}>
          {num}
        </Form.Item>
      </Card>
    </PageHeaderWrapper>
  );
};

export default OnlineTrafficStatistics;
