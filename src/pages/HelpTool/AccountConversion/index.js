import React, { useState, useEffect } from 'react';
import { Tabs, Card } from 'antd';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import ScrmOneid from '../ScrmOneid';
import DeviceOneid from '../DeviceOneid';

const { TabPane } = Tabs;

const AccountConversion = () => {
  const [activeKey, setActiveKey] = useState('1');

  return (
    <PageHeaderWrapper inner title="账号转化">
      <Card bodyStyle={{padding: '0 10px 10px'}}>
        <Tabs
          destroyInactiveTabPane
          activeKey={activeKey}
          onChange={key => {
            setActiveKey(key);
          }}
        >
          <TabPane tab="Scrm-Oneid工具" key="1">
            <ScrmOneid />
          </TabPane>
          <TabPane tab="Device-Oneid工具" key="2">
            <DeviceOneid />
          </TabPane>
        </Tabs>
      </Card>
    </PageHeaderWrapper>
  );
};

export default AccountConversion;
