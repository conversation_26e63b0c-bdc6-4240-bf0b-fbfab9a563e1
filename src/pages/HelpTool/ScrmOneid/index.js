import React, { useState, useEffect } from 'react';
import { Tag, Radio, Button, Input, Card, Typography, message, Modal } from 'antd';
import { SwapOutlined, ArrowRightOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { connect } from 'dva';
import { get } from 'lodash';
import styles from './index.less';
import { uidToScrmOneId, offlineMapping, queryByCrowdId } from '@/services/api';
import { OFFLINE_ODPS_STATUS } from '@/constants';
const { confirm } = Modal;

const { Paragraph } = Typography;

const ScrmOneid = props => {
  const [type, setType] = useState(1); // 1-实时查询， 2-离线查询
  const [inputVal, setInputVal] = useState(null);
  const [outputVal, setOutputVal] = useState(null);
  const [messages, setMessages] = useState('');
  const [info, setInfo] = useState({}); //离线odps信息
  const [queryBtnLoading, setQueryBtnLoading] = useState(false);
  const [createBtnLoading, setCreateBtnLoading] = useState(false);

  const onTypeChange = e => {
    setType(e.target.value);
    setInputVal(null);
    setOutputVal(null);
    setInfo({});
    setMessages('')
  };

  const onTaobaoIdChange = e => {
    let data = e.target.value.replace(/[^0-9]/g,'');
    setInputVal(data);
  };

  // 生成
  const onCreate = async () => {
    if (!inputVal) {
      message.error('请输入人群包ID')
      return
    }
    setCreateBtnLoading(true)
    const res = await queryByCrowdId({ crowdId: inputVal });
    setCreateBtnLoading(false)
    if (get(res,'data.extInfo.idMappingTableName', '')) {
      confirm({
        title: '生成新的人群包将会覆盖老的人群包，是否确认生成并覆盖？',
        icon: <ExclamationCircleOutlined />,
        onOk() {
          return onOffline()
        },
        onCancel() {},
      });
    } else {
      onOffline()
    }
  };

  const onOffline = async () => {
    const res = await offlineMapping({
      creator: get(props, 'currentUser.workId', ''),
      crowdId: inputVal,
      sourceIdType: 'USER_ID',
      targetIdType: 'SCRM_ONE_ID',
    });
    if (res && res.success && res.data) {
      setMessages(res.data);
    } else {
      message.error(res.msg);
    }
  }

  const onQuery = async () => {
    let res = {};
    if (!inputVal) {
      message.error(type === 1 ? '请输入淘宝ID' : '请输入人群包ID')
      return
    }
    setQueryBtnLoading(true);
    if (type === 1) {
      res = await uidToScrmOneId({ userId: inputVal });
      setQueryBtnLoading(false);
      if (res.success && res.data) {
        setOutputVal(res.data);
        message.success('查询成功');
        return;
      }
      message.error(res.msg);
    }

    if (type === 2) {
      res = await queryByCrowdId({ crowdId: inputVal });
      setQueryBtnLoading(false);
      if (res.success) {
        if (res.data) {
          const { status, extInfo } = res.data;
          setInfo({
            status,
            ...extInfo,
          });
          setMessages('')
          return
        }
        setMessages('该人群包ID暂未生成离线表，请点击生成按钮生成离线表')
        return;
      }
      setMessages('')
      setInfo({})
    }
  };

  const outRender = () => {
    if (type === 1) {
      return (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Input disabled style={{ width: '400px' }} value={outputVal} />
          <Paragraph copyable={{ text: outputVal }} style={{ marginBottom: 0 }} />
        </div>
      );
    }

    if (type === 2) {
      // 查询未存在odps表或生成中
      if (messages) {
        return (
          <span style={{ color: 'red' }}>{messages}</span>
        )
      }

      // 初始值
      if (JSON.stringify(info) === '{}') {
        return '暂无'
      }

      // 查询已存在odps表
      if (info && info.status && info.idMappingTableName) {
        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <a
              href={`https://dmc.dw.alibaba-inc.com/dm/table/odps.${info.idMappingTableName}/detail/col`}
              target="_blank"
            >
              {info.idMappingTableName}
            </a>
            <Tag
              style={{marginLeft: '20px'}}
              color={OFFLINE_ODPS_STATUS[info.status] && OFFLINE_ODPS_STATUS[info.status].status}
            >
              {OFFLINE_ODPS_STATUS[info.status] && OFFLINE_ODPS_STATUS[info.status].text}
            </Tag>
          </div>
        );
      }
      
    }
  };

  return (
    <Card bordered={false}>
      <div className={styles.toolRadio}>
        <h3>目标类型</h3>
        <Radio.Group onChange={onTypeChange} value={type}>
          <Radio value={1}>单个查询</Radio>
          <Radio value={2}>生成Oneid离线表</Radio>
        </Radio.Group>
      </div>
      <div className={styles.toolContent}>
        <div className={styles.toolContentLeft}>
          {type === 1 ? (
            <h3>
              输入淘宝ID
            </h3>
          ) : (
            <h3>输入人群包ID</h3>
          )}
          <Input style={{ width: '400px' }} onChange={onTaobaoIdChange} value={inputVal} />
        </div>
        <div className={styles.toolContentCentre}>
          {type === 1 ? <SwapOutlined /> : <ArrowRightOutlined />}
        </div>
        <div className={styles.toolContentRight}>
          <h3>{type === 1 ? '输出Scrm-OneID' : '输出离线表'}</h3>
          {outRender()}
        </div>
      </div>
      <Button
        type="primary"
        shape="round"
        style={{ width: '100px' }}
        onClick={onQuery}
        loading={queryBtnLoading}
      >
        查询
      </Button>
      {type === 2 && (
        <Button
          type="primary"
          shape="round"
          style={{ minWidth: '100px', marginLeft: '20px' }}
          onClick={onCreate}
          loading={createBtnLoading}
        >
          生成离线表
        </Button>
      )}
    </Card>
  );
};

export default connect(({ user }) => ({
  currentUser: user.currentUser,
}))(ScrmOneid);
