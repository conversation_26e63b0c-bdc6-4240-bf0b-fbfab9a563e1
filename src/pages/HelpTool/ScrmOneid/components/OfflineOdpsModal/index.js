import React, { useState } from 'react';
import { Modal, Tag } from 'antd';
import { OFFLINE_ODPS_STATUS } from '@/constants'
import { get, cloneDeep } from 'lodash'


const OfflineOdpsModal = (props) => {
  const { odpsModalVisible, onOfflineMapping, close, info } = props;
  const [btnLoading, setBtnLoading] = useState(false)

  const handleOk = async () => {
    setBtnLoading(true)
    await onOfflineMapping()
    setBtnLoading(false)
    close()
  };

  const handleCancel = () => {
    close()
  }

  return (
    <Modal
      title="查看odps表"
      visible={odpsModalVisible}
      destroyOnClose={true}
      width={700}
      confirmLoading={btnLoading}
      onOk={handleOk}
      okText='重新生成'
      onCancel={handleCancel}
    >
      {
        info && info.status && info.idMappingTableName && <div style={{ display: 'flex', alignItems: 'center' }}>
          <a
            href={`https://dmc.dw.alibaba-inc.com/dm/table/odps.${idMappingTableName}/detail/col`}
            target="_blank"
          >{info.idMappingTableName}</a>
          <Tag
            color={
              OFFLINE_ODPS_STATUS[info.status] &&
              OFFLINE_ODPS_STATUS[info.status].status
            }
          >
            {OFFLINE_ODPS_STATUS[info.status] &&
              OFFLINE_ODPS_STATUS[info.status].text}
          </Tag></div>
      }
    </Modal>
  );
};

export default OfflineOdpsModal;
