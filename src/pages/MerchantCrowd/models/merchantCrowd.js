import {
  queryMerchantCrowd,
  queryTagCategories,
  queryMerchantByMerchantId,
  batchAddCrowdByMerchant,
  queryTagAttrs,
  updateMerchantCrowd,
  removeMerchantCrowd,
} from '@/services/strategy';
import { queryCrowdById, delayCrowd } from '@/services/api';
import pathToRegexp from 'path-to-regexp';
import { message } from 'antd';

export default {
  namespace: 'merchantCrowd',
  state: {
    loading: false,
    pagination: {
      pageSize: 10,
      current: 1,
    },
    merchantCrowdList: [],
    merchant: {},
    multMerchatsGpVisible: false,
    tagCategories: [],
    editFormData: {},
    detailModalVisible: false,
    refreshLoading: false,
    editCrowdDelayFormData: {},
    crowdDelayModalVisible: false,
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(({ pathname }) => {
        const match = pathToRegexp('/crowd-stategy/merchant-gather-person/merchant-crowd/:id').exec(pathname);
        if (pathname.includes('/crowd-stategy/merchant-gather-person/merchant-crowd')) {
          const sellerId = match[1] && parseInt(match[1], 10);
          dispatch({
            type: 'query',
            payload: {
              sellerId,
            },
          });

          dispatch({
            type: 'updateState',
            payload: {
              sellerId,
            },
          });
        }
      });
    },
  },
  effects: {
    *query({ payload }, { put, all }) {
      yield all([
        put.resolve({
          type: 'queryMerchantCrowd',
          payload,
        }),
        put.resolve({
          type: 'queryMerchantByMerchantId',
          payload,
        }),
        put.resolve({
          type: 'queryTagCategories',
        }),
      ]);
    },
    *queryMerchantCrowd({ payload = {} }, { put, call, select }) {
      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const { pagination, sellerId } = yield select(state => state.merchantCrowd);
      const res = yield call(queryMerchantCrowd, {
        pageNum: pagination.current || 1,
        pageSize: pagination.pageSize || 10,
        sellerId,
        ...payload,
      });

      if (!res.success) {
        message.error(res.msg);
        return;
      }
      yield put({
        type: 'updateState',
        payload: {
          loading: false,
          pagination: {
            curent: payload.pageNo || 1,
            pageSize: payload.pageSize || 10,
            total: res.total,
          },
          merchantCrowdList: res.data,
        },
      });
    },
    *queryMerchantByMerchantId({ payload }, { put, call }) {
      const res = yield call(queryMerchantByMerchantId, payload);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          merchant: res.data,
        },
      });
    },
    *queryCrowdById(_, { call, put, select }) {
      const { curCrowd } = yield select(state => state.merchantCrowd);

      if (!curCrowd || !curCrowd.id) {
        message.error('不存在该人群');
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          refreshLoading: true,
        },
      });

      const res = yield call(queryCrowdById, {
        id: curCrowd.id,
      });

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success('刷新成功');

      yield put({
        type: 'updateState',
        payload: {
          curCrowd: { ...curCrowd, ...(res.data || {}) },
          refreshLoading: false,
        },
      });
    },
    *queryTagCategories(_, { call, put }) {
      const res = yield call(queryTagCategories);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      const categories = res.data || {};
      const promises = Object.keys(categories).map(key => queryTagAttrs({ key: categories[key] }));

      const attrs = yield Promise.all(promises);

      // 处理成可用的数据格式
      const tagCategories = Object.keys(categories).map((key, index) => {
        const attrRes = attrs[index];
        if (!res.success) {
          message.error(res.msg);
          return {
            name: key,
            value: categories[key],
            attrs: null,
          };
        }

        const { data } = attrRes;

        const attrOptions = Object.keys(data).map(k => ({
          name: data[k],
          value: k,
        }));

        return {
          name: key,
          value: categories[key],
          attrOptions,
        };
      });

      yield put({
        type: 'updateState',
        payload: {
          tagCategories,
        },
      });
    },
    *batchAddCrowdByMerchant({ payload }, { call, put }) {
      let data = payload;
      const [item] = data.sellerCrowdEditVOS;

      const isUpdate = !!item.id;

      if (isUpdate) {
        data = item;
      }

      const res = yield call(isUpdate ? updateMerchantCrowd : batchAddCrowdByMerchant, data);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success(isUpdate ? '更新成功' : '创建成功');

      yield put({
        type: 'onCloseMultMerchatsGpDrawer',
      });

      yield put({
        type: 'queryMerchantCrowd',
      });
    },
    *onOpenDetailModal({ payload }, { put }) {
      const { curCrowd } = payload;

      yield put({
        type: 'updateState',
        payload: {
          curCrowd,
          detailModalVisible: true,
        },
      });
    },
    *editCrowdDelayTime({ payload }, { call, put }) {
      const data = payload;

      const res = yield call(delayCrowd, data);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success('延期成功');

      yield put({
        type: 'queryMerchantCrowd',
      });

      yield put({
        type: 'onCrowdDelayModalCancel',
      });
    },
    *delete({ payload }, { call, put }) {
      const res = yield call(removeMerchantCrowd, payload);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success('删除成功');

      yield put({
        type: 'queryMerchantCrowd',
      });
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    onOpenMultMerchatsGpDrawer(state) {
      return {
        ...state,
        multMerchatsGpVisible: true,
      };
    },
    onCloseMultMerchatsGpDrawer(state) {
      return {
        ...state,
        multMerchatsGpVisible: false,
        editFormData: {},
      };
    },
    onOpenCrowdDelayModal(state) {
      return {
        ...state,
        crowdDelayModalVisible: true,
      };
    },
    onCrowdDelayModalCancel(state) {
      return {
        ...state,
        crowdDelayModalVisible: false,
      };
    },
  },
};
