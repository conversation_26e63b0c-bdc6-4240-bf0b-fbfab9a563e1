import React, { PureComponent } from 'react';
import { connect } from 'dva';
import dayjs from 'dayjs';
import { Table, Divider, Badge, Tag, Popconfirm, Card, Button, Popover, Row, Pagination, } from 'antd';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { CROWD_TYPE, CROWD_PROGRESS_STATUS } from '@/constants';
import MultmerchantGpDrawer from '../MerchantGp/components/MultMerchantsGpDrawer';
import CrowdDetailModal from '../GatherPerson/components/CrowdDetailModal';
import CrowdDelayModal from '../GatherPerson/components/CrowdDelayModal';

import styles from './index.less';

@connect(state => ({ merchantCrowd: state.merchantCrowd, user: state.user }))
class MerchantCrowd extends PureComponent {
  batchAddCrowdByMerchant = data => {
    const {
      dispatch,
      merchantCrowd: { pagination: pager }
    } = this.props;
    dispatch({
      type: 'merchantCrowd/updateState',
      payload: {
        pagination: {
          ...pager,
          current: 1,
          pageSize: 10,
        },
      }
    });
    dispatch({
      type: 'merchantCrowd/batchAddCrowdByMerchant',
      payload: data,
    });
  };

  addTag = (name, value, properties, isArrayStr = false) => {
    if (!value) return;
    const {
      merchantCrowd: { tagCategories },
    } = this.props;
    const tag = tagCategories.find(item => item.name === name);
    if (isArrayStr) {
      properties.push({ name: tag.value, value: value.split(',') });
    } else {
      properties.push({ name: tag.value, value: String(value) });
    }
  };

  openDetailModal = record => {
    const { dispatch } = this.props;

    dispatch({
      type: 'merchantCrowd/onOpenDetailModal',
      payload: { curCrowd: record.crowdVO },
    });
  };

  onCrowdDetailModalCancel = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'merchantCrowd/updateState',
      payload: {
        detailModalVisible: false,
      },
    });
  };

  refresh = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'merchantCrowd/queryCrowdById',
    });
  };

  onDelete = id => {
    if (!id) return;
    const { dispatch } = this.props;

    dispatch({
      type: 'merchantCrowd/delete',
      payload: {
        id,
      },
    });
  };

  onEdit = record => {
    const { dispatch } = this.props;

    const { crowdVO, lifeCycleTag, potentialGuestTag, resTagSet, id, sellerId } = record;
    const {
      conditions,
      id: crowdId,
      crowdName,
      crowdDescription,
      operator,
      expiredDate,
      extInfo,
      crowdTags,
      crowdApplyScene,
      needUpdate,
    } = crowdVO;
    const { group } = conditions;
    const [oneGroup] = group;
    const { label } = oneGroup;
    const [selectedTag] = label;
    const { name, dimEnumMetaId, desc } = selectedTag;
    const properties = [];

    this.addTag('生命周期', lifeCycleTag, properties);
    this.addTag('潜客属性', potentialGuestTag, properties);
    // this.addTag('对应资源位', resourceTag, properties);
    this.addTag('对应资源位', resTagSet, properties, true);

    dispatch({
      type: 'merchantCrowd/updateState',
      payload: {
        editFormData: {
          id,
          crowdId,
          sellerId,
          selectedTags: [
            {
              propertyName: name,
              dimEnumMetaId,
              propertyDescription: desc,
              properties,
            },
          ],
          crowdName,
          crowdDescription,
          operator,
          crowdApplyScene,
          needUpdate,
          extInfo,
          // dependenceBizOption,
          // updateType,
          isLoss: (crowdTags || []).includes('LOSS_OF_ASSETS"') ? 1 : 0,
          expiredDate: dayjs(expiredDate),
        },
        multMerchatsGpVisible: true,
      },
    });
  };

  openCrowdDelayModal = record => {
    const { dispatch } = this.props;

    dispatch({
      type: 'merchantCrowd/updateState',
      payload: {
        editCrowdDelayFormData: record,
        crowdDelayModalVisible: true,
      },
    });
  };

  onCrowdDelayModalCancel = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'merchantCrowd/onCrowdDelayModalCancel',
    });
  };

  // 提交过期时间
  onSubmitDelayTime = values => {
    const { dispatch } = this.props;

    dispatch({
      type: 'merchantCrowd/editCrowdDelayTime',
      payload: values,
    });
  };

  handleTableChange = (current, pageSize) => {
    const {
      dispatch,
      merchantCrowd: { pagination: pager },
    } = this.props;

    dispatch({
      type: 'merchantCrowd/updateState',
      payload: {
        pagination: {
          ...pager,
          current,
          pageSize,
        },
      },
    });

    dispatch({
      type: 'merchantCrowd/queryMerchantCrowd',
      payload: {
        pageNo: current,
      },
    });
  };

  render() {
    const {
      merchantCrowd: {
        merchantCrowdList,
        pagination,
        multMerchatsGpVisible,
        tagCategories,
        merchant,
        editFormData,
        loading,
        curCrowd,
        detailModalVisible,
        refreshLoading,
        crowdDelayModalVisible,
        editCrowdDelayFormData,
      },
      user: { currentUser },
      dispatch,
    } = this.props;

    const columns = [
      {
        title: '人群ID',
        dataIndex: 'crowdVO',
        width: 100,
        align: 'center',
        render: crowdVO => crowdVO.id,
      },
      {
        title: '人群名称',
        dataIndex: 'crowdVO',
        width: 150,
        className: styles.crowdName,
        render: (crowdVO, record) => {
          const crowdName = crowdVO.crowdName || '';
          return (
            <Popover content={crowdName} placement="topLeft">
              <a onClick={() => this.openDetailModal(record)}>{crowdName}</a>
            </Popover>
          );
        },
      },
      {
        title: '类型',
        dataIndex: 'crowdVO',
        width: 120,
        render: crowdVO => crowdVO && CROWD_TYPE[crowdVO.crowdType],
      },
      {
        title: '生命周期',
        dataIndex: 'lifeCycleTagName',
        width: 180,
        render: text => text || '暂无',
      },
      {
        title: '对应资源位',
        dataIndex: 'resourceTagName',
        width: 220,
        render: text => text || '暂无',
      },
      {
        title: '潜客属性',
        dataIndex: 'potentialGuestTagName',
        width: 180,
        render: text => text || '暂无',
      },
      {
        title: '数量',
        dataIndex: 'crowdVO',
        width: 100,
        render: crowdVO =>
          crowdVO &&
          (crowdVO.crowdAmount === -1 || !crowdVO.crowdAmount ? '暂无' : crowdVO.crowdAmount),
      },
      {
        title: '状态',
        dataIndex: 'crowdVO',
        width: 130,
        render: crowdVO => {
          const obj = crowdVO && CROWD_PROGRESS_STATUS[crowdVO.crowdStatus];
          return (
            crowdVO && crowdVO.crowdStatus && obj && <Badge status={obj.status} text={obj.text} />
          );
        },
      },
      {
        title: '上次构建时间',
        dataIndex: 'crowdVO',
        width: 150,
        render: crowdVO =>
          crowdVO &&
          (crowdVO.gmtLastBuilt
            ? dayjs(crowdVO.gmtLastBuilt).format('YYYY-MM-DD HH:mm:ss')
            : '暂无'),
      },
      {
        title: '过期时间',
        dataIndex: 'crowdVO',
        width: 180,
        render: crowdVO => {
          const isExpired = Date.now() > crowdVO.expiredDate;
          return isExpired ? (
            <Badge status="error" text="已过期" />
          ) : (
            dayjs(crowdVO.expiredDate).format('YYYY-MM-DD HH:mm:ss')
          );
        },
      },
      {
        title: '创建人',
        dataIndex: 'crowdVO',
        width: 100,
        render: crowdVO =>
          crowdVO &&
          crowdVO.creator && (
            <span>
              <Tag color="green">{crowdVO.creator.nickName}</Tag>
            </span>
          ),
      },
      {
        title: '操作',
        width: 200,
        render: (text, record) => {
          const { creator = {}, operator = [] } = record.crowdVO;
          const owners = [...operator, creator];
          const isGranted = owners.map(o => o.nickName).includes(currentUser.name);

          return (
            <span>
              <a
                disabled={!isGranted}
                type="link"
                onClick={() => this.openCrowdDelayModal(record.crowdVO)}
              >
                延期
              </a>
              <Divider type="vertical" />
              <a type="link" onClick={() => this.onEdit(record)}>
                编辑
              </a>
              <Divider type="vertical" />
              <Popconfirm
                disabled={!isGranted}
                title="确定删除该人群吗？"
                onConfirm={() => this.onDelete(record.id)}
                okText="确定"
                cancelText="取消"
              >
                <a disabled={!isGranted}>删除</a>
              </Popconfirm>
            </span>
          );
        },
      },
    ];

    return (
      <PageHeaderWrapper
        inner
        title={merchant.sellerName}
        breadcrumbNameMap={{
          '/': { path: '/', redirect: '/develop-tool/entity' },
          '/crowd-stategy': {
            name: '商家圈人',
            component: './MerchantGp',
            path: '/crowd-stategy/merchant-gather-person',
          },
          '/crowd-stategy/merchant-gather-person/merchant-crowd/:id': {
            path: '/crowd-stategy/merchant-gather-person/merchant-crowd/:id',
            name: merchant.sellerName,
            component: './MerchantCrowd',
          },
        }}
      >
        <Card>
          <Button
            type="primary"
            style={{ marginBottom: 10 }}
            onClick={() => {
              dispatch({
                type: 'merchantCrowd/onOpenMultMerchatsGpDrawer',
              });
            }}
          >
            标签圈人
          </Button>
          <Table
            scroll={{ x: 1500 }}
            loading={loading}
            rowKey={record => record.id}
            columns={columns}
            dataSource={merchantCrowdList}
            pagination={false}
          />
          <Row gutter={24} justify="end" align="right" style={{ marginTop: 24 }}>
            <Pagination
              {...pagination}
              onChange={this.handleTableChange} />
          </Row>
        </Card>

        <MultmerchantGpDrawer
          editFormData={editFormData}
          visible={multMerchatsGpVisible}
          selectedMerchants={[merchant]}
          tagCategories={tagCategories}
          currentUser={currentUser}
          onSubmit={this.batchAddCrowdByMerchant}
          onClose={() => {
            dispatch({
              type: 'merchantCrowd/onCloseMultMerchatsGpDrawer',
            });
          }}
        />

        {curCrowd && (
          <CrowdDetailModal
            curCrowd={curCrowd}
            editModalVisible={detailModalVisible}
            onCancel={this.onCrowdDetailModalCancel}
            refresh={this.refresh}
            loading={refreshLoading}
          />
        )}

        <CrowdDelayModal
          editFormData={editCrowdDelayFormData}
          editModalVisible={crowdDelayModalVisible}
          onCancel={this.onCrowdDelayModalCancel}
          onSubmit={this.onSubmitDelayTime}
        />
      </PageHeaderWrapper>
    );
  }
}

export default MerchantCrowd;
