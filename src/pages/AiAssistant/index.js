import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { useEffect } from 'react';
import styles from './index.less'

const AiAssistant = () => {
  const isTouchPlan = window.location.hash.includes('ai-assistant');

  //用户触达页面添加机器人
  useEffect(() => {
    if (isTouchPlan) {
      const aiStudioChatConfig = {
        rootElementID: 'ai-assistant',
        appCode: 'eOQCbasrDgV', // 发布的appCode
        title: '诸葛AI智能助手', // 弹窗标题
        showType: 'page',
        icon:
          'https://idealab-platform.oss-accelerate.aliyuncs.com/20250417/4ca7b8c0-ce47-4d2c-8128-0603ba21f87f_zgl-pic.png?Expires=4102329600&amp;OSSAccessKeyId=LTAI5tFJF3QLwHzEmkhLs9dB&amp;Signature=7VSJxsg89%2F2o7eKlcYsJekfDo4I%3D',
        robotConfig: {
          display:"none"
        }
      };
      window.AiStudioChat?.init(aiStudioChatConfig);
    }
  }, [isTouchPlan]);

  return <div id="ai-assistant" className={styles.aiAssistant} />
};

export default AiAssistant;
