import React, { useEffect, useState } from 'react';
import { Table, message, Button } from 'antd';
import { get } from 'lodash';
import styles from './index.less';
import dayjs from 'dayjs';
import { labelMarketDetailEnumStart } from '@/services/api';
import { zhugeUrl } from '@/utils/utils';
import EnumMonitorModal from '../EnumMonitorModal';

const ValueDistrib = props => {
  const [dimEnumId, setDimEnumId] = useState('');
  const [updateTime, setUpdateTime] = useState('');
  const [dataSource, setDataSource] = useState([]);
  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [monitorVisible, setMonitorVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState(null);
  const { labelCode, activeKey, disabled } = props;

  useEffect(() => {
    if (!disabled && activeKey === '2') {
      queryStatisticsList({
        pageNo: 1,
        pageSize: 10,
      });
    }
  }, [activeKey]);

  const columns = [
    {
      title: '序号',
      dataIndex: 'id',
    },
    {
      title: '枚举值名称',
      dataIndex: 'enumName',
      width: 200,
    },
    {
      title: '枚举值id',
      dataIndex: 'enumId',
    },
    {
      title: '枚举值code',
      dataIndex: 'enumCode',
    },
    {
      title: '人群数量',
      dataIndex: 'cnt',
    },
    {
      title: '占比',
      dataIndex: 'rate',
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Button
          type="link"
          onClick={() => {
            setCurrentRecord(record);
            setMonitorVisible(true);
          }}
        >
          枚举值波动监控
        </Button>
      ),
    },
  ];

  const queryStatisticsList = async params => {
    setLoading(true);
    const res = await labelMarketDetailEnumStart({ ...params, labelCode });
    setLoading(false);
    if (!res?.success) {
      message.error(res?.msg);
      return;
    }

    let data = get(res, 'data.dataList', []);
    const pageSize = get(res, 'data.pageSize', 10);
    const pageNo = get(res, 'data.pageNo', 1);
    const modifiedTime = get(res, 'data.modifiedTime', undefined);
    const startNumber = (pageNo - 1) * pageSize + 1;
    data = data?.map((ele, index) => {
      return {
        ...ele,
        id: startNumber + index,
      };
    });

    setPageNo(pageNo);
    setPageSize(pageSize);
    setDataSource(data);
    setTotal(get(res, 'data.total', 0));
    setDimEnumId(get(data, '[0].dimEnumId', ''));
    setUpdateTime(modifiedTime ? dayjs(modifiedTime).format('YYYY-MM-DD HH:mm:ss') : '暂无');
  };

  return (
    <div className={styles.valueDistrib}>
      <div className={styles.top}>
        {dimEnumId > 0 ? (
          <Button
            type="link"
            size="small"
            onClick={() => {
              window.open(`${zhugeUrl}/knowledge/enumValueDetail?id=${dimEnumId}`);
            }}
          >
            查看枚举值详情
          </Button>
        ) : (
          <div />
        )}

        <div>更新时间：{updateTime}</div>
      </div>
      <Table
        loading={loading}
        scroll={{ x: 'max-content' }}
        columns={columns}
        dataSource={dataSource}
        pagination={{
          total,
          pageSize,
          current: pageNo,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: total => `总计 ${total} 行数据`,
          onChange: (page, pageSize) => {
            queryStatisticsList({
              pageNo: page,
              pageSize,
            });
          },
        }}
      />
      <EnumMonitorModal
        visible={monitorVisible}
        onCancel={() => setMonitorVisible(false)}
        labelCode={labelCode}
        dimEnumId={currentRecord?.dimEnumId}
        enumCode={currentRecord?.enumCode}
      />
    </div>
  );
};

export default ValueDistrib;
