import React, { useEffect, useState } from 'react';
import { Modal, message } from 'antd';
import { Line } from '@ant-design/plots';
import { labelMarketDetailEnumMonitor } from '@/services/api';

const EnumMonitorModal = ({ visible, onCancel, labelCode, dimEnumId, enumCode }) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);

  useEffect(() => {
    if (visible && labelCode && dimEnumId && enumCode) {
      fetchMonitorData();
    }
  }, [visible, labelCode, dimEnumId, enumCode]);

  const fetchMonitorData = async () => {
    setLoading(true);
    try {
      const res = await labelMarketDetailEnumMonitor({
        labelCode,
        dimEnumId,
        enumCode,
      });
      if (res?.success) {
        setData(res.data || []);
      } else {
        message.error(res?.message || '获取监控数据失败');
      }
    } catch (error) {
      message.error('获取监控数据失败');
    } finally {
      setLoading(false);
    }
  };

  const config = {
    data,
    xField: 'bizdate',
    yField: 'originalCnt',
    point: false,
    tooltip: {
      fields: ['bizdate', 'originalCnt'],
      formatter: (datum) => {
        return {
          name: '数量',
          value: datum.originalCnt,
        };
      },
    },
    loading,
  };

  return (
    <Modal
      title="枚举值波动监控"
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={null}
    >
      <Line {...config} />
    </Modal>
  );
};

export default EnumMonitorModal; 