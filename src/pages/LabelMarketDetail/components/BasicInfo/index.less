.basicInfo {
  padding: 6px 10px;

  :global {
    .ant-descriptions {
      border-bottom: 1px solid #f0f0f0;
      margin-bottom: 18px;

      .ant-descriptions-view {
        padding-left: 10px;

        .ant-descriptions-row {
          .ant-descriptions-item:nth-child(1) {
            width: 30%;
          }
        }
      }

      &:last-child {
        border-bottom: none
      }
    }
  }
}

.sourceConfig {
  display: flex;
  
  :global {
    .ant-descriptions-item-content {
      flex: 1;
      display: contents;
    }
  }
}

.ellipsis {
  overflow: hidden; //超出的文本隐藏
  white-space: nowrap;  // 默认不换行；
  text-overflow: ellipsis; /* 添加省略号 */
}

