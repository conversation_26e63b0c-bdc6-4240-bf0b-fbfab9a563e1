import React, { useEffect, useState } from 'react';
import { Descriptions, Tag, Spin, Tooltip, Popover, message, Button } from 'antd';
import { QuestionCircleFilled } from '@ant-design/icons';
import dayjs from 'dayjs';
import styles from './index.less';

import { SAFE_TIP } from '@/pages/Knowledge/CreateCrowdTag';
import { labelMarketDetailInfo } from '@/services/api';
import { zhugeUrl } from '@/utils/utils';
import {
  getKeyAndValue,
  getDefinition,
  getCommonEmptyContent,
} from '@/pages/LabelMarket/common/utils';
import { DEFINITION, UPDATEPERID } from '@/pages/LabelMarket/common/constants';

const BasicInfo = props => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState({});
  const {
    id,
    category,
    accountType,
    timeType,
    securityLevel,
    dataType,
    field,
    updatePeriod,
    sourceConfig,
    source,
    gmtCreate,
    dataUpdateTime,
    physicalProfileCode,
    commonEmptyContent,
    dataSourceConfig,
    scopeList = [],
    status = {}
  } = data;
  const { labelCode, activeKey } = props;
  const definition = getDefinition(physicalProfileCode);

  useEffect(() => {
    if (activeKey === '1') {
      queryBasicInfo();
    }
  }, [activeKey]);

  const queryBasicInfo = async () => {
    setLoading(true);
    const res = await labelMarketDetailInfo({ labelCode, isCore: false });
    setLoading(false);
    if (!res?.success) {
      message.error(res?.msg);
      return;
    }

    setData(res?.data || {});
  };

  return (
    <div className={styles.basicInfo}>
      <Spin spinning={loading}>
        <Descriptions title="关键信息" column={4}>
          <Descriptions.Item label="标签类目">{category}</Descriptions.Item>
          <Descriptions.Item label="用户主键">{accountType}</Descriptions.Item>
          <Descriptions.Item label="标签时效">{getKeyAndValue(timeType)?.label}</Descriptions.Item>
          <Descriptions.Item label="安全等级">
            {securityLevel ? (
              <div>
                {`L${securityLevel}`}
                <Popover content={SAFE_TIP}>
                  <QuestionCircleFilled style={{ marginLeft: 4, color: '#d9d9d9' }} />
                </Popover>
              </div>
            ) : null}
          </Descriptions.Item>
        </Descriptions>
        <Descriptions title="标签信息" column={4}>
          <Descriptions.Item label="标签支持项">
            {scopeList?.map(ele => (
              <Tag color="#fae7d3" key={ele} style={{ color: '#e44b4b' }}>
                {ele}
              </Tag>
            ))}
          </Descriptions.Item>
          {getKeyAndValue(timeType)?.value !== 'REALTIME' && (
            <Descriptions.Item label="标签定义方式">
              {DEFINITION[definition] || ''}
            </Descriptions.Item>
          )}
          <Descriptions.Item label="标签值类型">
            {definition === 1 ? (
              <Button
                type="link"
                size="small"
                onClick={() => {
                  window.open(`${zhugeUrl}/develop-tool/create-crowd-tag/TAOBAO_USER?id=${id}`);
                }}
              >
                点我查看
              </Button>
            ) : (
              getKeyAndValue(dataType)?.label
            )}
          </Descriptions.Item>
          <Descriptions.Item label="标签字段">
            {definition === 1 ? (
              <Button
                type="link"
                size="small"
                onClick={() => {
                  window.open(`${zhugeUrl}/develop-tool/create-crowd-tag/TAOBAO_USER?id=${id}`);
                }}
              >
                点我查看
              </Button>
            ) : (
              field
            )}
          </Descriptions.Item>
        </Descriptions>
        <Descriptions title="数据源信息" column={4}>
          <Descriptions.Item label="标签来源">
            {getKeyAndValue(dataSourceConfig)?.label}
          </Descriptions.Item>
          <Descriptions.Item label="更新周期">
            {getKeyAndValue(timeType)?.value === 'REALTIME'
              ? '实时更新'
              : UPDATEPERID[updatePeriod]}
          </Descriptions.Item>
          <Descriptions.Item label="接入来源">{getKeyAndValue(source)?.label}</Descriptions.Item>
          <Descriptions.Item label="数据源" className={styles.sourceConfig}>
            <Tooltip title={sourceConfig}>
              {getKeyAndValue(source)?.value === 'ODPS' ? (
                <a
                  href={`https://dw.alibaba-inc.com/dmc/odps-table/odps.${sourceConfig}`}
                  target="_blank"
                  className={styles.ellipsis}
                >
                  {sourceConfig}
                </a>
              ) : (
                <div className={styles.ellipsis}>{sourceConfig}</div>
              )}
            </Tooltip>
          </Descriptions.Item>
        </Descriptions>
        <Descriptions title="标签更新信息" column={4}>
          <Descriptions.Item label="标签状态">
            {Object.values(status)[0] || ''}
          </Descriptions.Item>
          <Descriptions.Item label="最近更新时间">
            {getCommonEmptyContent(
              data,
              dataUpdateTime ? dayjs(dataUpdateTime).format('YYYY-MM-DD HH:mm:ss') : ''
            )}
          </Descriptions.Item>
          <Descriptions.Item label="上架时间">
            {gmtCreate && dayjs(gmtCreate).format('YYYY-MM-DD HH:mm:ss')}
          </Descriptions.Item>
        </Descriptions>
      </Spin>
    </div>
  );
};

export default BasicInfo;
