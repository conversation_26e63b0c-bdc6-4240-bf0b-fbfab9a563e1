import React, { useEffect, useState } from 'react';
import { Line } from '@ant-design/plots';
import dayjs from 'dayjs';
import styles from './index.less';
import { labelMarketDetailOutputMonitor } from '@/services/api';
import { Spin, message } from 'antd';
import duration from 'dayjs/plugin/duration';

dayjs.extend(duration);

// 转换时间字符串到小时数
const parseTimeToHours = time => {
  const [hours, minutes, seconds] = time?.split(':').map(Number);
  return hours * 3600 + minutes * 60 + seconds;
};

const formatNumber = num => {
  return num?.toString()?.replace(/\B(?=(\d{3})+(?!\d))/g, ",") || 0;
}


const OutputMonitor = props => {
  const [coverList, setCoverList] = useState([]); // 覆盖量统计信息
  const [timeList, setTimeList] = useState([]); // 产出时间统计信息
  const [averageTime, setAverageTime] = useState(''); // 平均产出时间
  const [loading, setLoading] = useState(false);
  const { labelCode, activeKey, disabled } = props;

  useEffect(() => {
    if (!disabled && activeKey === '3') {
      queryOutputMonitor();
    }
  }, [activeKey]);

  const queryOutputMonitor = async () => {
    let coverList = [];
    let timeList = [];
    setLoading(true)
    const res = await labelMarketDetailOutputMonitor({ labelCode })
    setLoading(false)
    if (!res?.success) {
      message.error(res?.msg)
      return
    }

    const { statList, avgOutputTime } = res?.data || {}

    setAverageTime(avgOutputTime)
    const data = statList || []

    data?.forEach(ele => {
      const { coverAmount, outputDate, outputTime } = ele;
      coverList.push({
        coverAmount,
        outputDate,
      });
      timeList.push({
        outputTime,
        outputDate,
      });
    });

    setCoverList(coverList);
    transformedData(timeList);
  };

  const transformedData = data => {
    if (!data?.length) return;
    let newData = [];

    newData = data.filter(ele => ele?.outputTime)?.map(item => ({
      date: item?.outputDate,
      value: item?.outputTime,
      time: parseTimeToHours(item?.outputTime)
    }))

    setTimeList(newData);
  };

  const coverConfig = {
    data: coverList,
    padding: 'auto',
    xField: 'outputDate',
    yField: 'coverAmount',
    color: '#42c566',
    yAxis: {
      label: {
        formatter: v => formatNumber(v)
      },
    },
    tooltip: {
      formatter: datum => {
        return { name: '覆盖量', value: formatNumber(datum?.coverAmount)};
      },
    },
  };

  const timeConfig = {
    data: timeList,
    padding: 'auto',
    xField: 'date',
    yField: 'time',
    color: '#42c566',
    yAxis: {
      label: {
        formatter: v => {
          const duration = dayjs.duration(v, 'seconds');
          const hours = duration.hours();
          const minutes = duration.minutes();
          const seconds = duration.seconds();
          return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        },
      },
    },
    tooltip: {
      formatter: datum => {
        const temp = timeList?.find(ele => ele?.date === datum?.date);
        return { name: '产出时间', value: temp?.value };
      },
    },
  };

  return (
    <div className={styles.monitor}>
      <div className={styles.cover}>
        <div className={styles.title}>标签覆盖量监控</div>
        <div className={styles.lineBox}>
          <Spin spinning={loading}>
            <Line {...coverConfig} />
          </Spin>
        </div>
      </div>
      <div className={styles.time}>
        <div className={styles.title}>标签产出时间监控</div>
        <div className={styles.lineBox}>
          <div className={styles.averageTime}>近30天平均产出时间：{averageTime}</div>
          <Spin spinning={loading}>
            <Line {...timeConfig} />
          </Spin>
        </div>
      </div>
    </div>
  );
};

export default OutputMonitor;
