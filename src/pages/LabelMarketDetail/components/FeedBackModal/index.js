import React, { useEffect, useState } from 'react';
import { Modal, Input } from 'antd';
import { userFeedback } from '@/services/api';

const { TextArea } = Input;

const FeedBackModal = props => {
  const [text, setText] = useState('');
  const [confirmLoading, setConfirmLoading]  = useState(false);
  const { isFeedModalOpen, setIsFeedModalOpen, messageRender, currentUser, labelCode } = props;

  const onOk = async () => {
    setConfirmLoading(true)
    const data = {
      labelCode,
      upOrDown: 0,
      otherIdeas: text,
      operator: {
        empId: currentUser.workId,
        nickName: currentUser.name,
      },
    };
    const res = await userFeedback(data);
    setConfirmLoading(false)
    if (!res?.success) {
      message.error(res?.msg || '请求错误');
      return;
    }
    setIsFeedModalOpen(false);
    messageRender('您的认可对我们很重要，我们会继续加油，提供更好的服务!');
  };

  return (
    <Modal
      title="其他反馈"
      okText="提交建议"
      visible={isFeedModalOpen}
      confirmLoading={confirmLoading}
      okButtonProps={{
        disabled: !text
      }}
      onCancel={() => {
        setIsFeedModalOpen(false);
        setConfirmLoading(false)
      }}
      onOk={onOk}
      destroyOnClose
      afterClose={() => {
        setText('');
      }}
    >
      <TextArea
        rows={4}
        value={text}
        maxLength={300}
        onChange={e => {
          setText(e.target.value);
        }}
      />
    </Modal>
  );
};

export default FeedBackModal;
