import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Card, Row, Col, Tag, Descriptions, Tooltip, message, Tabs } from 'antd';
import { QuestionCircleFilled } from '@ant-design/icons';
import classNames from 'classnames';
import { connect } from 'dva';
import styles from './index.less';

import { LABEL_ICON, DEFINITION } from '@/pages/LabelMarket/common/constants';
import MyIcon from '@/components/MyIcon';
import FeedBackModal from './components/FeedBackModal';
import BasicInfo from './components/BasicInfo';
import ValueDistrib from './components/ValueDistrib';
import OutputMonitor from './components/OutputMonitor';
import {
  getUrlParams,
  getDefinition,
  getKeyAndValue,
  getCommonEmptyContent,
  formatNumber,
} from '@/pages/LabelMarket/common/utils';
import { labelMarketDetailInfo, userFeedback, labelMarketDetailPageView } from '@/services/api';
import { zhugeUrl } from '@/utils/utils';

const { TabPane } = Tabs;

const getUniqueNickNames = text => {
  if (!text || text.length === 0) return '';

  const uniqueNickNames = new Set(
    text.map(item => item.nickName).filter(name => name !== null && name !== undefined)
  );

  return Array.from(uniqueNickNames).join(', ');
};

const LabelMarketDetail = props => {
  const [data, setData] = useState({});
  const [activeKey, setActiveKey] = useState('1');
  const [coreInfoLoading, setCoreInfoLoading] = useState(false);
  const [isFeedModalOpen, setIsFeedModalOpen] = useState(false);
  const [satisfiedLoading, setSatisfiedLoading] = useState(false);
  const [disSatisfiedLoading, setDisSatisfiedLoading] = useState(false);
  const [distribDisabled, setDistribDisabled] = useState(false); // 取值分布、监控置灰
  const [distribTitle, setDistribTitle] = useState(''); // 取值分布、监控提示消息
  const [isShowOperate, setIsShowOperate] = useState(true); // 好用等操作展示
  const { currentUser } = props;

  const {
    id,
    name,
    description,
    code,
    coverAmount,
    healthScore,
    pvCnt,
    useAmount,
    type,
    commonEmptyContent,
    upOrDown,
    timeType,
    physicalProfileCode,
    bizOwner = {},
    dataOwner = {},
    tagList = [],
    managers = []
  } = data;
  const { labelCode } = getUrlParams();

  useEffect(() => {
    queryCoreInfo();
    onPageView();
  }, []);

  const onPageView = async () => {
    const res = await labelMarketDetailPageView({ entity: labelCode, entityType: 'LABEL' });

    if (!res?.success) {
      message.error(res?.msg);
    }
  };

  // 查询核心信息
  const queryCoreInfo = async () => {
    setCoreInfoLoading(true);
    const res = await labelMarketDetailInfo({ labelCode, isCore: true });
    setCoreInfoLoading(false);
    if (!res?.success) {
      message.error(res?.msg);
      return;
    }

    const data = res?.data || {};
    setIsShowOperate(data?.upOrDown || data?.upOrDown === 0 ? false : true);
    setDistribTips(data);
    setData(data);
  };

  const setDistribTips = data => {
    const { physicalProfileCode } = data;
    const definition = getDefinition(physicalProfileCode);
    const timeType = getKeyAndValue(data?.timeType)?.value;
    const status = getKeyAndValue(data?.status)?.value;
    let disabled = true;
    let title = '实时标签不展示';

    if (timeType === 'OFFLINE') {
      if (definition === 1) {
        title = 'sql标签不展示';
      } else if (definition === 6 && status !== 'ACTIVATE') {
        title = '仅展示已上线标签';
      }
    }

    if (timeType === 'OFFLINE' && definition === 6 && status === 'ACTIVATE') {
      title = '';
      disabled = false;
    }

    setDistribDisabled(disabled);
    setDistribTitle(title);
  };

  const messageRender = text => {
    message.success({
      content: (
        <div className={styles.messageContent}>
          <div className={styles.messageTitle}>感谢您的评价!</div>
          <div className={styles.messageDesc}>{text}</div>
        </div>
      ),
      duration: 5,
      icon: <div></div>,
    });
  };

  const onSatisfied = async () => {
    const data = {
      labelCode,
      upOrDown: 1,
      operator: {
        empId: currentUser.workId,
        nickName: currentUser.name,
      },
    };
    const res = await userFeedback(data);

    if (!res?.success) {
      message.error(res?.msg || '请求错误');
      return;
    }
    messageRender('您的认可对我们很重要，我们会继续加油，提供更好的服务!');
    setIsShowOperate(false);
  };

  const onDissatisfied = async () => {
    const data = {
      labelCode,
      upOrDown: -1,
      operator: {
        empId: currentUser.workId,
        nickName: currentUser.name,
      },
    };
    const res = await userFeedback(data);

    if (!res?.success) {
      message.error(res?.msg || '请求错误');
      return;
    }

    messageRender('您的反馈对我们很重要，我们会继续加油，提供更好的服务!');
    setIsShowOperate(false);
  };

  const setCoverAmount = () => {
    const result = getCommonEmptyContent(data, coverAmount);

    if (result === '-1') {
      return '产出中';
    }

    return (
      <Tooltip title={formatNumber(result)}>
        <div>{result}</div>
      </Tooltip>
    );
  };

  return (
    <>
      <Card bodyStyle={{ padding: 10 }} className={styles.detailTitle} loading={coreInfoLoading}>
        <Row>
          <Col style={{ width: '43%' }}>
            <div className={styles.info}>
              <div className={classNames(styles.title, !isShowOperate && styles.titleWidth)}>
                <div className={styles.labelIcon}>
                  <MyIcon style={{ fontSize: 22, marginRight: 6 }} type="icon-biaoqian" />
                  {/* <Tag
                    color={LABEL_ICON['private']?.bgColor}
                    style={{ color: LABEL_ICON['private']?.textColor }}
                    className={styles.tag}
                  >
                    {LABEL_ICON['private']?.text}
                  </Tag> */}
                </div>
                <Tooltip title={name}>
                  <div className={classNames(styles.name, styles.ellipsis)}>{name}</div>
                </Tooltip>
                <div className={styles.tags}>
                  {tagList
                    ?.filter(item => item !== 'new' && item !== 'high_quality' && item !== 'output_late')
                    ?.map(ele => (
                      <Tag
                        color={LABEL_ICON[ele]?.bgColor}
                        style={{ color: LABEL_ICON[ele]?.textColor }}
                        className={styles.tag}
                        key={ele}
                      >
                        {LABEL_ICON[ele]?.text}
                      </Tag>
                    ))}
                </div>
              </div>
              {isShowOperate ? (
                <div className={styles.operate}>
                  <Button
                    type="link"
                    size="small"
                    className={styles.btn}
                    onClick={onSatisfied}
                    loading={satisfiedLoading}
                  >
                    <MyIcon style={{ fontSize: 14 }} type="icon-aixin" />
                    <span>好用</span>
                  </Button>
                  <Button
                    type="link"
                    size="small"
                    className={styles.btn}
                    onClick={onDissatisfied}
                    loading={disSatisfiedLoading}
                  >
                    <MyIcon style={{ fontSize: 14 }} type="icon-cuowu" />
                    <span>不满意</span>
                  </Button>
                  <Button
                    type="link"
                    size="small"
                    className={styles.btn}
                    onClick={() => {
                      setIsFeedModalOpen(true);
                    }}
                  >
                    <MyIcon style={{ fontSize: 14, color: '#000000a6' }} type="icon-xiaoxi" />
                    <span>其他反馈</span>
                  </Button>
                </div>
              ) : null}
            </div>
            <div className={styles.desc}>
              <div>标签描述：</div>
              {description}
            </div>
          </Col>
          <Col style={{ width: '57%' }}>
            <Descriptions column={4}>
              <Descriptions.Item label="标签id" style={{ width: '21%' }}>
                {id}
              </Descriptions.Item>
              <Descriptions.Item label="标签code" className={styles.labelCode}>
                <Tooltip title={code}>
                  <div className={styles.ellipsis}>{code}</div>
                </Tooltip>
              </Descriptions.Item>
              <Descriptions.Item label="覆盖量" style={{ width: '23%' }}>
                {setCoverAmount()}
              </Descriptions.Item>
              <Descriptions.Item
                className={styles.labelCode}
                label={
                  <>
                    <Tooltip
                      title={
                        <Button
                          type="link"
                          size="small"
                          onClick={() => {
                            window.open(
                              'https://aliyuque.antfin.com/qnwrq9/kzl68s/colc5usme00ckugw'
                            );
                          }}
                        >
                          点我查看
                        </Button>
                      }
                    >
                      <QuestionCircleFilled style={{ marginRight: 4, color: '#d9d9d9' }} />
                    </Tooltip>
                    <span>标签健康分</span>
                  </>
                }
              >
                <Tooltip title={getCommonEmptyContent(data, healthScore || 0)}>
                  <div className={styles.ellipsis}>
                    {getCommonEmptyContent(data, healthScore || 0)}
                  </div>
                </Tooltip>
              </Descriptions.Item>
              <Descriptions.Item
                label={
                  <>
                    <Tooltip title="当前标签累计被访问的次数">
                      <QuestionCircleFilled style={{ marginRight: 4, color: '#d9d9d9' }} />
                    </Tooltip>
                    <span>访问次数</span>
                  </>
                }
              >
                {pvCnt}
              </Descriptions.Item>
              <Descriptions.Item
                label={
                  <>
                    <Tooltip title="当前标签目前被应用圈人的数量">
                      <QuestionCircleFilled style={{ marginRight: 4, color: '#d9d9d9' }} />
                    </Tooltip>
                    <span>圈人数量</span>
                  </>
                }
              >
                {useAmount}
              </Descriptions.Item>
              <Descriptions.Item label="管理员">
                {
                  getUniqueNickNames(managers)
                }
                {/* {bizOwner?.nickName && (
                  <span>
                    {bizOwner?.nickName}
                    <MyIcon
                      style={{ fontSize: 18, marginLeft: 6, cursor: 'pointer' }}
                      type="icon-dingding"
                      onClick={() => {
                        window.open(
                          `dingtalk://dingtalkclient/action/sendmsg?dingtalk_id=${bizOwner?.dingTalkId}`
                        );
                      }}
                    />
                  </span> */}
                {/* )} */}
              </Descriptions.Item>
              <Descriptions.Item label="负责人">
                {dataOwner?.nickName && (
                  <span>
                    {dataOwner?.nickName}
                    <MyIcon
                      style={{ fontSize: 18, marginLeft: 6, cursor: 'pointer' }}
                      type="icon-dingding"
                      onClick={() => {
                        window.open(
                          `dingtalk://dingtalkclient/action/sendmsg?dingtalk_id=${dataOwner?.dingTalkId}`
                        );
                      }}
                    />
                  </span>
                )}
              </Descriptions.Item>
            </Descriptions>
          </Col>
        </Row>
      </Card>
      <Card bodyStyle={{ padding: '0 10px 10px', position: 'relative' }}>
        <Button
          type="link"
          className={styles.more}
          onClick={() => {
            window.open(`${zhugeUrl}/develop-tool/label-management/detail?id=${id}&type=view`);
          }}
        >
          更多信息 &gt;
        </Button>
        <Tabs
          activeKey={activeKey}
          onChange={key => {
            setActiveKey(key);
          }}
        >
          <TabPane tab="基础信息" key="1">
            <BasicInfo labelCode={labelCode} activeKey={activeKey} />
          </TabPane>
          <TabPane
            tab={<Tooltip title={distribTitle}>取值分布</Tooltip>}
            key="2"
            disabled={distribDisabled}
          >
            <ValueDistrib labelCode={labelCode} activeKey={activeKey} disabled={distribDisabled} />
          </TabPane>
          <TabPane
            tab={<Tooltip title={distribTitle}>产出监控</Tooltip>}
            key="3"
            disabled={distribDisabled}
          >
            <OutputMonitor labelCode={labelCode} activeKey={activeKey} disabled={distribDisabled} />
          </TabPane>
        </Tabs>
      </Card>
      <FeedBackModal
        labelCode={labelCode}
        currentUser={currentUser}
        isFeedModalOpen={isFeedModalOpen}
        setIsFeedModalOpen={setIsFeedModalOpen}
        messageRender={() => {
          messageRender('您的反馈对我们很重要，我们会继续加油，提供更好的服务!');
          setIsShowOperate(false);
        }}
      />
    </>
  );
};

export default connect(({ user }) => ({
  currentUser: user.currentUser,
}))(LabelMarketDetail);
