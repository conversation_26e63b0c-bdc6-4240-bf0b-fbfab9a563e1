.detailTitle {
  margin-bottom: 10px;

  .info {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
  
    .title {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      width: calc(100% - 240px);
  
      .name {
        font-size: 16px;
        margin-right: 6px;
        font-weight: bold;
      }

      .labelIcon {
        display: flex;
        align-items: center;

        :global {
          .ant-tag {
            margin-left: 6px;
          }
        }
      }
    }

    .titleWidth {
      width: auto !important;
    }

    .tags {
      display: flex;
      align-items: center;
    }

    .tag {
      margin-left: 0px !important;
      margin-right: 4px;
      padding: 0px 4px;
      line-height: 1.4;
    }
  
    .operate {
      display: flex;
      align-items: center;
  
      .btn {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #5e5dfd;
  
        > span {
          margin-left: 2px;
        }
      }
    }
  }

  .labelCode {
    display: flex;
    margin-right: 6px;
    width: 400px;

    :global {
      .ant-descriptions-item-content {
        flex: 1;
        display: contents;
      }
    }
  }

  .desc {
    margin: 0 20px 10px 0;
    display: flex;
    
    > div {
      white-space: nowrap;
    }
  }

  :global {
    .ant-col:nth-child(1) {
      border-right: 1px solid #f0f0f0;
    }

    .ant-col:nth-child(2) {
      padding-left: 20px;
    }
  }

}

.more {
  position: absolute;
  top: 6px;
  right: 0px;
  z-index: 4;
}

.messageContent {
  padding: 0px 10px;

  .messageTitle {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 6px;
  }
  
  .messageDesc {
    color: #6d6d6d;
    margin-bottom: 10px;
  }
}

.ellipsis {
  width: 140px;
  overflow: hidden; //超出的文本隐藏
  white-space: nowrap;  // 默认不换行；
  text-overflow: ellipsis; /* 添加省略号 */
}
