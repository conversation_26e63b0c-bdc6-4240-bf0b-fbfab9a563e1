.plateCard {
  .title {
    font-size: 16px;
    font-weight: bold;
    color: #2a2a2a;
  }
  
  .largeMarketTitle {
    text-align: center;
    font-size: 12px;
    color: #a7a7a6;
    font-weight: bold;
  }
  
  .largeMarketNum {
    text-align: center;
    font-weight: bold;
  }

  :global {
    .ant-card-bordered {
      border: none;

      .ant-card-body {
        padding: 0;
      }
    }
  }
}

.plateContent {
  overflow: hidden;
  max-height: 120px;
  transition: all 0.5s ease; /* 动画效果 */

  .labelItem {
    display: block;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  :global {
    .ant-empty {
      margin: 0px;
    }
  }
}

.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .rotate {
    margin-top: -2px !important;
    transform: rotate(-180deg);
  }
}

.divider {
  height: 100% !important;
  margin: 0px 15px 0px 10px !important;
  opacity: 1;
}

.plateClose {
  .plateContent {
    max-height: 0px;
  }
  .divider {
    opacity: 0;
  }
}

