import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Row, Col, Tooltip, Card, Divider, message, Empty } from 'antd';
import { QuestionCircleFilled, DownOutlined } from '@ant-design/icons';
import classNames from 'classnames';
import styles from './index.less';
import { queryLabelMarketStatistics, queryLabelMarketHotLabel, queryLabelMarketNewLabel, isSuperAdmin } from '@/services/api';
import { zhugeUrl } from '@/utils/utils';
import MyIcon from '@/components/MyIcon';
import { setMapToList } from '../../common/utils'

const INIT_MARKET_DATA = {
  labelCntInUse: 0,
  labelCntWaitOffline: 0,
  labelAddIn30Days: 0,
  labelAddIn7Days: 0,
};

const ICON = ['icon-daochu1024-26', 'icon-daochu1024-27', 'icon-daochu1024-28'];

const LabelPlate = () => {
  const [isOpen, setIsOpen] = useState(true);
  const [marketData, setMarketData] = useState(INIT_MARKET_DATA); //数据大盘数据
  const [popLabelList, setPopLabelList] = useState([]);
  const [newAddLabelList, setNewAddLabelList] = useState([]);
  const [marketLoading, setMarketLoading] = useState(false);
  const [popLabelLoading, setPopLabelLoading] = useState(false);
  const [newAddLabelLoading, setNewAddLabelLoading] = useState(false);
  const { labelCntInUse, labelCntWaitOffline, labelAddIn30Days, labelAddIn7Days } = marketData;

  useEffect(() => {
    queryMarketData();
    queryPopLabel();
    queryNewAddLabel()
  }, []);

  const queryMarketData = async () => {
    setMarketLoading(true);
    const res = await queryLabelMarketStatistics()
    setMarketLoading(false);
    if (!res?.success) {
      message.error(res?.msg);
      return;
    }

    setMarketData(res?.data || INIT_MARKET_DATA);
  };

  const queryPopLabel = async () => {
    setPopLabelLoading(true);
    const res = await queryLabelMarketHotLabel()
    setPopLabelLoading(false);

    if (!res?.success) {
      message.error(res?.msg);
      return;
    }

    const data = setMapToList(res?.data || [])
    setPopLabelList(data.slice(0, 8));
  };

  const queryNewAddLabel = async () => {
    setNewAddLabelLoading(true);
    const res = await queryLabelMarketNewLabel()
    setNewAddLabelLoading(false);

    if (!res?.success) {
      message.error(res?.msg);
      return;
    }

    const data = setMapToList(res?.data || [])
    setNewAddLabelList(data.slice(0, 8));
  };

  const setNumber = index => {
    if (index < 3) {
      return <MyIcon style={{ fontSize: 16, position: 'relative', top: 2 }} type={ICON[index]} />;
    }

    return index + 1;
  };

  return (
    <div className={classNames(styles.plateCard, !isOpen && styles.plateClose)}>
      <Row>
        <Col span={4} style={{ display: 'flex' }}>
          <div style={{ flex: 1 }}>
            <div className={styles.title}>标签大盘</div>
            <Card loading={marketLoading} className={styles.plateContent}>
              <Row style={{ paddingRight: 20, marginTop: 6 }}>
                <Col span={12} style={{ marginBottom: 10 }}>
                  <div className={styles.largeMarketTitle}>使用中标签</div>
                  <div className={styles.largeMarketNum}>{labelCntInUse}</div>
                </Col>
                <Col span={12} style={{ marginBottom: 10 }}>
                  <div className={styles.largeMarketTitle}>
                    待下线标签
                    <Tooltip title="“待下线”标签不可再新增人群，但不影响存量人群使用，请注意尽快切换对应标签">
                      <QuestionCircleFilled style={{ marginLeft: 4 }} />
                    </Tooltip>
                  </div>
                  <div className={styles.largeMarketNum}>{labelCntWaitOffline}</div>
                </Col>
                <Col span={12}>
                  <div className={styles.largeMarketTitle}>近30日新增</div>
                  <div className={styles.largeMarketNum}>{labelAddIn30Days}</div>
                </Col>
                <Col span={12}>
                  <div className={styles.largeMarketTitle}>近7日新增</div>
                  <div className={styles.largeMarketNum}>{labelAddIn7Days}</div>
                </Col>
              </Row>
            </Card>
          </div>
          <Divider type="vertical" className={styles.divider} />
        </Col>
        <Col span={10} style={{ display: 'flex' }}>
          <div style={{ flex: 1 }}>
            <div className={styles.title}>热门标签</div>
            <Card loading={popLabelLoading} className={styles.plateContent}>
              <Row gutter={[16, 24]} style={{ padding: '0px 4px 0px 6px', margin: '20px -8px 2px 6px' }}>
                {popLabelList?.length ? (
                  popLabelList?.map((ele, index) => (
                    <Col key={index} xs={24} sm={12} md={8} lg={6} xl={6}>
                      <Tooltip placement="topLeft" title={ele?.label}>
                        <a className={styles.labelItem} onClick={() => {window.open(`${zhugeUrl}/label-market/detail?labelCode=${ele?.value}`)}}>
                          {setNumber(index)}
                          <span style={{ paddingLeft: 4 }}>{ele?.label}</span>
                        </a>
                      </Tooltip>
                    </Col>
                  ))
                ) : (
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} style={{width: '100%'}}/>
                )}
              </Row>
            </Card>
          </div>
          <Divider type="vertical" className={styles.divider} />
        </Col>
        <Col span={10}>
          <div className={classNames(styles.title, styles.flex)}>
            <span>新增标签</span>
            <Button
              type="link"
              size="small"
              style={{ display: 'flex', alignItems: 'center' }}
              onClick={() => {
                setIsOpen(pre => !pre);
              }}
            >
              {isOpen ? '收起' : '展开'}
              <DownOutlined style={{ marginLeft: 4, fontSize: 10 }} className={isOpen && styles.rotate}/>
            </Button>
          </div>
          <Card loading={newAddLabelLoading} className={styles.plateContent}>
            <Row gutter={[16, 24]} style={{ padding: '0px 4px 0px 6px',  margin: '20px -8px 2px 6px' }}>
              {newAddLabelList?.length ? (
                newAddLabelList?.map((ele, index) => (
                  <Col key={index} xs={24} sm={12} md={8} lg={6} xl={6}>
                    <Tooltip placement="topLeft" title={ele?.label}>
                      <a className={styles.labelItem} onClick={() => {window.open(`${zhugeUrl}/label-market/detail?labelCode=${ele?.value}`)}}>
                        {setNumber(index)}
                        <span style={{ paddingLeft: 4 }}>{ele?.label}</span>
                      </a>
                    </Tooltip>
                  </Col>
                ))
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} style={{width: '100%'}}/>
              )}
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default LabelPlate;
