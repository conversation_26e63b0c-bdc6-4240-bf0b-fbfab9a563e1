import React, { useEffect, useState } from 'react';
import { Modal, List, message } from 'antd';
import styles from './index.less'

import ApplyItem from './ApplyItem';

const ApplyModal = props => {
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [selectedList, setSelectedList] = useState([]);
  const { applyLabel, setApplyLabel, isApplyModalOpen, setIsApplyModalOpen } = props;

  const onOk = async () => {
    const selectedCodeList = selectedList.map(ele => ele?.code)
    const _pnames = selectedCodeList.map(item => `zhuge_label_${item}_permission`).toString()

    // 判断是日常环境还是线上
    if (window.location.hostname.includes('.net') || window.location.hostname.includes('localhost')) {
      window.open(`http://acl-test.alibaba-inc.com/apply/cart/detail.htm?pnames=${_pnames}`,"_blank")
    } else if (window.location.hostname.includes('pre')) {
      window.open(`https://acl.alibaba-inc.com/apply/cart/detail.htm?pnames=${_pnames}`,"_blank")
    } else {
      window.open(`https://acl.alibaba-inc.com/apply/cart/detail.htm?pnames=${_pnames}`,"_blank")
    }
    const newApplyLabel = applyLabel?.filter(ele => !selectedCodeList?.includes(ele?.code))

    setSelectedList([])
    setApplyLabel(newApplyLabel)
    if (!newApplyLabel?.length) {
      setIsApplyModalOpen(false);
    }
  };

  return (
    <Modal
      width={700}
      title="标签申请列表"
      okText="申请"
      visible={isApplyModalOpen}
      confirmLoading={confirmLoading}
      onCancel={() => {
        setSelectedList([])
        setIsApplyModalOpen(false);
        setConfirmLoading(false);
      }}
      onOk={onOk}
      destroyOnClose
      okButtonProps={{
        disabled: !selectedList?.length
      }}
      afterClose={() => { 
        setSelectedList([])
      }}
    >
      <List
        className={styles.applyList}
        itemLayout="vertical"
        size="large"
        dataSource={applyLabel}
        renderItem={item => (
          <ApplyItem
            item={item}
            selectedList={selectedList}
            applyLabel={applyLabel}
            setApplyLabel={setApplyLabel}
            setSelectedList={setSelectedList}
          />
        )}
      />
    </Modal>
  );
};

export default ApplyModal;
