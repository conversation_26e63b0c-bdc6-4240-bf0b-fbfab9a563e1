import React, { useEffect, useState } from 'react';
import { Checkbox, Button } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import { cloneDeep } from 'lodash';
import styles from './index.less';

const ApplyItem = props => {
  const { item, selectedList = [], applyLabel = [], setApplyLabel, setSelectedList } = props;
  const { id, name } = item;

  const onSelect = e => {
    const checked = e.target.checked;
    const data = cloneDeep(selectedList);
    if (checked) {
      setSelectedList([...data, item]);
    } else {
      setSelectedList(data?.filter(ele => ele?.id !== id));
    }
  };

  return (
    <div className={styles.applyItem}>
      <Checkbox checked={selectedList?.find(ele => ele?.id === id)} onChange={onSelect}>{name}</Checkbox>
      <Button
        type="link"
        onClick={() => {
          setSelectedList(pre => pre?.filter(ele => ele?.id !== id))
          setApplyLabel(applyLabel?.filter(ele => ele?.id !== id));
        }}
      >
        <CloseOutlined />
      </Button>
    </div>
  );
};

export default ApplyItem;
