import React, { useEffect, useState } from 'react';
import { Table, Checkbox, Tag, Button, Tooltip, Popover, message } from 'antd';
import { QuestionCircleFilled } from '@ant-design/icons';
import { connect } from 'dva';
import { cloneDeep } from 'lodash';
import classNames from 'classnames';
import dayjs from 'dayjs';
import styles from './index.less';
import { labelMarketCollect } from '@/services/api';
import { zhugeUrl, getBpmsUrl } from '@/utils/utils';

import { LABEL_ICON } from '../../common/constants';
import MyIcon from '@/components/MyIcon';
import { SAFE_TIP } from '@/pages/Knowledge/CreateCrowdTag';
import {
  getKeyAndValue,
  getDefinition,
  getCommonEmptyContent,
  formatNumber,
} from '../../common/utils';

const getFirstValue = (obj) => {
  if (!obj || typeof obj !== 'object') return '空';
  const values = Object.values(obj);
  return values[0] || '';
};

const LabelItem = props => {
  const [hovered, setHovered] = useState(false);
  const [collectCnt, setCollectCnt] = useState(0);
  const [isCollect, setIsCollect] = useState(false);
  const {
    item,
    applyLabel = [],
    setApplyLabel,
    selectedLabel,
    setSelectedLabel,
    setIsRefresh,
    currentUser,
  } = props;
  const {
    id,
    code,
    name,
    description,
    authBucket,
    authMsg,
    type,
    dataType,
    procInsId,
    commonEmptyContent,
    timeType,
    status,
    physicalProfileCode,
    dimEnumId,
    previewList = [],
    tagList = [],
    dataOwner = {},
    openScope
  } = item;

  useEffect(() => {
    setCollectCnt(item?.collectAmount || 0);
    setIsCollect(item?.isCollect);
  }, [JSON.stringify(item)]);

  const onSelect = e => {
    const checked = e.target.checked;
    const data = cloneDeep(selectedLabel);
    if (checked) {
      setSelectedLabel([...data, item]);
    } else {
      setSelectedLabel(data?.filter(ele => ele?.id !== id));
    }
  };

  const onJoinApply = () => {
    const isExist = applyLabel?.findIndex(ele => ele.id === id) > -1;
    if (!isExist) {
      setApplyLabel([...applyLabel, item]);
    } else {
      setApplyLabel(applyLabel?.filter(ele => ele?.id !== id));
    }
  };

  const onCollect = async () => {
    const res = await labelMarketCollect({
      labelCode: code,
      collect: isCollect ? 0 : 1,
      operator: {
        empId: currentUser.workId,
        nickName: currentUser.name,
      },
    });
    if (!res?.success) {
      message.error(res?.msg);
      return;
    }

    message.success(isCollect ? '取消收藏成功' : '收藏成功');
    setIsCollect(pre => !pre);
    setCollectCnt(pre => (isCollect ? pre - 1 : pre + 1));
    setIsRefresh(pre => !pre);
  };

  const showPreviewList = () => {
    const definition = getDefinition(physicalProfileCode);
    if (getKeyAndValue(timeType)?.value === 'OFFLINE' && definition === 1) {
      return 'sql标签不展示';
    }

    if (!['ENUM', 'SINGLE_ENUM', 'MULTI_VALUE']?.includes(getKeyAndValue(dataType)?.value)) {
      return '暂无';
    }

    const displayedItems =
      previewList.length > 3 ? [...previewList.slice(0, 3), '...'] : previewList;

    return (
      <span>
        {displayedItems?.map(ele => (
          <Tag key={ele}>{ele}</Tag>
        ))}
        {dimEnumId > 0 && (
          <a
            onClick={() => {
              window.open(`${zhugeUrl}/knowledge/enumValueDetail?id=${dimEnumId}`);
            }}
          >
            查看详情
          </a>
        )}
      </span>
    );
  };

  const columns = [
    {
      title: '标签来源',
      dataIndex: 'dataSourceConfig',
      align: 'center',
      render: (text, record) => getKeyAndValue(text)?.label,
    },
    {
      title: '覆盖量',
      dataIndex: 'coverAmount',
      align: 'center',
      render: (text, record) => {
        const result = getCommonEmptyContent(record, text);
        if (result === '-1') {
          return '产出中';
        }

        return (
          <Tooltip title={formatNumber(result)}>
            <div>{result}</div>
          </Tooltip>
        );
      },
    },
    {
      title: '标签时效',
      dataIndex: 'timeType',
      align: 'center',
      render: (text, record) => getKeyAndValue(text)?.label,
    },
    {
      title: '标签类型',
      dataIndex: 'labelType',
      align: 'center',
      render: (text) => {
        return getFirstValue(text);
      },
    },
    {
      title: '健康分',
      dataIndex: 'healthScore',
      align: 'center',
      render: (text, record) =>
        getCommonEmptyContent(
          record,
          <div>
            {text || 0}
            <Tooltip
              title={
                <Button
                  type="link"
                  size="small"
                  onClick={() => {
                    window.open('https://aliyuque.antfin.com/qnwrq9/kzl68s/colc5usme00ckugw');
                  }}
                >
                  点我查看
                </Button>
              }
            >
              <QuestionCircleFilled style={{ marginLeft: 4, color: '#d9d9d9' }} />
            </Tooltip>
          </div>
        ),
    },
    {
      title: '标签支持项',
      dataIndex: 'scopeList',
      align: 'center',
      render: (text, record) => {
        const { scopeList = [] } = record;
        if (!scopeList?.length) return;
        return scopeList?.map(ele => (
          <Tag color="#fae7d3" style={{ color: '#e44b4b' }} key={ele}>
            {ele}
          </Tag>
        ));
      },
    },
    {
      title: '安全等级',
      dataIndex: 'securityLevel',
      align: 'center',
      render: (text, record) => {
        return text ? (
          <div>
            {`L${text}`}
            <Popover content={SAFE_TIP}>
              <QuestionCircleFilled style={{ marginLeft: 4, color: '#d9d9d9' }} />
            </Popover>
          </div>
        ) : null;
      },
    },
    {
      title: '最近更新时间',
      dataIndex: 'dataUpdateTime',
      align: 'center',
      render: (text, record) =>
        getCommonEmptyContent(record, text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '上架时间',
      dataIndex: 'gmtCreate',
      align: 'center',
      render: (text, record) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
  ];

  const setAuthMsg = () => {
    if (authMsg && !procInsId) return authMsg;
    if (authMsg && procInsId) {
      return (
        <span>
          {authMsg}
          <a
            onClick={() => {
              window.open(getBpmsUrl(procInsId));
            }}
          >
            链接
          </a>
        </span>
      );
    }

    return '';
  };

  return (
    <div className={styles.labelItem}>
      <div className={styles.select} style={{ marginRight: 30 }}>
        <Checkbox onChange={onSelect} checked={selectedLabel?.find(ele => ele?.id === id)} />
      </div>
      <div className={styles.content}>
        <div className={styles.labelInform}>
          <div className={styles.left}>
            <div className={styles.title}>
              <MyIcon style={{ fontSize: 22, marginRight: 6 }} type="icon-biaoqian" />
              {/* <Tag
                color={LABEL_ICON['private']?.bgColor}
                style={{ color: LABEL_ICON['private']?.textColor }}
                className={styles.tag}
              >
                {LABEL_ICON['private']?.text}
              </Tag> */}
              {
                openScope && Object.keys(openScope)[0] === "PRIVATE" && (
                  <Tag color='#dffeed' style={{ color: '#036cff' }} className={styles.private_tag}>私有标签</Tag>
                )
              }
              <a
                className={styles.name}
                href={`${zhugeUrl}/label-market/detail?labelCode=${code}`}
                target="_blank"
              >
                {name}
              </a>
              <div className={styles.tags}>
                {/* 过滤上新 */}
                {tagList
                  ?.filter(item => item !== 'new' && item !== 'high_quality' && item !== 'output_late')
                  ?.map(ele => (
                    <Tag
                      key={ele}
                      color={LABEL_ICON[ele]?.bgColor}
                      style={{ color: LABEL_ICON[ele]?.textColor }}
                      className={styles.tag}
                    >
                      {LABEL_ICON[ele]?.text}
                    </Tag>
                  ))}
              </div>
            </div>
            <div className={styles.desc}>
              <span>标签描述：</span>
              {description}
            </div>
            <div className={styles.dataPreview}>
              标签取值：
              {showPreviewList()}
            </div>
          </div>
          <div className={styles.right}>
            <div className={styles.operate}>
              <div
                className={classNames(styles.collect, isCollect && styles.collected)}
                onClick={onCollect}
              >
                <MyIcon
                  style={{ fontSize: 18 }}
                  type={isCollect ? 'icon-star-full' : 'icon-star'}
                />
                <span className={styles.collectNum}>{collectCnt || 0}</span>
              </div>
              <Tooltip
                title={setAuthMsg()}
                placement="topRight"
                overlayClassName={classNames(authBucket && styles.shopCartPop)}
              >
                <Button type="link" disabled={!authBucket} onClick={onJoinApply}>
                  <MyIcon style={{ fontSize: 18 }} type="icon-gouwugouwuchedinggou" />
                  {applyLabel?.findIndex(ele => ele.id === id) > -1 ? '移除申请篮' : '加入申请篮'}
                </Button>
              </Tooltip>
            </div>
            <div className={styles.director}>
              {dataOwner?.nickName && (
                <>
                  负责人：{dataOwner?.nickName}
                  <MyIcon
                    style={{ fontSize: 18, marginLeft: 6, cursor: 'pointer' }}
                    type="icon-dingding"
                    onClick={() => {
                      window.open(
                        `dingtalk://dingtalkclient/action/sendmsg?dingtalk_id=${dataOwner?.dingTalkId}`
                      );
                    }}
                  />
                </>
              )}
            </div>
          </div>
        </div>
        <div className={styles.table}>
          <Table
            scroll={{ x: 'max-content' }}
            columns={columns}
            dataSource={[item]}
            pagination={false}
          />
        </div>
      </div>
    </div>
  );
};

export default connect(({ user }) => ({
  currentUser: user.currentUser,
}))(LabelItem);
