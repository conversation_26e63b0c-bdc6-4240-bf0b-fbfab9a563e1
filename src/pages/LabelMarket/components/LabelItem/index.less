.labelItem {
  display: flex;
  justify-content: space-between;
  padding: 20px 0px;
  border-bottom: 1px solid #f0f0f0;

  .select {
    margin-right: 30px;
  }

  .content {
    width: calc(100% - 46px);

    .labelInform {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;

      .left {
        .title {
          display: flex;
          align-items: center;
          margin-bottom: 10px;

          .name {
            margin-right: 6px;
            font-weight: bold;
            font-size: 16px;
          }

          .tags {
            display: contents;
          }

          .tag {
            margin-right: 4px;
            padding: 0px 4px;
            line-height: 1.4;
          }

          .private_tag {
            margin:0 4px
          }
        }

        .desc {
          margin-bottom: 10px;
          display: flex;
          > span {
            white-space: nowrap;
          }
        }
      }

      .right {
        .operate {
          display: flex;
          align-items: center;
          margin: 0 0 10px 30px;

          .collect {
            display: flex;
            align-items: center;
            margin-right: 40px;
            cursor: pointer;

            .collectNum {
              margin-left: 6px;
            }

            &:hover {
              color: rgb(227, 124, 51);
            }
          }

          .collected {
            color: rgb(227, 124, 51);
          }
        }

        .director {
          display: flex;
          align-items: center;
          justify-content: end;
        }
      }
    }

    .table {
      margin-top: 10px;
      width: 100%;
    }
  }
}

.shopCartPop {
  display: none;
}
