import React, { useEffect, useState } from 'react';
import { Row, Col, Tree, Spin, Empty, List } from 'antd';
import { queryBuildTree } from '@/services/api';
import { get } from 'lodash';
import styles from './index.less';

import LabelSearch from '../LabelSearch';
import TableSearch from '../TableSearch';
import LabelItem from '../LabelItem';
import { transformTree } from '@/utils/utils'

const { TreeNode } = Tree;

const LabelList = props => {
  const [treeData, setTreeData] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState(['-1']);
  const [treeLoading, setTreeLoading] = useState(false);
  const {
    tabCntMap,
    searchVal,
    setSearchVal,
    queryList,
    dataSource,
    applyLabel,
    setApplyLabel,
    classifyTabList,
    selectedLabel,
    setSelectedLabel,
    setIsRefresh,
    tableLoading
  } = props;
  const { categoryId, pageSize, pageNo, classifyTab } = searchVal;

  useEffect(() => {
    setTreeLoading(true);
    queryBuildTree({ bizEntityName: 'TAOBAO_USER' }).then(res => {
      setTreeLoading(false);
      setTreeData([
        {
          id: -1,
          name: '全部标签',
          children: get(res, 'data.root.children', []),
        },
      ]);
    });
  }, []);

  const queryLabelList = async data => {
    setSearchVal(data);
    await queryList(data);
  };

  const onExpand = expandedKeys => {
    setExpandedKeys(expandedKeys);
  };

  const onSelect = async (selectedKeys, info) => {
    const { selectedNodes, node } = info;
    const { key, children } = node;
    const isChild = Boolean(children?.length);
    const data = {
      ...searchVal,
      categoryId: categoryId === key ? undefined :key,
      pageNo: 1,
      pageSize: 10
    };
    if (!isChild) {
      await queryLabelList(data);
    }
  };

  return (
    <div className={styles.labelList}>
      <Spin spinning={tableLoading}>
        <LabelSearch
          tabCntMap={tabCntMap}
          searchVal={searchVal}
          setSearchVal={setSearchVal}
          applyLabel={applyLabel}
          setApplyLabel={setApplyLabel}
          queryLabelList={queryLabelList}
          classifyTabList={classifyTabList}
        />
        <Row>
          <Col style={{ padding: '20px 10px', borderRight: '1px solid #f0f0f0', width: '15%' }}>
            <Spin spinning={treeLoading}>
              {treeData?.length ? (
                <Tree
                  selectedKeys={[categoryId]}
                  onExpand={onExpand}
                  expandedKeys={expandedKeys}
                  onSelect={onSelect}
                  treeData={transformTree(treeData)}
                />
              ) : (
                <Empty />
              )}
            </Spin>
          </Col>
          <Col style={{ paddingLeft: '10px', width: '85%' }}>
            <TableSearch
              searchVal={searchVal}
              applyLabel={applyLabel}
              setApplyLabel={setApplyLabel}
              queryLabelList={queryLabelList}
              selectedLabel={selectedLabel}
              setSelectedLabel={setSelectedLabel}
              dataSource={dataSource}
            />
            <List
              className={styles.list}
              itemLayout="vertical"
              rowKey={item => item.id}
              size="large"
              dataSource={dataSource}
              renderItem={item => (
                <LabelItem
                  item={item}
                  setIsRefresh={setIsRefresh}
                  applyLabel={applyLabel}
                  setApplyLabel={setApplyLabel}
                  selectedLabel={selectedLabel}
                  setSelectedLabel={setSelectedLabel}
                />
              )}
              pagination={{
                total: tabCntMap[classifyTab] || 0,
                pageSize,
                current: pageNo,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: total => `总计 ${total} 行数据`,
                onChange: (page,pageSize) => {
                  queryLabelList({
                    ...searchVal,
                    pageNo: page,
                    pageSize
                  })
                },
              }}
            />
          </Col>
        </Row>
      </Spin>
    </div>
  );
};

export default LabelList;
