import React, { useEffect, useState } from 'react';
import { Button, Checkbox, Divider, Select, message } from 'antd';
import { queryLabelMarketSortType } from '@/services/api';
import { setMapToList } from '../../common/utils'
import styles from './index.less'


const uniqueArray = (arr = [], key) => {
  if (!arr?.length) return []
  return arr.filter((item, index, self) =>
    index === self.findIndex(t => t[key] === item[key])
  );
}

const TableSearch = props => {
  const [options, setOptions] = useState([])
  const [indeterminate, setIndeterminate] = useState(false)
  const { searchVal, queryLabelList, selectedLabel, setSelectedLabel, applyLabel, setApplyLabel, dataSource } = props
  const { sortType } = searchVal

  useEffect(() => {
    querySortType()
  }, [])

  useEffect(() => {
    if (selectedLabel?.length ) {
      setIndeterminate(!!dataSource.length && selectedLabel.length < 10);
      return
    }
    setIndeterminate(false)
  }, [selectedLabel?.length])

  const querySortType = async () => {
    const res = await queryLabelMarketSortType()

    if (!res?.success) {
      message.error(res?.msg)
      return
    }

    setOptions(setMapToList(res?.data || []))
  }

  const onSelectAll = (e) => {
    const isChecked = e.target.checked
    if (isChecked) {
      setSelectedLabel(dataSource)
    } else {
      setSelectedLabel([])
    }
  };

  const onSelect = async (value) => {
    const data = {
      ...searchVal,
      sortType: value,
      pageNum: 1,
      pageSize: 10,
    }
    await queryLabelList(data)
  }

  const onApply = () => {
    let newData = [...selectedLabel, ...applyLabel]
    newData = newData?.filter(ele => ele.authBucket) // 去除不可加入申请篮的数据
    setApplyLabel(uniqueArray(newData, 'id'))
  }

  return (
    <div className={styles.tableSearch}>
      <div className={styles.left}>
        <Checkbox onChange={onSelectAll} indeterminate={indeterminate} checked = {selectedLabel?.length && (selectedLabel?.length === dataSource?.length)}>全选</Checkbox>
        <div style={{ fontSize: 12, marginTop: 2 }}> 已选 {selectedLabel?.length}</div>
        <Divider type="vertical" />
        <Button type="link" className={styles.btn} onClick={onApply}>批量加入申请篮</Button>
      </div>
      <Select
        value={sortType}
        options={options}
        onChange={onSelect}
      />
    </div>
  )
}

export default TableSearch