import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, Col, Tabs, Badge } from 'antd';
import styles from './index.less';
import { history } from 'umi';

import ApplyModal from '../ApplyModal';

const LabelSearch = props => {
  const [isApplyModalOpen, setIsApplyModalOpen] = useState(false);
  const {
    tabCntMap,
    searchVal,
    setSearchVal,
    applyLabel,
    setApplyLabel,
    queryLabelList,
    classifyTabList,
  } = props;
  const { classifyTab } = searchVal;

  const onActiveKey = async activeKey => {
    queryLabelList({
      ...searchVal,
      classifyTab: activeKey,
      categoryId: undefined,
      pageNo: 1,
      pageSize: 10,
    });
  };

  return (
    <div className={styles.labelSearch}>
      <Tabs activeKey={classifyTab} onChange={onActiveKey}>
        {classifyTabList.map(ele => (
          <Tabs.TabPane tab={`${ele?.label}（${tabCntMap[ele?.value] || 0}）`} key={ele?.value} />
        ))}
      </Tabs>
      <div>
        <Button
          type="link"
          className={styles.btn}
          onClick={() => {
            window.open('https://aliyuque.antfin.com/g/qnwrq9/kzl68s/oof11pkz6mdkhew3/collaborator/join?token=hN01E6f82uvlwwYG&source=doc_collaborator#');
          }}
        >
          我有标签需求 &gt;
        </Button>
        <Button
          type="link"
          className={styles.btn}
          onClick={() => {
            history.push('/develop-tool/label-management/detail');
          }}
        >
          我要上架标签 &gt;
        </Button>
        <Badge count={applyLabel?.length || 0} size="small">
          <Button
            type="primary"
            onClick={() => {
              setIsApplyModalOpen(true);
            }}
          >
            我的申请篮 &gt;
          </Button>
        </Badge>
      </div>

      <ApplyModal
        applyLabel={applyLabel}
        setApplyLabel={setApplyLabel}
        isApplyModalOpen={isApplyModalOpen}
        setIsApplyModalOpen={setIsApplyModalOpen}
      />
    </div>
  );
};

export default LabelSearch;
