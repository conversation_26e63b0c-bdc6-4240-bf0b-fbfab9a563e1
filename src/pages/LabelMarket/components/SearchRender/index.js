import React, { useEffect, useState } from 'react';
import { Input, Button, Checkbox, Row, Col, Spin, Tooltip } from 'antd';
import { DownOutlined, QuestionCircleFilled } from '@ant-design/icons';
import classNames from 'classnames';
import {
  queryLabelMarketAccountType,
  queryLabelMarketLabelStatus,
  queryLabelMarketTimeType,
} from '@/services/api';
import { setMapToList } from '../../common/utils'
import { get } from 'lodash';

import styles from './index.less';
const { Search } = Input;

const SearchRender = props => {
  const [isOpen, setIsOpen] = useState(false);
  const [accountTypeList, setAccountTypeList] = useState([]); // 用户主键
  const [status, setStatus] = useState([]); // 标签状态
  const [timeTypeList, setTimeTypeList] = useState([]); // 标签实效
  const [fileLoading, setFileLoading] = useState(false);
  const { searchVal, setSearchVal, loading, queryList, tableLoading } = props;
  const { profileCodes, statusList, typeList, content } = searchVal;

  useEffect(() => {
    queryFileItem()
  }, []);

  const onChange = async values => {
    const data = {
      ...searchVal,
      ...values,
      classifyTab: 'ALL',
      sortType: 'DESC_BY_QUALITY',
      categoryId: undefined,
      pageNo: 1,
      pageSize: 10,
    };
    // 输入框搜索需点击搜索再执行
    setSearchVal(data);
    if (Object.keys(values)[0] !== 'content') {
      await queryList(data);
    }
  };

  const queryFileItem = async () => {
    setFileLoading(true);
    const res = await Promise.all([
      queryLabelMarketAccountType(),
      queryLabelMarketLabelStatus(),
      queryLabelMarketTimeType()
    ]);
    setFileLoading(false);

    const success = res?.map(item => item?.success) || [];
    if (success.every(x => x)) {
      setAccountTypeList(setMapToList(get(res,'[0].data', [])));
      setStatus(setMapToList(get(res,'[1].data', [])));
      setTimeTypeList(setMapToList(get(res,'[2].data', [])));
    }
  };

  return (
    <div style={{ width: '50%' }}>
      <div className={styles.search}>
        <Search
          loading={tableLoading}
          value={content}
          placeholder="请输入标签id、标签名称、负责人花名或者工号"
          enterButton="搜索"
          size="large"
          onChange={e => {
            onChange({ content: e.target.value });
          }}
          onSearch={() => {
            const data = {
              ...searchVal,
              classifyTab: 'ALL',
              sortType: 'DESC_BY_QUALITY',
              categoryId: undefined,
              pageNo: 1,
              pageSize: 10,
            };
            setSearchVal(data);
            queryList(data);
          }}
        />
        <Button
          type="link"
          size="small"
          style={{ display: 'flex', alignItems: 'center' }}
          onClick={() => {
            setIsOpen(pre => !pre);
          }}
        >
          {isOpen ? '收起筛选' : '展开筛选'}
          <DownOutlined
            style={{ marginLeft: 4, fontSize: 10 }}
            className={isOpen && styles.rotate}
          />
        </Button>
      </div>
      <div className={classNames(styles.accordionContent, isOpen && styles.accordionOpen)}>
        <Spin spinning={fileLoading}>
          <div className={styles.searchFile}>
            <div className={styles.fileItem}>
              <div className={styles.fileLabel}>
                <Tooltip title="淘宝id”指淘宝账户uid；“设备id”指用户手机的唯一设备码（如ios的idfa，Android的imei等）">
                  <QuestionCircleFilled className={styles.icon} />
                </Tooltip>
                用户主键：
              </div>
              <Checkbox.Group
                style={{ width: '100%' }}
                value={profileCodes}
                onChange={value => {
                  onChange({ profileCodes: value });
                }}
              >
                <Row>
                  {accountTypeList?.map(ele => (
                    <Col span={6} key={ele?.value}>
                      <Checkbox value={ele?.value}>{ele?.label}</Checkbox>
                    </Col>
                  ))}
                </Row>
              </Checkbox.Group>
            </div>
            <div className={styles.fileItem}>
              <div className={styles.fileLabel}>
                <Tooltip title="仅“使用中”标签可被圈选使用">
                  <QuestionCircleFilled className={styles.icon} />
                </Tooltip>
                标签状态：
              </div>
              <Checkbox.Group
                style={{ width: '100%' }}
                value={statusList}
                onChange={value => {
                  onChange({ statusList: value });
                }}
              >
                <Row>
                  {status?.map(ele => (
                    <Col span={6} key={ele?.value}>
                      <Checkbox value={ele?.value}>{ele?.label}</Checkbox>
                    </Col>
                  ))}
                </Row>
              </Checkbox.Group>
            </div>
            <div className={styles.fileItem}>
              <div className={styles.fileLabel}>
                <Tooltip title="“离线标签”是天/周/月进行更新；“实时标签”是分钟级或者秒级进行更新">
                  <QuestionCircleFilled className={styles.icon} />
                </Tooltip>
                标签时效：
              </div>
              <Checkbox.Group
                style={{ width: '100%' }}
                value={typeList}
                onChange={value => {
                  onChange({ typeList: value });
                }}
              >
                <Row>
                  {timeTypeList?.map(ele => (
                    <Col span={6} key={ele?.value}>
                      <Checkbox value={ele?.value}>{ele?.label}</Checkbox>
                    </Col>
                  ))}
                </Row>
              </Checkbox.Group>
            </div>
          </div>
        </Spin>
      </div>
    </div>
  );
};

export default SearchRender;
