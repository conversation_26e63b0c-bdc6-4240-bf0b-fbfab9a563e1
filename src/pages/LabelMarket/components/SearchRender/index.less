.search {
  display: flex;
  align-items: flex-end;
  justify-content: center;

  .rotate {
    margin-top: -2px !important;
    transform: rotate(-180deg);
  }
}

.searchFile {
  padding:0 12px;
  margin-top: 10px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;

  .fileItem {
    display: flex;
    align-items: center;
    margin: 12px 0;

    .fileLabel {
      white-space: nowrap;
      width: 90px;

      .icon {
        margin-right: 4px;
        color: #d9d9d9;
      }
    }
  }
}

.accordionContent {
  overflow: hidden;
  max-height: 0;
  transition: all 0.5s ease; /* 动画效果 */
}

.accordionOpen {
  max-height: 150px;
}