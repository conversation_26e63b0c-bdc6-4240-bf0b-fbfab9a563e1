import React, { useEffect, useState } from 'react';
import { Card, message } from 'antd';
import styles from './index.less';
import { queryLabelMarketClassifyTab, labelMarketSearch } from '@/services/api';
import { get } from 'lodash';

import SearchRender from './components/SearchRender';
import LabelPlate from './components/LabelPlate';
import LabelList from './components/LabelList';
import { setMapToList } from './common/utils';

const INIT_SEARCH = {
  profileCodes: ['tbu'],
  statusList: ['ACTIVATE'],
  typeList: ['OFFLINE', 'REALTIME'],
  classifyTab: 'ALL',
  sortType: 'DESC_BY_QUALITY',
  pageNo: 1,
  pageSize: 10,
};

const LabelMarket = () => {
  const [searchVal, setSearchVal] = useState(INIT_SEARCH);
  const [applyLabel, setApplyLabel] = useState([]); // 申请的标签
  const [dataSource, setDataSource] = useState([]); // 表单数据
  const [tableLoading, setTableLoading] = useState(false); // 表格加载
  const [classifyTabList, setClassifyTabList] = useState([]); // 分类tab
  const [classifyLoading, setClassifyLoading] = useState(false);
  const [selectedLabel, setSelectedLabel] = useState([]); // 选中的标签
  const [isRefresh, setIsRefresh] = useState(false); // 重新请求列表
  const [tabCntMap, setTabCntMap] = useState({});

  useEffect(() => {
    queryClassifyTabList();
    getLocalApplyLabel();
  }, []);

  useEffect(() => {
    queryList(searchVal);
  }, [isRefresh]);

  // 查询列表
  const queryList = async searchVal => {
    setTableLoading(true)
    const res = await labelMarketSearch(searchVal)
    setTableLoading(false)
    if (!res?.success) {
      message.error(res?.msg)
      return
    }

    setTabCntMap(get(res, 'data.tabCntMap', {}));
    setDataSource(get(res, 'data.labelInfoList', []));
    setSelectedLabel([]);
  };

  // 查询分类tab
  const queryClassifyTabList = async () => {
    setClassifyLoading(true);
    const res = await queryLabelMarketClassifyTab();
    setClassifyLoading(false);
    if (!res?.success) {
      message.error(res?.msg);
      return;
    }
    setClassifyTabList(setMapToList(res?.data || []));
  };

  // 本地缓存选中的数据
  const setLocalApplyLabel = value => {
    setApplyLabel(value);
    localStorage.setItem('applyLabel', JSON.stringify(value));
  };

  // 获取本地缓存选中的数据
  const getLocalApplyLabel = () => {
    const applyLabel = JSON.parse(localStorage.getItem('applyLabel')) || [];
    setApplyLabel(applyLabel);
  };

  return (
    <>
      <Card bodyStyle={{ display: 'flex', justifyContent: 'center' }} className={styles.card}>
        <SearchRender
          searchVal={searchVal}
          setSearchVal={setSearchVal}
          queryList={queryList}
          tableLoading={tableLoading}
        />
      </Card>

      <Card className={styles.card} bodyStyle={{ padding: 10 }}>
        <LabelPlate />
      </Card>

      <Card className={styles.card} bodyStyle={{ padding: 10 }} loading={classifyLoading}>
        <LabelList
          tabCntMap={tabCntMap}
          tableLoading={tableLoading}
          setIsRefresh={setIsRefresh}
          searchVal={searchVal}
          setSearchVal={setSearchVal}
          applyLabel={applyLabel}
          setApplyLabel={setLocalApplyLabel}
          queryList={queryList}
          dataSource={dataSource}
          selectedLabel={selectedLabel}
          setSelectedLabel={setSelectedLabel}
          classifyTabList={classifyTabList}
        />
      </Card>
    </>
  );
};

export default LabelMarket;
