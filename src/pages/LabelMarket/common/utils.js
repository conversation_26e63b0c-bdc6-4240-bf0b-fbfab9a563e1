export const getDefinition = (physicalProfileCode) => {
  switch (physicalProfileCode) {
    case 'tbup':
    case 'scrm_up':
    case 'device_p':
      return 6
    case 'tbus':
    case 'scrm_ups':
    case 'device_ps':
      return 1
    default:
      return undefined
  }
}

// 获取对象中的key和value
export const getKeyAndValue = (obj) => {
  if (!obj || JSON.stringify(obj) === '{}') return {}
  const label = Object.values(obj)?.[0];
  const value = Object.keys(obj)?.[0];

  return {
    label,
    value
  }
}

export const setMapToList = (data) => {
  const list = []
  data?.forEach(ele => {
    const label = Object.values(ele)?.[0];
    const value = Object.keys(ele)?.[0];
    list.push({ label, value })
  })

  return list
}

// 获取ULR中的参数
export const getUrlParams = (str) => {
  let results = {},
    hash;
  const loc = str || window.location.href;
  if (loc.indexOf('?') == -1 || !loc.split('?')[1].length) {
    return {};
  }
  let params = loc.slice(loc.indexOf('?') + 1).split('&');
  for (let i = 0; i < params.length; i++) {
    hash = params[i].split('=');
    results[hash[0]] = hash[1];
  }
  return results;
};


// 展示文案
export const getCommonEmptyContent = (data, dom) => {
  const { timeType, physicalProfileCode, status } = data
  const definition = getDefinition(physicalProfileCode);
  if (getKeyAndValue(timeType)?.value === 'REALTIME') {
    return '实时不展示'
  }

  if (getKeyAndValue(timeType)?.value === 'OFFLINE') {
    if (definition === 1) {
      return 'sql标签不展示';
    }

    if (getKeyAndValue(status)?.value !== 'ACTIVATE') {
      return '仅展示已上线标签';
    }
  }

  return dom

}

export const checkIfNaN = (value) => {
  return value !== value; // 只有当value为NaN时，该表达式才为真
}

export const formatNumber = (numStr) => {
  // 将输入的字符串转换成数字
  let num = parseFloat(numStr?.replace(/,/g, ''));
  if (checkIfNaN(num)) {
    return "";
  }
  let result;
  if (num >= 100000000) {  // 超过一亿
    result = (num / 100000000).toFixed(2) + '亿';
  } else if (num >= 10000) {  // 超过一万
    result = (num / 10000).toFixed(2) + '万';
  } else {
    result = num.toString();  // 千及以下保持原样
  }

  return result;
}