import React, { PureComponent } from 'react';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import {
  Input,
  Col,
  Card,
  Button,
  Row,
  Empty,
  message,
  Collapse,
  Divider,
  Modal,
  Tooltip,
  Alert,
  Tag,
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { connect } from 'dva';
import TagGroups from '@/pages/GatherPerson/components/TagGroups';
import { transform } from '@/utils/crowd';
import { Form } from '@ant-design/compatible';
import 'dayjs/locale/zh-cn';
import dayjs from 'dayjs';
import { get } from 'lodash';
import { Chart, Interval, Axis, Coordinate, Interaction, Slider } from 'bizcharts';
import DataSet from '@antv/data-set';
import PictureModal from './CreatePicture/index.js';

dayjs.locale('zh-cn');
const { confirm } = Modal;
const { Meta } = Card;
const { Search } = Input;
const FormItem = Form.Item;
const { Panel } = Collapse;
@Form.create()
@connect(state => ({ createPicture: state.createPicture, user: state.user }))
class EditPicture extends PureComponent {
  componentDidMount() {
    const pictureId = this.props.history.location.query.id; //人物id
    const id = this.props.computedMatch.params.id; //画像id
    const { dispatch } = this.props;
    
    dispatch({
      type: 'createPicture/queryCreatePicture',
      payload: {
        id: id,
      },
    });
    dispatch({
      type: 'createPicture/querySearchCanvas',
      payload: {
        taskId: pictureId,
        deleted: 0,
      },
    });
    // const { dispatch } = this.props;
    dispatch({
      type: 'createPicture/queryPictureSearch',
      payload: {
        pageSize: 10000,
        status: 'ACTIVATE',
        realtime: 0,
      },
    });

    dispatch({
      type:'createPicture/savePictureId',
      payload: {
        saveId:pictureId
      }
    })
    
    setInterval(() => {
      dispatch({
        type: 'createPicture/querySearchCanvas',
        payload: {
          taskId: pictureId,
          deleted: 0,
        },
      });
    }, 60000);
  }
  onOpenEditModal = () => {
    const { dispatch } = this.props;
    const {
      createPicture: { createPictureData },
    } = this.props;

    if (createPictureData && createPictureData.crowdAmount > 0) {
      dispatch({
        type: 'createPicture/onModalOpen',
        payload: {
          creator: createPictureData.creator,
          profileCrowdId: createPictureData.id,
          pictureId: this.props.history.location.query.id,
        },
      });
    } else {
      message.info('有目标用户才可以添加');
      return;
    }
  };

  onCancelPictureModal = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'createPicture/onModalCancel',
    });
  };

  SesrchList = () => {
    const {
      dispatch,
      form: { getFieldValue },
    } = this.props;
    const searchEditId = this.props.form.getFieldsValue(searchEditId).searchEditId;
    dispatch({
      type: 'createPicture/queryCreatePicture',
      payload: {
        id: searchEditId,
      },
    });
  };

  operationRender = (value, index, record) => {
    return (
      <>
        <Button
          style={{ margin: '0 -2px 0 -2px' }}
          size="small"
          type="link"
          disabled={value.analysisStatus&&value.analysisStatus=="RUNNING"?true:false}
          onClick={() => this.refreshCard(value)}
        >
          <Tooltip title="点击会更新当前图表数据">刷新</Tooltip>
        </Button>
        <Divider type="vertical" />
        <Button size="small" type="link" onClick={() => this.deleteCard(value)}>
          删除
        </Button>
      </>
    );
  };

  // 刷新操作
  refreshCard = value => {
    const { dispatch } = this.props;
    const pictureId = this.props.history.location.query.id;
    dispatch({
      type: 'createPicture/queryRefreshCanvas',
      payload: {
        indexId: value.id,
      },
    });
  };

  //删除操作
  deleteCard = value => {
    const { dispatch } = this.props;
    const pictureId = this.props.history.location.query.id;
    confirm({
      title: '确认要删除吗',
      content: '删除后不可恢复!',
      onOk() {
        dispatch({
          type: 'createPicture/queryDeleteCanvas',
          payload: {
            indexId: value.id,
          },
        });
      },
    });
  };

  headTitle = value => {
    const { lableMap } = this.props.createPicture;
    return (
      <>
        <div>
          {(value && lableMap[value.analysisIndexName]) || ''}
          <Tooltip
            title={`最后修改时间:${dayjs(value && value.gmtModified).format(
              'YYYY-MM-DD HH:mm:ss'
            )}`}
          >
            <span style={{ color: '#ccc', marginLeft: '4px', fontSize: '12px' }}>
              ({`最后修改时间:${dayjs(value && value.gmtModified).format('YYYY-MM-DD HH:mm:ss')}`})
            </span>
          </Tooltip>
        </div>
      </>
    );
  };

  editStatus = status => {
    const {
      crowdType,
      crowdName,
      crowdAmount,
      operator,
      gmtCreate,
      gmtModified,
      crowdApplyScene,
      crowdTags,
      progress,
      conditions,
      taskName,
      modelName,
      errorCode,
      creator,
      gmtLastBuilt,
      seedCrowdID,
      seedCrowdName,
      exceptCrowdName,
      tableName,
      uid,
      partition,
      where,
      sql,
      crowdLeftName,
      crowdLeftId,
      crowdRightName,
      crowdRightId,
      operateCrowdType,
      extInfo,
      bizDate,
    } = status || '';
    // let conditions = status && status.conditions;
    switch (crowdType) {
      case 'LABEL_CROWD':
        return (
          <Form>
            {/* {common} */}
            <FormItem
              label="人群逻辑"
              labelCol={{
                xs: { span: 24 },
                sm: { span: 2 },
              }}
              wrapperCol={{
                xs: { span: 24 },
                sm: { span: 20 },
              }}
            >
              <TagGroups tagGroups={transform(conditions)} />
            </FormItem>
          </Form>
        );

        break;
      case 'ALGO_CROWD':
        return (
          <Form>
            <FormItem label="人群逻辑">
              <div>
                <span>关联选品任务：</span>
                <span>
                  <Tag color="volcano">{taskName}</Tag>
                </span>
              </div>
              <div>
                <span>算法模型：</span>
                <span>
                  <Tag color="volcano">{modelName}</Tag>
                </span>
              </div>
            </FormItem>
          </Form>
        );

        break;
      case 'TRIP_GALAXY_CROWD':
        return (
          <Form>
            {/* {common} */}
            <FormItem label="人群逻辑">
              <div>
                <span>关联选品池：</span>
                <span>
                  <Tag color="volcano">{taskName}</Tag>
                </span>
              </div>
              <div>
                <span>算法模型：</span>
                <span>
                  <Tag color="volcano">{modelName}</Tag>
                </span>
              </div>
            </FormItem>
          </Form>
        );
        break;
      case 'ALGO_ENLARGE_CROWD':
        return (
          <Form>
            {/* {common} */}
            <FormItem label="种子人群">
              {seedCrowdName ? (
                <div>
                  <Tag color="volcano">
                    {seedCrowdName}-{seedCrowdID}
                  </Tag>{' '}
                  <Link to={`/crowd-stategy/crowd-circle?crowdId=${seedCrowdID}`} target="_blank">
                    去查看
                  </Link>
                </div>
              ) : (
                '暂无'
              )}
            </FormItem>
            <FormItem label="过滤人群">
              {exceptCrowdName ? <Tag color="volcano">{exceptCrowdName}</Tag> : '暂无'}
            </FormItem>
            <FormItem label="人群逻辑">
              <div>
                <span>算法模型：</span>
                <span>
                  <Tag color="volcano">{modelName}</Tag>
                </span>
              </div>
            </FormItem>
          </Form>
        );
        break;
      case 'ODPS_TABLE_CROWD':
        return (
          <Form>
            {/* {common} */}
            <FormItem label="ODPS表名">
              {(status.extInfo && status.extInfo.ODPS_TABLE) || '暂无'}
            </FormItem>
            <FormItem label="用户id字段">
              {(status.extInfo && status.extInfo.ODPS_TABLE_UID) || '暂无'}
            </FormItem>
            <FormItem label="分区名">
              {(status.extInfo && status.extInfo.ODPS_TABLE_PARTITION) || '暂无'}
            </FormItem>
            <FormItem label="where条件">
              {(status.extInfo && status.extInfo.ODPS_TABLE_WHERE) || '暂无'}
            </FormItem>
          </Form>
        );
        break;
      case 'ODPS_SQL_CROWD':
        return (
          <Form>
            {/* {common} */}
            <FormItem label="SQL语句">{(status.extInfo && status.extInfo.ODPS_SQL) || '暂无'}</FormItem>
          </Form>
        );
        break;
      case 'OPERATE_CROWD':
        return (
          <Form>
            {/* {common} */}
            <FormItem label="人群1">
              {status.extInfo.OPERATE_CROWD_LEFT ? (
                <div>
                  <Tag color="volcano">{status.extInfo && status.extInfo.OPERATE_CROWD_LEFT}</Tag>
                </div>
              ) : (
                '暂无'
              )}
            </FormItem>
          </Form>
        );
        break;
      case 'FILE_CROWD':
        return (
          <Form>
            {/* {common} */}
            <FormItem label="用户ID文件名">
              {((status.extInfo && status.extInfo.UPLOAD_FILE_NAME) || '').replace(/.*\//g, '') ||
                '暂无'}
            </FormItem>
          </Form>
        );
        break;
      default:
        // content = '';
        break;
    }
  };

  generateChart = item => {
    switch (item.analysisStatus) {
      case 'INIT':
        return <Alert message="INIT" description="数据生成中" type="info" showIcon />;
        break;
      case 'RUNNING':
        return <Alert message="RUNNING" description="数据生成中" type="info" showIcon />;
        break;
      case 'SUCCESS':
        let reverseData = item.analysisResult && item.analysisResult.result
        // console.log(reverseData,'reverseData')
        return (
          <Chart
            appendPadding={[10, 65, 0, 0]}
            height={350}
            autoFit={true}
            forceFit={true}
            data={reverseData}
          >
            <Slider
              // start={0.5}
              formatter={(v, d, i) => {
                return `${v}`;
              }}
            />
            <Coordinate transpose />
            <Interval
              position="name*count"
              label={[
                'value',
                val => {
                  return {
                    content: val + '%',
                  };
                },
              ]}
            />
          </Chart>
        );
        break;
      case 'FAILED':
        return <Alert message="FAILED" description="失败" type="error" showIcon />;
        break;
    }
  };

  render() {
    const {
      form: { getFieldDecorator },
      createPicture: { modalVisible, createPictureData, canvasData },
    } = this.props;
    let conditions = createPictureData && createPictureData.conditions;
    return (
      <PageHeaderWrapper title="编辑人物画像">
        <Card
          bordered={false}
          title={`【${get(createPictureData, 'id', '人群ID加载中')}】${get(
            createPictureData,
            'crowdName',
            '人群名称加载中'
          )} (人群数量:${get(createPictureData, 'crowdAmount', '人群数量加载中')})`}
        >
          <Collapse>
            <Panel header="人群信息" key="1">
              {this.editStatus(createPictureData && createPictureData)}
            </Panel>
          </Collapse>
        </Card>

        <Card
          bordered={false}
          title="画像信息"
          extra={
            <Tooltip title='画像分析已迁移至洞察分析'>
              <Button type="primary" onClick={this.onOpenEditModal} disabled>
                <PlusOutlined />
                添加画像
              </Button>
            </Tooltip>
          }
          style={{ marginTop: '20px' }}
        >
          <Row gutter={24}>
            <Col span="20"></Col>
            <Col span="4" style={{ textAlign: 'right' }}></Col>
            <PictureModal modalVisible={modalVisible} onCancel={this.onCancelPictureModal} />
          </Row>
          
          <div className="picture-bottom">
            <Row gutter={24}>
              {canvasData && canvasData.length>0? 
                canvasData.map((item, index) => {
                  return (
                    <Col span="12" style={{ marginBottom: '20px' }} key={index}>
                      <Card
                        title={this.headTitle(item)}
                        extra={this.operationRender(item)}
                        style={{ width: '100%' }}
                        size="small"
                      >
                        {this.generateChart(item)}
                      </Card>
                    </Col>
                  );
                })
               : 
                <Empty style={{margin:'0 auto'}}/>
              }
            </Row>
          </div>
        </Card>
      </PageHeaderWrapper>
    );
  }
}

export default EditPicture;
