import React from 'react';
import SubmitForm from '@/components/SubmitForm';
import { Modal, Row, Col, Button, Select, Input, Switch, Radio, message } from 'antd';
import debounce from 'lodash/debounce';
import { Form } from '@ant-design/compatible';
import { connect } from 'dva';

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
const { Search } = Input;
const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const NOOP = () => {};

@Form.create()
@connect(state => ({ createPicture: state.createPicture, user: state.user }))
class PictureModal extends React.PureComponent {
  constructor(props) {
    super(props);
    this.fetchCrowd = debounce(this.fetchCrowd, 300);
  }

  fetchCrowd = (params = {}) => {
    const { dispatch } = this.props;
    dispatch({
      type: 'createPicture/queryPictureSearch',
      payload: {
        ...params,
      },
    });
  };


  handleSubmit = () => {
    const {
      dispatch,
      form: { getFieldValue },
      createPicture: { profileCrowdId, creator ,pictureId},
    } = this.props;
    const profileindexlist = this.props.form.getFieldsValue(profileindexlist).profileindexlist;

    let list = [];
    profileindexlist.map(item => {
      return list.push(item.value);
    });
    dispatch({
      type: 'createPicture/queryCreatCanvas',
      payload: {
        analysisIndexName: list,
        analysisGraphType: "BAR",
        analysisTaskId: +pictureId,
      },
    });

  };

  renderForm = () => {
    const {
      form: { getFieldDecorator },
      createPicture: { crowdList,canvasData },
    } = this.props;
    let biaoQian = []

    crowdList.map((item) => {
      let flag = 0
      canvasData.map((el) => {
        if (item.propertyName === el.analysisIndexName) {
          flag = 1
        }
      })
      if(!flag) {
        biaoQian.push(item)
      }
    });    
    return (
      <Form {...FORM_ITEM_LAYOUT}>
        <FormItem label="类型">
          {getFieldDecorator('type', {
            initialValue: 1,
          })(
            <RadioGroup>
              <Radio value={1}>标签属性分布</Radio>
              {/* <Radio value={0} disabled>
                用户特征分布
              </Radio> */}
            </RadioGroup>
          )}
        </FormItem>


        <FormItem label="标签">
          {getFieldDecorator('profileindexlist', {
            rules: [{ required: true }],
          })(
            <Select
              labelInValue
              allowClear //可以点击清除图标删除内容
              placeholder="请选择标签"
              showSearch
              filterOption={(input, option) => {
                return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
                
              }
              // onSearch={value => this.handleCrowdSearch(value)}
              defaultActiveFirstOption={false}
              showArrow={false}
              mode="multiple"
            >
              {biaoQian &&
                biaoQian.length > 0 &&
                biaoQian.map(c => (
                  <Option key={c.id} value={c.propertyName}>
                    {c.propertyDescription}
                  </Option>
                ))}
            </Select>
          )}
        </FormItem>

        <FormItem label="图形">
          {getFieldDecorator('extInfo', {
            initialValue: 'BAR',
            rules: [{ required: true }],
          })(
            <RadioGroup>
              <Radio value="BAR">条形图</Radio>
              {/* <Radio value="PIE" disabled>环形图</Radio> */}
            </RadioGroup>
          )}
        </FormItem>

        <Row gutter={24}>
          <Col span={24} style={{ textAlign: 'right' }}>
            <Button style={{ marginRight: 15 }} onClick={this.props.onCancel}>
              取消
            </Button>
            <Button type="primary" onClick={this.handleSubmit}>
              确定
            </Button>
          </Col>
        </Row>
      </Form>
    );
  };

  render() {
    const { modalVisible, onCancel = NOOP } = this.props;
    return (
      <Modal
        visible={modalVisible}
        footer={null}
        title={'添加画像信息'}
        onCancel={onCancel}
        width={600}
      >
        {this.renderForm()}
      </Modal>
    );
  }
}

export default PictureModal;