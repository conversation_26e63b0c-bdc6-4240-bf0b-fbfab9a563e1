.container {
  position: relative;

  .wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;

    .expression {
      margin-left: 20px;
    }
  }

  .tagGroup {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 5px 30px 5px 10px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;

    .tagOrder {
      display: inline-block;
      width: 26px;
      height: 26px;
      margin-right: 10px;
      color: #fff;
      font-size: 12px;
      line-height: 26px;
      text-align: center;
      background: #b5c5d4;
      border-radius: 50%;
    }

    .tagGroupDelIcon {
      position: absolute;
      top: 5px;
      right: 10px;
      color: #a0a2ad;
      font-weight: 500;
      font-size: 15px;
      cursor: pointer;
    }

    .tag {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
    }

    .tagInfo {
      display: flex;
      flex: 1;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      margin: 5px 0;
      padding: 5px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;

      .tagName {
        display: inline-block;
        height: 24px;
        margin-right: 10px;
        color: #666;
        font-size: 12px;
        line-height: 24px;
      }

      .manzuText {
        width: 40px;
        height: 25px;
        color: #a0a2ad;
        font-size: 12px;
        line-height: 25px;
        text-align: center;
        background-color: #f2f3f7;
        border-radius: 2px;
      }

      .enumValue {
        display: flex;
        flex: 1;
        flex-wrap: wrap;
        align-items: center;
        min-height: 30px;
        margin-left: 10px;
        padding: 2px 4px;
        font-size: 12px;
        line-height: 30px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;

        .myTag {
          height: 24px;
          line-height: 24px;
          margin: 2px;
        }
      }
      .tagDelIcon {
        margin-left: 5px;
        color: #a0a2ad;
        cursor: pointer;
      }
    }
  }
}

.container::before {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  background-color: rgba(255, 255, 255, 0);
  content: '';
}
