import React, { PureComponent } from 'react';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { Form } from '@ant-design/compatible';
import { Card, Row, Col, Tabs, Radio, Button, Table, Modal, message, Divider, Input ,Tooltip } from 'antd';
import { Link }  from 'umi';
import { history } from 'umi';
import { connect } from 'dva';
import 'dayjs/locale/zh-cn';
import { PlusOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import SearchForm from '@/components/SearchForm';
import Space from '@/components/Space';
import component from '@/locales/zh-CN/component';
import CreatPersonModal from './CreatPerson/index.js';
import EditPersonModal from './EditPerson/index.js';
dayjs.locale('zh-cn');
const { confirm } = Modal;
const { TabPane } = Tabs;
const RadioGroup = Radio.Group;
const RadioButton = Radio.Button;
const FormItem = Form.Item;
@connect(state => ({ createPicture: state.createPicture, user: state.user }))
class Template extends PureComponent {

  handleTableChange = pagination => {
    const {
      dispatch,
      user: { currentUser },
      createPicture: { pagination: pager, formData, twoType},
    } = this.props;
    if(twoType === 'all') {
      dispatch({
        type: 'createPicture/querySearchPicture',
        payload: {
          pageNum: pagination.current,
          pageSize: pagination.pageSize,
          profileName: formData.profileName,
          deleted: 0,
        },
      });
    }else{
      dispatch({
        type: 'createPicture/querySearchPicture',
        payload: {
          pageNum: pagination.current,
          pageSize: pagination.pageSize,
          profileName: formData.profileName,
          deleted: 0,
          creator: { empId: currentUser.workId, nickName: currentUser.name },
        },
      });
    }
    // dispatch({
    //   type:'creetePicture/updateState'
    // })
  };

  componentDidMount() {
    const {
      dispatch,
      user: { currentUser },
    } = this.props;
    dispatch({
      type: 'createPicture/querySearchPicture',
      payload: {
        deleted: 0,
      },
    });
    dispatch({
      type: 'createPicture/saveCurrent',
      payload: {
        currentUser: currentUser,
      },
    });
  }

  showDelete = record => {
    const {
      dispatch,
      createPicture: { twoType },
      user: { currentUser },
    } = this.props;
    confirm({
      title: '确认要删除吗',
      content: '删除后不可恢复!',
      onOk() {
        if (twoType === 'all') {
          dispatch({
            type: 'createPicture/queryPictureUpdate',
            payload: {
              id: record.id,
              deleted: 1,
            },
          });
        } else {
          dispatch({
            type: 'createPicture/queryPictureUpdate',
            payload: {
              id: record.id,
              deleted: 1,
              creator: { empId: currentUser.workId, nickName: currentUser.name },
            },
          });
        }
      },
    });
  };

  // 新建人物画像modal
  onOpenCreatPersonModal = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'createPicture/onCreatPersonModalOpen',
    });
  };

  onCancelCreatPersonModal = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'createPicture/onCreatPersonModalCancel',
    });
  };

  //编辑人物画像modal

  onEditPersonModal = record => {
    const {
      dispatch,
      createPicture: { pictureData },
    } = this.props;
    dispatch({
      type: 'createPicture/onEditPersonModalOpen',
      payload: {
        record: record,
      },
    });
  };

  onCancelEditPersonModal = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'createPicture/onEditPersonModalCancel',
    });
  };

  goEdit = id => {
    history.push({
      pathname: `/portrait-analysis/crowd-insight/edit-picture/${id.profileCrowdId}`,
      query: {
        id: id.id,
      },
    });
  };

  onRadioChange = e => {
    let _self = this;
    const {
      dispatch,
      user: { currentUser },
      createPicture: { formData, twoType },
    } = this.props;
    const { value } = e.target;

    dispatch({
      type: 'createPicture/saveType',
      payload: {
        type: value,
      },
    });
    if (value === 'all') {
      dispatch({
        type: 'createPicture/querySearchPicture',
        payload: {
          deleted: 0,
          profileName: formData.profileName,
        },
      });
    } else {
      dispatch({
        type: 'createPicture/querySearchPicture',
        payload: {
          creator: { empId: currentUser.workId, nickName: currentUser.name },
          deleted: 0,
          profileName: formData.profileName,
        },
      });
    }
  };

  render() {
    const {
      createPicture: {
        type,
        pictureData,
        loading,
        pagination,
        creatModalVisible,
        editModalVisible,
        formData,
        twoType,
      },
      user: { currentUser },
      dispatch,
    } = this.props;
    const columns = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        align: 'center',
        width: '15%',
      },
      {
        title: '名称',
        dataIndex: 'profileName',
        key: 'profileName',
        align: 'left',
        render: (text, id) => <a onClick={() => this.goEdit(id)}>{text}</a>,
      },
      {
        title: '创建者',
        dataIndex: 'creator',
        key: 'creator',
        align: 'center',
        render: text => <span>{text.nickName}</span>,
      },
      {
        title: '创建时间',
        dataIndex: 'gmtCreate',
        key: 'gmtCreate',
        align: 'center',
        render: text => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '暂无'),
      },
      {
        title: '操作',
        dataIndex: 'action',
        render: (text, record) =>
          record.creator.nickName ? (
            <span>
              <Button
                style={{ margin: '0 -15px' }}
                type="link"
                disabled={record.creator.nickName == currentUser.name ? false : true}
                onClick={() => this.onEditPersonModal(record)}
              >
                编辑
              </Button>
              <Divider type="vertical" />
              <Button
                style={{ margin: '0 -15px' }}
                disabled={record.creator.nickName == currentUser.name ? false : true}
                type="link"
                onClick={() => this.showDelete(record)}
              >
                删除
              </Button>
            </span>
          ) : (
            ''
          ),
      },
    ];

    const form = {
      searchForm: formData,
      setting: [
        {
          label: '画像名称',
          target: <Input allowClear placeholder="选输入画像名称" />,
          name: 'profileName',
        },
      ],

      onValuesChange: values => {
        dispatch({
          type: 'createPicture/updateFormData',
          payload: values,
        });
      },
      handleSubmit: values => {
        if (twoType === 'all') {
          dispatch({
            type: 'createPicture/querySearchPicture',
            payload: {
              profileName: values.profileName ? values.profileName : '',
              deleted: 0,
            },
          });
        } else {
          dispatch({
            type: 'createPicture/querySearchPicture',
            payload: {
              profileName: values.profileName ? values.profileName : '',
              deleted: 0,
              creator: { empId: currentUser.workId, nickName: currentUser.name },
            },
          });
        }
      },
    };

    return (
      <PageHeaderWrapper title="画像分析">
        <Card bordered={false} style={{ marginBottom: '15px' }}>
          <SearchForm key="searchForm" {...form} />
        </Card>
        <Card bordered={false}>
          {/* <Space distance={15} /> */}
          <Row gutter={24} style={{ marginBottom: 12 }}>
            <Col span={6}>
              <RadioGroup value={twoType} onChange={this.onRadioChange}>
                <RadioButton value="all">全部画像任务</RadioButton>
                <RadioButton value="mine">我的画像任务</RadioButton>
              </RadioGroup>
            </Col>
            <Col span={18} style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Tooltip title='画像分析已迁移至洞察分析'>
                <Button type="primary" onClick={this.onOpenCreatPersonModal} disabled>
                  <PlusOutlined />
                  创建画像任务
                </Button>
              </Tooltip>
            </Col>
            <CreatPersonModal
              modalVisible={creatModalVisible}
              onCancel={this.onCancelCreatPersonModal}
            />
            <EditPersonModal
              modalVisible={editModalVisible}
              onCancel={this.onCancelEditPersonModal}
            />
          </Row>
          <Table
            loading={loading}
            rowKey={record => record.id} //表格行 key 的取值
            columns={columns}
            dataSource={pictureData}
            pagination={pagination}
            onChange={this.handleTableChange}
          />
        </Card>
      </PageHeaderWrapper>
    );
  }
}

export default Template;
