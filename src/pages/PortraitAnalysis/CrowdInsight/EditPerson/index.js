import React from 'react';
import { Mo<PERSON>, Row, Col, Button, Select, Input, Switch, Radio, message } from 'antd';
import { Form } from '@ant-design/compatible';
import { connect } from 'dva';

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
const { Search } = Input;
const FormItem = Form.Item;
const NOOP = () => {};

@Form.create()
@connect(state => ({ createPicture: state.createPicture, user: state.user }))
class EditPersonModal extends React.PureComponent {
  constructor(props) {
    super(props);
  }

  handleSubmit = () => {
    const {
      dispatch,
      form: { getFieldValue },
      createPicture: { record, twoType, currentUser },
    } = this.props;

    const profileCrowdId = this.props.form.getFieldsValue(profileCrowdId).profileCrowdId; //人群id
    const profileName = this.props.form.getFieldsValue(profileName).profileName; //画像名称
    const profileDescription = this.props.form.getFieldsValue(profileDescription)
      .profileDescription; //画像描述
    const PersonId = record.id;
    if (twoType === 'all') {
      dispatch({
        type: 'createPicture/queryPictureUpdate',
        payload: {
          id: PersonId,
          profileName: profileName,
          profileDescription: profileDescription,
          operator: [],
        },
      });
    } else {
      dispatch({
        type: 'createPicture/queryPictureUpdate',
        payload: {
          id: PersonId,
          profileName: profileName,
          profileDescription: profileDescription,
          operator: [],
          creator: { empId: currentUser.workId, nickName: currentUser.name },
        },
      });
    }
  };

  renderForm = () => {
    const {
      form: { getFieldDecorator },
      createPicture: { crowdList, record },
    } = this.props;
    return (
      <Form {...FORM_ITEM_LAYOUT}>
        <FormItem label="人群id">
          <Input placeholder="请输入人群id" defaultValue={record.profileCrowdId} disabled />
        </FormItem>

        <FormItem label="画像名称">
          {getFieldDecorator('profileName', {
            initialValue: record.profileName,
            rules: [{ required: true, message: '请输入画像名称' }],
          })(<Input placeholder="请输入画像名称" />)}
        </FormItem>

        <FormItem label="画像描述">
          {getFieldDecorator('profileDescription', {
            initialValue: record.profileDescription,
            rules: [{ required: true, message: '请输入画像描述' }],
          })(<Input placeholder="请输入画像描述" />)}
        </FormItem>

        <Row gutter={24}>
          <Col span={24} style={{ textAlign: 'right' }}>
            <Button style={{ marginRight: 15 }} onClick={this.props.onCancel}>
              取消
            </Button>
            <Button type="primary" onClick={this.handleSubmit}>
              确定
            </Button>
          </Col>
        </Row>
      </Form>
    );
  };

  render() {
    const {
      createPicture: { editModalVisible },
      onCancel = NOOP,
    } = this.props;
    return (
      <Modal
        visible={editModalVisible}
        footer={null}
        title={'编辑人物画像'}
        onCancel={onCancel}
        width={600}
      >
        {this.renderForm()}
      </Modal>
    );
  }
}

export default EditPersonModal;
