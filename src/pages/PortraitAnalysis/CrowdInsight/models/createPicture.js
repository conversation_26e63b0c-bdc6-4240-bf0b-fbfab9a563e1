import { message } from 'antd';
import { get } from 'lodash'
import {
  querySearchPicture,
  queryCreatePicture,
  queryPictureSearch,
  queryPictureSubmit, //创建任务
  queryPictureUpdate, //更新任务
  queryCreatCanvas,  //新建指标结果
  querySearchCanvas ,//查询图表
  queryRefreshCanvas, //刷新图表
  queryDeleteCanvas, //删除图表

} from '@/services/api';

export default {
  namespace: 'createPicture',
  state: {
    type: 'primary',
    loading: false,
    modalVisible: false, //添加画像信息modal
    pictureData: [], //画像分析table数据
    createPictureData: [], //创建人物画像部分数据
    pagination: {}, //画像分析table分页
    crowdList: [], //添加画像信息数据
    lableMap: {}, 
    canvasData: [], //可视化数据
    creator: {}, //添加modal数据
    profileCrowdId: '', //画像id
    pictureId: '', // 人物id
    creatModalVisible: false, //创建人物画像modal
    editModalVisible: false, //编辑人物画像modal
    record: {}, //编辑人物画像所需要的数据
    saveId:'', //路由中获取到的id
    twoType:'all' ,//首页类型初始为所有
    currentUser:'', //首页用户所需要的数据
    formData:{
      profileName:'',
      // creator:''
    }
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(({ pathname }) => {
        if (pathname === '/portrait-analysis/crowd-insight') {
          dispatch({ 
            type: 'createPicture/saveType',
            payload: {
              type:'all'
            }
           });
           
          console.log('ok')
        }
      });
    },
  },
  effects: {
    *query(model, { put, all }) {
      yield all([
        put.resolve({
          type: 'querySearchPicture',
        }),
        put.resolve({
          type: 'queryCreatePicture',
        }),
        put.resolve({
          type: 'queryPictureSearch',
        }),
        put.resolve({
          type: 'queryPictureSubmit',
        }),
        put.resolve({
          type: 'queryPictureUpdate',
        }),
        put.resolve({
          type: 'queryCanvasSearch',
        }),
        put.resolve({
          type: 'queryCreatCanvas'
        }),
        put.resolve({
          type: 'queryRefreshCanvas'
        }),
        put.resolve({
          type: 'queryDeleteCanvas'
        })
      ]);
    },

    //首页画像展示
    *querySearchPicture({ payload }, { call, put, select }) {
      // debugger
      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      // debugger
      const { pagination } = yield select(state => state.createPicture);

      yield put({
        type: 'updateState',
        payload: {
          pictureData: [],
          loading: true,
        },
      });

      const res = yield call(querySearchPicture, {
        pageNum: pagination.pageNum || 1,
        pageSize: pagination.pageSize || 10,
        ...payload
      });

      const { twoType } = yield select(state => state.createPicture)
      const { currentUser } = yield select(state => state.createPicture)
      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }

      let { rows, totalNum, pageNum, pageSize } = res.data;

      const newPagination = {
        total: totalNum,
        current: pageNum || 1,
        pageSize: pageSize || 10
      }

      // if(twoType === 'all'){
        yield put({
        type: 'updateState',
        payload: {
          pictureData: rows,
          loading: false,
          pagination: newPagination
        },
      });
      // }else{
      //   yield put({
      //     type: 'updateState',
      //     payload: {
      //       pictureData: rows,
      //       loading: false,
      //       pagination: pagination,
      //       creator: { empId: currentUser.workId, nickName: currentUser.name },
      //     },
      //   });
      // }

      
    },

    //添加画像信息
    *queryCreatePicture({ payload }, { call, put, select }) {
      const res = yield call(queryCreatePicture, {
        ...payload,
      });
      // debugger
      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }
      // debugger
      yield put({
        type: 'updateState',
        payload: {
          createPictureData: res.data.rows[0],
        },
      });
    },

    //画像modal数据
    *queryPictureSearch({ payload }, { call, put, select }) {
      // debugger
      const res = yield call(queryPictureSearch, {
        ...payload,
      });
      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }

      const rows = get(res, 'data.rows', [])
      const lableMap = {}
      rows.map && rows.map(item => {
        lableMap[item.propertyName] = item.propertyDescription
      })
      yield put({
        type: 'updateState',
        payload: {
          crowdList: rows,
          lableMap,
        },
      });
    },

    //创建任务
    *queryPictureSubmit({ payload }, { call, put, select }) {
      // debugger
      const res = yield call(queryPictureSubmit, {
        ...payload,
      });
      const { twoType } = yield select(state => state.createPicture)
      const { currentUser } = yield select(state => state.createPicture)
      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }

      if(twoType === 'all'){
        yield put({
          type: 'createPicture/querySearchPicture',
          payload: {
            deleted:0
          },
        });
  
        yield put({
          type: 'createPicture/onCreatPersonModalCancel',
        })
      }else{
        yield put({
          type: 'createPicture/querySearchPicture',
          payload: {
            deleted:0,
            creator: { empId: currentUser.workId, nickName: currentUser.name },
          },
        });
  
        yield put({
          type: 'createPicture/onCreatPersonModalCancel',
        })
      }

      yield put({
        type: 'updateState',
      });
    },

    //首页画像编辑
    *queryPictureUpdate({ payload }, { call, put, select }) {
      // debugger
      const res = yield call(queryPictureUpdate, {
        ...payload,
      });
      const { twoType } = yield select(state => state.createPicture)
      const { currentUser } = yield select(state => state.createPicture)
      // debugger
      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }
      if(twoType === 'all'){
        yield put({
          type: 'createPicture/querySearchPicture',
          payload: {
            deleted:0
          },
        });
  
        yield put({
          type: 'createPicture/onEditPersonModalCancel',
        })
      }else{
        yield put({
          type: 'createPicture/querySearchPicture',
          payload: {
            deleted:0,
            creator: { empId: currentUser.workId, nickName: currentUser.name },
          },
        });
  
        yield put({
          type: 'createPicture/onEditPersonModalCancel',
        })
      }    

      yield put({
        type: 'updateState',
      });
    },

    //添加画像信息modal提交
    *queryCreatCanvas({ payload }, { call, put, select }) {
      // debugger
      const { saveId } = yield select(state => state.createPicture)
      
      const res = yield call(queryCreatCanvas, {
        ...payload,
      });
      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }
      yield put({
        type: 'createPicture/querySearchCanvas',
        payload: {
          taskId: saveId,
          deleted:0
        },
      });

      yield put({
        type: 'createPicture/onModalCancel',
      });


      yield put({
        type: 'updateState',
      });
    },
    
    //查询图表
    *querySearchCanvas({ payload }, { call, put, select }) {
      // debugger
      const res = yield call(querySearchCanvas, {
        ...payload,
      });
      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          canvasData: res.data,
        },
      });
    },

    //刷新图表
    *queryRefreshCanvas({ payload }, { call, put, select }) {
      // debugger
      const { saveId } = yield select(state => state.createPicture)
      const res = yield call(queryRefreshCanvas, {
        ...payload,
      });
      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }

      yield put({
        type: 'createPicture/querySearchCanvas',
        payload: {
          taskId: saveId,
          deleted:0
        },
      });

      yield put({
        type: 'updateState',
      });
    },

    //删除图表
    *queryDeleteCanvas({ payload }, { call, put, select }) {
      // debugger
      const { saveId } = yield select(state => state.createPicture)
      const res = yield call(queryDeleteCanvas, {
        ...payload,
      });
      if (!res.success) {
        message.error(res.msg || '请求失败');
        return;
      }

      yield put({
        type: 'createPicture/querySearchCanvas',
        payload: {
          taskId: saveId,
          deleted:0
        },
      });

      yield put({
        type: 'updateState',
      });
    },
  },
  reducers: {
    savePictureId(state, { payload }) {
      return {
        ...state,
        saveId: payload.saveId,
      };
    },

    saveType(state, { payload }) {
      return {
        ...state,
        twoType: payload.type,
      };
    },

    saveCurrent(state, { payload }) {
      return {
        ...state,
        currentUser: payload.currentUser,
      };
    },

    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    onModalCancel(state) {
      return {
        ...state,
        modalVisible: false,
        // editFormData: {},
      };
    },
    updateFormData(state, { payload }) {
      return {
        ...state,
        formData: {
          ...state.formData,
          ...payload,
        },
      }
    },
    onModalOpen(state, { payload }) {
      return {
        ...state,
        modalVisible: true,
        creator: payload.creator,
        profileCrowdId: payload.profileCrowdId,
        pictureId: payload.pictureId,
      };
    },

    // 创建人物画像部分modal
    onCreatPersonModalOpen(state, { payload }) {
      return {
        ...state,
        creatModalVisible: true,
      };
    },
    onCreatPersonModalCancel(state, { payload }) {
      return {
        ...state,
        creatModalVisible: false,
      };
    },

    // 编辑人物画像部分modal
    onEditPersonModalOpen(state, { payload }) {
      return {
        ...state,
        editModalVisible: true,
        record: payload.record,
      };
    },
    onEditPersonModalCancel(state, { payload }) {
      return {
        ...state,
        editModalVisible: false,
      };
    },
  },
};
