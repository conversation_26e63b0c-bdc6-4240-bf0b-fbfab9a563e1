import React, { useState, useEffect } from 'react';
import {
  Col,
  Card,
  Button,
  Row,
  Collapse,
  Tag,
  Form,
  Modal,
  Select,
  Breadcrumb,
  message,
} from 'antd';
import TagGroups from '../components/TagGroups';
import { transform } from '@/utils/crowd';
import { history } from 'umi';
import { getUrlParams } from '@/utils/utils';
import ChartCom from '../components/Chart';
import EmptyCom from '../components/Empty';
import { get, unionWith, isEqual } from 'lodash';
import { Link } from 'umi';
import {
  queryNewAnalysisDimension,
  queryNewAnalysisV2Label,
  queryLabelByName,
  queryNewInsightAnalysis,
  queryAnalysisTemplate,
  createNewInsightAnalysis,
  createAnalysisTemplateAnalysis,
  queryCrowdCircle,
} from '@/services/api';

import styles from './index.less';
import { connect } from 'dva';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';

const { Option } = Select;
const { Panel } = Collapse;
const FormItem = Form.Item;

const EditPortrait = props => {
  const {
    user: { currentUser, isSuperAdmin },
  } = props;
  const [crowdData, setCrowdData] = useState([]);
  const [cardLoading, setCardLoading] = useState(false);
  const [labelData, setLabelData] = useState(false);
  const [rihInData, setRihInData] = useState({});
  const [allId, setAllId] = useState([]);
  const [allMeta, setAllMeta] = useState([]);
  const [allData, setAllData] = useState([]); //所有维度数据

  const [modalShow, setModalShow] = useState(false);
  const [latitude, setLatitude] = useState(0); // 纬度
  const [dimData, setDimData] = useState([]); // 所有纬度标签数据
  const [dimAnalyse, setDimAnalyse] = useState([]); //模版分析数据
  const [tableData, setTableData] = useState([]); // 画像表格数据
  const [metaData, setMetaData] = useState([]); // 图表元数据
  const [zhugeTag, setZhugeTag] = useState({}); //诸葛标签
  const [showModal, setShowModal] = useState(false);
  const [showModalAnalyse, setShowModalAnalyse] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  const [form] = Form.useForm();
  const [formPortrait] = Form.useForm();
  const [formAnalyse] = Form.useForm();

  useEffect(() => {
    const requestAll = getUrlParams().crowdIds
      ? getUrlParams()
          .crowdIds.split(',')
          .map(id => queryCrowdCircle(id))
      : [];
    Promise.all(requestAll).then(resAll => {
      if (resAll && resAll.length > 0) {
        setCrowdData(resAll.map(resItem => resItem.data));
      }
    });

    queryNewAnalysisDimension({ codeType: 'tbup' }).then(res => {
      if (res && res.success) {
        setDimData(Object.values(res.data).flat() || []);
      } else {
        console.log(res?.msg || '请求失败了。');
      }
    });

    // 查询人群标签用来创建人群
    queryNewAnalysisV2Label({
      deleted: '0',
      bizEntityName: 'TAOBAO_USER',
      status: 'ACTIVATE',
      bizRegionCode: 'public_region',
      name: '',
      pageSize: 1000,
      pageNum: 1,
    }).then(res => {
      if (res && res.success) {
        setLabelData(get(res, 'data.rows', []));
      } else {
        console.log(res?.msg || '请求失败了。');
      }
    });

    // 查询诸葛人群标签
    queryLabelByName({
      name: 'zhuge_crowd_tag',
    }).then(res => {
      if (res && res.success) {
        setZhugeTag(get(res, 'data', {}));
      } else {
        console.log(res?.msg || '请求失败了。');
      }
    });

    queryAnaList();
    getDimAnalyse();
  }, []);

  const getOwnerList = async () => {
    let params = {};
    params.creator = currentUser.name || currentUser.workId;
    params.pageNum = 1;
    params.pageSize = 100000;
    params.publicType = 1;
    const res = (await queryAnalysisTemplate(params)) || {};
    if (res && res.success) {
      return get(res, 'data.list', []);
    } else {
      message.error(res?.msg);
    }
  };

  const getAllList = async () => {
    let params = {};
    params.pageNum = 1;
    params.pageSize = 100000;
    params.publicType = 0;
    const res = (await queryAnalysisTemplate(params)) || {};
    if (res && res.success) {
      return get(res, 'data.list', []);
    } else {
      message.error(res?.msg);
    }
  };

  const getDimAnalyse = () => {
    Promise.all([getOwnerList(), getAllList()]).then(res => {
      if (res && res.length > 0) {
        let data = unionWith(res[0], res[1], isEqual)?.sort(function(a, b) {
          return a.id - b.id;
        });
        setDimAnalyse(data);
      }
    });
  };

  const queryAnaList = (isAdd = true) => {
    // 通过isAdd区分是否是删除，删除的还不更新重跑时间
    setCardLoading(true);
    queryNewInsightAnalysis(getUrlParams()['id']).then(res => {
      setCardLoading(false);
      if (res && res.success) {
        setTableData(
          res &&
            res.data &&
            res.data.map(ele => {
              const result = ele?.result && JSON.parse(ele.result);
              if (result?.data) {
                return result.data;
              } else {
                return [];
              }
            })
        );
        setMetaData(
          res &&
            res.data &&
            res.data.map(ele => {
              const result = ele?.result && JSON.parse(ele.result);
              if (result?.meta) {
                return result.meta;
              } else {
                return [];
              }
            })
        );
        setAllId(
          res &&
            res.data &&
            res.data.map(ele => {
              return ele.id;
            })
        );
        setAllMeta(
          res &&
            res.data &&
            res.data.map(ele => {
              if (ele?.meta && JSON.parse(ele.meta)) {
                return JSON.parse(ele.meta)?.tagInfoList || [];
              }
            })
        );
        setAllData(get(res, 'data', []));
      } else {
        message.error(res.msg);
      }
    });
  };

  const editStatus = status => {
    const {
      crowdType,
      conditions,
      taskName,
      modelName,
      seedCrowdID,
      seedCrowdName,
      exceptCrowdName,
      extInfo,
      sql,
    } = status || {};
    // let conditions = status && status.conditions;
    switch (crowdType) {
      case 'LABEL_CROWD':
        return (
          <Form>
            {/* {common} */}
            <FormItem
              label="人群逻辑"
              labelCol={{
                xs: { span: 24 },
                sm: { span: 2 },
              }}
              wrapperCol={{
                xs: { span: 24 },
                sm: { span: 20 },
              }}
            >
              <TagGroups tagGroups={transform(conditions)} />
            </FormItem>
          </Form>
        );

      case 'ALGO_CROWD':
        return (
          <Form>
            <FormItem label="人群逻辑">
              <div>
                <span>关联选品任务：</span>
                <span>
                  <Tag color="volcano">
                    {extInfo && extInfo.taskName}
                  </Tag>
                </span>
              </div>
              <div>
                <span>算法模型：</span>
                <span>
                  <Tag color="volcano">
                    {extInfo && extInfo.modelName}
                  </Tag>
                </span>
              </div>
            </FormItem>
          </Form>
        );

      case 'TRIP_GALAXY_CROWD':
        return (
          <Form>
            {/* {common} */}
            <FormItem label="人群逻辑">
              <div>
                <span>关联选品池：</span>
                <span>
                  <Tag color="volcano">
                    {extInfo && extInfo.taskName}
                  </Tag>
                </span>
              </div>
              <div>
                <span>算法模型：</span>
                <span>
                  <Tag color="volcano">
                    {extInfo && extInfo.taskName}
                  </Tag>
                </span>
              </div>
            </FormItem>
          </Form>
        );
      case 'ALGO_ENLARGE_CROWD':
        return (
          <Form>
            {/* {common} */}
            <FormItem label="种子人群">
              {seedCrowdName ? (
                <div>
                  <Tag color="volcano">
                    {seedCrowdName}-{seedCrowdID}
                  </Tag>{' '}
                  <Link to={`/crowd-stategy/gather-person?crowdId=${seedCrowdID}`} target="_blank">
                    去查看
                  </Link>
                </div>
              ) : (
                '暂无'
              )}
            </FormItem>
            <FormItem label="过滤人群">
              {exceptCrowdName ? <Tag color="volcano">{exceptCrowdName}</Tag> : '暂无'}
            </FormItem>
            <FormItem label="人群逻辑">
              <div>
                <span>算法模型：</span>
                <span>
                  <Tag color="volcano">{modelName}</Tag>
                </span>
              </div>
            </FormItem>
          </Form>
        );
      case 'ODPS_TABLE_CROWD':
        return (
          <Form>
            {/* {common} */}
            <FormItem label="ODPS表名">
              {status.extInfo.ODPS_TABLE ? status.extInfo && status.extInfo.ODPS_TABLE : '暂无'}
            </FormItem>
            <FormItem label="用户id字段">
              {status.extInfo.ODPS_TABLE_UID ? status.extInfo && status.extInfo.ODPS_TABLE_UID : '暂无'}
            </FormItem>
            <FormItem label="分区名">
              {status.extInfo.ODPS_TABLE_PARTITION ? status.extInfo && status.extInfo.ODPS_TABLE_PARTITION : '暂无'}
            </FormItem>
            <FormItem label="where条件">
              {status.extInfo.ODPS_TABLE_WHERE ? status.extInfo && status.extInfo.ODPS_TABLE_WHERE : '暂无'}
            </FormItem>
          </Form>
        );
      case 'ODPS_SQL_CROWD':
        return (
          <Form>
            {/* {common} */}
            <FormItem label="SQL语句">
              {status.extInfo.ODPS_SQL ? status.extInfo && status.extInfo.ODPS_SQL : '暂无'}
            </FormItem>
          </Form>
        );
      case 'OPERATE_CROWD':
        return (
          <Form>
            {/* {common} */}
            <FormItem label="人群1">
              {status.extInfo.OPERATE_CROWD_LEFT ? (
                <div>
                  <Tag color="volcano">{status.extInfo && status.extInfo.OPERATE_CROWD_LEFT}</Tag>
                </div>
              ) : (
                '暂无'
              )}
            </FormItem>
          </Form>
        );
      case 'FILE_CROWD':
        return (
          <Form>
            {/* {common} */}
            <FormItem label="用户ID文件名">
              {((status.extInfo && status.extInfo.UPLOAD_FILE_NAME) || '').replace(/.*\//g, '') ||
                '暂无'}
            </FormItem>
          </Form>
        );
      default:
      // content = '';
    }
  };

  const onChange = (value, option) => {
    // 多维
    if (latitude) {
      if (value.length > 2) {
        message.info('多维的标签维度最多为两个！');
        value = value.slice(0, 2);
      }
      form.setFieldsValue({ tag: value });
    } else {
      form.setFieldsValue({ tag: value });
    }
  };

  const createFinish = formData => {
    // const tagInfoListMeta = [{
    //   name:formData.tag
    // }]
    if (formData?.tag.length > 2) {
      message.info('多维的标签维度最多为两个！');
      return;
    }
    const tagInfoListMeta = (formData?.tag || []).map(ele => {
      return { name: ele };
    });
    let data = {
      parentId: rihInData?.id,
      meta: {
        orderBy: 'DESC',
        limitCount: 100,
        tagInfoList: tagInfoListMeta,
      },
      // AnalysisV2Meta:{
      //   orderBy:"DESC",
      //   limitCount:100,
      //   tagInfoList:rihInData?.tagInfoList
      // },
      parentTagInfoList: rihInData?.allParentTagInfoList,
      parentMetaAndValue: {
        orderBy: 'DESC',
        limitCount: 100,
        tagInfoList: rihInData?.parentTagInfoList,
      },
    };
    createNewInsightAnalysis(data)
      .then(res => {
        if (res && res.success) {
          message.info('创建成功');
          queryAnaList();
          setModalShow(false);
        } else {
          message.error(res?.msg || '创建失败');
        }
      })
      .catch(err => {
        message.error(err.message);
      });
  };

  // 下钻弹出弹窗
  const rihIn = () => {
    setModalShow(true);
    setLatitude(0);
    form.resetFields();
  };

  const handleOk = () => {
    form.submit();
  };

  const handleOkPortrait = () => {
    formPortrait.submit();
  };

  const handleOkAnalyse = () => {
    formAnalyse.submit();
  };

  // 提交
  const createPortrait = formData => {
    const { id, crowdIds } = getUrlParams();
    formData.description = ' ';
    if (formData?.tag.length > 2) {
      message.info('多维的标签维度最多为两个！');
      return;
    }

    const tagInfoListMeta = (formData?.tag || []).map(ele => {
      return { name: ele };
    });
    const meta = {
      orderBy: 'DESC',
      limitCount: 100,
      tagInfoList: tagInfoListMeta,
    };
    formData.meta = meta;
    formData.parentId = id;
    formData.crowdId = crowdIds;
    delete formData.tag;
    setConfirmLoading(true);
    createNewInsightAnalysis(formData)
      .then(res => {
        setConfirmLoading(false);
        if (res && res.success) {
          message.info('创建成功');
          setShowModal(false);
          queryAnaList();
        } else {
          message.error(res?.msg || '创建失败');
        }
      })
      .catch(err => {
        message.error(err.message);
      });
  };

  const createAnalyse = formData => {
    setConfirmLoading(true);
    createAnalysisTemplateAnalysis({
      parentId: getUrlParams()['id'],
      templateId: formData.templateId,
    })
      .then(res => {
        setConfirmLoading(false);
        if (res && res.success) {
          message.info('创建成功');
          setShowModalAnalyse(false);
          queryAnaList();
        } else {
          message.error(res?.msg || '创建失败');
        }
      })
      .catch(err => {
        message.error(err.message);
      });
  };

  return (
    <>
      {/* <Breadcrumb style={{ margin: '0 0 12px' }}>
        <Breadcrumb.Item
          onClick={() => {
            history.push(`/insight/portray`);
          }}
          // style={{ cursor: 'pointer' }}
        >
          画像分析
        </Breadcrumb.Item>
        <Breadcrumb.Item>画像编辑</Breadcrumb.Item>
      </Breadcrumb> */}
      <Modal
        visible={modalShow}
        title={latitude ? '添加多维分析' : '添加分析'}
        // footer={null}
        onCancel={() => {
          setModalShow(false);
          form.setFieldsValue({ tag: undefined });
        }}
        // okButtonProps={{ disabled: isSubmit }}
        onOk={handleOk}
        width={600}
      >
        <Form form={form} style={{ height: '100px' }} name="basic" onFinish={createFinish}>
          <Form.Item
            label="人群标签选择"
            name="tag"
            rules={[{ required: true, message: '请选择人群标签！' }]}
          >
            <Select
              placeholder="请选择人群标签"
              showSearch
              onChange={onChange}
              mode="multiple"
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              allowClear
            >
              {dimData.map &&
                dimData.map(ele => (
                  <Option key={ele.code} value={ele.code}>
                    {ele.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        title={`新建--画像分析`}
        visible={showModal}
        onOk={handleOkPortrait}
        onCancel={() => {
          setShowModal(false);
        }}
        maskClosable={false}
        width={600}
        destroyOnClose
        confirmLoading={confirmLoading}
      >
        <Form
          form={formPortrait}
          name="control-hooks"
          onFinish={createPortrait}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
        >
          <Form.Item
            label="人群标签选择"
            name="tag"
            rules={[{ required: true, message: '请选择人群标签！' }]}
          >
            <Select
              placeholder="请选择人群标签"
              showSearch
              mode="multiple"
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              allowClear
            >
              {dimData.map &&
                dimData.map(ele => (
                  <Option key={ele.code} value={ele.code}>
                    {ele.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        title={`新建--模板分析`}
        visible={showModalAnalyse}
        onOk={handleOkAnalyse}
        onCancel={() => {
          setShowModalAnalyse(false);
        }}
        maskClosable={false}
        width={600}
        destroyOnClose
        confirmLoading={confirmLoading}
      >
        <Form
          form={formAnalyse}
          name="control-hooks"
          onFinish={createAnalyse}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
        >
          <Form.Item
            label="人群模板选择"
            name="templateId"
            rules={[{ required: true, message: '请选择人群模板选择！' }]}
          >
            <Select
              placeholder="请选择人群模板选择"
              showSearch
              // mode='multiple'
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              allowClear
            >
              {dimAnalyse.map &&
                dimAnalyse.map(ele => (
                  <Option key={ele.id} value={ele.id}>
                    {ele.publicType === 0 ? `【个人模版】${ele.name}` : `【公共模版】${ele.name}`}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
      <PageHeaderWrapper title="详情">
      <Card bordered={false} className={styles.collapse}>
        <Collapse>
          {crowdData.map((ele, idx) => (
            <Panel
              header={
                <div>
                  人群信息：{`【${ele.id}】${ele.crowdName}`}
                  <span>{`（数量：${ele.crowdAmount || ''}）`}</span>
                </div>
              }
              key={idx}
            >
              {editStatus(ele)}
            </Panel>
          ))}
        </Collapse>
      </Card>

      <Card
        bordered={false}
        loading={cardLoading}
        style={{ marginTop: '10px' }}
        extra={[
          <Button
            style={{ marginRight: '20px' }}
            type="primary"
            key={1}
            disabled={!(isSuperAdmin || (getUrlParams()['isAdmin'] === 'true' ? true : false))}
            onClick={() => {
              setShowModalAnalyse(true);
              formAnalyse.resetFields();
            }}
          >
            模版分析
          </Button>,
          <Button
            type="primary"
            key={2}
            disabled={!(isSuperAdmin || (getUrlParams()['isAdmin'] === 'true' ? true : false))}
            onClick={() => {
              setShowModal(true);
              formPortrait.resetFields();
            }}
          >
            添加画像分析
          </Button>,
        ]}
      >
        <Row gutter={24}>
          {tableData &&
            tableData.length > 0 &&
            tableData.map((item, index) => (
              <Col span={12} style={{ marginBottom: '20px' }} key={index}>
                {item.length > 0 ? (
                  <div style={{ border: '1px solid #eee', padding: 12 }}>
                    <ChartCom
                      // dimensionDate={dimensionDate}
                      allData={allData}
                      data={item}
                      metaData={metaData}
                      index={index}
                      id={allId[index]}
                      rihIn={rihIn}
                      labelData={labelData}
                      // 提供方法删除已选纬度标签数据
                      redSelDim={ind => {
                        queryAnaList(false);
                      }}
                      rihInOk={handleOk}
                      setRihInData={setRihInData}
                      metaCnData={allMeta[index]}
                      queryAnaList={queryAnaList}
                      zhugeTag={zhugeTag}
                      isAdmin={isSuperAdmin || (getUrlParams()['isAdmin'] === 'true' ? true : false)}
                    />
                  </div>
                ) : (
                  <EmptyCom
                    allData={allData}
                    index={index}
                    id={allId[index]}
                    queryAnaList={queryAnaList}
                    isAdmin={isSuperAdmin || (getUrlParams()['isAdmin'] === 'true' ? true : false)}
                  />
                )}
              </Col>
            ))}
        </Row>
        </Card>
        </PageHeaderWrapper>
    </>
  );
};

export default connect(state => ({
  user: state.user,
}))(EditPortrait);
