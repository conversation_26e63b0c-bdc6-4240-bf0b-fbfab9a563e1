export const getStatus = (status) => {
  switch (status) {
    case '1':
      return {color:'cyan', text:'创建中'};
    case '2':
      return {color:'orange', text:'执行中'};
    case '3':
      return {color:'green', text:'执行完成'};
    case '4':
      return {color:'volcano', text:'执行异常'};

    default:
      break;
  }
}

//数组对象去重
export const arrMerge = (array) => {
  let newArr = [];
  array.forEach((item) => {
      // 保存判断结果
      let res = newArr.isExist(item.code, "code");
      // 若不存在新数组中，则直接追加进新数组
      if (res == -1) {
          newArr.push(item);
      } else {
          newArr[res].value = item.value
      };
  });
  return newArr;
}

// 处理小数运算精度丢失
const strip = (num, precision = 12)=>{
  return +parseFloat(num.toPrecision(precision));
}

export const toPercent = (val)=>{
  if (val==0) {
      return 0;
  };
  var str = Number(val*100);
  str = strip(str) + "%";
  return str;
}