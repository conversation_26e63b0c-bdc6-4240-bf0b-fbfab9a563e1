import React, { useState, useMemo } from 'react';
import {
  Badge,
  Row,
  Col,
  Radio,
  Modal,
  Popconfirm,
  message,
  Space,
  Button,
  Divider,
  Tooltip
} from 'antd';
import { Column, PivotTable } from 'chart-render';
import {
  BarChartOutlined,
  AppstoreOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { zhugeUrl, getUrlParams } from '@/utils/utils';
import { getStatus, arrMerge, toPercent } from '../../utils';
import { cloneDeep, get } from 'lodash';
import dayjs from 'dayjs';
import { deleteNewInsightAnalysis, queryLabelEnumValueBuildTree, reRunNewInsightAnalysis } from '@/services/api'

const OPERATOR_MAP = {
  and: 'AND',
  or: 'OR',
  '-': 'NOTIN',
};

export default function ChartCom({
  data,
  metaData,
  index,
  dimNames,
  id,
  rihIn,
  labelData,
  setRihInData,
  allData,
  queryAnaList,
  zhugeTag,
  isAdmin
}) {
  const [displayType, setDisplayType] = useState('chart');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [tooltipData, setTooltipData] = useState({});
  const [isReload, setIsReload] = useState(false); // 分析子项ID

  // 判断多维还是单维，多维暂时不支持下钻和创建人群
  const isShow = () => {
    const meta = allData[index] && allData[index]?.meta && JSON.parse(allData[index]?.meta)
    if (get(meta,'tagInfoList',[])?.length > 1) {
      return false
    }

    return true
  }
  const ColumnCharts = (config) => {
    const component = useMemo(() => {
      if (displayType === 'chart' && allData?.length) {
        const configs = cloneDeep(config)
        // 判断多维还是单维，修改柱形图颜色
        const meta = allData[index] && allData[index]?.meta && JSON.parse(allData[index]?.meta)
        if (get(meta,'tagInfoList',[])?.length > 1) {
          configs.color = [ '#5D7092', '#5B8FF9','#5AD8A6','#F6BD16','#6DC8EC','#945FB9','#FF9845','#1E9493','#bae637','#ffccc7','#ffd8bf','#ffe7ba','#08979c','#d3adf7','#ffadd2','#434343','#613400','#3f6600','#002329']
        } else {
          configs.color = '#5D7092'
        }
        return (
          <div style={{ height: '320px' }}>
            <Column inverted={false} {...configs}  onReady={onReady} />
          </div>
        );
      }
    }, [allData,displayType]);

    return component;
  };

  const confirm = () => {
    deleteNewInsightAnalysis(id).then((res) => {
      if (res && res.success) {
        message.info('删除成功！');
        queryAnaList();
      } else {
        message.error(res?.msg || '删除失败！');
      }
    });
  };

  const config = {
    data: data,
    columnStyle: {
      cursor: 'pointer',
    },
    columnBackground: {
      style: {
        fill: '#fff',
        fillOpacity: 0,
        cursor: 'pointer',
      },
    },
    meta: metaData[index] &&
    metaData[index].filter((itmm) => itmm.id !== 'rate'),
    legend: {
      flipPage:false
    },
    tooltip: {
      customContent: (name, items) => {
        const container = document.createElement('div');
        container.className = 'g2-tooltip';
        const title = `<div style="padding-top: 12px;padding-bottom: 12px;margin:0">${name}</div>`;
        let listItem = '';
        items.forEach((item) => {
          listItem += `<li class="g2-tooltip-list-item" data-index={index} style="margin-bottom:4px;display:flex;align-items: center;">
            <span style="background-color:${
              item?.mappingData?.color || item?.color
            };" class="g2-tooltip-marker"></span>
            <span style="display:inline-flex;flex:1;justify-content:space-between">
            <span style="margin-right: 16px;">${item?.name}:</span><span>${
            item?.value
          }</span>
            </span>
        </li>`;
        });
        return (
          <div>
            <div dangerouslySetInnerHTML={{ __html: title }}></div>
            <div dangerouslySetInnerHTML={{ __html: listItem }}></div>
            {isShow() && <p style={{ color: 'red' }}>点击即可下钻分析</p>}
            {isShow() && <p style={{ color: 'red' }}>点击即可生成人群</p>}
          </div>
        );
      },
    },
  };

  const onReady = (plot) => {
    plot.on('plot:click', (evt) => {
      if (isShow()) {
        const { x, y } = evt;
        const { xField } = plot.options;
        const tooltipData = plot.chart.getTooltipItems({ x, y });
        setTooltipData(tooltipData[0]);
        setIsModalVisible(true);
      }
    });
  };

  const handleOk = () => {
    setIsModalVisible(false);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const createCrowd = async(tooltipData) => {
    const result = allData[index] && allData[index]?.result && JSON.parse(allData[index]?.result)
    let crowdList = [
      {
        code:'zhuge_crowd_tag', //默认诸葛标签
        value: getUrlParams().crowdIds,
      },
    ];
    //父标签
    get(result,'parentTagInfoList',[]).forEach(ele => {
      crowdList.push({code:ele.name,value: ele.value || ''})
    })

    //当前选择标签
    const temp = cloneDeep(tooltipData)?.data || {}
    delete temp.group
    delete temp.picasso_group_user_cnt
    delete temp.rate
    const keyList = Object.keys(temp) || []
    const valueList = Object.values(temp) || []

    const crowdTemp = keyList.map((ele,i) => {
      return {
        code:ele,
        value:temp[ele] || '',
      }
    })

    crowdList.push(...crowdTemp)

    // 标签数组去重复
    let crowd = arrMerge(cloneDeep(crowdList))

    const selectedTagTemp = labelData.filter((ele) =>
      crowd.find((i) => ele.code === i.code),
    );

    selectedTagTemp.unshift(zhugeTag)
    const selectedTags = selectedTagTemp.map(ele => {
      if (crowd.find((i) => ele?.code === i?.code) !== undefined) {
        ele.v2Value = crowd.find((i) => ele?.code === i?.code)?.value
      }
      return ele
    })

    for (let index = 0 ; index < selectedTags.length; index++) {
      if (selectedTags[index].dataType === 'DATE') {
        selectedTags[index].values = [];
      } else if (selectedTags[index].dataType === 'ENUM' || selectedTags[index].dataType === 'MULTI_VALUE') {
        let valueList = []
        const enumData = await queryLabelEnumValueBuildTree({dimMetaId:selectedTags[index]?.dimEnumMetaId})
        get(enumData,'data.root.children',[]).forEach((ele) => {
          if (selectedTags[index].v2Value && typeof(selectedTags[index].v2Value) === 'string') {
            if(selectedTags[index].v2Value.split().find(i => i?.toString() === ele?.enumCode?.toString()) !== undefined){
              valueList.push(`${ele.enumCode} ${ele.name}`)
            }
          }
        })
        selectedTags[index].values = valueList;
      } else if (selectedTags[index].dataType === 'NUMBER' || selectedTags[index].dataType === 'KV') {
        selectedTags[index].values = [];
      } else if (selectedTags[index].dataType === 'NONE_TYPE') {
        if (selectedTags[index].code === "zhuge_crowd_tag") {
          let sqlParams;
          if (selectedTags[index]?.sourceConfig) {
            sqlParams = selectedTags[index]?.sourceConfig?.sqlConfig?.sqlParams;
          } else {
            sqlParams = selectedTags[index]?.sqlParams;
          }

          let newData = [];
          for (let t = 0 ; t < sqlParams.length; t++) {
            if (sqlParams[t].paramType === 'TEXT') {
              newData.push({
                label: `${sqlParams[t].description}:${selectedTags[index].v2Value}`,
                value: [selectedTags[index].v2Value],
                name: sqlParams[t].name,
                description: sqlParams[t].description,
                paramType: sqlParams[t].paramType,
              });
            } else if (sqlParams[t].paramType === 'NUMBER') {
              newData.push({
                label: `${sqlParams[t].description}:${selectedTags[index].v2Value}`,
                value: [selectedTags[index].v2Value],
                name: sqlParams[t].name,
                description: sqlParams[t].description,
                paramType: sqlParams[t].paramType,
              });
            } else if (sqlParams[t].paramType === 'DATE') {
              newData.push({
                label: `${sqlParams[t].description}:${selectedTags[index].v2Value}`,
                value: [selectedTags[index].v2Value],
                name: sqlParams[t].name,
                description: sqlParams[t].description,
                paramType: sqlParams[t].paramType,
              });
            } else if (sqlParams[t].paramType === 'ENUM') {
              const paramValueList = []
              const paramEnumData = await queryLabelEnumValueBuildTree({dimMetaId:sqlParams[t]?.enumId})
              get(paramEnumData,'data.root.children',[]).forEach((ele) => {
                if (selectedTags[index].v2Value && typeof(selectedTags[index].v2Value) === 'string') {
                  if(selectedTags[index]?.v2Value?.split().find((i) => i?.toString() === ele?.enumCode.toString()) !== undefined){
                    paramValueList.push({label:ele.name, value:ele.enumCode})
                  }
                }
              })
              newData.push({
                label: `${sqlParams[t].description}:${paramValueList.map(e => e.label)}`,
                value: paramValueList.map(e => `${e.value} ${e.label}`),
                name: sqlParams[t].name,
                description: sqlParams[t].description,
                paramType: sqlParams[t].paramType,
                enumId: sqlParams[t].enumId,
              });
            }
          }
          selectedTags[index].values = newData
        } else {
          selectedTags[index].values = [];
        }
      }
    }
    let tagGroups = [];
    tagGroups.push({
      operator: 'and',
      selectedTags,
    });

    let outerExpression = ''; // 标签组表达式

    const ret = tagGroups.map((tg, index) => {
      let innerExpression = ''; // 标签组内部表达式
      let label = [];
      const { selectedTags, operator = 'and' } = tg;

      const tagGroupName = String.fromCharCode(65 + index);
      if (index > 0) {
        outerExpression += ` ${OPERATOR_MAP[operator || 'and']} `;
      }

      outerExpression += tagGroupName;

      if (selectedTags && selectedTags.length > 0) {
        label = selectedTags.map((st, i) => {
          if (i > 0) {
            innerExpression += ` ${OPERATOR_MAP[st.operator || 'and']} `;
          }

          innerExpression += st.code || st.propertyName;
          if (st.dataType === 'NONE_TYPE') {
            if (st.code === 'zhuge_crowd_tag') {
              let params =
              st.values &&
              st.values.map(item => {
                let newObj;
                if (item.paramType === 'ENUM') {
                  newObj = {
                    name: item.name,
                    value: item.value,
                    description: item.description,
                    paramType: item.paramType,
                    enumId: item.enumId,
                  };
                } else {
                  newObj = {
                    name: item.name,
                    value: item.value,
                    description: item.description,
                    paramType: item.paramType,
                  };
                }
                return newObj;
              });
              return {
                name: st.code || st.propertyName,
                desc: st.name || st.propertyDescription,
                type: st.dataType,
                params,
              };
            } else {
              return {
                name: st.code || st.propertyName,
                desc: st.name || st.propertyDescription,
                type: st.dataType,
                params: undefined,
              }
            }
          } else {
            return {
              name: st.code || st.propertyName,
              desc: st.name || st.propertyDescription,
              type: st.dataType,
              dimEnumMetaId: st.dimEnumMetaId,
              value: st.values,
            };
          }
        });
      }

      return {
        name: tagGroupName,
        expression: innerExpression,
        label,
      };
    });

    const conditions = {
      group: ret,
      expression: outerExpression,
    };

    window.open(`${zhugeUrl}?crowdParams=${JSON.stringify({conditions})}#/zhuge/crowd-stategy/gather-person`)
    setIsModalVisible(false);
  };

  const chartRihIn = (Data) => {
    const temp = cloneDeep(Data)?.data || {}
    delete temp.group
    delete temp.picasso_group_user_cnt
    delete temp.rate
    const keyList = Object.keys(temp) || []
    // const valueList = Object.values(temp) || []
    // const metaCnDataList = metaCnData.filter(ele => {
    //   if(keyList.find(i => i === ele.name) !== undefined){
    //     return ele
    //   }
    // })
    
    const meta = allData[index] && allData[index]?.meta && JSON.parse(allData[index]?.meta)
    const result = allData[index] && allData[index]?.result && JSON.parse(allData[index]?.result)
    // 当前选择的下钻值
    const parentTagInfoList = get(meta,'tagInfoList',[]).map(ele => {
      ele.value = temp[ele.name]
      return ele
    })

    // 之前的下钻值
    const allParentTagInfoList = get(result,'parentTagInfoList',[])

    // const res = keyList.map((ele,i) => {
    //   return {
    //     name:ele,
    //     value:valueList[i],
    //     cnName: metaCnDataList.find(item => item.name === ele)?.cnName
    //   }
    // })
    rihIn(), 
    setIsModalVisible(false);
    setRihInData({id,parentTagInfoList,allParentTagInfoList})
  }

  const handleReload = ()=>{
    setIsReload(true);
    reRunNewInsightAnalysis(id).then(res=>{
      if(res && res.success){
        setIsReload(false)
        message.success('重跑成功！')
        // setLastUpdateTime(res.data.slice(-1)[0] && res.data.slice(-1)[0].lastUpdateTime)
        queryAnaList();
      }else{
        setIsReload(false)
        message.error(res?.msg || '重跑失败！')
      }
    })
  }

  const showStatus = () => {
    if(allData[index]?.status) {
      let text = ''
      if (allData[index]?.status === '2') {
        text = '执行大概需要3分钟'
      } else if (allData[index]?.status === '4') {
        text = '【请联系长弘】'
      }
      return <span><Badge color={getStatus(allData[index]?.status)?.color} text={getStatus(allData[index]?.status)?.text} /><span style={{color:'red',marginLeft:'10px'}}>{text}</span> </span>
    } else {
      return '暂无'
    }
  }

  return (
    <>
      <Row style={{ margin: '12px 0' }}>
        <Col span={2}>
          {/* <Badge count={index + 1} size="default" style={{ backgroundColor: '#1890ff' }}/> */}
          <div style={{width:'30px',height:'30px',backgroundColor: '#5D7092',borderRadius:"10px",color:"#fff",display:'flex',alignItems:'center',justifyContent:'center'}}>{index + 1}</div>
        </Col>
        <Col span={20}>
          <div style={{ marginRight: '12px' }}>{`ID：${id}`}{!isShow() ? <span style={{marginLeft: '12px',color:'red'}}>(⚠️ 多维分析暂不支持下钻)</span> : <span></span>}</div>
          <div>状态：{showStatus()}</div>
          <div>下钻路径：{get(allData[index] && allData[index]?.result && JSON.parse(allData[index]?.result), 'nestedLablePath','暂无')}</div>
          {dimNames && dimNames.length >= 2 && (
            <div style={{ background: '#fff' }}>
              <ExclamationCircleOutlined style={{ color: '#1890ff' }} />
            </div>
          )}
          <div>执行时间：{`${dayjs(allData[index]?.gmtCreate).format('YYYY-MM-DD HH:mm:ss')}`}</div>
        </Col>
      </Row>
      <Row style={{margin:"20px 0"}}>
        <Col span={16} style={{ display: 'flex'}}>
          <Radio.Group
            value={displayType}
            style={{ display: 'flex' }}
            onChange={(event) => setDisplayType(event.target.value)}
          >
            <Radio.Button value="chart">
              <BarChartOutlined />
            </Radio.Button>
            <Radio.Button value="table">
              <AppstoreOutlined />
            </Radio.Button>
          </Radio.Group>
        </Col>
        <Col span={8} style={{ display: 'flex', flexDirection:'row-reverse'}}>
          <Popconfirm
            title="确认删除吗？"
            onConfirm={confirm}
            okText="确认"
            cancelText="取消"
            disabled={!isAdmin}
          >
            <Button type="primary" style={{ marginLeft: 9 }} disabled={!isAdmin}>
              删除
            </Button>
          </Popconfirm>
          <Tooltip key={3} style={{width: '100px'}} title={<><div>上次运行时间：</div><div>{allData[index] && allData[index] && allData[index].gmtModified? dayjs(allData[index].gmtModified).format("YYYY-MM-DD HH:mm:ss"): '暂无'}</div></>}>
            <Button loading={isReload} type="primary" onClick={handleReload} style={{ marginLeft: 9 }} disabled={!isAdmin}>
              重跑
            </Button>
          </Tooltip>
        </Col>
      </Row>

      {ColumnCharts(config)}
      {displayType === 'table' && (
        <PivotTable
          style={{ height: '318px',overflowY: 'auto' }}
          showSubtotal={false}
          useVirtual={false} //不开启虚拟滚动
          data={data.map((item) => {
            return { ...item, edit: JSON.stringify(item) };
          })}
          meta={[
            ...metaData[index],
            {
              id: 'edit',
              name: '操作',
              isDim: false,
            },
          ]}
          cellRender={(value, dimRecoord, indId) => {
            if (indId === 'edit') {
              return (
                <>
                {isShow() ? (
                  <>
                    {
                      isAdmin ? <a
                      onClick={() => {
                        const temp = data.filter((ele => {
                          if(ele[Object.keys(dimRecoord)] === Object.values(dimRecoord)[0]){
                            return ele
                          }
                        }))
                        setTooltipData({data:temp[0]});
                        chartRihIn({data:temp[0]})
                        rihIn(), setIsModalVisible(false);
                      }}
                    >
                      下钻
                    </a> : <span>下钻</span>
                    }
                    <Divider type="vertical"/>
                    {
                      isAdmin ? <a type="link" onClick={() => {
                        const temp = data.filter((ele => {
                          if(ele[Object.keys(dimRecoord)] === Object.values(dimRecoord)[0]){
                            return ele
                          }
                        }))
                        setTooltipData({data:temp[0]});
                        createCrowd({data:temp[0]})}}>
                      生成人群
                    </a> : <span>生成人群</span>
                    }
                  </>
                  ) : <span style={{color:"red"}}>不支持</span>}
                </>
              );
            } else if (indId === 'rate') {
              return toPercent(value);
            }
            return value;
          }}
        />
      )}
      <Modal
        visible={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={null}
      >
        <h3>{get(tooltipData, 'title', '')}</h3>
        <p>
          <span>{get(tooltipData, 'name', '')}</span>
          <span style={{ margin: '0 5px 0 10px' }}>:</span>
          <span>{get(tooltipData, 'value', '')}</span>
        </p>
        {!isAdmin ? <p style={{color:"red"}}>无该分析操作权限</p> : <p></p>}
        {!isShow() ? <p style={{color:"red"}}>二维不支持下钻和生成人群</p> : <p></p>}
        <Space>
          <Button type="primary" onClick={() => createCrowd(tooltipData)} disabled={!isAdmin || !isShow()}>
            生成人群
          </Button>
          <Button
            type="primary"
            disabled={!isAdmin || !isShow()}
            onClick={() => {
              chartRihIn(tooltipData)
            }}
          >
            下钻
          </Button>
        </Space>
      </Modal>
    </>
  );
}
