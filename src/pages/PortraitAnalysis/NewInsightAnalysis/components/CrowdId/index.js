import React, { useEffect, useState } from 'react';
import { Input } from 'antd';
import { get, debounce } from 'lodash';
import { getUrlParams } from '@/utils/utils';



const CrowdId = (props) => {
  let { value = '', onChange, name, mode = '', options = {}, style = {}, disabled, placeholder } = props;

  useEffect(() => {
    if(getUrlParams()['crowdId']) {
      change(getUrlParams()['crowdId'])
    }
  }, []);

  mode = mode || options.mode || ''; // 兼容 FormRender，一般组件都是props传递下来，FR只能通过schema的ui:options传递下来
  if (mode === 'multiple' && !value) {
    value = [];
  } else if (!value) {
    value = '';
  }

  const handleChange = (e) => {
    // 为了兼容 FormRender，所以需要有下面的 name 判断，一般的组件封装直接通过回调函数把value带回去就行了，这里的onChange(name, value)主要是为了保证FR知道自定义组件的key
    typeof onChange === 'function' && name ? onChange(name, e.target.value) : onChange(e.target.value);
  };

  const change = (value) => {
    typeof onChange === 'function' && name ? onChange(name, value) : onChange(value);
  }
  
  return (
    <Input onChange={handleChange} placeholder={placeholder} value={value}/>
  );
};

export default CrowdId;
