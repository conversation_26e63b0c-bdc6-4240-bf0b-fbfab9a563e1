import React, { useState, useMemo } from 'react';
import {
  Bad<PERSON>,
  Row,
  Col,
  Popconfirm,
  Empty,
  Tooltip,
  message,
  Button,
  Radio,
} from 'antd';
import dayjs from 'dayjs';
import { getStatus } from '../../utils';
import { get } from 'lodash';
import { deleteNewInsightAnalysis, reRunNewInsightAnalysis } from '@/services/api';

export default function EmptyCom({ allData, index, queryAnaList, id, isAdmin }) {
  const [isReload, setIsReload] = useState(false); // 分析子项ID

  const handleReload = () => {
    setIsReload(true);
    reRunNewInsightAnalysis(id).then((res) => {
      if (res && res.success) {
        setIsReload(false);
        message.success('重跑成功！');
        // setLastUpdateTime(res.data.slice(-1)[0] && res.data.slice(-1)[0].lastUpdateTime)
        queryAnaList();
      } else {
        setIsReload(false);
        message.error(res?.msg || '重跑失败！');
      }
    });
  };

  const confirm = () => {
    deleteNewInsightAnalysis(id).then((res) => {
      if (res && res.success) {
        message.info('删除成功！');
        queryAnaList();
      } else {
        message.error(res?.msg || '删除失败！');
      }
    });
  };

  const showStatus = () => {
    if (allData[index]?.status) {
      let text = '';
      if (allData[index]?.status === '2') {
        text = '执行大概需要3分钟';
      } else if (allData[index]?.status === '4') {
        text = '【请联系长弘】';
      }
      return (
        <span>
          <Badge
            color={getStatus(allData[index]?.status)?.color}
            text={getStatus(allData[index]?.status)?.text}
          />
          <span style={{ color: 'red',marginLeft:'10px' }}>{text}</span>{' '}
        </span>
      );
    } else {
      return '暂无';
    }
  };

  return (
    <>
      <div style={{ border: '1px solid #eee', padding: 12 }}>
        <Row gutter={24} style={{ margin: '12px 0' }}>
          <Col span={2}>
            {/* <Badge count={index + 1} size="default" style={{ backgroundColor: '#1890ff' }}/> */}
            <div style={{width:'30px',height:'30px',backgroundColor: '#5D7092',borderRadius:"10px",color:"#fff",display:'flex',alignItems:'center',justifyContent:'center'}}>{index + 1}</div>
          </Col>
          <Col span={20}>
            <div style={{ marginRight: '12px' }}>{`ID：${id}`}</div>
            <div>状态：{showStatus()}</div>
            <div>
              下钻路径：
              {get(
                allData[index] &&
                  allData[index]?.result &&
                  JSON.parse(allData[index]?.result),
                'nestedLablePath',
                '暂无',
              )}
            </div>
            <div>
              执行时间：
              {`${dayjs(allData[index]?.gmtCreate).format(
                'YYYY-MM-DD HH:mm:ss',
              )}`}
            </div>
          </Col>
        </Row>
        <Row style={{ margin: '20px 0' }}>
          <Col
            span={8}
            style={{ display: 'flex', flexDirection: 'row-reverse' }}
          >
            <Popconfirm
              title="确认删除吗？"
              onConfirm={confirm}
              okText="确认"
              cancelText="取消"
              disabled={!isAdmin}
            >
              <Button type="primary" style={{ marginLeft: 9 }} disabled={!isAdmin}>
                删除
              </Button>
            </Popconfirm>
            <Tooltip
              key={3}
              style={{ width: '100px' }}
              title={
                <>
                  <div>上次运行时间：</div>
                  <div>
                    {allData[index] &&
                    allData[index] &&
                    allData[index].gmtModified
                      ? dayjs(allData[index].gmtModified).format(
                          'YYYY-MM-DD HH:mm:ss',
                        )
                      : '暂无'}
                  </div>
                </>
              }
            >
              <Button
                loading={isReload}
                type="primary"
                onClick={handleReload}
                style={{ marginLeft: 9 }}
                disabled={!isAdmin}
              >
                重跑
              </Button>
            </Tooltip>
          </Col>
        </Row>
        <Empty style={{ height: '320px' }} />
      </div>
    </>
  );
}
