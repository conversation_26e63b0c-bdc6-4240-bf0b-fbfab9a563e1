import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import {
  Flex,
  Radio,
  Space,
  Button,
  Card,
  Tooltip,
  Tag,
  Popconfirm,
  Table,
  Form,
  Modal,
  Select,
  Input,
  message,
} from 'antd';
import { BarChartOutlined } from '@ant-design/icons';
import { SearchForm, useForm } from 'form-render';
import { search } from './schema';
import styles from './index.less';
import GetUser from '@/components/GetUser';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { history } from 'umi';
import {
  queryNewInsightAnalysisList,
  queryNewAnalysisV2CrowdList,
  createNewInsightAnalysis,
  updateNewInsightAnalysis,
  deleteNewInsightAnalysis
} from '@/services/api';
import { get } from 'lodash';
import { connect } from 'dva';

const NewInsightAnalysis = props => {
  const {
    user: { currentUser, isSuperAdmin },
  } = props;
  const searchForm = useForm();
  const [form] = Form.useForm();
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState(0); // 0新增，正数代表编辑数据的ID
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [crowdList, setCrowdList] = useState([]); // 所有人群数据
  const [dimData, setDimData] = useState([]); // 所有纬度标签数据
  const [selCrowdList, setSelCrowdList] = useState([]); // 选中的人群数据
  const [isMy, setIsMy] = useState(true); // 是否我的

  useEffect(() => {
    if (currentUser && currentUser.workId) {
      fetchData({
        creator: currentUser.nickNameCn || currentUser.workId,
      });
    }
  }, [currentUser]);

  useEffect(() => {
    queryNewAnalysisV2CrowdList({ pageNum: 1, pageSize: 10000 }).then(res => {
      if (res && res.success) {
        setCrowdList((res.data && res.data.crowdMetaInfoSummaryList) || []);
      }
    });
  }, []);

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 50,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: '分析人群',
      dataIndex: 'crowdId',
      render: (value, row) => {
        return (
          <Tooltip key={row?.crowdId} title={row?.crowdName}>
            <Tag color="blue">{`${row?.crowdId}-${row?.crowdName}`}</Tag>
          </Tooltip>
        );
      },
    },
    {
      title: '创建者',
      dataIndex: 'creator',
      key: 'creator',
      width: 80,
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: 100,
      render: (value, row) => {
        return <div>{value === '1' ? '画像分析' : '指标分析'}</div>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate',
      width: 120,
      render: (value, row) => {
        return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '操作',
      align: 'center',
      width: 160,
      render: row => {
        // 权限判断：系统管理员 || 创建者
        const hasEditPermission = isSuperAdmin || row?.isAdmin;

        return (
          <Space>
            <Button
              type="link"
              style={{ padding: 0 }}
              onClick={() => {
                history.push({
                  pathname: `/portrait-analysis/new-insight-analysis/edit`,
                  search: `id=${row.id}&crowdIds=${row.crowdId}&isAdmin=${hasEditPermission}`,
                });
              }}
            >
              查看
            </Button>
            <Button
              type="link"
              style={{ padding: 0 }}
              disabled={!hasEditPermission}
              onClick={() => {
                setModalType(row.id);
                setShowModal(true);
                form.setFieldsValue({
                  creator: {empId: row.creator, nickName: row.creator },
                  crowdId: row?.crowdId,
                  name: row?.name,
                  tag: row?.meta && get(JSON.parse(row?.meta), 'tagInfoList[0].name', ''),
                });
                // setSelCrowdList(row.analysisConfig.groups)
              }}
            >
              编辑
            </Button>
            <Popconfirm
              title="确定删除吗？"
              onConfirm={() => {
                deleteNewInsightAnalysis(row.id).then(res => {
                  if (res && res.success) {
                    message.info('删除成功！');
                    refresh();
                  } else {
                    message.error(res.msg || '删除失败！');
                  }
                });
              }}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" style={{ padding: 0 }} disabled={!hasEditPermission}>
                删除
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  const refresh = () => {
    fetchData({
      creator: currentUser.nickNameCn ||  currentUser.workId,
    });
    setIsMy(true);
  }

  const fetchData = async value => {
    const params = {
      pageSize: value.pageSize || 10,
      pageNum: value.pageNum || 1,
      ...value,
    };
    setLoading(true);
    const result = await queryNewInsightAnalysisList(params);
    if (result && result.data && result.data.rows) {
      const list = JSON.parse(result.data.rows);
      setDataSource(list);
    }
    setLoading(false);
  };

  const createFinish = async formData => {
    formData.crowdName = get(selCrowdList, '[0].children', '').split('-')[1];
    formData.creator = formData.creator.nickName || formData.creator.empId;
    formData.description = ' ';
    formData.parentId = 0;
    delete formData.tag;
    setConfirmLoading(true);
    if (modalType === 0) {
      createNewInsightAnalysis(formData)
        .then(res => {
          setConfirmLoading(false);
          if (res && res.success) {
            message.info('创建成功');
            setShowModal(false);
            refresh()
          } else {
            message.error(res?.msg || '创建失败');
          }
        })
        .catch(err => {
          message.error(err.message);
        });
    } else {
      updateNewInsightAnalysis({
        id: modalType,
        name: formData.name,
        creator: formData.creator,
      })
        .then(res => {
          setConfirmLoading(false);
          if (res && res.success) {
            message.info('修改成功');
            setShowModal(false);
            refresh();
          } else {
            message.error('修改失败');
          }
        })
        .catch(err => {
          message.error(err.message);
        });
    }
  };

  const onChange = (value, option) => {
    setSelCrowdList([option]);
    form.setFieldsValue({ crowd: value });
  };

  return (
    <PageHeaderWrapper wrapperClassName={styles.newInsightAnalysisPage} title="洞察分析">
      <Card style={{ marginBottom: 16 }}>
        <SearchForm
          style={{
            padding: 0,
            margin: 0,
          }}
          form={searchForm}
          schema={search}
          labelWidth={100}
          searchOnMount={false}
          widgets={{
            searchUser: GetUser }}
          onSearch={value => {
            const data = {
              ...value,
            };
            if (data.creator && !isMy) {
              data.creator = data.creator.nickName || data.creator.empId;
            }  else if(isMy) {
              data.creator = currentUser.workId || data.creator.empId;
            }
            fetchData(data);
          }}
        />
      </Card>
      <Card>
        <Flex style={{ marginBottom: 14 }} justify={'space-between'}>
          <Radio.Group
            onChange={val => {
              setIsMy(val.target.value === '1');
              fetchData({
                creator: val.target.value === '1' ? ( currentUser.nickNameCn || currentUser.workId ) : '',
              });
            }}
            optionType="button"
            defaultValue={'1'}
            value={isMy ? '1' : '2'}
          >
            <Radio.Button value="1">我的</Radio.Button>
            <Radio.Button value="2">全部</Radio.Button>
          </Radio.Group>
          <Button
            type="primary"
            onClick={() => {
              setModalType(0);
              setShowModal(true);
              form.resetFields();
            }}
          >
            <BarChartOutlined />
            创建分析集
          </Button>
        </Flex>
        <Table
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          rowKey={'id'}
          pagination={{
            showTotal: total => `共 ${total} 条`,
          }}
        />
      </Card>
      <Modal
        title={`${modalType ? '修改' : '新建'}--画像分析`}
        open={showModal}
        onOk={form.submit}
        onCancel={() => {
          setShowModal(false);
        }}
        maskClosable={false}
        width={600}
        destroyOnClose
        confirmLoading={confirmLoading}
      >
        <Form
          form={form}
          name="control-hooks"
          onFinish={createFinish}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
        >
          <Form.Item name="crowdId" label="人群选择" rules={[{ required: true }]}>
            <Select
              disabled={!!modalType}
              placeholder="请选择人群"
              onChange={onChange}
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {crowdList.map &&
                crowdList.map(item => (
                  <Option key={item.id} value={item.id}>
                    {`${item.id}-${item.name}`}
                  </Option>
                ))}
            </Select>
          </Form.Item>
          <Form.Item name="creator" label={'负责人'} rules={[{ required: true }]}>
            <GetUser mode={'default'} placeholder="请输入花名或者工号" isAnalysis={true} />
          </Form.Item>
          <Form.Item name="name" label={`画像名称`} rules={[{ required: true }]}>
            <Input placeholder={`请输入画像名称`} />
          </Form.Item>
        </Form>
      </Modal>
    </PageHeaderWrapper>
  );
};

export default connect(({ user }) => ({
  user,
}))(NewInsightAnalysis);
