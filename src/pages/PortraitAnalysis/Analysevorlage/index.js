import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { SearchForm, useForm } from 'form-render';
import { search } from './schema';
import GetUser from '@/components/GetUser';
import {
  Table,
  Tooltip,
  Space,
  Button,
  Popconfirm,
  Flex,
  Radio,
  Card,
  Modal,
  Form,
  Input,
  Select,
  message,
} from 'antd';
import { get } from 'lodash';
import dayjs from 'dayjs';
import { connect } from 'dva';
import {
  queryAnalysisTemplate,
  queryAnalysisDimInfo,
  deleteAnalysisTemplate,
  createAnalysisTemplate,
  updateAnalysisTemplate,
} from '@/services/api';
import { useState, useEffect } from 'react';
import styles from './index.less';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';

const { TextArea } = Input;
const { Option } = Select;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};
const formItemLayoutWithOutLabel = {
  wrapperCol: {
    xs: { span: 16, offset: 6 },
    sm: { span: 16, offset: 6 },
  },
};

const Analysevorlage = props => {
  const {
    user: { currentUser },
  } = props;
  const searchForm = useForm();
  const [form] = Form.useForm();
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isMy, setIsMy] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState(0); // 0新增，正数代表编辑数据的ID
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [dimData, setDimData] = useState([]); // 所有纬度标签数据

  useEffect(() => {
    queryAnalysisDimInfo({ codeType: 'tbup' }).then(res => {
      if (res && res.success) {
        setDimData(Object.values(res.data).flat() || []);
      } else {
        message.error(res?.msg || '请求失败');
      }
    });
  }, []);

  useEffect(() => {
    if (currentUser && currentUser.workId) {
      fetchData({
        creator: currentUser.nickNameCn || currentUser.workId,
        publicType: 1,
      });
    }
  }, [currentUser]);

  const getConfigValue = value => {
    return value.map(ele => {
      const temp = [];
      ele.tagInfoList.forEach(item => {
        temp.push([item.name, item.cnName].join('|'));
      });
      return temp;
    });
  };

  const getConfigList = value => {
    let temp = [];
    temp = getConfigValue(value).map(ele => {
      const arrList = ele.map(item => {
        return item.split('|')[1];
      });
      return arrList.join(',');
    });

    return (
      <>
        {temp.map((item, index) => (
          <div key={index} style={{ whiteSpace: 'nowrap' }}>{`维度${index + 1}：${item}`}</div>
        ))}
      </>
    );
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 50,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: '包含维度',
      dataIndex: 'config',
      key: 'config',
      width: 300,
      render: (value, row) => {
        return (
          <div>
            <Tooltip title={getConfigList(row.config)} overlayStyle={{ maxWidth: 'none' }}>
              <span>{row.config.length}</span>
            </Tooltip>
          </div>
        );
      },
    },
    {
      title: '创建者',
      dataIndex: 'creator',
      key: 'creator',
      width: 80,
      render: (value, row) => {
        return get(row, 'creator.nickName', '')
          ? get(row, 'creator.nickName', '')
          : get(row, 'creator.empId', '');
      },
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate',
      width: 160,
      render: (value, row) => {
        return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '操作',
      align: 'center',
      width: 160,
      render: row => {
        return (
          <Space>
            <Button
              type="link"
              style={{ padding: 0 }}
              onClick={() => {
                setModalType(row.id);
                setShowModal(true);
                form.setFieldsValue({
                  name: row.name,
                  publicType: row?.publicType,
                  description: row?.description,
                  config: getConfigValue(row.config),
                });
              }}
            >
              编辑
            </Button>
            <Popconfirm
              title="确定删除吗？"
              onConfirm={() => {
                onDel(row);
              }}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" style={{ padding: 0 }}>
                删除
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  const refresh = () => {
    fetchData({
      creator: currentUser.workId,
    });
    setIsMy(true);
  };

  const onDel = async row => {
    const res = await deleteAnalysisTemplate(row.id);
    if (res && res.success) {
      message.info('删除成功！');
      refresh();
    } else {
      message.error(res.msg || '删除失败！');
    }
  };

  const fetchData = value => {
    console.log(value,isMy,'1234');
    const data = {
      pageSize: value.pageSize || 10,
      pageNum: value.pageNum || 1,
      ...value,
    };

    console.log(data)

    setLoading(true);

    queryAnalysisTemplate(data).then(result => {
      if (result && result.data && result.data.list) {
        setDataSource(result.data.list);
      }
      setLoading(false);
    });
  };

  const createFinish = formData => {
    const config = formData.config.map(ele => {
      const temp = [];
      ele.forEach(item => {
        temp.push({ name: item.split('|')[0], cnName: item.split('|')[1] });
      });
      return { tagInfoList: temp };
    });

    formData.config = config;
    setConfirmLoading(true);
    if (modalType === 0) {
      createAnalysisTemplate(formData).then(res => {
        setConfirmLoading(false);
        if (res && res.success) {
          message.success('创建成功');
          setShowModal(false);
          refresh(); // 刷新表单
        } else {
          message.error(res?.msg || '创建失败');
        }
      });
    } else {
      updateAnalysisTemplate({ id: modalType, ...formData }).then(res => {
        setConfirmLoading(false);
        if (res && res.success) {
          message.success('修改成功');
          setShowModal(false);
          refresh(); // 刷新表单
        } else {
          message.error(res?.msg || '修改失败');
        }
      });
    }
  };

  return (
    <PageHeaderWrapper title="分析模版">
      <SearchForm
        form={searchForm}
        schema={search}
        searchOnMount={false}
        onSearch={value => {
          const data = {
            ...value,
          };
          data.publicType = isMy ? 1 : 0;
          if (data.creator && !isMy) {
            data.creator = data.creator.nickName;
          } else if (isMy) {
            data.creator = currentUser.workId;
          }
          fetchData(data);
        }}
        widgets={{ getUser: GetUser }}
        onReset={() => {
          fetchData({
            publicType: 1,
            creator: currentUser.workId,
          });
          setIsMy(true)
        }}
      />
      <Card>
        <Flex style={{ marginBottom: 14 }} justify={'space-between'}>
          <Radio.Group
            onChange={val => {
              setIsMy(val.target.value === '1');
              fetchData({
                creator: val.target.value === '1' ? currentUser.workId : '',
                publicType: val.target.value === '1' ? 1 : 0,
              });
            }}
            optionType="button"
            defaultValue={'1'}
            value={isMy ? '1' : '2'}
          >
            <Radio.Button value="1">我的</Radio.Button>
            <Radio.Button value="2">公共模版</Radio.Button>
          </Radio.Group>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setShowModal(true);
              setModalType(0);
              form.resetFields();
            }}
          >
            新增
          </Button>
        </Flex>
        <Table columns={columns} dataSource={dataSource} loading={loading} rowKey="id" />
      </Card>
      <Modal
        title={`${modalType ? '修改' : '新建'}--分析模版`}
        visible={showModal}
        onOk={form.submit}
        onCancel={() => {
          setShowModal(false);
        }}
        maskClosable={false}
        width={600}
        destroyOnClose
        confirmLoading={confirmLoading}
      >
        <Form
          form={form}
          name="control-hooks"
          onFinish={createFinish}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
        >
          <Form.Item name="name" label="模板名称" rules={[{ required: true }]}>
            <Input placeholder="请输入模版名称" />
          </Form.Item>
          <Form.Item name="description" label="描述">
            <TextArea rows={4} placeholder="请输入描述" />
          </Form.Item>
          <Form.Item
            initialValue={0}
            name="publicType"
            label="模板类型"
            rules={[{ required: true }]}
          >
            <Radio.Group>
              <Radio value={0}>公共模板</Radio>
              <Radio value={1}>个人模板</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.List
            name="config"
            initialValue={[[]]}
            rules={[
              {
                validator: async (_, config) => {
                  if (!config || config.length == 0) {
                    message.error('请选择模板配置');
                    return Promise.reject(new Error('请选择模板配置'));
                  }
                },
              },
            ]}
          >
            {(fields, { add, remove }, { errors }) => (
              <>
                {fields.map((field, index) => (
                  <Form.Item
                    {...(index === 0 ? formItemLayout : formItemLayoutWithOutLabel)}
                    label={index === 0 ? '模板配置' : ''}
                    required
                    key={field.key}
                    className={styles.cardBorder}
                  >
                    <Form.Item
                      {...field}
                      validateTrigger={['onChange', 'onBlur']}
                      rules={[
                        {
                          validator: async (_, value) => {
                            if (!value || value.length === 0) {
                              return Promise.reject(new Error('请选择子分析'));
                            }
                            if (value.length > 2) {
                              return Promise.reject(new Error('多维的标签维度最多为两个！'));
                            }
                          },
                        },
                      ]}
                      noStyle
                    >
                      <Select
                        placeholder="请选择人群标签"
                        showSearch
                        mode="multiple"
                        filterOption={(input, option) =>
                          option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        }
                        allowClear
                      >
                        {dimData.map &&
                          dimData.map(ele => (
                            <Option key={ele.code} value={`${ele.code}|${ele.name}`}>
                              {ele.name}
                            </Option>
                          ))}
                      </Select>
                    </Form.Item>
                    {fields.length > 1 ? (
                      <MinusCircleOutlined
                        className="dynamic-delete-button"
                        onClick={() => remove(field.name)}
                      />
                    ) : null}
                  </Form.Item>
                ))}
                <Form.Item {...formItemLayoutWithOutLabel}>
                  <Button
                    type="dashed"
                    onClick={() => add()}
                    style={{ width: '100%' }}
                    icon={<PlusOutlined />}
                  >
                    新增
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
        </Form>
      </Modal>
    </PageHeaderWrapper>
  );
};

export default connect(({ user }) => ({
  user,
}))(Analysevorlage);
