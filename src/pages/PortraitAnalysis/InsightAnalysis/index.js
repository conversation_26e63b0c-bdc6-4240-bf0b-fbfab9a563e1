import React, { PureComponent } from 'react';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { Spin } from 'antd'
import { connect } from 'dva';
import 'dayjs/locale/zh-cn';
import dayjs from 'dayjs';
dayjs.locale('zh-cn');

@connect(state => ({ createPicture: state.createPicture, user: state.user }))
class Template extends PureComponent {
  constructor(props) {
    super(props);

    this.state = {
      loading: true,
    };
  }

  render() {

    // 当前环境是否是的picasso预发环境。
    const picassoUrl = window.location.host.indexOf('pre-') > -1? 'https://pre-picasso-platform.alibaba-inc.com/': 'https://picasso-platform.alibaba-inc.com/';
    return (
      <PageHeaderWrapper title="洞察分析">
        <Spin spinning={this.state.loading} size='large'>
          <iframe
            id="picassoIframe"
            onLoad={()=>this.setState({loading: false})}
            style={{
              width: '100%',
              height: '90vh',
              border: 0
            }}
            src={`${picassoUrl}?isFrame=true#/analysis-insight-platform/insight/portrait`}
          >
          </iframe>
        </Spin>
      </PageHeaderWrapper>
    )    
  }
}

export default Template;
