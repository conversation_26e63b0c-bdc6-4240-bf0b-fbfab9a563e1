import {
  queryMerchantByName,
  deleteMerchants,
  queryMerchants,
  batchAddMerchants,
  queryTagCategories,
  queryTagAttrs,
  batchAddCrowdByMerchant,
} from '@/services/strategy';
import { message } from 'antd';

export default {
  namespace: 'merchantGp',
  state: {
    loading: false,
    selectedRowKeys: [],
    selectedRows: [],
    importMerchantsVisible: false,
    multMerchatsGpVisible: false,
    merchants: [],
    pagination: {
      pageSize: 10,
    },
    tagCategories: [],
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(({ pathname }) => {
        if (pathname === '/crowd-stategy/merchant-gather-person') {
          dispatch({ type: 'query' });
        }
      });
    },
  },
  effects: {
    *query(_, { put, all }) {
      yield all([
        put.resolve({
          type: 'queryMerchants',
        }),
        put.resolve({
          type: 'queryTagCategories',
        }),
      ]);
    },
    *search({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const res = yield call(queryMerchantByName, payload);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          pagination: {
            curent: 1,
            pageSize: 10,
            total: res.total,
          },
          merchants: res.data,
          loading: false,
        },
      });
    },
    *deleteMerchants({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const res = yield call(deleteMerchants, payload);

      if (res.success) {
        message.success('删除成功！');
      } else {
        message.error(res.msg || '删除失败，请稍后再试！');
        return;
      }

      yield put({
        type: 'queryMerchants',
      });
    },
    *queryMerchants({ payload = {} }, { select, call, put }) {
      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const { pagination } = yield select(state => state.merchantGp);
      const res = yield call(queryMerchants, {
        pageNum: pagination.current || 1,
        pageSize: pagination.pageSize || 10,
        ...payload,
      });

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          pagination: {
            ...pagination,
            curent: payload.pageNo || 1,
            pageSize: payload.pageSize || 10,
            total: res.total,
          },
          merchants: res.data,
          loading: false,
        },
      });
    },
    *batchAddMerchants({ payload }, { call, put }) {
      const res = yield call(batchAddMerchants, payload);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success('导入成功');

      yield put({
        type: 'queryMerchants',
      });

      yield put({
        type: 'onCancelImportMerchantsModal',
      });
    },
    *queryTagCategories(_, { call, put }) {
      const res = yield call(queryTagCategories);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      const categories = res.data || {};
      const promises = Object.keys(categories).map(key => queryTagAttrs({ key: categories[key] }));

      const attrs = yield Promise.all(promises);

      // 处理成可用的数据格式
      const tagCategories = Object.keys(categories).map((key, index) => {
        const attrRes = attrs[index];
        if (!res.success) {
          message.error(res.msg);
          return {
            name: key,
            value: categories[key],
            attrs: null,
          };
        }

        const { data } = attrRes;

        const attrOptions = Object.keys(data).map(k => ({
          name: data[k],
          value: k,
        }));

        return {
          name: key,
          value: categories[key],
          attrOptions,
        };
      });

      yield put({
        type: 'updateState',
        payload: {
          tagCategories,
        },
      });
    },
    *batchAddCrowdByMerchant({ payload }, { call, put }) {
      const res = yield call(batchAddCrowdByMerchant, payload);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success('创建成功');

      yield put({
        type: 'onCloseMultMerchatsGpDrawer',
      });
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    onOpenImportMerchantsModal(state) {
      return {
        ...state,
        importMerchantsVisible: true,
      };
    },
    onCancelImportMerchantsModal(state) {
      return {
        ...state,
        importMerchantsVisible: false,
      };
    },
    onOpenMultMerchatsGpDrawer(state) {
      return {
        ...state,
        multMerchatsGpVisible: true,
      };
    },
    onCloseMultMerchatsGpDrawer(state) {
      return {
        ...state,
        multMerchatsGpVisible: false,
      };
    },
  },
};
