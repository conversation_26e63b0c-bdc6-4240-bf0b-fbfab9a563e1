import React from 'react';
import { RightOutlined } from '@ant-design/icons';

const FoldIcon = ({ isFold, style, ...rest }) => {
  return (
    <RightOutlined
      style={{
        fontSize: 18,
        transition: 'transform .24s',
        transform: isFold ? 'rotate(0deg)' : 'rotate(90deg)',
        cursor: 'pointer',
        ...style,
      }}
      {...rest}
    />
  );
};

export default FoldIcon;
