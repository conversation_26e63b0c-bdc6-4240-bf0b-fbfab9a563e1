import React from 'react';
import { Modal, Form, Input, Row, Col, Button } from 'antd';

const NOOP = () => {};
const FormItem = Form.Item;
const { TextArea } = Input;

export default function ImportMerchants({ visible = false, onSubmit = NOOP, onCancel = NOOP }) {
  const onFinish = values => {
    onSubmit(values);
  };

  return (
    <Modal width={800} visible={visible} footer={null} title="导入商家" onCancel={onCancel}>
      <Form onFinish={onFinish} scrollToFirstError>
        <FormItem noStyle>
          <div>提示：</div>
          <div style={{ color: 'red' }}>1) 添加后暂不支持编辑删除，请谨慎操作</div>
          <div>
            2) 格式 商家id|商家名称|商家描述，一行代表一条记录，忽略空行
            如：2856437246|飞猪度假官方旗舰店|这是描述
          </div>
        </FormItem>
        <FormItem name="merchants">
          <TextArea style={{ height: 300 }} allowClear />
        </FormItem>
        <Row gutter={24}>
          <Col span={4} />
          <Col span={14} style={{ textAlign: 'center' }}>
            <Button onClick={() => onCancel()} style={{ marginRight: 15 }}>
              取消
            </Button>
            <Button type="primary" htmlType="submit">
              导入
            </Button>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
}
