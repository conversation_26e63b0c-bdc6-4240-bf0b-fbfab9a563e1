import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Row, Col, Select } from 'antd';
import dayjs from 'dayjs';
import { MinusCircleOutlined } from '@ant-design/icons';
import SelectTagModal from '@/components/SelectTagModal';
import SortableSection from '../SortableSection';
import { MyContext } from './context-manager';
import { unParseDepData, parseDepData } from './handle-dep';

const { Option } = Select;
const NOOP = () => {};
const addProperties = (properties, name) => {
  const result = properties.find(item => item.name === name);
  if (result && result.value && Array.isArray(result.value)) {
    return result.value.join(',');
  }
  return (result && parseInt(result.value, 10)) || 0;
};

export default function MultMerchantsGpDrawer(props) {
  let {
    editFormData = {}
  } = props;
  const {
    visible = false,
    onClose = NOOP,
    selectedMerchants = [],
    tagCategories = [],
    currentUser = {},
    onSubmit = NOOP,
    // editFormData = {},
  } = props;
  const [selectTagModalVisible, setSelectTagModalVisible] = useState(false);
  const [selectedTags, setSelectedTags] = useState(editFormData.selectedTags || []);
  const [payload, setPayload] = useState({});
  editFormData = unParseDepData(editFormData);

  const initialValues = {
    crowdApplyScene: ['PUSH'],
    // needUpdate: editFormData.needUpdate,
    needUpdate: editFormData.hasOwnProperty('needUpdate') ? editFormData.needUpdate : true,
    isLoss: 1,
    operator: [
      {
        empId: currentUser.workId,
        nickName: currentUser.name,
      },
    ],
    extInfo: editFormData.extInfo,
    updateType: editFormData.updateType,
    dependenceBizOption: editFormData.dependenceBizOption,
    expiredDate: dayjs(new Date().setHours(0, 0, 0, 0) + 90 * 24 * 60 * 60 * 1000 - 1),
  };

  const onOpenSelectTagModal = () => {
    setSelectTagModalVisible(true);
  };

  const onCancelSelectTagModal = () => {
    setSelectTagModalVisible(false);
    document.body.style.overflow = 'auto';
  };

  const onSelectedTags = values => {
    setSelectTagModalVisible(false);
    const sts = values;

    sts.forEach(st => {
      // eslint-disable-next-line no-param-reassign
      st.properties = [];
    });
    setSelectedTags(sts);
  };

  const hanleSubmit = () => {
    // 处理提交的数据结构
    const data = Object.keys(payload).map(key => {
      const [labelName, sellerId] = key.split('%%');
      const {
        operator,
        crowdApplyScene,
        needUpdate,
        dependenceBizOption,
        updateType,
        isLoss,
        expiredDate,
        crowdName,
        crowdDescription,
        extInfo,
      } = payload[key];
      const selectedTag = selectedTags.find(item => item.code === labelName);
      const { dimEnumMetaId, name, properties } = selectedTag;
      const crowdTags = [];

      if (isLoss) {
        crowdTags.push('LOSS_OF_ASSETS');
      }

      const creator = currentUser.name;
      const editor = currentUser.name;

      // 生命周期
      const lifeCycleTag = addProperties(
        properties,
        'com.fliggy.zhuge.client.domain.seller.LifeCycleTag'
      );

      // 潜客属性
      const potentialGuestTag = addProperties(
        properties,
        'com.fliggy.zhuge.client.domain.seller.PotentialGuestTag'
      );


      const resTagSet = addProperties(
        properties,
        'com.fliggy.zhuge.client.domain.seller.ResourceTag'
      );

      const resouceTagsStr = addProperties(
        properties,
        'com.fliggy.zhuge.client.domain.seller.ResourceTag'
      );
      // 对应资源位
      const resourceTag = resouceTagsStr === 0 ? 0 : parseInt((resouceTagsStr || '').split(',')[0], 10);

      const crowdItem = {
        creator,
        editor,
        labelCrowdVO: {
          conditions: {
            expression: 'A',
            group: [
              {
                expression: labelName,
                label: [
                  {
                    value: [`${sellerId} ${sellerId}`],
                    name: labelName,
                    desc: name,
                    type: 'MULTI_VALUE',
                    dimEnumMetaId,
                  },
                ],
                name: 'A',
              },
            ],
          },
          crowdName,
          crowdDescription,
          operator,
          creator: {
            empId: currentUser.workId,
            nickName: currentUser.name,
          },
          crowdApplyScene,
          needUpdate,
          crowdTags,
          expiredDate: expiredDate.valueOf(),
          dependenceBizOption,
          updateType,
          extInfo,
        },
        lifeCycleTag,
        potentialGuestTag,
        resourceTag,
        resTagSet,
        sellerId,
        labelName,
      };

      const crowdVo = crowdItem.labelCrowdVO;
      parseDepData(crowdVo);
      crowdItem.labelCrowdVO = crowdVo;

      // 更新的情况
      if (editFormData.id && editFormData.crowdId) {
        crowdItem.id = editFormData.id;
        crowdItem.labelCrowdVO.id = editFormData.crowdId;
      }

      return crowdItem;
    });

    onSubmit({ sellerCrowdEditVOS: data });
  };

  // 选择属性
  const selectProperty = (tcIndex, stIndex, value) => {
    selectedTags[stIndex].properties.splice(tcIndex, 1, {
      name: tagCategories[tcIndex].value,
      value,
    });
    setSelectedTags(selectedTags.slice());
  };

  const onValuesChange = values => {
    const data = {
      ...payload,
      ...values,
    };
    setPayload(data);
  };

  const removeTag = stIndex => {
    setSelectedTags(selectedTags.filter((item, index) => index !== stIndex));
  };

  // 选择商家或者标签更改时重新计算
  useEffect(() => {
    if (!selectedMerchants.length || !selectedTags.length) return;
    let initialPayload;
    // 表单初始数据维护
    selectedMerchants.forEach(sm => {
      const tmp = selectedTags.reduce((acc, cur) => {
        const key = `${cur.code}%%${sm.sellerId}`;
        const value = payload[key] ||
        (editFormData.id && editFormData) || {
          crowdName: `${cur.name}_${sm.sellerName}`,
          crowdDescription: `${cur.name}_${sm.sellerName}描述`,
          ...initialValues,
        };
        value.updateType = value.updateType === undefined ? 'dependence' : value.updateType;
        // 'every_day';
        value.dependenceBizOption = value.updateType === 'dependence' ? 'huokebao' : value.dependenceBizOption;
        return {
          ...acc,
          // 如果已经填写就取原来的否则会造成数据的不一致性
          [key]: value,
        };
      }, {});
      initialPayload = {
        ...initialPayload,
        ...tmp,
      };
    });
    setPayload(initialPayload);
  }, [selectedTags, selectedMerchants]);

  useEffect(() => {
    if (editFormData.selectedTags !== selectedTags) {
      setSelectedTags(editFormData.selectedTags || []);
    }
  }, [editFormData.selectedTags]);

  const multiSelectNames = ['对应资源位'];

  return (
    <>
      <Drawer width={1000} closable title="商家批量圈人" visible={visible} onClose={onClose}>
        <Card title="选择标签&属性">
          <Button
            shape="round"
            onClick={onOpenSelectTagModal}
            style={{ width: '20%', marginBottom: 10 }}
          >
            添加标签
          </Button>
          <Card bordered>
            <Row gutter={24}>
              <Col span={6}>标签名称</Col>
              {tagCategories.map((tc, tcIndex) => (
                <Col span={6} key={tcIndex}>
                  <span>{tc.name}</span>
                </Col>
              ))}
            </Row>
            {selectedTags.map((st, stIndex) => {
              return (
                <Row gutter={24} key={st.code} style={{ marginTop: 10 }}>
                  <Col span={6}>{st.name}</Col>
                  {tagCategories.map((tc, tcIndex) => {
                    const hasMulti = multiSelectNames.includes(tc.name);
                    const property =
                      (st.properties && st.properties.find(item => item.name === tc.value)) || {};

                    const addProperty = {};
                    if (hasMulti) {
                      addProperty.mode = 'multiple';
                    }
                    
                    return (
                      <Col span={6} key={`${st.code}_col_${tcIndex}`}>
                        <Select
                          defaultValue={property.value}
                          style={{ width: '100%' }}
                          {...addProperty}
                          onChange={value => selectProperty(tcIndex, stIndex, value)}
                        >
                          {tc.attrOptions &&
                            tc.attrOptions.length > 0 &&
                            tc.attrOptions.map(attr => (
                              <Option key={attr.value} value={attr.value}>
                                {attr.name}
                              </Option>
                            ))}
                        </Select>
                      </Col>
                    );
                  })}
                  <Col onClick={() => removeTag(stIndex)}>
                    <MinusCircleOutlined style={{ fontSize: 24 }} />
                  </Col>
                </Row>
              );
            })}
          </Card>
        </Card>

        <Card title="商家信息" style={{ marginTop: 20, marginBottom: 20 }}>
          <MyContext.Provider value={{ selectedTags, onValuesChange, initialValues, editFormData }}>
            <SortableSection items={selectedMerchants} visible={visible} onClose={onClose} />
          </MyContext.Provider>
        </Card>

        <Row gutter={24}>
          <Col span={4} />
          <Col span={14} style={{ textAlign: 'center' }}>
            <Button onClick={onClose} style={{ marginRight: 15 }}>
              取消
            </Button>
            <Button type="primary" htmlType="submit" onClick={hanleSubmit}>
              保存
            </Button>
          </Col>
        </Row>
      </Drawer>

      <SelectTagModal
        showAll={false}
        visible={selectTagModalVisible}
        onCancel={onCancelSelectTagModal}
        onOk={onSelectedTags}
      />
    </>
  );
}
