
// 将服务端返回的数据进行处理
export const unParseDepData = formData => {
  const data = formData;
  if (!data) {
    return data;
  }
  if (Object.keys(data).length === 0) {
    return data;
  }

  if (data.extInfo) {
    let currentExtInfo = {};
    try {
      currentExtInfo = (JSON.parse(data.extInfo.replace(/&quot;/g, '"')) || {}).UPDATE_MODEL || {};
    } catch (ex) {
      console.error(ex.message);
    }
    const {
      dependenceBizOption,
      updateType
    } = currentExtInfo;

    data.updateType = (updateType === undefined && data.needUpdate) ? 'dependence' : updateType;
    data.dependenceBizOption = data.updateType === 'dependence' ? 'huokebao' : dependenceBizOption;
  }
  return data;
}

// 将传给服务端返回的数据进行解析
// 包装成extInfo里的内容
// 满足 大浩老师的要求
export const parseDepData = data => {
  const crowdVo = data;
  if (crowdVo.needUpdate) {
    if (!crowdVo.extInfo) {
      crowdVo.extInfo = {};
    }
    if (typeof crowdVo.extInfo === 'string') {
      try {
        crowdVo.extInfo = JSON.parse(crowdVo.extInfo.replace(/&quot;/g, '"'));
      } catch (ex) {
        console.error(ex.message);
      }
    }
    if (!crowdVo.extInfo.UPDATE_MODEL) {
      crowdVo.extInfo.UPDATE_MODEL = {};
    }
    crowdVo.extInfo.UPDATE_MODEL.updateType = crowdVo.updateType === undefined ? 'dependence' : crowdVo.updateType;
    crowdVo.extInfo.UPDATE_MODEL.dependenceBizOption = crowdVo.extInfo.UPDATE_MODEL.updateType === 'dependence' ? 'huokebao' : undefined;
  } else if (crowdVo.extInfo) {
    if (typeof crowdVo.extInfo === 'string') {
      try {
        crowdVo.extInfo = JSON.parse(crowdVo.extInfo.replace(/&quot;/g, '"'));
      } catch (ex) {
        console.error(ex.message);
      }
    }
    if (crowdVo.extInfo.UPDATE_MODEL) {
      delete crowdVo.extInfo.UPDATE_MODEL.updateType
      delete crowdVo.extInfo.UPDATE_MODEL.dependenceBizOption
    }
  }
  return crowdVo;
}
