import React, { useState, useContext, useEffect } from 'react';
import { Sortable<PERSON>ontainer, SortableHandle, SortableElement, arrayMove } from 'react-sortable-hoc';
import { Table, Card, Form, DatePicker, Select, Switch, Radio, Input } from 'antd';
import dayjs from 'dayjs';
import GetUser from '@/components/GetUser';
import {
  hasMerchantCrowdName
} from '@/services/strategy';
import { MyContext } from '../MultMerchantsGpDrawer/context-manager';
import FoldIcon from '../FoldIcon';
import styles from './index.less';

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const { TextArea } = Input;

const FormItem = Form.Item;
const { Option } = Select;
const RadioGroup = Radio.Group;
const DragHandle = SortableHandle(() => <span className={styles.moveIcon}>:::</span>);

const SortableItem = SortableElement(({ value }) => {
  const [isFold, setIsFold] = useState(true);
  const { selectedTags, onValuesChange = () => {}, initialValues, editFormData } = useContext(
    MyContext
  );

  const toggleFold = () => {
    setIsFold(!isFold);
  };

  const { sellerName, sellerId } = value;
  const columns = [
    {
      title: '序号',
      render: (text, record, index) => `${index + 1}`,
    },
    {
      title: '人群名称',
      dataIndex: 'name',
      render: text => `${text}_${sellerName}`,
    },
  ];

  const disabledDate = current => {
    return current < dayjs().startOf('day') || current > dayjs() + 90 * 24 * 60 * 60 * 1000;
  };

  const [needUpdate, setNeedUpdate] = useState(initialValues.needUpdate);
  const [updateType, setUpdateType] = useState(initialValues.updateType === undefined ? 'dependence' : initialValues.updateType);
  const [dependenceBizOption, setDependenceBizOption] = useState(initialValues.dependenceBizOption || 'huokebao');

  const checkCommonName = (cvalue, callback) => {
    hasMerchantCrowdName(cvalue)
      .then(res => {
        if (res && res.success) {
          Promise.resolve();
          callback()
        } else {
          Promise.reject((res && res.msg) || '人群名称重复，请修改！');
          callback((res && res.msg) || '人群名称重复，请修改！');
        }
      })
      .catch(ex => {
        Promise.reject(ex.message);
        callback(ex.message);
      });
  }
  const expandedRowRender = record => {
    return (
      <Form
        {...FORM_ITEM_LAYOUT}
        name={sellerId}
        initialValues={
          (editFormData.id && editFormData) || {
            crowdName: `${record.name}_${sellerName}`,
            crowdDescription: `${record.name}_${sellerName}描述`,
            ...initialValues,
          }
        }
        onValuesChange={(changedValues, allValues) => {
          onValuesChange({ [`${record.code}%%${sellerId}`]: allValues });
        }}
      >
        <FormItem
          label="人群名称"
          name="crowdName"
          value={`${record.name}_${sellerName}`}
          rules={
            [
              () => ({
                validator(rule, cvalue, callback) {
                  // 如果是编辑，则判断略过最开始的
                  if (editFormData.id) {
                    if (cvalue === editFormData.crowdName) {
                      Promise.resolve();
                      callback()
                    } else {
                      checkCommonName(cvalue, callback);
                    }
                  } else {
                    checkCommonName(cvalue, callback);
                  }
                },
              }),
            ]
          }
        >
          <Input />
        </FormItem>
        <FormItem label="人群描述" name="crowdDescription">
          <TextArea allowClear />
        </FormItem>
        <FormItem label="是否更新" name="needUpdate" valuePropName="checked">
          <Switch onChange={checked => setNeedUpdate(checked)} />
        </FormItem>

        <FormItem label="更新类型" hidden={!needUpdate} name="updateType">
          <RadioGroup
            defaultValue={updateType}
            onChange={e => setUpdateType(e.target.value)}>
            <Radio value="every_day">每天更新</Radio>
            <Radio value="dependence">依赖更新</Radio>
          </RadioGroup>
        </FormItem>

        <FormItem
          label="依赖更新方"
          name="dependenceBizOption"
          hidden={!(needUpdate && updateType === 'dependence')}>
          <Select
            defaultValue={dependenceBizOption}
            onChange={updateDep => setDependenceBizOption(updateDep)}>
            <Option value="huokebao">获客宝</Option>
          </Select>
        </FormItem>

        <FormItem label="是否涉及资损" name="isLoss">
          <RadioGroup>
            <Radio value={1}>是</Radio>
            <Radio value={0}>否</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem
          label="人群使用场景"
          name="crowdApplyScene"
          extra={
            <span style={{ color: 'red' }}>
              注：非PUSH场景请选中人群匹配，否则会导致无法判断人群, 选择实时标签之后只能选择人群匹配
            </span>
          }
        >
          <Select placeholder="请选择人群使用场景" mode="multiple">
            {/* 实时标签只有人群匹配 */}
            <Option key="PUSH">PUSH</Option>
            <Option key="MATCH">人群匹配</Option>
            {/* <Option key="ANALYSIS">人群分析</Option> */}
          </Select>
        </FormItem>
        <FormItem label="管理员" name="operator">
          <GetUser mode="multiple" style={{ width: '100%' }} />
        </FormItem>
        <FormItem label="人群过期时间" name="expiredDate">
          <DatePicker
            disabledDate={disabledDate}
            format="YYYY-MM-DD HH:mm:ss"
            showTime={{
              defaultValue: dayjs('23:59:59', 'HH:mm:ss'),
            }}
          />
        </FormItem>

        <FormItem
          label="extInfo"
          name="extInfo"
          hidden />
      </Form>
    )
  };

  return (
    <li className={styles.itemContainer}>
      <Card bordered>
        <div onClick={toggleFold} className={styles.item} style={{ width: '100%', height: 60 }}>
          <div className="st-title" onClick={toggleFold}>
            {sellerName}
          </div>
        </div>

        <div style={{ position: 'absolute', top: 20, right: 32 }} onClick={toggleFold}>
          <FoldIcon isFold={isFold} />
        </div>

        <DragHandle />
        {!isFold && (
          <Table
            columns={columns}
            expandable={{
              expandedRowRender,
              expandRowByClick: true,
            }}
            rowKey={record => record.code}
            dataSource={selectedTags}
            style={{ marginTop: 50 }}
          />
        )}
      </Card>
    </li>
  );
});

const SortableList = SortableContainer(({ items = [] }) => {
  return (
    <ul style={{ padding: 0 }}>
      {items.map((item, index) => {
        return <SortableItem key={item.id} index={index} value={item} />;
      })}
    </ul>
  );
});

const SortableSection = ({ items: intialItems = [] }) => {
  const [items, setItems] = useState(intialItems);

  useEffect(() => {
    if (items !== intialItems) {
      setItems(intialItems);
    }
  }, [intialItems]);

  const onSortEnd = ({ oldIndex, newIndex }) => {
    setItems(arrayMove(items, oldIndex, newIndex));
  };

  return <SortableList distance={6} useDragHandle items={items} onSortEnd={onSortEnd} />;
};

export default SortableSection;
