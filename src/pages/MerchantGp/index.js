import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { Link }  from 'umi';
import { Card, Table, Button, Row, Popconfirm, Input, Col } from 'antd';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import ImportMerchantsModal from './components/ImportMerchantsModal';
import MultmerchantGpDrawer from './components/MultMerchantsGpDrawer';

const { Search } = Input;

@connect(state => ({ merchantGp: state.merchantGp, user: state.user }))
class MerchantGp extends PureComponent {
  handleTableChange = pagination => {
    const { dispatch } = this.props;

    dispatch({
      type: 'merchantGp/queryMerchants',
      payload: {
        pageNo: pagination.current,
      },
    });
  };

  importMerchants = values => {
    const { dispatch } = this.props;

    dispatch({
      type: 'merchantGp/batchAddMerchants',
      payload: {
        rawData: values.merchants,
      },
    });
  };

  onSelectChange = (keys, rows) => {
    const {
      dispatch,
      merchantGp: { selectedRows },
    } = this.props;
    const selectedRowKeys = keys;
    let sr = rows.filter(item => item);

    if (keys.length !== sr.length) {
      sr = keys.map(k => selectedRows.find(r => r.id === k) || sr.find(r => r.id === k));
    }

    dispatch({
      type: 'merchantGp/updateState',
      payload: {
        selectedRowKeys,
        selectedRows: sr,
      },
    });
  };

  batchAddCrowdByMerchant = data => {
    const { dispatch } = this.props;

    dispatch({
      type: 'merchantGp/batchAddCrowdByMerchant',
      payload: data,
    });
  };

  onDelete = id => {
    const { dispatch } = this.props;
    dispatch({
      type: 'merchantGp/deleteMerchants',
      payload: id,
    });
  }

  onSearch = name => {
    const { dispatch } = this.props;
    dispatch({
      type: 'merchantGp/search',
      payload: name,
    });
  }

  render() {
    const {
      merchantGp: {
        loading,
        pagination,
        importMerchantsVisible,
        multMerchatsGpVisible,
        merchants,
        selectedRowKeys,
        selectedRows,
        tagCategories,
      },
      user: { currentUser },
      dispatch,
    } = this.props;

    const columns = [
      {
        title: '商家ID',
        dataIndex: 'sellerId',
        key: 'sellerId',
      },
      {
        title: '商家名称',
        dataIndex: 'sellerName',
        key: 'sellerName',
      },
      {
        title: '商家描述',
        dataIndex: 'description',
        key: 'description',
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        render: (text, record) => {
          const {
            hasCrowd
          } = record;
          return (
            <div style={{ display: 'flex', flexDirection: 'row' }}>
              <Link to={`/crowd-stategy/merchant-gather-person/merchant-crowd/${record.sellerId}`}>
                去圈人
              </Link>
              {
                !hasCrowd ? (
                  <div style={{ marginLeft: '12px', color: '#1890ff' }} onClick={() => {}}>
                    <Popconfirm
                      title="商家删除后，无法恢复，请再次确认"
                      onConfirm={() => this.onDelete(record.sellerId)}
                      okText="继续执行"
                      cancelText="取消">
                      <a>删除</a>
                    </Popconfirm>
                  </div>
                ) : null
              }
            </div>
          );
        },
      },
    ];

    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange,
    };
    return (
      <PageHeaderWrapper
        inner
        breadcrumbNameMap={{
          '/': { path: '/', redirect: '/develop-tool/entity' },
          '/crowd-stategy/merchant-gather-person': {
            path: '/crowd-stategy/merchant-gather-person',
            name: '商家圈人',
            component: './MerchantGp',
          },
        }}
      >
        <Row style={{ marginBottom: 10 }} justify="space-between">
          <Col span="12">
            <Button
              type="primary"
              style={{ marginRight: 10 }}
              onClick={() => {
                dispatch({
                  type: 'merchantGp/onOpenImportMerchantsModal',
                });
              }}
            >
              导入商家
            </Button>
            <Button
              disabled={!selectedRowKeys.length}
              type="primary"
              onClick={() => {
                dispatch({
                  type: 'merchantGp/onOpenMultMerchatsGpDrawer',
                });
              }}
            >
              {!selectedRowKeys.length ? '批量圈人（请先选择商家）' : '批量圈人'}
            </Button>
          </Col>
          <Col span="7" align="right">
            <Search
              placeholder="请输入商家名称"
              onSearch={this.onSearch}
              style={{ width: 300 }}
            />
          </Col>
        </Row>
        <Card bordered>
          <Table
            size="small"
            loading={loading}
            bordered
            dataSource={merchants}
            rowKey={record => record.id}
            rowSelection={rowSelection}
            columns={columns}
            pagination={pagination}
            onChange={this.handleTableChange}
          />
        </Card>

        <ImportMerchantsModal
          visible={importMerchantsVisible}
          onSubmit={this.importMerchants}
          onCancel={() => {
            dispatch({
              type: 'merchantGp/onCancelImportMerchantsModal',
            });
          }}
        />

        <MultmerchantGpDrawer
          visible={multMerchatsGpVisible}
          selectedMerchants={selectedRows}
          tagCategories={tagCategories}
          currentUser={currentUser}
          onSubmit={this.batchAddCrowdByMerchant}
          onClose={() => {
            dispatch({
              type: 'merchantGp/onCloseMultMerchatsGpDrawer',
            });
          }}
        />
      </PageHeaderWrapper>
    );
  }
}

export default MerchantGp;
