import {
  Button,
  Tree,
  Menu,
  Dropdown,
  Tag,
  Modal,
  Form,
  Input,
  message,
  Pagination,
  Empty,
  Tooltip,
  Spin,
} from 'antd';
import styles from './index.less';
import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  VerticalAlignTopOutlined,
  EditOutlined,
  DeleteOutlined,
  SmallDashOutlined,
} from '@ant-design/icons';
import { useEffect, useState } from 'react';
import AddCrowdGroupModal from '../components/AddCrowdGroupModal';

import { connect } from 'dva';

const { TreeNode } = Tree;

const LeftTree = props => {
  const {
    setIsFold,
    isFold,
    dispatch,
    crowdGroup: { dataList, loading, total, pageSize, pageNo, modelConfig, categoryId },
    crowdCircle: { searchParams, tabKey },
  } = props;
  const [form] = Form.useForm();

  useEffect(() => {
    dispatch({
      type: 'crowdGroup/fetchData',
      payload: {
        pageNo: 1,
      },
    });
  }, []);

  const handleFinish = values => {
    const data = {
      ...values,
      type: 'CROWD',
    };
    if (modelConfig.isEdit) {
      data.id = modelConfig.record.key;
    }
    dispatch({
      type: 'crowdGroup/operateCrowdGroup',
      payload: { ...data, isEdit: modelConfig.isEdit },
    }).then(res => {
      if (res.success) {
        form.resetFields();
      }
    });
  };

  const onFold = () => {
    setIsFold(true);
  };

  const onUnFold = () => {
    setIsFold(false);
  };

  // 保存搜索参数到 localStorage，包含分组信息
  const saveSearchParams = (params) => {
    try {
      localStorage.setItem('crowdCircle_searchParams', JSON.stringify(params));
    } catch (error) {
      console.error('保存筛选条件失败:', error);
    }
  };

  const treeMenu = item => (
    <Menu>
      <Menu.Item>
        <div
          onClick={event => {
            event.stopPropagation();
            dispatch({
              type: 'crowdGroup/operateCrowdGroup',
              payload: {
                isTop: item.isTop ? false : true,
                id: item.key,
                name: item.title,
                type: 'CROWD',
                isEditTop: true,
                isEdit: true,
              },
            });
          }}
        >
          <VerticalAlignTopOutlined
            style={{
              marginRight: 8,
            }}
          />
          {item.isTop ? '取消置顶' : '置顶'}
        </div>
      </Menu.Item>
      <Menu.Item>
        <div
          onClick={event => {
            event.stopPropagation();
            dispatch({
              type: 'crowdGroup/updateState',
              payload: {
                modelConfig: {
                  visible: true,
                  isEdit: true,
                  record: item,
                },
              },
            });
            form.setFieldsValue({
              name: item.title,
            });
          }}
        >
          <EditOutlined
            style={{
              marginRight: 8,
            }}
          />
          编辑
        </div>
      </Menu.Item>
      <Menu.Item>
        <div
          onClick={event => {
            event.stopPropagation();
            dispatch({
              type: 'crowdGroup/deleteCrowdGroup',
              payload: {
                id: item.key,
              },
            }).then(res => {
              if (res) {
                dispatch({
                  type: 'crowdCircle/pageQuery',
                  payload: {
                    ...searchParams,
                    tabKey,
                    pageNo: 1,
                  },
                });
                dispatch({
                  type: "crowdGroup/fetchData",
                  payload: {
                    pageNo: 1,
                  },
                })
              }
            });
          }}
        >
          <DeleteOutlined
            style={{
              marginRight: 8,
            }}
          />
          删除
        </div>
      </Menu.Item>
    </Menu>
  );

  const renderTreeNodes = () => {
    return [{ title: '全部', key: '-1' }, ...(dataList || [])].map(item => {
      return (
        <TreeNode
          key={item.key}
          title={
            <div className={styles.treeNode} style={{}}>
              {item.isTop && <Tag color="#BA3C7C">置顶</Tag>}
              <div className={styles.rightContent}>
                <div className={styles.titleContainer}>
                  <Tooltip title={item.title.length > 4 ? item.title : ''}>
                    <span className={styles.title}>{item.title}</span>
                  </Tooltip>
                </div>
                {item.key !== '-1' && (
                  <Dropdown overlay={treeMenu(item)}>
                    <SmallDashOutlined />
                  </Dropdown>
                )}
              </div>
            </div>
          }
        />
      );
    });
  };

  return (
    <div className={styles.leftTree}>
      <Spin spinning={loading}>
        <div
          style={{
            justifyContent: isFold ? 'end' : 'space-between',
          }}
          className={styles.fold}
        >
          {!isFold && (
            <Button
              type="primary"
              onClick={() =>
                dispatch({
                  type: 'crowdGroup/updateState',
                  payload: {
                    modelConfig: {
                      visible: true,
                      isEdit: false,
                      record: {},
                    },
                  },
                })
              }
            >
              新增分组
            </Button>
          )}
          {isFold ? (
            <MenuUnfoldOutlined onClick={onUnFold} />
          ) : (
            <MenuFoldOutlined onClick={onFold} />
          )}
        </div>
        <Tree
          style={{ display: isFold ? 'none' : 'block' }}
          onSelect={e => {
            dispatch({
              type: 'crowdGroup/updateState',
              payload: {
                categoryId: e[0],
              },
            });
            const data = {
              ...searchParams,
              tabKey,
              pageNo: 1,
            };
            if (e[0] !== '-1' && e[0]) {
              data.groupId = Number(e[0]);
            } else {
              delete data.groupId;
            }

            // 保存搜索参数，包含分组信息
            saveSearchParams({
              ...data,
              categoryId: e[0], // 保存选中的分组ID
            });

            dispatch({
              type: 'crowdCircle/pageQuery',
              payload: data,
            });
          }}
          selectedKeys={[categoryId]}
          blockNode
        >
          {renderTreeNodes()}
        </Tree>
        {total > 20 && (
          <Pagination
            style={{ display: isFold ? 'none' : 'block' }}
            size="small"
            total={total}
            onChange={(page, pageSize) => {
              dispatch({
                type: 'crowdGroup/fetchData',
                payload: {
                  pageNo: page,
                },
              });
            }}
            defaultPageSize={20}
            defaultCurrent={1}
            current={pageNo}
          />
        )}
      </Spin>
      {modelConfig.visible && <AddCrowdGroupModal handleFinish={handleFinish} form={form} />}
    </div>
  );
};

export default connect(({ crowdGroup, crowdCircle }) => ({
  crowdGroup,
  crowdCircle,
}))(LeftTree);
