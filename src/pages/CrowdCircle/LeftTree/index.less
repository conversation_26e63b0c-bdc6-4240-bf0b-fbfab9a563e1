.leftTree {
    padding-right: 10px;
    :global{
        .ant-tree-switcher {
            display: none;
        }
        .ant-pagination {
            text-align: end;
        }
    }
    .treeNode {
        display: flex;
        justify-content: space-between;
        align-items: center;
        :global{
            .ant-tag {
                padding:0 4px;
                font-size: 10px;
                line-height: 14px;
            }
        }
        .rightContent {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            .titleContainer {
                display: flex;
                align-items: center;
                .title {
                    display: block;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    width: 68px;
                }
                .entityCnt {

                }
            }
        }
    }
    .fold {
        display: flex;
        align-items: center;
        height: 32px;
        margin-bottom: 10px;

        :global {
            .ant-btn {
                width: 80%;
            }
        }
    }
}
