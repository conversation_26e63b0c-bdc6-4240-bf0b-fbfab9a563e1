import MyIcon from '@/components/MyIcon';
import { getQueryParams } from '../common/utils';
import { connect } from 'dva';
import { useEffect, useState } from 'react';
import error from '@/pages/Exception/models/error';
import { Spin } from 'antd';
import styles from './index.less';

const CrowdOperation = ({ dispatch }) => {
  const { crowdIds, days, alarmType } = getQueryParams();
  const [pageConfig, setPageConfig] = useState({
    isSuccess: false,
    errorMsg: '',
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setLoading(true);
    dispatch({
      type: 'crowdCircle/crowdCircleQuickPostpone',
      payload: {
        crowdIds,
        days,
        alarmType,
      },
    }).then(res => {
      if (res) {
        setPageConfig({
          isSuccess: res.data,
          errorMsg: res.msg,
        });
      }
      setLoading(false);
    });
  }, []);

  return (
    <div className={styles.crowdOperation}>
      <Spin spinning={loading} tip="加载中">
        {!loading && (
          <>
            <div className={styles.title}>
              <MyIcon
                style={{ fontSize: 28 }}
                type={`icon-${pageConfig.isSuccess ? 'success' : 'error'}`}
              />
              <h2 style={{ margin: '0 0 0 8px' }}>操作{pageConfig.isSuccess ? '成功' : '失败'}</h2>
            </div>
            <p className={styles.subTitle}>人群ID：{crowdIds || '-'}</p>
            <p className={styles.subTitle}>
              操作提醒：
              {pageConfig.isSuccess ? `延期至${days || '-'}天后` : pageConfig.errorMsg || '-'}
            </p>
          </>
        )}
      </Spin>
    </div>
  );
};

export default connect(state => ({
  crowdCircle: state.crowdCircle,
}))(CrowdOperation);
