export const crowdBuildErrMsg = [{
  10000: '构建成功'
}, {
  10001: '人群数量为空'
}, {
  10002: '生成OSS文件出错'
}, {
  10003: '生成TFS文件出错'
}, {
  10004: '同步LINDORM出错'
}, {
  10005: '使用已下线标签'
}, {
}, {
  10006: '同步LINDORM用于分页查询出错'
}, {
}, {
  10007: '人群数据回写Hologres失败'
}, {
}, {
  10008: '人群数据回写ODPS失败'
}, {
  11001: 'PAI任务执行失败'
}, {
  11002: '参数转换失败'
}, {
  11003: '种子人群异常'
}, {
  11004: '过滤人群异常'
}, {
  12001: '权限异常'
}, {
  12002: 'ODPS任务执行失败'
}, {
  12003: '参数错误，请检查是否缺少分区和字段'
}, {
  13001: 'ADB任务执行失败'
}, {
  14001: '运算人群相同'
}, {
  14002: '运算人群为空'
}, {
  14003: '未知运算符'
}, {
  15001: '文件内容非法'
}, {
  15002: '超出文件大小限制'
}, {
  15003: '文件不存在'
}]

const crowdBuildErrMsgMap = {}
crowdBuildErrMsg.forEach(item => {
  const key = Object.keys(item);
  crowdBuildErrMsgMap[key] = item[key];
});

export const crowdErrMsgMap = {
  ...crowdBuildErrMsgMap,
}

// 人群列表类型文案
export const getCrowdTypeText = (type, text) => {
  switch (type) {
    case 'TAOBAO_USER':
      return `${text}-淘宝id`
    case 'SCRM_USER':
      return `${text}-Oneid`
    case 'DEVICE':
      return `${text}-设备id`
  }
}


// 审批类型枚举
export const approvalApplySceneEnum = {
  FUND_LOSS: 'FUND_LOSS', // 资损场景
  CROWD_UPDATE: 'CROWD_UPDATE', // 人群创建/更新场景
  CROWD_EXPORT: 'CROWD_EXPORT' // 人群导出场景
}

export const PROFILE_TYPE = {
  LABEL_CROWD: '标签圈人',
  IMPORT_CROWD: '人群导入',
  OPERATE_CROWD: '人群组合',
  ALGO_CROWD: '诸葛池圈人',
  FILE_CROWD: '文件上传',
  ALGO_ENLARGE_CROWD: '人群放大',
  TRIP_GALAXY_CROWD: '星辰池圈人',
  ODPS_TABLE_CROWD: 'ODPS导入',
  ODPS_SQL_CROWD: 'SQL圈人',
  TRANSITION_CROWD: '跃迁圈人',
  BUCKET_CHILD: '实验分组'
}


//人群类型枚举
export const ProfileCodeEnum = {
  "USER": "淘宝id",
  "DEVICE": "设备id",
  "SCRM_USER": "scrm用户画像",
  "GALAXY": "星辰物料画像"
}
