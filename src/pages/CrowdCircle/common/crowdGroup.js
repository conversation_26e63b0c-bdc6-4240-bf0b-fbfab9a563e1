const OPERATOR_MAP_REVERSE = {
  AND: 'and',
  OR: 'or',
  NOTIN: '-',
};

// 提交表单的标签值
export const setCrowdGroup = (tagGroups, OPERATOR_MAP) => {
  let outerExpression = ''; // 标签组表达式
  let realTime = 0;

  const group = tagGroups.map((tg, index) => {
    let innerExpression = '';
    let crowdAmount = ""
    const { selectedTags, operator } = tg;

    const tagGroupName = String.fromCharCode(65 + index);
    if (index > 0) {
      outerExpression += ` ${OPERATOR_MAP[operator]} `;
    }
    outerExpression += tagGroupName;

    if (selectedTags && selectedTags.length > 0) {
      const innerLabels = selectedTags.map((st, i) => {
        if (i > 0) {
          innerExpression += ` ${OPERATOR_MAP[st.operator]} `;
        }
        if(i > 0){
          crowdAmount += `,${st.crowdAmount ?? "暂无"}`
          realTime += `,${st.realTime ?? "0"}`
        } else {
          crowdAmount = `${st.crowdAmount ?? "暂无"}`
          realTime = `${st.realTime ?? "0"}`
        }
        innerExpression += st.name ?? "";
        return innerExpression;
      });

      return {
        name: tagGroupName,
        expression: innerExpression,
        realTime,
        crowdAmount
      };
    }

    return {
      name: tagGroupName,
      expression: innerExpression,
    };
  });

  return { group, outerExpression };
};

export const extractValues = input => {
  let result = {
    name: '',
    realTime: 0,
    crowdAmount: 0,
  };

  if (input.includes('/')) {
    const regex = /^(.*?)\/(.*?)_(.*)$/;
    const match = input.match(regex);

    if (match) {
      result.name = match[1].trim();
      result.realTime = Number(match[2]) || 0;
      result.crowdAmount = Number(match[3]) || 0;
    }
  } else {
    result.name = input.trim();
  }

  return result;
};

export function transform(conditions) {
  const { expression, group } = conditions;
  const outerExpression = {};

  if (expression && group && group.length > 0) {
    const arr = expression.split(' ');

    let c = 0;
    for (let i = 0; i < arr.length; i += 2) {
      outerExpression[c] = i > 0 ? OPERATOR_MAP_REVERSE[arr[i - 1]] : 'and';
      c += 1;
    }

    const tagGroups = group.map((g, index) => {
      const innerExpression = {};
      const a = g.expression.split(' ');

      let d = 0;
      for (let j = 0; j < a.length; j += 2) {
        innerExpression[d] = j > 0 ? OPERATOR_MAP_REVERSE[a[j - 1]] : 'and';
        d += 1;
      }

      const newLabel = a.filter(b => !['AND', 'OR', "NOTIN"].includes(b));
      
      return {
        operator: outerExpression[index],
        selectedTags: newLabel.map((v, idx) => {
          const crowdAmountValue = String(g?.crowdAmount || "").split(',')[idx]
          const realTimeValue = String(g?.realTime || "0").split(',')[idx]
          return {
            name: v,
            operator: innerExpression[idx],
            realTime: realTimeValue !== "undefined" && realTimeValue !== "null" && realTimeValue !== "" ? realTimeValue === "0" ? 0 : 1 : 0,
            crowdAmount: crowdAmountValue !== "undefined" && crowdAmountValue !== "null" && crowdAmountValue !== "" && crowdAmountValue !== "暂无" ? crowdAmountValue : null
          };
        }),
      };
    });

    return tagGroups;
  }

  return [];
}
