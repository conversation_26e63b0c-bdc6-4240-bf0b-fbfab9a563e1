import { queryLabelEnumValueBuildTree } from '@/services/api';
import dayjs from 'dayjs';
import { cloneDeep, get } from 'lodash';

/**
 * 实时hsf自定义类型&离线自定义标签
 * @param {*} params // 用于构建表单
 * @param {*} changeState // 用于修改状态
 */
export const hsfOrSqlParams = (params, changeState) => {
  (params || [])?.forEach(ele => {
    if (['SINGLE_ENUM', 'ENUM']?.includes(ele.paramType)) {
      queryLabelEnumValueBuildTree({ dimMetaId: ele.enumId })
        .then(res => {
          if (!res.success) {
            message.error(res.msg);
            return;
          }

          const o = res.data && res.data.root && res.data.root.children;
          ele.enumList = o.map(item => ({ label: item.name, value: item.enumCode }));
          changeState(params);
        })
        .catch(() => {
          message.error('请求失败');
        });
    } else {
      changeState(params);
    }
  });
};

/**
 * 判断是否存在hsf的自定义标签
 * @param {*} tag
 */
export const isHsfCustom = tag =>
  Boolean(get(tag, 'sourceConfig.apiSourceExtInfo.hsfParams', null) || get(tag, 'hsfParams', null));

/**
 * 判断标签表单返回的数据
 */

export const getSelectEnumData = record => {
  if (record?.otherData) {
    return record?.otherData;
  }

  return record;
};

const setHsfFilterValue = (value, type) => {
  if (!value?.length) return;
  if (['SINGLE_ENUM', 'ENUM'].includes(type)) {
    const newValue = (value || [])?.map(ele => ele?.split(' ')[0]);
    return type === 'SINGLE_ENUM' ? newValue[0] : JSON.stringify(newValue);
  }

  return value[0];
};

// 设置自定义标签
export const setCustomTagParams = (values, type) => {
  let params =
    values &&
    values.map(item => {
      let newObj = {
        name: item.name,
        value: item.value,
        description: item.description,
        paramType: item.paramType,
        valueSource: item?.valueSource,
        hsfFilterValue: type === 'hsf' ? setHsfFilterValue(item.value, item.paramType) : undefined,
      };
      if (['ENUM', 'SINGLE_ENUM'].includes(item.paramType)) {
        Object.assign(newObj, {
          enumId: item.enumId,
        });
      }
      return newObj;
    });
  return params;
};

// 时空标签标签值
const setTimeSpaceLabel = st => {
  const { editFormData, description, code, dataType, name, propertyName, propertyDescription, labelParamsData } = st;
  const data = editFormData?.data || labelParamsData || [];
  let labelParams = [];
  let labelExpression = '';

  data?.forEach((ele, index) => {
    const { paramLabel, behaviorCode, paramCode, logicRelation, ...other } = ele;
    if (index > 0) {
      labelExpression += ` ${logicRelation} `;
    }

    labelExpression += `TRADE_SUCCESS.${behaviorCode}.${paramCode}`;

    labelParams.push({
      ...other,
      behaviorCode,
      paramCode,
      logicRelation,
    });
  });

  return {
    labelExpression,
    labelParams,
    name: code || propertyName,
    desc: name || propertyDescription,
    type: dataType,
  };
};

// 提交表单的标签值
export const setTagGroup = (tagGroups, OPERATOR_MAP) => {
  let outerExpression = ''; // 标签组表达式

  const group = tagGroups.map((tg, index) => {
    let innerExpression = ''; // 标签组内部表达式
    let label = [];
    const { selectedTags, operator, profileType } = tg;

    const tagGroupName = String.fromCharCode(65 + index);
    if (index > 0) {
      outerExpression += ` ${OPERATOR_MAP[operator]} `;
    }

    outerExpression += tagGroupName;

    if (selectedTags && selectedTags.length > 0) {
      label = selectedTags.map((st, i) => {
        if (i > 0) {
          innerExpression += ` ${OPERATOR_MAP[st.operator]} `;
        }

        innerExpression += st.code || st.propertyName;
        if (st.dataType === 'NONE_TYPE') {
          let params = setCustomTagParams(st?.values, 'sql');
          return {
            name: st.code || st.propertyName,
            desc: st.name || st.propertyDescription,
            type: st.dataType,
            params,
          };
        } else if (st.dataType === 'JSON_OBJECT') {
          return setTimeSpaceLabel(st);
        } else {
          const labelData = {
            name: st.code || st.propertyName,
            desc: st.name || st.propertyDescription,
            type: st.dataType,
            dimEnumMetaId: st.dimMetaId,
            value: (st.values || []).map(item => {
              let res = '';

              if (st.dataType === 'ENUM' || st.dataType === 'MULTI_VALUE') {
                res = `${item.value} ${item.label}`;
              } else {
                res = item.value;
              }
              return res;
            }),
          };

          if (st.hsfValues) {
            Object.assign(labelData, {
              params: setCustomTagParams(st.hsfValues, 'hsf'),
            });
          }
          return labelData;
        }
      });
    }

    return {
      name: tagGroupName,
      expression: innerExpression,
      profileType: profileType ?? "tbup",
      label,
    };
  });

  return { group, outerExpression };
};

/**
 * 设置标签回填
 * @param {*} data
 * @returns
 */
export const setTagParams = (data) => {
  let editFormData = {}
  let values = []
  let params = []
  data && data.forEach((item) => {
    if (['TEXT', 'TIME'].includes(item.paramType)) {
      editFormData[item.name] = item.value.toString()
      values.push({ label: `${item.description}:${item.value}`, value: item.value, name: item.name, description: item.description, paramType: item.paramType, valueSource: item?.valueSource })
      params.push({ name: item.name, description: item.description, paramType: item.paramType, valueSource: item?.valueSource })
    } else if (['ENUM', 'SINGLE_ENUM'].includes(item.paramType)) {
      let newList = item.value.map(ele => {
        return ele.split(' ')[0];
      });
      editFormData[item.name] = newList
      values.push({ label: `${item.description}:${item.value.map(ele => ele.split(' ')[1])}`, value: item.value, name: item.name, description: item.description, paramType: item.paramType, enumId: item.enumId, valueSource: item?.valueSource })
      params.push({ name: item.name, description: item.description, paramType: item.paramType, enumId: item.enumId, valueSource: item?.valueSource })
    } else if (item.paramType === 'NUMBER') {
      editFormData[item.name] = Number(item.value)
      values.push({ label: `${item.description}:${item.value}`, value: item.value, name: item.name, description: item.description, paramType: item.paramType, valueSource: item?.valueSource })
      params.push({ name: item.name, description: item.description, paramType: item.paramType, valueSource: item?.valueSource})
    } else if (item.paramType === 'DATE') {
      editFormData[item.name] = item.value.toString()
      values.push({ label: `${item.description}:${item.value[0]}`, value: item.value, name: item.name, description: item.description, paramType: item.paramType, valueSource: item?.valueSource })
      params.push({ name: item.name, description: item.description, paramType: item.paramType, valueSource: item?.valueSource })
    }
  })

  return {
    values,
    editFormData,
    params,
  };
};

/**
 * 设置时空标签回填标签回填
 */
export const setTimeSpaceLabelParams = data => {
  let editFormData = {};
  let values = [];
  let newData = cloneDeep(data)
  let firstItem = newData.shift(); // 第一项
  let restItems = newData; // 剩余项
  const { paramValue, behaviorDesc, paramDesc, paramCode, behaviorCode } = firstItem;
  const [startDate, endDate] = paramValue || [];
  const newParamValue = paramValue[0]?.toString()

  if (paramValue.length > 1) {
    values.push(
      { label: `开始时间:${startDate}`, value: startDate },
      { label: `结束时间:${endDate}`, value: endDate }
    );

    Object.assign(editFormData, {
      dateType: 'fixed',
      date: [dayjs(startDate), dayjs(endDate)]
    });
  } else if (newParamValue?.substring(0, 1) === '-') {
    values.push({ label: `最近${newParamValue?.substring(1)}天`, value: newParamValue });

    Object.assign(editFormData, {
      dateType: 'relativePast',
      pastTime: parseInt(newParamValue?.substring(1), 10),
    });
  } else if (paramValue?.length === 1 && Number(paramValue[0]) === 0) {
    values.push({ label: '当天', value: Number(paramValue[0]) });

    Object.assign(editFormData, {
      dateType: 'intraday',
      pastTime: Number(paramValue[0]),
    });
  } else {
    values.push({ label: `未来${newParamValue}天`, value: newParamValue });

    Object.assign(editFormData, {
      dateType: 'relativeFuture',
      futureTime: parseInt(newParamValue, 10),
    });
  }

  if (paramDesc && behaviorDesc) {
    values.push(
      {
        label: `时间类型:${paramDesc}`,
        value: paramDesc,
      },
      {
        label: `行为:${behaviorDesc}`,
        value: behaviorDesc,
      }
    );
    Object.assign(editFormData, {
      behaviorCode,
      timeType: paramCode,
    })
  }


  let additional = restItems?.length && restItems.map(item => {
    const { paramCode, paramValue, sameBehaviorTag, behaviorCode, paramOffsetMode, paramOffsetTs, paramDimEnumMetaId, paramDesc, logicRelation } = item
    if (paramDesc === '同目的地行为') {
      return {
        logicRelation,
        sameBehaviorTag,
        behaviorCode,
        paramOffsetMode,
        paramCode: `${paramCode}-${paramDesc}`,
        paramOffsetTs: paramOffsetTs ? paramOffsetTs / 3600000 : 0,
      }
    }
    return {
      paramCode,
      paramDimEnumMetaId,
      values: paramValue
    }
  });

  Object.assign(editFormData, {
    additional
  })

  return { values, editFormData}
};

/**
 * 展示标签
 * @param {*} data
 */
export const setTagShow = tag => {
  if (tag.values && tag.values.length) {
    if (tag.hsfValues && tag.hsfValues.length) {
      return [...tag.hsfValues, ...tag.values];
    }
    return tag.values;
  }

  return [];
};

/**
 * 获取hsf自定义标签
 */
export const getHsfParams = (tag) => get(tag, 'sourceConfig.apiSourceExtInfo.hsfParams', null) || get(tag, 'hsfParams', null)
export const getHsfServiceParams = (tag) => tag?.hsfServiceParams || get(tag, 'sourceConfig.apiSourceExtInfo.serviceParams', [])

export const setHsfOrSqlEnumValue = data => {
  let newData = [];
  let { mock } = data;
  delete data.mock;
  mock.forEach((ele, i) => {
    Object.keys(data).forEach((key, j) => {
      if (ele.name === key) {
        ele.value = data[key];
      }
    });
  });

  mock.forEach(ele => {
    if (['ENUM', 'SINGLE_ENUM'].includes(ele.paramType)) {
      let newObj = [];
      const value = Array.isArray(ele.value) ? ele.value : [ele.value]
      value.forEach(n => {
        ele.enumList.forEach(t => {
          if (n === t.value) {
            newObj.push(t);
          }
        });
      });
      ele.value = newObj;
    }
  });

  mock.forEach(ele => {
    if (['TEXT', 'TIME'].includes(ele.paramType)) {
      newData.push({
        label: `${ele.description}:${ele.value}`,
        value: [ele.value],
        name: ele.name,
        description: ele.description,
        paramType: ele.paramType,
        valueSource: ele?.valueSource,
      });
    } else if (['SINGLE_ENUM', 'ENUM'].includes(ele.paramType)) {
      newData.push({
        label: `${ele.description}:${ele.value.map(e => e.label)}`,
        value: ele.value.map(e => `${e.value} ${e.label}`),
        name: ele.name,
        description: ele.description,
        paramType: ele.paramType,
        enumId: ele.enumId,
        valueSource: ele?.valueSource,
      });
    } else if (ele.paramType === 'NUMBER') {
      newData.push({
        label: `${ele.description}:${ele.value}`,
        value: [ele.value],
        name: ele.name,
        description: ele.description,
        paramType: ele.paramType,
        valueSource: ele?.valueSource,
      });
    } else if (ele.paramType === 'DATE') {
      newData.push({
        label: `${ele.description}:${ele.value}`,
        value: [ele.value],
        name: ele.name,
        description: ele.description,
        paramType: ele.paramType,
        valueSource: ele?.valueSource,
      });
    }
  });

  return newData;
};

/**
 * 递归过滤hsf中的valueSource
 */
export const getFlattenParams = serviceParams => {
  let result = [];

  function recursiveFlatten(params) {
    for (let param of params) {
      if (param?.valueSource) {
        result.push({ paramName: param?.paramName, valueSource: param?.valueSource });
      }
      if (param?.subParams && param?.subParams.length > 0) {
        recursiveFlatten(param?.subParams);
      }
    }
  }

  recursiveFlatten(serviceParams);
  return result;
};

// 时空标签提交
export const timeSpaceLabelSubmit = (
  data,
  conditionOptions,
  behaviorTypeOptions,
  timeTypeOptions
) => {
  const firstParam = {}; // 拼接第一项
  let labelParams = [];
  let firstParamLabel = [];
  let firstParamValue = [];
  const { dateType, date, pastTime, futureTime, behaviorCode, timeType, additional = [] } = data;
  let [startDate, endDate] = date || [];

  if (dateType === 'fixed') {
    (startDate = startDate.format('YYYY-MM-DD')), (endDate = endDate.format('YYYY-MM-DD'));
    firstParamLabel = [
      { label: `开始时间:${startDate}`, value: startDate },
      { label: `结束时间:${endDate}`, value: endDate },
    ];
    firstParamValue = [startDate, endDate];
  } else if (dateType === 'relativePast') {
    firstParamLabel = [{ label: `最近${pastTime}天`, value: parseInt(`-${pastTime}`, 10) }];
    firstParamValue = [parseInt(`-${pastTime}`, 10)];
  } else if (dateType === 'relativeFuture') {
    firstParamLabel = [{ label: `未来${futureTime}天`, value: futureTime }];
    firstParamValue = [futureTime];
  } else if (dateType === 'intraday') {
    firstParamLabel = [{ label: '当天', value: 0 }];
    firstParamValue = [0];
  }

  const behaviorDesc = behaviorTypeOptions?.find(ele => ele?.value === behaviorCode)?.label;
  const paramDesc = timeTypeOptions?.find(ele => ele?.value === timeType)?.label;

  firstParamLabel.push(
    {
      label: `时间类型:${paramDesc}`,
      value: paramDesc,
    },
    {
      label: `行为:${behaviorDesc}`,
      value: behaviorDesc,
    }
  );

  Object.assign(firstParam, {
    behaviorCode,
    behaviorDesc,
    paramDesc,
    paramCode: timeType,
    paramDataType: 'DATE',
    paramLabel: firstParamLabel,
    paramValue: firstParamValue,
  });

  labelParams.push(firstParam);

  let validBehaviors = additional.filter(item => item.paramCode === 'valid-同目的地行为');
  let otherItems = additional.filter(item => item.paramCode && (item.paramCode !== 'valid-同目的地行为'));

  if (otherItems?.length) {
    otherItems = otherItems?.map(ele => {
      const { paramCode, values } = ele;
      const o = conditionOptions?.find(item => item?.paramCode === paramCode) || {};
      return {
        ...o,
        behaviorCode,
        logicRelation: 'AND',
        paramValue: Array.isArray(values) ? values : [values],
      };
    });
    labelParams.push(...otherItems);
  }

  if (validBehaviors?.length) {
    validBehaviors = validBehaviors?.map(ele => {
      const { paramCode, values, logicRelation, paramOffsetMode, paramOffsetTs } = ele;
      const o =
        conditionOptions?.find(item => `${item.paramCode}-${item.paramDesc}` === paramCode) || {};

      return {
        ...o,
        logicRelation,
        paramOffsetMode,
        behaviorCode: ele?.behaviorCode,
        paramOffsetTs: paramOffsetTs ? paramOffsetTs * 3600000 : undefined,
      };
    });

    labelParams.push(...validBehaviors);
  }

  // 遍历增加
  labelParams = labelParams?.map((ele, index) => {
    return {
      ...ele,
      paramIndex: index,
    };
  });

  return labelParams;
};
