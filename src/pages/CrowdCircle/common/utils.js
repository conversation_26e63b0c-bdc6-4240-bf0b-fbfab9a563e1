import crowdApprovalTip from '../../GatherPerson/components/CrowdApprovalTip';
import { CROWD_PROGRESS_STATUS } from '@/constants';
import { approvalApplySceneEnum } from '../constants';
import { getBpmsUrl } from '@/utils/utils';
import { get } from 'lodash';

//状态跳转
export const goBpms = record => {
  if (
    record.crowdStatus === 'APPROVAL_RUNNING' ||
    record.crowdStatus === 'APPROVAL_REJECTED' ||
    record.crowdStatus === 'APPROVAL_CANCELLED' ||
    record.crowdStatus === 'APPROVAL_ERROR'
  ) {
    if (record.crowdApprovalInfo && record.crowdApprovalInfo.approvalInstanceId) {
      window.open(getBpmsUrl(record.crowdApprovalInfo.approvalInstanceId));
    }
  }
};

// 审批状态跳转
export const goApprovalBpms = record => {
  const obj = get(record, 'approvalInfo', {});
  if (
    obj.approvalStatus === 'APPROVAL_RUNNING' ||
    obj.approvalStatus === 'APPROVAL_REJECTED' ||
    obj.approvalStatus === 'APPROVAL_CANCELLED' ||
    obj.approvalStatus === 'APPROVAL_ERROR'
  ) {
    if (obj?.approvalId) {
      window.open(getBpmsUrl(obj?.approvalId));
    }
  }
};

/**
 * 展示标签
 * @param {*} data
 */
export const setTagShow = tag => {
  if (tag.values && tag.values.length) {
    if (tag.hsfValues && tag.hsfValues.length) {
      return [...tag.hsfValues, ...tag.values];
    }
    return tag.values;
  }

  return [];
};

export const getCode = (index1, index2, len) =>
  len === 1 ? String.fromCharCode(65 + index1) : `${String.fromCharCode(65 + index1)}${index2 + 1}`;

export const getExpression = list => {
  let expression = '';
  list.forEach((tg, index, arr) => {
    const { selectedTags, operator, profileType } = tg;

    if (index > 0) {
      expression += ` ${operator} `;
    }

    if (selectedTags && selectedTags.length > 1) {
      if (arr.length > 1) {
        expression += '( ';
      }

      selectedTags.forEach((st, i) => {
        if (i > 0) {
          expression += ` ${st.operator} `;
        }
        expression += getCode(index, i, selectedTags.length);
      });

      if (arr.length > 1) {
        expression += ' )';
      }
    } else {
      expression += String.fromCharCode(65 + index);
    }
  });
  return expression;
};

export const setMapToList = data => {
  const list = [];
  data?.forEach(ele => {
    const label = Object.values(ele)?.[0];
    const value = Object.keys(ele)?.[0];
    list.push({ label, value });
  });

  return list;
};

export const isHasDate = value => {
  if (!value) {
    return false;
  }
  if (
    value.includes('${date}') ||
    value.includes('${date+') ||
    value.includes('${date-') ||
    value.includes('${date +') ||
    value.includes('${date -')
  ) {
    return true;
  }
  return false;
};

export const getQueryParams = () => {
  const hash = window.location.hash.substring(1);
  
  const queryString = hash.split('?')[1];

  if (!queryString) {
    return {};
  }

  const urlParams = new URLSearchParams(queryString);
  const params = {};
  
  urlParams.forEach((value, key) => {
    params[key] = value;
  });

  return params;
}


