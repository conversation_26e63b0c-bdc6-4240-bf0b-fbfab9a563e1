import { message } from 'antd';
import {
  labelMarketSearch,
  queryBuildTree,
  queryLabelMarketSortType,
  queryCrowdCount,
  queryIsRealTime,
} from '@/services/api';
import { get } from 'lodash';
import { setMapToList } from '../common/utils';

export default {
  namespace: 'tagCircle',
  state: {
    selectTag: {}, //选择的标签
    tagGroups: [
      {
        selectedTags: [{}],
      },
    ], //已选中的标签组
    selectIndex: {
      childrenIndex: 0, // 当前选中的子标签下标
      parentIndex: 0, // 当前选中的父标签下标
    },
    selectEnumValueModalVisible: false, // 枚举值弹窗
    tabCntMap: {
      ALL: 0,
      AUTHORIZED: 0,
      COLLECTED: 0,
    },
    labelInfoList: [],
    loading: false,
    treeData: [{ id: -1, name: '全部标签' }],
    searchParams: {},
    sortType: 'DESC_BY_USE_AMOUNT',
    sortTypeList: [],
    calculating: false,
    crowdCount: '',
    isRealTime: false, //是否是实时
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(({ pathname, query }) => {
        if (pathname.includes('/crowd-stategy/gather-person/detail')) {
          if (query.id) {
            dispatch({
              type: 'updateState',
              payload: {
                selectIndex: {
                  childrenIndex: -1,
                  parentIndex: -1,
                },
              },
            });
          }
        }
      });
    },
  },
  effects: {
    *pageQuery({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });
      const data = {
        ...payload,
        pageNo: payload.pageNo ?? 1,
        pageSize: payload.pageSize ?? 10,
        classifyTab: payload.classifyTab ?? 'ALL',
        statusList: ['ACTIVATE'],
        typeList: payload.typeList ?? ['REALTIME', 'OFFLINE'],
        sortType: payload.sortType ?? 'DESC_BY_USE_AMOUNT',
      };
      const res = yield call(labelMarketSearch, data);
      if (!res?.success) {
        message.error(res?.msg);
        return;
      }
      yield put({
        type: 'updateState',
        payload: {
          tabCntMap: get(res, 'data.tabCntMap', {}),
          labelInfoList: get(res, 'data.labelInfoList', []),
          loading: false,
          searchParams: data,
        },
      });
    },
    *queryBuildTree({ payload }, { call, put }) {
      const res = yield call(queryBuildTree, {
        bizEntityName: 'TAOBAO_USER',
      });
      if (!res?.success) {
        message.error(res?.msg);
        return;
      }
      yield put({
        type: 'updateState',
        payload: {
          treeData: [
            {
              id: '-1',
              name: '全部标签',
              children: get(res, 'data.root.children', []),
            },
          ],
        },
      });
    },
    *querySortType({ payload }, { call, put }) {
      const res = yield call(queryLabelMarketSortType);
      if (!res?.success) {
        message.error(res?.msg);
        return;
      }
      yield put({
        type: 'updateState',
        payload: {
          sortTypeList: setMapToList(res?.data || []),
        },
      });
    },
    *getCrowdCount({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: {
          calculating: true,
        },
      });
      const res = yield call(queryCrowdCount, { conditions: payload });

      yield put({
        type: 'updateState',
        payload: {
          calculating: false,
        },
      });

      if (!res?.success) {
        message.error(res?.msg || '计算失败');
        return;
      }

      yield put({
        type: 'updateState',
        payload: {
          crowdCount: res?.data,
        },
      });
    },
    *getQueryIsRealTime({ payload }, { call, put }) {
      const res = yield call(queryIsRealTime, payload);
      if (!res?.success) {
        message.error(res?.msg || '获取失败');
        return;
      }

      if (!res?.data) {
        yield put({
          type: 'tagCircle/getCrowdCount',
          payload: payload.conditions,
        });
      }

      yield put({
        type: 'updateState',
        payload: {
          isRealTime: res?.data,
        },
      });
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    //判断当前标签是否出现在标签组中
    handleUpdateVisibleAndData(state, { payload }) {
      const { tagGroups, selectIndex } = state;

      const findTag = tagGroups[selectIndex.parentIndex].selectedTags.find(
        item => item.propertyDescription === payload.tag.name
      );
      if (
        findTag &&
        Object.keys(findTag).length > 1 &&
        tagGroups[selectIndex.parentIndex].selectedTags.length > 1
      ) {
        message.warning('同一标签组内不能存在相同标签！');
        return {
          ...state,
        };
      }

      return {
        ...state,
        selectEnumValueModalVisible: payload.visible,
        selectTag: payload.tag,
      };
    },
    handleUpdateTagGroups(state, { payload }) {
      const { selectIndex } = state;
      selectIndex.parentIndex = payload.length - 1 || 0;
      selectIndex.childrenIndex = payload[payload.length - 1].selectedTags.length - 1 || 0;

      return {
        ...state,
        tagGroups: payload,
        selectIndex,
      };
    },
    handleCancelModal(state, { payload }) {
      return {
        ...state,
        selectTag: {},
        selectEnumValueModalVisible: false,
      };
    },
    //更改下标
    handleSelectIndex(state, { payload }) {
      return {
        ...state,
        selectIndex: payload,
      };
    },
    //重置
    handleReset(state, { payload }) {
      return {
        ...state,
        selectTag: {},
        tagGroups: [
          {
            selectedTags: [{}],
          },
        ],
        selectIndex: {
          childrenIndex: 0,
          parentIndex: 0,
        },
      };
    },
  },
};
