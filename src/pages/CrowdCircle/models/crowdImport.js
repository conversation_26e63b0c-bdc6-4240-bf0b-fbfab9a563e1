import { message } from 'antd';
import {
  checkOdpsAuth,
  queryColumnInfos,
  checkOdpsPreTable,
  queryPartitionFields,
} from '@/services/api';

export default {
  namespace: 'crowdImport',
  state: {
    primaryKeyOptions: [],
    partitionOptions: [],
    isHaveDate: false,
  },
  effects: {
    *fetchUserKeyFields({ payload }, { call, put }) {
      const res = yield call(queryColumnInfos, payload);

      if (!res.success) {
        message.error(res.msg);
        return;
      }
      if (res.success && res.data && res.data.columns) {
        const result = (res.data.columns || [])
          .filter(item => {
            return !item.isPartitionCol;
          })
          .map(itx => {
            return {
              label: itx.columnName,
              desc: itx.columnDesc,
              value: itx.columnName,
            };
          });
        yield put({
          type: 'updateState',
          payload: {
            primaryKeyOptions: result,
          },
        });
      }
    },
    *getQueryPartitionFields({ payload }, { call, put }) {
      const res = yield call(queryPartitionFields, payload);
      if (!res.success) {
        message.error(res.msg);
        return;
      }
      if (res.success && res.data) {
        const result = (res.data || []).map(item => {
          return {
            label: item.columnName,
            value: item.columnName,
          };
        });
        yield put({
          type: 'updateState',
          payload: {
            partitionOptions: result,
          },
        });
        return result
      }
    },
    *checkOdpsAuthStatus({ payload }, { call, put }) {
      const res = yield call(checkOdpsAuth, payload);
      return res;
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
