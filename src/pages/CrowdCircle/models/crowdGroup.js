import { message } from 'antd';
import {
  createCrowdGroup,
  pageQueryCrowdGroup,
  updateCrowdGroup,
  deleteCrowdGroup,
  updateCrowdBatchGroup,
  queryCrowdGroupId,
  queryCrowdCount,
  checkOperateCntCrowdCircle,
  checkCrowdId,
  queryCrowdCircle
} from '@/services/api';
import { zhugeUrl } from '@/utils/utils';


export default {
  namespace: 'crowdGroup',
  state: {
    dataList: [], // 人群分组
    loading: false, // 加载状态,
    total: 0, // 人群分组总数
    pageNo: 1, // 当前页
    modelConfig: {
      visible: false,
      isEdit: false,
      record: {},
    },
    categoryId: '-1',
    calculating: false,
    checkLoading: false,
  },
  effects: {
    *fetchData({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });
      const res = yield call(pageQueryCrowdGroup, {
        pageSize: payload.pageSize ?? 20,
        pageNo: payload.pageNo ?? 1,
        type: 'CROWD',
      });
      if (!res.success) {
        message.error(res.msg);
        return;
      }
      yield put({
        type: 'updateState',
        payload: {
          loading: false,
        },
      });
      if (res.data && res.data.data && res.data.data.length > 0) {
        const result = res.data.data.map(item => {
          return {
            title: item.name,
            key: item.id,
            isTop: item.isTop,
            entityCnt: item.entityCnt,
          };
        });
        yield put({
          type: 'updateState',
          payload: {
            dataList: result,
            total: res.data.totalCnt,
            pageNo: payload.pageNo ?? 1,
          },
        });
      }
    },
    *operateCrowdGroup({ payload }, { call, put }) {
      const { isEdit, isEditTop, ...rest } = payload;
      const request = isEdit ? updateCrowdGroup : createCrowdGroup;
      const res = yield call(request, {
        ...rest,
      });
      let msg;
      if (isEditTop && isEdit && payload.isTop) {
        msg = '置顶人群分组';
      } else if (isEditTop && isEdit && !payload.isTop) {
        msg = '取消置顶人群分组';
      } else if (!isEditTop && !isEdit) {
        msg = '创建人群分组';
      } else if (!isEditTop && isEdit) {
        msg = '编辑人群分组';
      }
      if (!res.success) {
        message.error(res.msg || msg + '失败');
        return;
      } else {
        message.success(msg + '成功');
        yield put({
          type: 'updateState',
          payload: {
            modelConfig: {
              visible: false,
              isEdit: false,
              record: {},
            },
          },
        });
        yield put({
          type: 'fetchData',
          payload: {
            pageNo: 1,
          },
        });
        return res;
      }
    },
    *deleteCrowdGroup({ payload }, { call, put }) {
      const res = yield call(deleteCrowdGroup, payload.id);
      if (!res.success) {
        message.error(res.msg || '删除人群分组失败');
        return false;
      }
      message.success('删除人群分组成功');
      yield put({
        type: 'fetchData',
        payload: {
          pageNo: 1,
        },
      });
      return true
    },
    *updateCrowdBatchGroup({ payload }, { call, put }) {
      const res = yield call(updateCrowdBatchGroup, payload);
      if (!res.success) {
        message.error(res.msg || '分组失败');
        return;
      } else {
        message.success('分组成功');
        return res;
      }
    },
    *queryCrowdGroupId({ payload }, { call, put }) {
      const res = yield call(queryCrowdGroupId, payload);
      if (!res.success) {
        message.error(res.msg || '查询人群分组失败');
        return;
      }
      return res.data;
    },
    *checkOperateCntCrowdCircleStatus({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: {
          checkLoading: true,
        },
      });
      const res = yield call(checkOperateCntCrowdCircle, payload);
      if (!res.data) {
        message.error("该引用人群存在多层嵌套（>5个），若有特殊场景请联系诸葛产品进行沟通咨询");
        yield put({
          type: 'updateState',
          payload: {
            checkLoading: false,
          },
        });
        return false;
      }
      return true;
    },
    *checkCrowdIdStatus({ payload }, { call, put }) {
      const res = yield call(checkCrowdId, payload);
      if (!res.success) {
        message.error(res.msg || '校验失败', 5);
        yield put({
          type: 'updateState',
          payload: {
            checkLoading: false,
          },
        });
        return false;
      }
      yield put({
        type: 'updateState',
        payload: {
          checkLoading: false,
        },
      });
      message.success('校验成功');
      return true;
    },
     *queryCrowdRouterDetail({ payload }, { call, put, select }) {
      const res = yield call(queryCrowdCircle, payload);
      if (!res.success) {
        message.error(res.msg);
        return;
      }

       const data = res.data;
       if (data.crowdType === "BUCKET_CHILD") {
        message.error("实验分组不支持查看")
        return
       }
       if (data.crowdType !== 'OPERATE_CROWD') {
        window.open(
          zhugeUrl +
            `/crowd-stategy/gather-person/detail?crowdType=${data.circleType}&profileType=${data.physicalProfileCode}&id=${data.id}&type=view`
        );
      } else {
        window.open(zhugeUrl + `/crowd-stategy/gather-person?crowdId=${data.id}`);
      }
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
