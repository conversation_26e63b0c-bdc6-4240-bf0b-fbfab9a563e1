import { message } from 'antd';
import {
  delayNewCrowd,
  editExportCrowdTask,
  updateBlackWhiteListCrowdTask,
  addBlackWhiteListCrowdTask,
  getRerunCrowd,
  updateCreator,
  pageQueryCrowd,
  updateCrowdCircleManager,
  deleteCrowdCircle,
  queryblackwhitelist,
  queryCrowdById,
  editCrowdBySQL,
  updateMonitorConfig,
  queryMonitorByCrowd,
  crowdCircleQuickPostpone,
  crowdBucket,
} from '@/services/api';
import { history } from 'umi'

export default {
  namespace: 'crowdCircle',
  state: {
    record: {},
    crowdDelayModalVisible: false, //延期弹窗
    crowdTestModalVisible: false, //测试弹窗
    crowdExportModalVisible: false, //导出弹窗
    blackOrWhiteListModalVisible: false, //黑白名单弹窗
    addGroupVisible: false, //添加实验分组
    crowdMonitorVisible: false, //人群监控弹窗
    crowdTransferVisible: false, //人群转交弹窗
    tabCntMap: {
      ALL: 0,
      MY_CROWDS: 0,
    },
    loading: false,
    dataSource: [],
    searchParams: {
      pageNo: 1,
    },
    categoryId: '-1',
    modelConfig: {
      isOwner: false,
      visible: false,
      record: {},
    },
    tabKey: 'MY_CROWDS',
    isChildSearch: false,
    expandedRowKeys: [],
    detailModalVisible: false,
    curCrowd: null, // 查看详情时当前的人群
    refreshLoading: false,
    editCrowdByOperateFormData: {},
    crowdByOperateModalVisible: false,
    detailLoading: false, //老的人群组合弹窗loading
    bucketLoading: false, //人群分桶操作loading
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(({ pathname, query }) => {
        if (pathname.includes('/crowd-stategy/gather-person')) {
          if (query.crowdId) {
            dispatch({
              type: 'pageQuery',
              payload: {
                content: query.crowdId,
                tabKey: 'ALL',
              },
            });
            dispatch({
              type: 'queryCrowdById',
              payload: {
                id: query.crowdId,
                isEdit: false,
                isDetail: true,
              },
            });
          }
        }
      });
    },
  },
  effects: {
    *pageQuery({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: {
          loading: true,
          isChildSearch: false,
          expandedRowKeys: [],
          tabCntMap: {
            ALL: 0,
            MY_CROWDS: 0,
          },
        },
      });
      const data = {
        ...payload,
        classifyTab: payload.tabKey ?? 'MY_CROWDS',
        pageSize: payload.pageSize ?? 10,
        pageNo: payload.pageNo ?? 1,
        validityType: payload.validityType ?? 'ALL',
      };
      delete data.tabKey;
      const res = yield call(pageQueryCrowd, data);
      if (!res.success) {
        message.error(res.msg);
        yield put({
          type: 'updateState',
          payload: {
            loading: false,
          },
        });
        return;
      }
      if (res.data && res.success) {
        yield put({
          type: 'updateState',
          payload: {
            dataSource: res.data.crowdInfoList,
            tabCntMap: res.data.tabCntMap,
            loading: false,
            searchParams: {
              ...data,
              pageNo: payload.pageNo,
            },
            tabKey: payload.tabKey,
            isChildSearch: res.data.isChildSearch,
            expandedRowKeys: res.data.isChildSearch
              ? (res.data.crowdInfoList || []).map(item => item.id)
              : [],
          },
        });
      }
    },
    *editCrowdDelayTime({ payload }, { call, put, select }) {
      const data = payload;
      const { searchParams, tabKey } = yield select(state => state.crowdCircle);

      const res = yield call(delayNewCrowd, data);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success('延期成功');

      yield put({
        type: 'pageQuery',
        payload: {
          ...searchParams,
          tabKey,
        },
      });

      yield put({
        type: 'updateState',
        payload: {
          crowdDelayModalVisible: false,
          record: {},
        },
      });
    },
    *editExportCrowdTask({ payload }, { call, put, select }) {
      const data = payload;
      const isUpdate = !!data.id;
      const { searchParams, tabKey } = yield select(state => state.crowdCircle);
      const { currentUser } = yield select(state => state.user);
      const res = yield call(editExportCrowdTask, {
        ...data,
        exportCreator: { empId: currentUser.workId, nickName: currentUser.name },
      });

      if (!res.success) {
        message.error(res.msg);
        return res;
      }

      message.success(isUpdate ? '导出任务更新成功' : '导出任务创建成功');

      yield put({
        type: 'updateState',
        payload: {
          crowdExportModalVisible: false,
          record: {},
        },
      });
      yield put({
        type: 'pageQuery',
        payload: {
          ...searchParams,
          tabKey,
        },
      });

      return res;
    },
    *queryblackwhitelist({ payload }, { call, put, select }) {
      const res = yield call(queryblackwhitelist, payload);

      if (!res.success) {
        return;
      }
      if (res.data) {
        const groupId = res.data ? res.data.groupId : null;
        const whiteList = res.data ? res.data.whiteList : '';
        const blackList = res.data ? res.data.blackList : '';
        const id = res.data ? res.data.id : null;

        yield put({
          type: 'updateState',
          payload: {
            record: res.data,
          }, // 群组类型，目前均默认为：TAOBAO_USER
        });
      }
    },
    *editBlackWhiteListCrowdTask({ payload }, { call, put, select }) {
      const { record, searchParams, tabKey } = yield select(state => state.crowdCircle);

      if (record.id) {
        // 更新群组黑白名单
        const data = {
          groupId: record.id,
          operator: record.operator,
          groupId: record.groupId,
          groupType: 'TAOBAO_USER',
          id: record.id,
        };
        const res = yield call(updateBlackWhiteListCrowdTask, { ...data, ...payload });
        if (res.success) {
          message.success('更新成功！');
          yield put({
            type: 'updateState',
            payload: {
              blackOrWhiteListModalVisible: false,
              record: {},
            },
          });
          yield put({
            type: 'pageQuery',
            payload: {
              ...searchParams,
              tabKey,
            },
          });
        } else {
          message.error(res?.code || res?.msg || '更新失败');
        }
      } else {
        const data = {
          groupId: record.groupId,
          creator: record.creator,
          groupType: 'TAOBAO_USER',
        };
        // 新增群组黑白名单
        const res = yield call(addBlackWhiteListCrowdTask, {
          ...data,
          ...payload,
          creator: payload.operator,
          whiteList: payload.whiteList ?? '',
          blackList: payload.blackList ?? '',
        });
        if (res.success) {
          message.success('添加成功！');
          yield put({
            type: 'updateState',
            payload: {
              blackOrWhiteListModalVisible: false,
              record: {},
            },
          });
          yield put({
            type: 'pageQuery',
            payload: {
              ...searchParams,
              tabKey,
            },
          });
        } else {
          message.error(res?.code || res?.msg || '添加失败');
        }
      }
      yield put({
        type: 'message/fetchUnreadCount',
      });
    },
    *delete({ payload }, { call, put, select }) {
      const { searchParams, tabKey } = yield select(state => state.crowdCircle);

      const res = yield call(deleteCrowdCircle, payload);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success('删除成功');

      yield put({
        type: 'pageQuery',
        payload: {
          ...searchParams,
          tabKey,
        },
      });
    },
    *reRun({ payload }, { call, put, select }) {
      const res = yield call(getRerunCrowd, payload);
      const { searchParams, tabKey } = yield select(state => state.crowdCircle);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      yield put({
        type: 'pageQuery',
        payload: {
          ...searchParams,
          tabKey,
        },
      });

      message.success('重跑成功');
    },
    *editUpdateCreator({ payload }, { call, put, select }) {
      const res = yield call(updateCreator, payload);
      const { searchParams, tabKey } = yield select(state => state.crowdCircle);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success('人群交接成功！');

      yield put({
        type: 'pageQuery',
        payload: {
          ...searchParams,
          tabKey,
        },
      });
    },
    *updateCrowdCircleManager({ payload }, { call, put, select }) {
      const { searchParams, tabKey } = yield select(state => state.crowdCircle);
      const res = yield call(updateCrowdCircleManager, payload);
      if (!res.success) {
        message.error(res.msg || '修改失败');
        return;
      }
      message.success('修改成功！');

      yield put({
        type: 'pageQuery',
        payload: {
          ...searchParams,
          tabKey,
        },
      });
      yield put({
        type: 'updateState',
        payload: {
          modelConfig: {
            isOwner: false,
            visible: false,
            record: {},
          },
        },
      });
    },
    *queryCrowdById({ payload }, { call, put, select, all }) {
      const { curCrowd: curCrowdDetail } = yield select(state => state.crowdCircle);

      if ((!curCrowdDetail || !curCrowdDetail.id) && !payload.id) {
        message.error('不存在该人群');
        return;
      }

      const newPayload = payload.isDetail ? { detailLoading: true } : { refreshLoading: true };

      yield put({
        type: 'updateState',
        payload: newPayload,
      });

      const res = yield call(queryCrowdById, {
        id: payload.id || curCrowdDetail.id,
      });

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      const curCrowd = res.data;

      if (curCrowd.crowdType === 'OPERATE_CROWD') {
        if (payload.isEdit && !payload.refresh) {
          yield put({
            type: 'onOpenCrowdByOperateModal',
          });
        } else if (!payload.isEdit && !payload.refresh) {
          yield put({
            type: 'updateState',
            payload: {
              detailModalVisible: true,
            },
          });
        }
      } else {
        history.push(`/crowd-stategy/gather-person/detail?crowdType=${curCrowd.crowdType}&profileType=${curCrowd.profileType}&id=${curCrowd.id}&type=view`);
        return
      }

      if (curCrowd.crowdType === 'OPERATE_CROWD' && !payload.isEdit) {
        const MAP = {
          AND: '交集',
          OR: '并集',
          NOTIN: '差集',
        };
        const { extInfo } = curCrowd;
        const { OPERATE_CROWD_LEFT, OPERATE_CROWD_RIGHT, OPERATE_CROWD_TYPE } = extInfo;
        const [res1, res2] = yield all([
          OPERATE_CROWD_LEFT ? queryCrowdById({ id: OPERATE_CROWD_LEFT }) : Promise.resolve(),
          OPERATE_CROWD_RIGHT ? queryCrowdById({ id: OPERATE_CROWD_RIGHT }) : Promise.resolve(),
        ]);

        const left = res1 && res1.data;
        const right = res2 && res2.data;
        curCrowd.crowdLeftName = left && left.crowdName;
        curCrowd.crowdLeftId = left && left.id;
        curCrowd.crowdRightName = right && right.crowdName;
        curCrowd.crowdRightId = right && right.id;
        curCrowd.operateCrowdType = MAP[OPERATE_CROWD_TYPE];
        curCrowd.crowdLeftRealTime = left && left.realTime;
        curCrowd.crowdRightRealTime = right && right.realTime;
      }

      if (payload.isEdit && curCrowd.crowdType === 'OPERATE_CROWD') {
        const editFormData = res.data;
        const { extInfo = {} } = editFormData;

        const { OPERATE_CROWD_LEFT, OPERATE_CROWD_RIGHT, OPERATE_CROWD_TYPE } = extInfo;
        editFormData.crowdLeft = OPERATE_CROWD_LEFT;
        editFormData.crowdRight = OPERATE_CROWD_RIGHT;
        editFormData.operateCrowdType = OPERATE_CROWD_TYPE;

        yield put({
          type: 'onEditCrowdByOperate',
          payload: editFormData,
        });
      }

      //刷新接口提示
      if (payload.refresh) {
        message.success('刷新成功');
      }

      yield put({
        type: 'updateState',
        payload: {
          curCrowd,
          detailLoading: false,
          refreshLoading: false,
        },
      });
    },
    *onEditCrowdByOperate({ payload }, { put }) {
      yield put({
        type: 'onUpdateEditCrowdByOperateFormData',
        payload,
      });

      yield put({
        type: 'queryCrowdAndUpdateOperateFormData',
        payload,
      });
    },
    *queryCrowdAndUpdateOperateFormData({ payload }, { put, all }) {
      const { crowdLeft, crowdRight } = payload;

      const [res1, res2] = yield all([
        crowdLeft ? queryCrowdById({ id: crowdLeft }) : Promise.resolve(),
        crowdRight ? queryCrowdById({ id: crowdRight }) : Promise.resolve(),
      ]);

      const left = res1 && res1.data;
      const right = res2 && res2.data;
      yield put({
        type: 'onUpdateEditCrowdByOperateFormData',
        payload: {
          leftCrowdNum: left && left.crowdAmount,
          rightCrowdNum: right && right.crowdAmount,
          crowdLeft: left && {
            label: left.crowdName,
            key: left.id,
          },
          crowdRight: right && {
            label: right.crowdName,
            key: right.id,
          },
          crowdLeftRealTime: left && left.realTime,
          crowdRightRealTime: right && right.realTime,
        },
      });
    },
    *editCrowdByOperate({ payload }, { call, put, select }) {
      const data = payload;

      const { editCrowdByOperateFormData, searchParams, tabKey } = yield select(
        state => state.crowdCircle
      );

      const isUpdate = !!(editCrowdByOperateFormData && editCrowdByOperateFormData.id);
      if (isUpdate) {
        data.id = editCrowdByOperateFormData.id;
      }

      const res = yield call(editCrowdBySQL, data);

      if (!res.success) {
        message.error(res.msg);
        return;
      }

      message.success('更新成功');

      yield put({
        type: 'onCrowdByOperateModalCancel',
      });

      yield put({
        type: 'pageQuery',
        payload: {
          ...searchParams,
          tabKey,
        },
      });
    },
    *confirmMonitorUpdate({ payload }, { call, put, select }) {
      const { searchParams, tabKey } = yield select(state => state.crowdCircle);
      const res = yield call(updateMonitorConfig, payload);
      if (!res.success) {
        message.error(res.msg);
        return;
      }
      message.success('更新成功');
      yield put({
        type: 'updateState',
        payload: {
          crowdMonitorVisible: false,
        },
      });
      yield put({
        type: 'pageQuery',
        payload: {
          ...searchParams,
          tabKey,
        },
      });
    },
    *queryMonitorByCrowd({ payload }, { call, put }) {
      const res = yield call(queryMonitorByCrowd, payload.id);
      if (!res.success) {
        message.error(res.msg);
        return;
      }
      return res.data;
    },
    *crowdCircleQuickPostpone({ payload }, { call, put, select }) {
      const res = yield call(crowdCircleQuickPostpone, payload);
      if (!res.success) {
        message.error(res.msg);
        return;
      }
      return res;
    },
    *crowdBucket({ payload }, { call, put, select }) {
      yield put({
        type: 'updateState',
        payload: {
          bucketLoading: true,
        },
      });

      try {
        const res = yield call(crowdBucket, payload);

        if (!res.success) {
          message.error(res.msg);
          return res;
        }

        message.success('分组正在执行中...');

        // 刷新列表数据
        const { searchParams, tabKey } = yield select(state => state.crowdCircle);
        yield put({
          type: 'pageQuery',
          payload: {
            ...searchParams,
            tabKey,
          },
        });

        // 关闭分组弹窗
        yield put({
          type: 'updateState',
          payload: {
            addGroupVisible: false,
            record: {},
          },
        });

        return res;
      } finally {
        yield put({
          type: 'updateState',
          payload: {
            bucketLoading: false,
          },
        });
      }
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    onUpdateEditCrowdByOperateFormData(state, { payload }) {
      return {
        ...state,
        editCrowdByOperateFormData: {
          ...state.editCrowdByOperateFormData,
          ...payload,
        },
      };
    },
    onOpenCrowdByOperateModal(state) {
      return {
        ...state,
        crowdByOperateModalVisible: true, // 人群逻辑运算
      };
    },
    onCrowdByOperateModalCancel(state) {
      return {
        ...state,
        crowdByOperateModalVisible: false, // 人群逻辑运算
        editCrowdByOperateFormData: {}, // 人群逻辑运算
      };
    },
  },
};
