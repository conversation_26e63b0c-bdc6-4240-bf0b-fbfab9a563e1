.copBtns {
  margin-bottom: 50px;

  .copBtn {
    margin-left: 10px;
  }
}

.searchCard {
  margin-bottom: 10px;
}

.tableCard {
  :global {
    .ant-card-body {
      padding: 10px;
    }
    .ant-table-row-expand-icon-cell {
      z-index: 1;
    }
    .ant-table-cell-fix-right {
      background-color: #f5f5f5 !important;
    }
  }
  .columnTitle {
    :global {
      .anticon-question-circle {
        margin-left: 4px;
      }
    }
  }
  .realTimeTag {
    padding: 0 4px;
    line-height: 14px;
    font-size: 10px;
  }
  .expandContainer {
    display: flex;
    align-items: baseline;
    width: 40px;
    cursor: pointer;
    &>:last-child {
      font-size: 16px;
    }
    .icon {
      margin-right: 8px;
      color: #ff5769;
      font-size: 18px;
    }
  }
}

.crowdBtns {
  margin-bottom: 20px;
  .crowdBtn {
    margin-right: 10px;
  }
}

.crowdName {
  max-width: 180px;
  overflow: hidden;
  color: #1890ff;
  // white-space: nowrap;
  text-overflow: ellipsis;
}

:global {
  .fl-search-form-wrap {
    .ant-row.ant-form-item {
      height: 32px;
    }
  }
  // .ant-table-row .ant-table-cell {

  // }

  // .ant-table-row .ant-table-cell:nth-child(2) {
  //   max-width: 150px;
  // }

  .ant-table-row .ant-table-cell:nth-child(4) {
    max-width: 130px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .bucket {
    max-width: 130px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
