import {
  Card,
  Table,
  Steps,
  Tabs,
  Tree,
  Select,
  Tooltip,
  Tag,
  Button,
  Input,
  Form,
  Switch,
  DatePicker,
  Modal,
  message,
} from 'antd';
import { connect } from 'dva';
import { useEffect, useState } from 'react';
import LabelHeader from './SearchHeader';
import TagCrowdGroup from '../../components/TagCrowdGroup';
import styles from './index.less';
import dayjs from 'dayjs';
import { Link } from 'umi';
import AddCrowdGroupModal from '../../components/AddCrowdGroupModal';
import SelectEnumValueModal from '../../components/SelectEnumValueModal';
import CrowdApprovalModal from '../../components/CrowdApprovalModal';
import SelectTagLeft from './TagCrowdLeft';
import CrowdInfoForm from '../CrowdInfoForm';
import { setTagGroup } from '../../common/tag';
import { approvalApplySceneEnum } from '../../constants';
import {
  createCrowd,
  queryCrowdCircle,
  updateCrowdCircle,
  crowdCircleApprovalJudge,
  getIsSupportPush
} from '@/services/api';
// import { getUrlParams } from '@/pages/LabelMarket/common/utils';
import { getQueryParams } from '../../common/utils';
import { history } from 'umi';
import { transform } from '@/utils/crowd';

let items = [
  {
    title: '标签组合',
    description: '',
  },
  {
    title: '人群信息',
    description: '',
  },
];

const TagCircle = props => {
  const {
    dispatch,
    tagCircle: { isRealTime },
    user: { currentUser },
  } = props;
  const { crowdType, id, profileType, type } = getQueryParams();
  const [current, setCurrent] = useState(0);
  const [conditions, setConditions] = useState([]);
  const [isApproval, setIsApproval] = useState(false);
  const [formData, setFormData] = useState({});
  const [loading, setLoading] = useState(false);
  const [isSupportPush, setIsSupportPush] = useState([]);
  const [supportPushLoading, setSupportPushLoading] = useState(false);

  const [form] = Form.useForm();

  const changeConditions = conditions => {
    setConditions(conditions);
  };

  useEffect(() => {
    if (id) {
      fetchDetail();
    }
  }, [id]);

  const getQueryIsSupportPush = () => {
    const conditions = form.getFieldValue('conditions');
    if (conditions) {
      setSupportPushLoading(true);
      getIsSupportPush({
        conditions,
      }).then(res => {
        setIsSupportPush([]);
        if (res.success) {
          if ((res.data || []).length > 0) {
            setIsSupportPush(res.data);
          } else {
            message.warning('实时标签和sql标签不能混用，建议通过人群组合来实现！');
          }
        } else {
          message.error(res.msg);
        }
        setSupportPushLoading(false);
      });
    }
  }

  const fetchDetail = async () => {
    const result = await queryCrowdCircle(id);
    if (result.success && result.data) {
      const data = {
        ...result.data,
      };
      if (data.expiredDate) {
        data.expiredDate = dayjs(data.expiredDate);
      }
      if (data.crowdApplyScene) {
        (data.crowdApplyScene || []).map(item => {
          data[item] = true;
        });
      }

      data.deliveryMethod = []

      if (data.crowdApplyScene.includes("MATCH")) {
        data.deliveryMethod.push("MATCH")
      }
      if (data.crowdApplyScene.includes("PUSH")) {
        data.deliveryMethod.push("PUSH")
      }

      dispatch({
        type: 'tagCircle/getCrowdCount',
        payload: data.conditions ?? {},
      })

      dispatch({
        type: 'tagCircle/updateState',
        payload: {
          isRealTime: data.timeType === "ONLINE" ? 1 : 0
        },
      })

      form.setFieldsValue(data);
    }
  };

  const onSubmit = data => {
    const request = id ? updateCrowdCircle : createCrowd;

    if (id) {
      data['id'] = id;
    }

    setLoading(true);

    request(data).then(res => {
      if (res.success) {
        message.success(id ? '编辑成功' : '创建成功');
        setFormData({});
        setIsApproval(false)
        history.push('/crowd-stategy/gather-person');
      } else {
        message.error(res.msg || (id ? '编辑失败' : '创建失败'));
      }
      setLoading(false);
    });
  };

  const onApprovalSubmit = async value => {
    const payload = {
      ...formData,
      approvalInfos: {
        approvalCreator: { empId: currentUser?.workId, nickName: currentUser?.name },
        applyScene: approvalApplySceneEnum.CROWD_UPDATE,
        entityType: 'TAOBAO_USER',
        approvalInfo: value,
      },
    };
    onSubmit(payload);
  };

  return (
    <div className={styles.detailRow} wrap={false}>
      <div className={styles.leftCol}>
        <Card>
          <SelectTagLeft current={current} profileType={profileType} />
        </Card>
      </div>
      <div className={styles.rightCol}>
        <Steps current={current}>
          {items.map(ele => {
            return <Steps.Step title={ele.title} key={ele.title} description={ele.description} />;
          })}
        </Steps>
        <Form
          form={form}
          onFinish={values => {
            const data = values;
            const { needUpdate, expiredDate, deliveryMethod = [], ANALYSIS } = values;
            data.crowdType = crowdType;
            data.needUpdate = needUpdate ? true : false;
            data.expiredDate = expiredDate.valueOf();
            data.physicalProfileCode = profileType;

            let applyScenes = [];
            if (deliveryMethod.includes('MATCH')) {
              applyScenes.push('MATCH');
            }
            if (deliveryMethod.includes('PUSH')) {
              applyScenes.push('PUSH');
            }
            if (ANALYSIS) {
              applyScenes.push('ANALYSIS');
            }
            //如果是实时是默认值给MATCH
            if (isRealTime && !applyScenes.includes('MATCH')) {
              message.error('实时人群请选择投放方式');
              return
            }

            data.applyScenes = applyScenes;
            if (data.applyScenes.length === 0) {
              message.error('离线人群请选择投放方式或洞察分析其中一项');
              return
            }
            delete data.ANALYSIS;
            delete data.updateType;
            delete data.deliveryMethod

            setFormData(data);

            if (profileType === 'TAOBAO_USER') {
              crowdCircleApprovalJudge(data).then(result => {
                if (!result?.success) {
                  message.error(result?.msg);
                  return;
                }
                if (result?.data) {
                  setIsApproval(true);
                  return;
                }

                onSubmit(data);
              });
            } else {
              onSubmit(data);
            }
          }}
        >
          <Card hidden={current !== 0}>
            <Form.Item name="conditions">
              <TagCrowdGroup changeConditions={changeConditions} id={id} />
            </Form.Item>
          </Card>
          <Card hidden={current !== 1}>
            <CrowdInfoForm
              conditions={conditions}
              id={id}
              form={form}
              crowdType={crowdType}
              propsIsRealTime={isRealTime}
              isSupportPush={isSupportPush}
              supportPushLoading={supportPushLoading}
              checked=""
            />
          </Card>
          <div className={styles.rightBtn}>
            {current === 0 && (
              <Button
                type="primary"
                onClick={() => {
                  const conditionsValues = form.getFieldValue('conditions');
                  let haveConditionsValues = true;

                  if (conditionsValues && conditionsValues.group) {
                    (conditions.group || []).map((item) => {
                      if(!item.expression || item.expression.indexOf("undefined") > -1) {
                        haveConditionsValues = false;
                      }
                    })
                  }

                  if (!haveConditionsValues || !conditionsValues) {
                    message.warning('标签选择项不能存在空选项！');
                    return;
                  }
                  getQueryIsSupportPush()
                  setCurrent(current + 1);
                }}
              >
                下一步
              </Button>
            )}
            {current === 1 && (
              <>
                <Button
                  onClick={() => {
                    setCurrent(current - 1);
                  }}
                  style={{
                    marginRight: 12,
                  }}
                >
                  上一步
                </Button>
                <Button
                  style={{
                    marginRight: 12,
                  }}
                  type="primary"
                  onClick={form.submit}
                  disabled={type === 'view' || (crowdType === "LABEL_CROWD" && isSupportPush.length === 0)}
                  loading={loading}
                >
                  提交
                </Button>
                <Link to="/crowd-stategy/gather-person">
                  <Button>取消</Button>
                </Link>
              </>
            )}
          </div>
        </Form>
      </div>
      <CrowdApprovalModal
        title="所选标签在使用场景下需要审批后才可生成人群，审批流程按照标签最高等级进行生成"
        reasonLabel={`请填写人群${id?'更新' : '创建'}理由`}
        visible={isApproval}
        onSubmit={onApprovalSubmit}
        loading={loading}
        close={() => {
          setIsApproval(false);
        }}
      />
    </div>
  );
};

export default connect(state => ({
  tagCircle: state.tagCircle,
  user: state.user,
}))(TagCircle);
