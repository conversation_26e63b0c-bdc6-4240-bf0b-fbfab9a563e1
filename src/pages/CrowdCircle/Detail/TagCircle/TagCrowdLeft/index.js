import { Fragment, useEffect, useState } from 'react';
import SearchHeader from '../SearchHeader';
import styles from '../index.less';
import {
  Tabs,
  Row,
  Col,
  Card,
  Tree,
  Select,
  Table,
  Tooltip,
  Button,
  Tag,
  message,
  Spin,
  Alert,
} from 'antd';
import { MenuFoldOutlined, MenuUnfoldOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { connect } from 'dva';
import { transformTree, zhugeUrl } from '@/utils/utils';
import MyIcon from '@/components/MyIcon';
import { getQueryParams } from '../../../common/utils';
import { get } from 'lodash';

let classifyTabList = [
  {
    label: '全部',
    value: 'ALL',
  },
  {
    label: '我有权限',
    value: 'AUTHORIZED',
  },
  {
    label: '我的收藏',
    value: 'COLLECTED',
  },
];

const TagCrowdLeft = props => {
  const {
    dispatch,
    profileType,
    current,
    tagCircle: {
      labelInfoList,
      loading,
      sortType,
      searchParams,
      tabCntMap,
      treeData,
      sortTypeList,
      selectIndex,
    },
  } = props;
  const { type } = getQueryParams();
  const [isFold, setIsFold] = useState(false);
  const [expandedKeys, setExpandedKeys] = useState(['-1']);
  const [categoryId, setCategoryId] = useState('');
  const [tabKey, setTabKey] = useState('ALL');

  useEffect(() => {
    dispatch({
      type: 'tagCircle/queryBuildTree',
    });
    dispatch({
      type: 'tagCircle/querySortType',
    });
    dispatch({
      type: 'tagCircle/pageQuery',
      payload: {
        profileCodes: [profileType === 'TAOBAO_USER' ? 'tbu' : 'device'],
        pageNo: 1,
        pageSize: 10,
      },
    });
  }, []);

  const columns = [
    {
      title: '标签名称',
      dataIndex: 'name',
      width: 250,
      fixed: 'left',
      render: (text, record) => {
        return (
          <Tooltip title={`${record.code} - ${record.description || '暂无标签描述'}`}>
            <div className={styles.description} style={{ whiteSpace: 'normal', wordBreak: 'break-all' }}>
              {Object.keys(record.openScope)[0] === 'PRIVATE' && (
                <Tag className={styles.privateTag} color="volcano">
                  私有
                </Tag>
              )}
              {record.tagList && record.tagList.includes('official') && (
                <Tag className={styles.privateTag} color="blue" style={{ marginRight: 4 }}>官方</Tag>
              )}
              {record.tagList && record.tagList.includes('output_late') && (
                <Tag className={styles.privateTag} color="red" style={{ marginRight: 4 }}>延迟产出</Tag>
              )}
              <a
                className={styles.text}
                href={`${zhugeUrl}/label-market/detail?labelCode=${record.code}`}
                target="_blank"
                style={{ whiteSpace: 'normal', wordBreak: 'break-all' }}
              >
                {text}
              </a>
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '标签时效',
      dataIndex: 'timeType',
      render: text => {
        if (!text) return '';
        const timeTypeMap = {
          OFFLINE: '离线',
          REALTIME: '实时',
          HYBRID: '混合'
        };
        const key = Object.keys(text)[0];
        return timeTypeMap[key] || key;
      },
    },
    {
      title: '圈人数量',
      dataIndex: 'useAmount',
    },
    {
      title: '负责人',
      dataIndex: 'dataOwner',
      render: text => {
        return (
          <div
            onClick={() => {
              window.open(
                `dingtalk://dingtalkclient/action/sendmsg?dingtalk_id=${text?.dingTalkId}`
              );
            }}
          >
            {text && text?.nickName}
            <MyIcon
              style={{ fontSize: 18, marginLeft: 6, cursor: 'pointer' }}
              type="icon-dingding"
            />
          </div>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
      render: (text, record) => {
        if (!record.usePermission && record.authBucket) {
          return (
            <Tooltip title={record.description || '暂无标签描述'}>
              <Button onClick={() => handleApplyPermission(record)} type="link">
                申请权限
              </Button>
            </Tooltip>
          );
        } else {
          return (
            <Tooltip
              title={
                !record.usePermission && !record.authBucket
                  ? record.authMsg
                  : current === 1
                  ? '请返回至"标签组合"步骤，才可操作标签' : type === "view" ? "预览模式不🉑️添加"
                  : record.description || '暂无标签描述'
              }
            >
              <Button
                disabled={(!record.usePermission && !record.authBucket) || current === 1 || type === "view"}
                type="link"
                onClick={() => {
                  if (selectIndex.childrenIndex === -1 || selectIndex.parentIndex === -1) {
                    message.warning('请先选择您要添加的位置!');
                    return;
                  }
                  dispatch({
                    type: 'tagCircle/handleUpdateVisibleAndData',
                    payload: {
                      visible: true,
                      tag: {
                        ...record,
                        dataType: Object.keys(record.dataType)[0],
                        dimEnumMetaId: record.dimEnumId > 0 ? record.dimEnumId : '',
                      },
                    },
                  });
                }}
              >
                添加
              </Button>
            </Tooltip>
          );
        }
      },
    },
  ];

  const handleApplyPermission = record => {
    const _pnames = `zhuge_label_${record.code}_permission`;

    // 判断是日常环境还是线上
    if (
      window.location.hostname.includes('.net') ||
      window.location.hostname.includes('localhost')
    ) {
      window.open(
        `http://acl-test.alibaba-inc.com/apply/cart/detail.htm?pnames=${_pnames}`,
        '_blank'
      );
    } else if (window.location.hostname.includes('pre')) {
      window.open(`https://acl.alibaba-inc.com/apply/cart/detail.htm?pnames=${_pnames}`, '_blank');
    } else {
      window.open(`https://acl.alibaba-inc.com/apply/cart/detail.htm?pnames=${_pnames}`, '_blank');
    }
  };

  const onExpand = expandedKeys => {
    setExpandedKeys(expandedKeys);
  };

  const onFold = () => {
    setIsFold(true);
  };

  const onUnFold = () => {
    setIsFold(false);
  };

  const handleTableChange = pagination => {
    dispatch({
      type: 'tagCircle/pageQuery',
      payload: {
        ...searchParams,
        pageNo: pagination.current,
      },
    });
  };

  return (
    <>
      <SearchHeader />
      <Tabs
        activeKey={tabKey}
        onChange={e => {
          setTabKey(e);
          dispatch({
            type: 'tagCircle/pageQuery',
            payload: {
              ...searchParams,
              classifyTab: e,
              pageNo: 1,
            },
          });
        }}
      >
        {classifyTabList.map(ele => (
          <Tabs.TabPane tab={`${ele?.label}（${tabCntMap[ele?.value]}）`} key={ele?.value} />
        ))}
      </Tabs>
      <Row>
        {treeData ? (
          <Col
            span={isFold ? 1 : 4}
            style={{
              paddingRight: 16,
              transition: 'all 0.2s',
            }}
          >
            <div className={styles.treeMenu} style={{ width: '100%', height: 20 }}>
              {isFold ? (
                <MenuUnfoldOutlined style={{ float: 'right' }} onClick={onUnFold} />
              ) : (
                <MenuFoldOutlined style={{ float: 'right' }} onClick={onFold} />
              )}
            </div>
            <Tree
              treeData={transformTree(treeData)}
              style={{ display: isFold ? 'none' : 'block' }}
              onSelect={(e, info) => {
                const {
                  node: { key, children },
                  selected,
                } = info;
                if (!key || (children && children.length > 0)) return;
                let categoryIdNew = selected ? key : '';
                setCategoryId(categoryIdNew);
                dispatch({
                  type: 'tagCircle/pageQuery',
                  payload: {
                    ...searchParams,
                    categoryId: categoryIdNew,
                    pageNo: 1,
                  },
                });
              }}
              onExpand={onExpand}
              expandedKeys={expandedKeys}
              selectedKeys={[categoryId]}
            />
          </Col>
        ) : (
          <Col span={5}></Col>
        )}
        <Col span={isFold ? 23 : 20} style={{ transition: 'all 0.2s' }}>
          <Card>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 16 }}>
              <Alert
                message={
                  <span style={{ fontSize: '12px', fontWeight: 'normal' }}>
                    <div>
                      <Tag color="blue" size="small" style={{ marginRight: 4, fontSize: '10px', padding: '0 4px', height: '16px', lineHeight: '14px' }}>官方</Tag>
                      由诸葛官方维护或者确认过底层逻辑，并有实时或离线监控的标签
                    </div>
                    <div style={{ marginTop: '4px' }}>
                      <Tag color="red" size="small" style={{ marginRight: 4, fontSize: '10px', padding: '0 4px', height: '16px', lineHeight: '14px' }}>延迟产出</Tag>
                      底表并未在规定时间产出 t-1 数据，标签圈人时可能使用 t-2 数据
                    </div>
                  </span>
                }
                type="info"
                showIcon
                style={{
                  flex: 1,
                  marginRight: 16,
                  padding: '4px 8px',
                  fontSize: '12px'
                }}
                banner
              />
              <Select
                style={{ width: '165px' }}
                value={sortType}
                options={sortTypeList}
                onChange={value => {
                  dispatch({
                    type: 'tagCircle/updateState',
                    payload: {
                      sortType: value,
                    },
                  });
                  dispatch({
                    type: 'tagCircle/pageQuery',
                    payload: {
                      ...searchParams,
                      sortType: value,
                      pageNo: 1,
                    },
                  });
                }}
              />
            </div>
            <Table
              rowKey={record => record.id}
              scroll={{ x: 'max-content' }}
              columns={columns}
              dataSource={labelInfoList}
              loading={loading}
              pagination={{
                total: tabCntMap[tabKey],
                pageSize: searchParams.pageSize ?? 10,
                current: searchParams.pageNo ?? 1,
                showTotal: total => {
                  return `总共 ${total} 条`;
                },
              }}
              onChange={handleTableChange}
            />
          </Card>
        </Col>
      </Row>
    </>
  );
};

export default connect(({ tagCircle }) => ({
  tagCircle,
}))(TagCrowdLeft);
