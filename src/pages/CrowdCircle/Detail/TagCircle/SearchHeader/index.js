import { Fragment } from 'react';
import { Input, Tooltip, Checkbox, Row, Col, Tag } from 'antd';
import styles from './index.less';
import { QuestionCircleFilled } from '@ant-design/icons';
import { connect } from 'dva';

const { Search } = Input;

const SearchHeader = props => {
  const {
    tagCircle: { searchParams },
    dispatch,
  } = props;
  return (
    <Fragment>
      <div className={styles.searchContainer}>
        <div className={styles.leftSearch}>
          <Search
            placeholder="请输入标签id、标签名称、负责人花名或者工号"
            enterButton="搜索"
            style={{ width: 400 }}
            onSearch={value => {
              dispatch({
                type: 'tagCircle/pageQuery',
                payload: {
                  ...searchParams,
                  content: value,
                  pageNo: 1,
                },
              });
            }}
          />
          <p className={styles.tips}>仅展示“使用中”状态的标签信息</p>
        </div>
        <a target="_blank" href="https://aliyuque.antfin.com/qnwrq9/kzl68s/iadc1sxwfgwh18iu#">
          教程文档 &gt;
        </a>
      </div>
      <div className={styles.fileItem}>
        <div className={styles.fileLabel}>
          <Tooltip title="“离线标签”是天/周/月进行更新；“实时标签”是分钟级或者秒级进行更新">
            <QuestionCircleFilled className={styles.icon} />
          </Tooltip>
          标签时效：
        </div>
        <Checkbox.Group
          style={{ width: '100%' }}
          onChange={value => {
            dispatch({
              type: 'tagCircle/pageQuery',
              payload: {
                ...searchParams,
                typeList: value,
                pageNo: 1,
              },
            });
          }}
          defaultValue={['REALTIME', 'OFFLINE']}
        >
          <Row>
            {[
              {
                label: '实时标签',
                value: 'REALTIME',
              },
              {
                label: '离线标签',
                value: 'OFFLINE',
              },
            ]?.map(ele => (
              <Col key={ele?.value} span={4}>
                <Checkbox value={ele?.value}>{ele?.label}</Checkbox>
              </Col>
            ))}
          </Row>
        </Checkbox.Group>
      </div>
    </Fragment>
  );
};

export default connect(state => ({
  tagCircle: state.tagCircle,
}))(SearchHeader);
