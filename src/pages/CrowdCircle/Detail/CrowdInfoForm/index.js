import {
  Button,
  Form,
  Input,
  Select,
  Switch,
  DatePicker,
  Modal,
  Radio,
  message,
  Spin,
  Tooltip,
} from 'antd';
import dayjs from 'dayjs';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import AddCrowdGroupModal from '../../components/AddCrowdGroupModal';
import { useEffect, useState } from 'react';
import { checkCrowdCircleName } from '@/services/api';
import { connect } from 'dva';
import { useCallback } from 'react';
import DateInput from '../../components/DateInput';

const CrowdInfoForm = props => {
  const {
    dispatch,
    form,
    crowdType,
    id,
    crowdGroup: { dataList, modelConfig },
    crowdImport: { partitionOptions, isHaveDate },
    propsIsRealTime,
    conditions,
    isSupportPush,
    supportPushLoading,
    checked,
  } = props;

  const [crowdGroupForm] = Form.useForm();

  useEffect(() => {
    dispatch({
      type: 'crowdGroup/fetchData',
      payload: {
        pageNo: 1,
        pageSize: 500,
      },
    });
  }, []);

  const handleFinish = values => {
    const data = {
      ...values,
      type: 'CROWD',
    };
    if (modelConfig.isEdit) {
      data.id = modelConfig.record.key;
      data.name = modelConfig.record.title;
    }
    dispatch({
      type: 'crowdGroup/operateCrowdGroup',
      payload: { ...data, isEdit: modelConfig.isEdit },
    }).then(res => {
      if (res.success) {
        crowdGroupForm.resetFields();
      }
    });
  };

  const disabledDate = current =>
    current < dayjs().startOf('day') || current >= dayjs() + 91 * 24 * 60 * 60 * 1000;


  const renderNeedUpdate = () => {
    return (
      <>
        <Form.Item
          label={
            <Tooltip title="依赖更新">
              是否日更新
            </Tooltip>
          }
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
          valuePropName="checked"
          name="needUpdate"
          colon={false}
        >
          <Switch
            onChange={checked => {
              if (crowdType === 'LABEL_CROWD') {
                form.setFieldsValue({ needUpdate: false });
                if (checked) {
                  Modal.confirm({
                    icon: <ExclamationCircleOutlined />,
                    content: '日更新消耗大量飞猪 ODPS 集群资源，开支巨大，请确认是否是必要选项！',
                    okText: '确认',
                    cancelText: '取消',
                    onOk() {
                      form.setFieldsValue({ needUpdate: true });
                    },
                    onCancel() {
                      form.setFieldsValue({ needUpdate: false });
                    },
                  });
                }
              }
            }}
            checkedChildren="是"
            unCheckedChildren="否"
          />
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) => prevValues.needUpdate !== curValues.needUpdate}
        >
          {({ getFieldValue }) => {
            const needUpdate = getFieldValue('needUpdate');
            if (!needUpdate) {
              return null;
            }
            if (needUpdate) {
              form.setFieldsValue({ updateType: 0 });
            }
            if (checked === '自定义SQL' && isHaveDate) {
              return (
                <Form.Item
                  name="scheduleModel"
                  label={
                    <Tooltip title="如果底表在设置的时间点未产出，则强制以最大分区运行">
                      强制运行时间
                    </Tooltip>
                  }
                  rules={[{ required: true, message: '请选择运行周期' }]}
                  labelCol={{ span: 24 }}
                  wrapperCol={{ span: 24 }}
                  colon={false}
                  initialValue={{ updatePeriod: 'DAY', updateTime: '12:00:00' }}
                >
                  <DateInput />
                </Form.Item>
              );
            }
            return (
              needUpdate && (
                <Form.Item
                  label="更新类型"
                  labelCol={{ span: 24 }}
                  wrapperCol={{ span: 24 }}
                  name="updateType"
                  colon={false}
                >
                  <Radio.Group>
                    <Radio value={0}>
                      每天更新
                    </Radio>
                  </Radio.Group>
                </Form.Item>
              )
            );
          }}
        </Form.Item>
      </>
    );
  };

  const renderLaunch = isBlock => {
    return (
      <Form.Item
        label="选择投放方式"
        labelCol={{ span: 24 }}
        wrapperCol={{ span: 24 }}
        colon={false}
        name="deliveryMethod"
      >
        <Select mode="multiple" placeholder="请选择投放方式">
          {(isBlock || isSupportPush.includes('MATCH')) && <Option value="MATCH">在线匹配</Option>}
          {(isBlock || isSupportPush.includes('PUSH')) && <Option value="PUSH">PUSH投放</Option>}
        </Select>
      </Form.Item>
    );
  };

  const renderAnalysis = () => {
    return (
      <Form.Item
        labelCol={{ span: 24 }}
        wrapperCol={{ span: 24 }}
        label="是否洞察分析"
        name="ANALYSIS"
        valuePropName="checked"
        colon={false}
      >
        <Switch checkedChildren="是" unCheckedChildren="否" />
      </Form.Item>
    );
  };

  const presetDates = (date) => {
    const newDate = dayjs(date).format("YYYY-MM-DD 23: 59: 59")
    form.setFieldsValue({ expiredDate: dayjs(newDate)  });
  };

  return (
    <>
      <Form.Item
        label="人群名称"
        name="crowdName"
        rules={[
          ({ getFieldValue }) => ({
            async validator(_, value) {
              if (!value) {
                return Promise.reject(new Error('人群名称不能为空'));
              }
              const data = {
                name: value,
              };
              if (id) {
                data.id = id;
              }

              const result = await checkCrowdCircleName(data);

              if (result.data) {
                return Promise.reject(new Error('人群名称已存在'));
              }

              return Promise.resolve();
            },
          }),
        ]}
        validateTrigger={['onBlur']}
        labelCol={{ span: 24 }}
        wrapperCol={{ span: 24 }}
        required
      >
        <Input placeholder="请输入人群名称" />
      </Form.Item>
      <Form.Item
        label="人群描述"
        name="crowdDescription"
        rules={[{ required: true, message: '请输入人群描述' }]}
        labelCol={{ span: 24 }}
        wrapperCol={{ span: 24 }}
      >
        <Input.TextArea placeholder="请输入人群描述" />
      </Form.Item>
      <Spin spinning={supportPushLoading}>
        <Form.Item
          noStyle
          dependencies={['conditions']}
        >
          {({ getFieldValue }) => {
            const conditions = getFieldValue('conditions');
            let isRealTime;
            let isShow = true;
            let isBlock = true;

            if (
              (conditions && conditions.group && (conditions.group || []).length > 0) ||
              crowdType === 'IMPORT_CROWD'
            ) {
              if (crowdType === 'OPERATE_CROWD') {
                isRealTime =
                  conditions.group.filter(item => (item.realTime ?? '').indexOf('1') > -1).length >
                  0;
              }

              if (crowdType === 'IMPORT_CROWD') {
                if (
                  (checked === 'ODPS导入' && partitionOptions.length <= 0) ||
                  checked === '文件上传' ||
                  (checked === '自定义SQL' && !isHaveDate)
                ) {
                  isShow = false;
                }
              }

              if (crowdType === 'OPERATE_CROWD') {
                if (isRealTime) {
                  isBlock = false;
                }
              }

              if (crowdType === 'LABEL_CROWD') {
                if (propsIsRealTime) {
                  isBlock = false;
                }
              }

              return (
                <>
                  {isBlock && isShow && renderNeedUpdate()}
                  {(isBlock || isSupportPush.includes('MATCH') || isSupportPush.includes('PUSH')) &&
                    renderLaunch(isBlock)}
                  {(isBlock || isSupportPush.includes('ANALYSIS')) && renderAnalysis()}
                </>
              );
            }
          }}
        </Form.Item>
      </Spin>
      <Form.Item
        label="人群过期时间"
        name="expiredDate"
        rules={[{ required: true, message: '请选择人群过期时间' }]}
        labelCol={{ span: 24 }}
        wrapperCol={{ span: 24 }}
      >
        <DatePicker
          format="YYYY-MM-DD HH:mm:ss"
          disabledDate={disabledDate}
          showTime={{
            defaultValue: dayjs('23:59:59', 'HH:mm:ss'),
          }}
          placeholder="请选择人群过期时间"
          renderExtraFooter={() => [
            <Button key="oneWeek" onClick={() => presetDates(dayjs(dayjs() + 7 * 24 * 60 * 60 * 1000))} type="link">一星期</Button>,
            <Button key="oneMonth" onClick={() => presetDates(dayjs(dayjs() + 30 * 24 * 60 * 60 * 1000))} type="link">一个月</Button>,
            <Button key="threeMonth" onClick={() => presetDates(dayjs(dayjs() + 90 * 24 * 60 * 60 * 1000))} type="link">三个月</Button>
          ]}
          showToday={false}
        />
      </Form.Item>
      <Form.Item labelCol={{ span: 24 }} wrapperCol={{ span: 24 }} label="人群分组">
        <Form.Item name="groupIds" noStyle>
          <Select mode="multiple" style={{ width: 262 }} placeholder="请选择人群分组">
            {(dataList || []).map(item => {
              return (
                <Select.Option key={item.key} value={item.key}>
                  {item.title}
                </Select.Option>
              );
            })}
          </Select>
        </Form.Item>
        <Button
          type="primary"
          onClick={() => {
            dispatch({
              type: 'crowdGroup/updateState',
              payload: {
                modelConfig: {
                  visible: true,
                  isEdit: false,
                  record: {},
                },
              },
            });
          }}
        >
          新增分组
        </Button>
      </Form.Item>
      {modelConfig.visible && (
        <AddCrowdGroupModal handleFinish={handleFinish} form={crowdGroupForm} />
      )}
    </>
  );
};

export default connect(state => ({
  crowdGroup: state.crowdGroup,
  crowdImport: state.crowdImport,
}))(CrowdInfoForm);
