import { connect } from 'dva';
import { getUrlParams } from '@/pages/LabelMarket/common/utils';
import { Fragment, useCallback } from 'react';
import TagCircle from './TagCircle';
import Crowd from './Crowd';
import { getQueryParams } from '../common/utils'

const Detail = props => {
  const { crowdType } = getQueryParams();

  const renderContent = useCallback(() => {
    switch (crowdType) {
      case 'LABEL_CROWD':
        return <TagCircle />;
      case 'IMPORT_CROWD':
        return <Crowd />;
      default:
        return <Crowd />;
    }
  }, [crowdType]);

  return <Fragment>{renderContent()}</Fragment>;
};

export default connect(state => ({
  crowdCircle: state.crowdCircle,
}))(Detail);
