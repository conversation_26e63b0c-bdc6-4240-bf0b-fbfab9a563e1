import { Form, Upload, Button, message } from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import { crowdCircleApproval } from '@/services/api';
const { Dragger } = Upload;

function downloadTxtFile() {
  const content = `34715019348\n13254315813\n10394580713`;

  const blob = new Blob([content], { type: 'text/plain' });

  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.href = url;
  link.download = '人群模版.txt';

  document.body.appendChild(link);
  link.click();

  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

const FileUpload = ({ initFileList }) => {
  const [fileList, setFileList] = useState([]);

  useEffect(() => {
    setFileList(initFileList ?? []);
  }, [initFileList]);

  function beforeUpload(file) {
    const isTxt = file.type === 'text/plain';
    if (!isTxt) {
      message.error('你需要上传txt文件');
    }

    const isLt50M = file.size / 1024 / 1024 < 50;

    if (!isLt50M) {
      message.error('文件必须小于50M');
    }

    return isTxt && isLt50M;
  }

  const handleFileChange = info => {
    if (info.file.status === 'done') {
      message.success(`${info.file.name} 上传成功`);
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} 上传失败`);
    }
    let fileList = [...info.fileList];

    fileList = fileList.slice(-1);
    setFileList(fileList);
  };

  return (
    <Form.Item label="上传文件" labelCol={{ span: 3 }} wrapperCol={{ span: 21 }} required>
      <div style={{ display: 'flex' }}>
        <Form.Item name={["extInfo","filePath"]} rules={[{ required: true, message: '请上传文件' }]} noStyle>
          <Dragger
            name="file"
            action="/crowd_circle/upload_file"
            showUploadList={{ showRemoveIcon: false }}
            style={{ width: 550 }}
            accept=".txt"
            maxCount={1}
            beforeUpload={beforeUpload}
            onChange={handleFileChange}
            fileList={fileList}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">
              点击或者将文件拖拽到这里上传（<span style={{ color: 'red' }}>用户ID</span>）
            </p>
            <p className="ant-upload-hint">注意事项:</p>
            <p className="ant-upload-hint">1.文件大小不超过50M</p>
            <p className="ant-upload-hint">2.文件格式为.txt</p>
            <p className="ant-upload-hint">3.需要UTF-8编码</p>
            <p className="ant-upload-hint">4.每行一个用户ID</p>
          </Dragger>
        </Form.Item>
        <Button type="link" onClick={() => downloadTxtFile()}>
          下载txt文件模版
        </Button>
      </div>
    </Form.Item>
  );
};

export default FileUpload;
