import { Form, Select, Button, message, Tooltip, Spin, Input, Typography } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import { queryDwsTableWithDesc, checkOdpsPreTable } from '@/services/api';
import { debounce, set } from 'lodash';
import { connect } from 'dva';
import { sortTablesByUserInput } from '@/utils/tableSort';

const { Option } = Select;

const { Paragraph } = Typography;

const OdpsImport = props => {
  const {
    form,
    id,
    crowdImport: { primaryKeyOptions, partitionOptions },
    dispatch,
    detail,
    onAuthStatusChange
  } = props;
  const [tableAuthConfig, setTableAuthConfig] = useState({
    tableOptions: [],
    loading: false,
  });
  const [checkLoading, setCheckLoading] = useState(false);
  const [fieldLoading, setFieldLoading] = useState(false);
  const [selectedTable, setSelectedTable] = useState('');
  const [selectedUserIdField, setSelectedUserIdField] = useState('');
  const [selectedPartitionField, setSelectedPartitionField] = useState('');
  const [isAuthPassed, setIsAuthPassed] = useState(false);

  // 监听表单字段变化和初始化状态
  useEffect(() => {
    const table = form.getFieldValue(['extInfo', 'ODPS_TABLE']) || detail?.ODPS_TABLE;
    const userIdField = form.getFieldValue(['extInfo', 'ODPS_TABLE_UID']) || detail?.ODPS_TABLE_UID;
    const partitionField = form.getFieldValue(['extInfo', 'ODPS_TABLE_PARTITION']) || detail?.ODPS_TABLE_PARTITION;

    setSelectedTable(table || '');
    setSelectedUserIdField(userIdField || '');
    setSelectedPartitionField(partitionField || '');

    if (id && table && userIdField) {
      setIsAuthPassed(true);
      onAuthStatusChange?.(true);
    }
  }, [form, detail, id]);

  // 新增：监听分区选项和详情数据的变化，确保在编辑模式下正确回显分区字段
  useEffect(() => {
    // 只有在编辑模式下，且分区选项已加载，且详情中有分区字段值时，才进行回显
    if (id && partitionOptions && partitionOptions.length > 0 && detail?.ODPS_TABLE_PARTITION) {
      const currentPartitionValue = form.getFieldValue(['extInfo', 'ODPS_TABLE_PARTITION']);

      // 如果当前表单中的分区字段值为空或与详情不一致，则设置为详情中的值
      if (!currentPartitionValue || currentPartitionValue !== detail.ODPS_TABLE_PARTITION) {
        form.setFieldsValue({
          extInfo: {
            ODPS_TABLE_PARTITION: detail.ODPS_TABLE_PARTITION
          }
        });
        setSelectedPartitionField(detail.ODPS_TABLE_PARTITION);
      }
    }
  }, [partitionOptions, detail, id, form]);

  useEffect(() => {
    onAuthStatusChange?.(isAuthPassed);
  }, [isAuthPassed, onAuthStatusChange]);

  const resetAuthStatus = () => {
    setIsAuthPassed(false);
    onAuthStatusChange?.(false);
  };

  const handleSearch = value => {
    if (!value) return;
    setTableAuthConfig({
      tableOptions: [],
      loading: true,
    });
    queryDwsTableWithDesc({ keyword: value }).then(res => {
      if (res.success && res.data) {
        let result = [];
        Object.keys(res.data).forEach(key => {
          result.push({
            label: key,
            value: key,
            desc: res.data[key],
          });
        });

        // 智能排序：将用户输入能完整匹配的结果放到最前面
        result = sortTablesByUserInput(result, value);

        setTableAuthConfig({
          tableOptions: result,
          loading: false,
        });
      }
    });
  };

  const fetchUserKeyFields = (tableGuid) => {
    const value = tableGuid || form.getFieldValue(['extInfo', 'ODPS_TABLE']);
    if (!value) return Promise.resolve();

    return dispatch({
      type: 'crowdImport/fetchUserKeyFields',
      payload: {
        tableGuid: value,
      },
    });
  };

  // 修改：增加参数来区分是否为用户手动切换表触发
  const getQueryPartitionFields = (tableGuid, isUserTableChange = false) => {
    const value = tableGuid || form.getFieldValue(['extInfo', 'ODPS_TABLE']);
    if (!value) return Promise.resolve();

    return dispatch({
      type: 'crowdImport/getQueryPartitionFields',
      payload: {
        tableGuid: value,
      },
    }).then(res => {
      // 只有在用户手动切换表时，才设置默认的第一个分区字段
      // 编辑模式下的初始化不应该覆盖已保存的分区字段值
      if (isUserTableChange && res && res.length > 0) {
        const defaultPartition = res[0]?.value ?? undefined;
        form.setFieldsValue({ extInfo: { ODPS_TABLE_PARTITION: defaultPartition } });
        setSelectedPartitionField(defaultPartition || '');
      }
      return res;
    });
  };

  const handleChange = async value => {
    if (!value) return;
    setFieldLoading(true);
    resetAuthStatus();

    try {
      // 首先进行表的预校验
      const result = await checkOdpsPreTable({ tableName: value });
      if (!result.data) {
        message.warning(result.msg || '校验失败');
        form.setFieldsValue({ extInfo: { ODPS_TABLE: '' } });
        setFieldLoading(false);
        return;
      }

      // 清空之前的字段选择
      form.setFieldsValue({
        extInfo: {
          ODPS_TABLE_UID: undefined,
          ODPS_TABLE_WHERE: undefined,
          ODPS_TABLE_PARTITION: undefined,
        },
      });
      setSelectedTable(value);
      setSelectedUserIdField('');
      setSelectedPartitionField('');

      // 获取表的字段信息，传入true表示这是用户手动切换表的操作
      message.loading('正在获取表字段信息...', 0.5);
      await Promise.all([
        fetchUserKeyFields(value),
        getQueryPartitionFields(value, true)
      ]);
      message.success('表字段信息获取成功，请选择用户ID字段');

    } catch (error) {
      message.error('获取表信息失败：' + (error.message || '未知错误'));
      form.setFieldsValue({ extInfo: { ODPS_TABLE: '' } });
      setSelectedTable('');
      setSelectedUserIdField('');
      setSelectedPartitionField('');
    } finally {
      setFieldLoading(false);
    }
  };

  const handleUserIdFieldChange = value => {
    setSelectedUserIdField(value || '');
    resetAuthStatus();
  };

  const handlePartitionFieldChange = value => {
    setSelectedPartitionField(value || '');
    // 同步更新表单值，确保表单验证能正确识别
    form.setFieldsValue({
      extInfo: {
        ODPS_TABLE_PARTITION: value
      }
    });
    resetAuthStatus();
  };

  const handleWhereConditionChange = () => {
    resetAuthStatus();
  };

  const isAuthCheckDisabled = () => {
    // 基本条件：必须选择表和用户ID字段
    if (!selectedTable || !selectedUserIdField) {
      return true;
    }

    // 如果有分区选项（即分区字段是必填的），则必须选择分区字段
    if (partitionOptions && partitionOptions.length > 0 && !selectedPartitionField) {
      return true;
    }

    return false;
  };

  const handleAuthCheck = () => {
    const tableName = form.getFieldValue(['extInfo', 'ODPS_TABLE']);
    const userIdField = form.getFieldValue(['extInfo', 'ODPS_TABLE_UID']);
    const partitionField = form.getFieldValue(['extInfo', 'ODPS_TABLE_PARTITION']);
    const whereClause = form.getFieldValue(['extInfo', 'ODPS_TABLE_WHERE']); // 获取where条件

    if (!tableName) {
      message.warning('请先选择ODPS表');
      return;
    }

    if (!userIdField) {
      message.warning('请先选择用户ID字段');
      return;
    }

    setCheckLoading(true);

    // 使用现有接口，传入表名和字段信息进行权限校验
    dispatch({
      type: 'crowdImport/checkOdpsAuthStatus',
      payload: {
        tableName: tableName,
        primaryKey: userIdField, // 传入用户ID字段作为primaryKey参数
        partitionField: partitionField, // 传入分区字段
        whereClause: whereClause, // 新增：传入where条件
      },
        }).then(res => {
      if (res.data && res.success) {
        const partitionMsg = partitionField ? `、分区字段 ${partitionField}` : '';
        const whereMsg = whereClause ? `、where条件 ${whereClause}` : '';
        message.success(`授权校验通过！表 ${tableName}、用户ID字段 ${userIdField}${partitionMsg}${whereMsg} 均具有查询权限`);
        setIsAuthPassed(true);
        fetchUserKeyFields();
      } else if (!res.data && res.success) {
        const partitionMsg = partitionField ? `、分区字段 ${partitionField}` : '';
        const whereMsg = whereClause ? `、where条件 ${whereClause}` : '';
        message.warning(`该表暂未授权诸葛平台，或用户ID字段 ${userIdField}${partitionMsg}${whereMsg} 缺少查询权限，请先进行表授权操作`);
        setIsAuthPassed(false);
        // 权限校验失败不应该清空用户已选择的分区字段
      } else if (!res.success) {
        const partitionMsg = partitionField ? `、分区字段 ${partitionField}` : '';
        const whereMsg = whereClause ? `、where条件 ${whereClause}` : '';
        message.error(res.msg || `授权校验失败，请检查表 ${tableName}、用户ID字段 ${userIdField}${partitionMsg}${whereMsg} 的权限`);
        setIsAuthPassed(false);
        // 权限校验失败不应该清空用户已选择的分区字段
      }
      setCheckLoading(false);
    }).catch(error => {
      message.error(`权限校验过程中发生错误：${error.message || '未知错误'}`);
      setIsAuthPassed(false);
      setCheckLoading(false);
    });
  };

  return (
    <Spin tip="校验中，预计需要30s" spinning={checkLoading}>
      <Form.Item labelCol={{ span: 3 }} wrapperCol={{ span: 21 }} required label="odps表">
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Form.Item
            rules={[{ required: true, message: '请选择odps表' }]}
            noStyle
            name={['extInfo', 'ODPS_TABLE']}
          >
            <Select
              style={{ flex: 1, width: detail.ODPS_TABLE ? 380 : 400 }}
              placeholder="请选择已授权诸葛平台的odps表"
              showSearch
              defaultActiveFirstOption={false}
              filterOption={false}
              notFoundContent={tableAuthConfig.loading ? <Spin size="small" /> : null}
              onSearch={debounce(handleSearch, 500)}
              onChange={handleChange}
              options={tableAuthConfig.tableOptions}
              optionRender={({data: item}) => (
                <Tooltip
                  zIndex={10004}
                  autoAdjustOverflow
                  title={
                    <>
                      {item.label}
                      <br />
                      <span style={{ color: '#999999' }}>描述：{item.desc ?? '空'}</span>
                    </>
                  }
                >
                  {item.label}
                  <br />
                  <span style={{ color: '#999999' }}>描述：{item.desc ?? '空'}</span>
                </Tooltip>
              )}
            >
            </Select>
          </Form.Item>
          {
            (detail.ODPS_TABLE || selectedTable) && (
              <Paragraph
                style={{ display: "inline-block", marginBottom:0 }}
                copyable={{ text: selectedTable || detail.ODPS_TABLE || "空" }}
              />
            )
          }
        </div>
      </Form.Item>
      <Spin spinning={fieldLoading}>
        <Form.Item
          rules={[{ required: true, message: '请选择用户ID字段' }]}
          name={['extInfo', 'ODPS_TABLE_UID']}
          label="用户ID字段"
          extra={primaryKeyOptions.length > 0 ? "请选择用户ID字段" : "请先选择ODPS表以获取字段信息"}
          labelCol={{ span: 3 }}
          wrapperCol={{ span: 21 }}
        >
          <Select
            filterOption={(input, option) => {
              return option.value.indexOf(input) >= 0;
            }}
            placeholder={primaryKeyOptions.length > 0 ? "请选择用户ID字段" : "请先选择ODPS表"}
            showSearch
            options={primaryKeyOptions}
            onChange={handleUserIdFieldChange}
            disabled={primaryKeyOptions.length === 0}
            optionRender={({ data: item }) => (
              <Tooltip
                title={
                  <>
                    {item.label}
                    <br />
                    <span style={{ color: '#999999' }}>描述：{item.desc ?? '空'}</span>
                  </>
                }
              >
                {item.label}
                <br />
                <span style={{ color: '#999999' }}>描述：{item.desc ?? '空'}</span>
              </Tooltip>
            )}
          >
          </Select>
        </Form.Item>
        <Form.Item
          label="where条件"
          name={['extInfo', 'ODPS_TABLE_WHERE']}
          labelCol={{ span: 3 }}
          wrapperCol={{ span: 21 }}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (value && value.includes('where')) {
                  return Promise.reject(new Error('仅需填写where之后的条件即可'));
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Input
            placeholder="请输入where条件"
            onChange={handleWhereConditionChange}
          />
        </Form.Item>
        {partitionOptions?.length > 0 && (
          <Form.Item
            rules={[{ required: true, message: '请选择分区字段' }]}
            name={['extInfo', 'ODPS_TABLE_PARTITION']}
            label="分区字段"
            labelCol={{ span: 3 }}
            wrapperCol={{ span: 21 }}
            extra="仅限日分区字段"
          >
            <Select
              style={{ flex: 1 }}
              options={partitionOptions}
              placeholder="请选择分区字段"
              onChange={handlePartitionFieldChange}
              value={selectedPartitionField}
            />
          </Form.Item>
        )}

        {/* 独立的授权校验按钮区域 */}
        <Form.Item
          labelCol={{ span: 3 }}
          wrapperCol={{ span: 21 }}
          label=" "
          colon={false}
        >
          <Button
            onClick={handleAuthCheck}
            type="primary"
            disabled={isAuthCheckDisabled()}
            title={isAuthCheckDisabled() ?
              (!selectedTable ? '请先选择ODPS表' :
               !selectedUserIdField ? '请先选择用户ID字段' :
               partitionOptions && partitionOptions.length > 0 && !selectedPartitionField ? '请先选择分区字段' :
               '请完善必填信息') :
              '点击校验表和字段的查询权限'}
          >
            授权校验
          </Button>
        </Form.Item>
      </Spin>
      {!isAuthPassed && selectedTable && selectedUserIdField && (
        <div style={{ marginTop: 16, padding: '8px 12px', backgroundColor: '#fff7e6', border: '1px solid #ffd591', borderRadius: '4px', marginLeft: 'calc(12.5% + 8px)', width: 'calc(87.5% - 16px)' }}>
          <span style={{ color: '#fa8c16' }}>⚠️ 请先点击"授权校验"按钮进行权限验证，通过后才能提交人群</span>
        </div>
      )}
      {isAuthPassed && (
        <div style={{ marginTop: 16, padding: '8px 12px', backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: '4px', marginLeft: 'calc(12.5% + 8px)', width: 'calc(87.5% - 16px)' }}>
          <span style={{ color: '#52c41a' }}>✅ 授权校验已通过，可以提交人群</span>
        </div>
      )}
    </Spin>
  );
};

export default connect(state => ({
  crowdImport: state.crowdImport,
}))(OdpsImport);
