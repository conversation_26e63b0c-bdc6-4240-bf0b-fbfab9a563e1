import { Card, Form, Select, Button, message, Spin } from 'antd';
import styles from './index.less';
import CheckCard from '@/components/CheckCard';
import { useEffect, useState } from 'react';
import { WarningFilled } from '@ant-design/icons';
import OdpsImport from './OdpsImport';
import CustomizeSql from './CustomizeSql';
import FileUpload from './FileUpload';
import CrowdInfoForm from '../CrowdInfoForm';
import { Link } from 'umi';
import CrowdGroup from '../../components/CrowdGroup';
import {
  createCrowd,
  queryCrowdCircle,
  queryIsRealTime,
  queryCrowdCircleChilds,
  updateCrowdCircle,
  queryTemplate,
} from '@/services/api';
import { history } from 'umi';
import { uniq } from 'lodash';
import { connect } from 'dva';
import { object } from 'prop-types';
import { isHasDate, getQueryParams } from '../../common/utils';
import dayjs from 'dayjs';

function validateStrings(crowdGroup) {
  const invalidConditions = [
    "AND ",
    "NOTIN ",
    "OR "
  ];

  if (!crowdGroup) {
    return false
  }

  for (let crowdItem of crowdGroup) {
    if (!crowdItem.expression) {
      return false;
    }

    for (let condition of invalidConditions) {
      if (crowdItem.expression.startsWith(condition) || crowdItem.expression.endsWith(condition)) {
        return false;
      }

      const parts = crowdItem.expression.split(condition);
      if (parts.length === 2 && (!parts[1].trim() || parts[1].trim() === '')) {
        return false;
      }
    }
  }

  return true;
}

const updateExpressionsWithCounts = (dataArray, countArray) => {
  return dataArray.map(item => {
    const ids = item.expression.split(/ AND | OR | NOTIN /).map(id => id.trim());

    const itemx = ids.map(id => {
      const countObj = countArray.find(c => String(c.id) === id);
      return countObj;
    });

    item.crowdAmount = itemx.map(item => item.crowdAmount ?? []);
    item.realTime = itemx.map(item => (item.timeType === 'OFFLINE' ? '0' : '1'));

    return item;
  });
};

const Crowd = props => {
  const { dispatch } = props;
  const [checked, setChecked] = useState('ODPS导入');
  const [isRealTime, setIsRealTime] = useState(false);
  const [form] = Form.useForm();
  const { crowdType, id, profileType, type } = getQueryParams();
  const [detailCrowdList, setDetailCrowdList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [sqlParams, setSqlParams] = useState([]);
  const [tempDataList, setTempDataList] = useState([{ id: 0, templateName: '自定义模版' }]);
  const [fileList, setFileList] = useState([]);
  const [detailLoading, setDetailLoading] = useState(false);
  const [detail, setDetail] = useState({});
  const [odpsAuthPassed, setOdpsAuthPassed] = useState(false);
  const [sqlAuthPassed, setSqlAuthPassed] = useState(false);

  useEffect(() => {
    if (tempDataList.length < 2 && checked === '自定义SQL') {
      queryTemplate()
        .then(res => {
          if (res.success) {
            setTempDataList([{ id: 0, templateName: '自定义模版' }, ...res.data.rows]);
            return;
          }
        })
        .catch(() => {
          message.error('请求模版失败');
        });
    }
  }, [tempDataList, checked]);

  useEffect(() => {
    if (id) {
      fetchDetail();
    }
  }, [id]);

  const fetchDetail = async () => {
    const result = await queryCrowdCircle(id);
    setDetailLoading(true);
    if (result.success && result.data) {
      let data = {
        ...result.data,
      };
      if (data.expiredDate) {
        data.expiredDate = dayjs(data.expiredDate);
      }
      if (data.crowdApplyScene) {
        (data.crowdApplyScene || []).map(item => {
          data[item] = true;
        });
      }

      data.deliveryMethod = [];

      if (data.crowdApplyScene.includes('MATCH')) {
        data.deliveryMethod.push('MATCH');
      }
      if (data.crowdApplyScene.includes('PUSH')) {
        data.deliveryMethod.push('PUSH');
      }

      if (data.crowdType === 'ODPS_TABLE_CROWD') {
        setChecked('ODPS导入');
        if (data.extInfo.ODPS_TABLE) {
          dispatch({
            type: 'crowdImport/getQueryPartitionFields',
            payload: {
              tableGuid: data.extInfo.ODPS_TABLE,
            },
          });
          dispatch({
            type: 'crowdImport/fetchUserKeyFields',
            payload: {
              tableGuid: data.extInfo.ODPS_TABLE,
            },
          });
          // 编辑模式下，如果有完整的ODPS配置，默认认为已通过授权校验
          if (data.extInfo.ODPS_TABLE_UID) {
            setOdpsAuthPassed(true);
          }
        }
      }

      if (data.crowdType === 'ODPS_SQL_CROWD') {
        setChecked('自定义SQL');
        // 编辑模式下，如果有SQL内容，默认认为已通过授权校验
        if (data.extInfo && (data.extInfo.ODPS_SQL || data.extInfo.templateSql)) {
          setSqlAuthPassed(true);
        }
      } else if (data.crowdType === 'FILE_CROWD') {
        setChecked('文件上传');
        if (data.extInfo.UPLOAD_FILE_NAME) {
          let file = [
            {
              uid: '2',
              name: 'ids.txt',
              url: data.extInfo.UPLOAD_FILE_URL,
            },
          ];
          setFileList(file);
          data['extInfo']['filePath'] = {
            file: {
              response: {
                data: {
                  fileName: data.extInfo.UPLOAD_FILE_NAME,
                  filePath: data.extInfo.UPLOAD_FILE_PATH,
                },
              },
            },
          };
        }
      }
      if (crowdType === 'OPERATE_CROWD') {
        queryIsRealTime({
          circleType: crowdType,
          conditions: data.conditions,
        }).then(res => {
          setIsRealTime(res.data);
        });
        const res = await queryCrowdCircleChilds({
          circleType: crowdType,
          conditions: data.conditions,
        });

        if (res.data) {
          setDetailCrowdList(res.data);
        }
      }

      if (data.extInfo) {
        if (data.extInfo.templateParams) {
          data = {
            ...data,
            ...data.extInfo.templateParams,
          };
        }
        if (data.extInfo.templateId === '0') {
          //回显运行周期
          if (isHasDate(data.extInfo.ODPS_SQL)) {
            dispatch({
              type: 'crowdImport/updateState',
              payload: {
                isHaveDate: true,
              },
            });
          }
          data.extInfo = {
            ...data.extInfo,
            templateSql: data.extInfo.ODPS_SQL ?? '',
          };
        }
      }
      setDetail(data);
      setDetailLoading(false);
    }
  };

  useEffect(() => {
    if (Object.keys(detail).length > 0) {
      let detailData = { ...detail };
      if (detailCrowdList && crowdType === 'OPERATE_CROWD') {
        let reversedArray2 = (detailCrowdList || []).reverse();
        detailData['conditions'] = {
          ...detailData.conditions,
          group: updateExpressionsWithCounts(detailData.conditions?.group, reversedArray2),
        };
      }
      form.setFieldsValue(detailData);
    }
  }, [detail, detailCrowdList]);

  const handleSubmit = values => {
    const data = values;
    const { needUpdate, expiredDate, deliveryMethod = [], ANALYSIS } = values;

    data.needUpdate = needUpdate ? true : false;
    data.expiredDate = expiredDate.valueOf();
    let physicalProfileCodeValue;
    let crowdTypeValue;
    let applyScenes = [];
    //如果是人群组合
    if (crowdType === 'OPERATE_CROWD') {
      physicalProfileCodeValue = profileType;
      crowdTypeValue = 'OPERATE_EXPRESSION_CROWD';
      if (data.conditions.group) {
        const isRealTime =
          (data.conditions.group || []).filter(item => (item.realTime ?? '').indexOf('1') > -1)
            .length > 0;
        if (isRealTime) {
          //如果是实时的话默认传match
          applyScenes.push('MATCH');
        }
      }
    } else {
      if (checked === 'ODPS导入') {
        physicalProfileCodeValue = 'ODPS_TABLE_CROWD';
        crowdTypeValue = 'ODPS_TABLE_CROWD';
      } else if (checked === '自定义SQL') {
        physicalProfileCodeValue = 'ODPS_SQL_CROWD';
        crowdTypeValue = 'ODPS_SQL_CROWD';
      } else if (checked === '文件上传') {
        physicalProfileCodeValue = 'FILE_CROWD';
        crowdTypeValue = 'FILE_CROWD';
      }
    }

    data.physicalProfileCode = physicalProfileCodeValue;
    data.crowdType = crowdTypeValue;
    if (deliveryMethod.includes('MATCH') && !applyScenes.includes('MATCH')) {
      applyScenes.push('MATCH');
    }
    if (deliveryMethod.includes('PUSH')) {
      applyScenes.push('PUSH');
    }
    if (ANALYSIS) {
      applyScenes.push('ANALYSIS');
    }
    data.applyScenes = uniq(applyScenes);
    if (data.applyScenes.length === 0) {
      message.error('离线人群请选择投放方式或洞察分析其中一项');
      return;
    }

    if (data.extInfo) {
      let str = data.extInfo.templateSql ?? '';
      if (data.extInfo.templateId && data.extInfo.templateId !== '0') {
        const matchData =
          (str.match(/\$\{(.*?)\}/g) &&
            str.match(/\$\{(.*?)\}/g).filter(item => !item.includes('date'))) ||
          [];
        (sqlParams || []).forEach((ele, idx) => {
          str = str.split(matchData[idx]).join(values[ele]);
        });
        let templateParams = {};
        uniq(sqlParams || []).forEach(ele => {
          templateParams[ele] = data[ele];
          delete data[ele];
        });
        data['extInfo'] = {
          ...data['extInfo'],
          ODPS_SQL: str.replace(';', ''),
          templateParams,
        };
      } else if (data.extInfo.templateId === '0' && data.extInfo) {
        data['extInfo'] = {
          ...data['extInfo'],
          ODPS_SQL: str.replace(';', ''),
        };
      }

      if (data.extInfo.filePath) {
        const { filePath = {} } = data.extInfo;
        const {
          file: {
            response: {
              data: { fileName, filePath: url },
            },
          },
        } = filePath;
        if (!fileName) {
          message.error('你需要上传txt文件');
          return;
        }
        data['extInfo'] = {
          UPLOAD_FILE_PATH: url,
          UPLOAD_FILE_NAME: fileName,
        };
        delete data['extInfo']['filePath'];
      }
    }

    delete data.ANALYSIS;
    delete data.updateType;
    delete data.deliveryMethod;

    if (id) {
      data['id'] = id;
    }

    const request = id ? updateCrowdCircle : createCrowd;

    setLoading(true);

    request(data).then(res => {
      if (res.success) {
        message.success(id ? '更新成功' : '创建成功');
        history.push('/crowd-stategy/gather-person');
      } else {
        message.error(res.msg || (id ? '更新失败' : '创建失败'));
      }
      setLoading(false);
    });
  };

  const onChangeSqlVariables = value => {
    setSqlParams(value);
  };

  const handleOdpsAuthStatusChange = (authPassed) => {
    setOdpsAuthPassed(authPassed);
  };

  const handleSqlAuthStatusChange = (authPassed) => {
    setSqlAuthPassed(authPassed);
  };

  const isSubmitDisabled = () => {
    if (type === 'view') {
      return true;
    }

    if (crowdType === 'OPERATE_CROWD') {
      return false;
    }

    if (checked === 'ODPS导入' && !odpsAuthPassed) {
      return true;
    }

    if (checked === '自定义SQL' && !sqlAuthPassed) {
      return true;
    }

    return false;
  };

  const getSubmitButtonTitle = () => {
    if (type === 'view') {
      return '查看模式下无法提交';
    }
    if (crowdType === 'OPERATE_CROWD') {
      return '提交人群';
    }
    if (checked === 'ODPS导入' && !odpsAuthPassed) {
      return '请先通过ODPS授权校验';
    }
    if (checked === '自定义SQL' && !sqlAuthPassed) {
      return '请先通过SQL授权校验';
    }
    return '提交人群';
  };

  return (
    <Form onFinish={handleSubmit} labelWrap form={form}>
      <Spin spinning={detailLoading}>
        <div className={styles.detailRow}>
          <div className={styles.leftCol}>
            <Card
              title={crowdType === 'OPERATE_CROWD' ? '组合配置' : '导入配置'}
              extra={
                <a
                  target="_blank"
                  href="https://aliyuque.antfin.com/qnwrq9/kzl68s/iadc1sxwfgwh18iu#"
                >
                  教程文档&gt;
                </a>
              }
              style={{
                height: '100%',
              }}
            >
              {crowdType === 'OPERATE_CROWD' ? (
                <Form.Item
                  name="conditions"
                  rules={[
                    {
                      validator: async (_, value) => {
                        if (!value) {
                          return Promise.reject('人群组合关系不能为空!');
                        }
                        if (!validateStrings(value.group)) {
                          return Promise.reject("人群项不能为空")
                        }
                        return Promise.resolve()
                      },
                    },
                  ]}
                >
                  <CrowdGroup detailCrowdList={detailCrowdList} id={id} />
                </Form.Item>
              ) : (
                <div className={styles.content}>
                  <div className={styles.cardList}>
                    {[
                      { name: 'ODPS导入', desc: '通过导入授权诸葛的表并指定字段进行人群圈选' },
                      { name: '自定义SQL', desc: '通过导入SQL语句的方式进行人群圈选' },
                      { name: '文件上传', desc: '通过上传txt文件的形式生成固定的人群包' },
                    ].map(ele => {
                      return (
                        <div
                          onClick={() => {
                            if (!id) {
                              setChecked(ele.name);
                              // 切换导入方式时重置ODPS授权状态
                              if (ele.name !== 'ODPS导入') {
                                setOdpsAuthPassed(false);
                              }
                              // 切换导入方式时重置SQL授权状态
                              if (ele.name !== '自定义SQL') {
                                setSqlAuthPassed(false);
                              }
                            }
                          }}
                          className={`${styles.card} ${checked === ele.name ? styles.checked : ''}`}
                          style={{ cursor: id ? 'not-allowed' : 'pointer' }}
                          key={ele.name}
                          span={4}
                        >
                          <p className={styles.name}>{ele.name}</p>
                          <p className={styles.desc}>{ele.desc}</p>
                        </div>
                      );
                    })}
                  </div>
                  {checked !== '文件上传' && (
                    <div className={styles.odpsTableHeader}>
                      <WarningFilled style={{ color: 'red', fontSize: 20 }} />
                      <span style={{ marginLeft: 8, fontSize: 14, fontWeight: 'bold' }}>
                        请先务必保证已将odps表授权给 诸葛云账号 读取表权限（👉
                        <a
                          target="_blank"
                          href="https://aliyuque.antfin.com/qnwrq9/kzl68s/ppra4i?singleDoc#"
                        >
                          授权方式
                        </a>
                        ）
                      </span>
                    </div>
                  )}

                  {checked === 'ODPS导入' && (
                    <OdpsImport
                      form={form}
                      id={id}
                      detail={detail.extInfo ?? {}}
                      onAuthStatusChange={handleOdpsAuthStatusChange}
                    />
                  )}
                  {checked === '自定义SQL' && (
                    <CustomizeSql
                      form={form}
                      tempDataList={tempDataList}
                      onChangeSqlVariables={onChangeSqlVariables}
                      detail={detail.extInfo ?? {}}
                      onAuthStatusChange={handleSqlAuthStatusChange}
                      id={id}
                    />
                  )}
                  {checked === '文件上传' && <FileUpload initFileList={fileList} />}
                </div>
              )}
            </Card>
          </div>
          <div className={styles.rightCol}>
            <Card title="人群信息">
              <CrowdInfoForm
                form={form}
                crowdType={crowdType}
                id={id}
                propsIsRealTime={isRealTime}
                supportPushLoading={false}
                isSupportPush={[]}
                checked={checked}
              />
              <div className={styles.rightBtn}>
                <Button
                  style={{
                    marginRight: 12,
                  }}
                  type="primary"
                  onClick={form.submit}
                  loading={loading}
                  disabled={isSubmitDisabled()}
                  title={getSubmitButtonTitle()}
                >
                  提交
                </Button>
                <Link to="/crowd-stategy/gather-person">
                  <Button>取消</Button>
                </Link>
              </div>
            </Card>
          </div>
        </div>
      </Spin>
    </Form>
  );
};

export default connect(state => ({
  crowdImport: state.crowdImport,
}))(Crowd);
