import { Form, Select, Input, Spin, Modal } from 'antd';
import CodeMirror, { sqlLinter } from '@/components/CodeMirror/CodeMirrorInput';
import { checkCrowdCircleSqlAuth } from '@/services/api';
import { message, Button } from 'antd';
import { useEffect, useState } from 'react';
import { connect } from 'dva';
import styles from './index.less';
import { isHasDate } from '../../../common/utils';

const { Option } = Select;

const CustomizeSql = props => {
  const { form, tempDataList, onChangeSqlVariables, detail, dispatch, onAuthStatusChange, id } = props;
  const [sqlParams, setSqlParams] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isAuthPassed, setIsAuthPassed] = useState(false);
  const [isMultiTable, setIsMultiTable] = useState(false);

  useEffect(() => {
    if (detail && detail.templateSql) {
      extractSqlVariables(detail.templateSql);
      if (detail.templateSql.trim()) {
        setIsAuthPassed(true);
        onAuthStatusChange?.(true);

        // 异步检测是否为多表SQL（编辑模式下）
        checkCrowdCircleSqlAuth({ sql: detail.templateSql }).then(res => {
          // 检查是否是多表SQL的特殊情况
          if (res.success && (res.code === '1001' || res.code === 1001)) {
            setIsMultiTable(true);
          } else {
            setIsMultiTable(false);
          }
        }).catch(error => {
          console.warn('编辑模式下异步检测多表SQL失败:', error);
          // 异步检测失败不影响页面展示，只是无法显示多表提示
        });
      }
    }
  }, [detail]);

  useEffect(() => {
    onAuthStatusChange?.(isAuthPassed);
  }, [isAuthPassed, onAuthStatusChange]);

  // 新建模式下默认选中自定义模版
  useEffect(() => {
    if (!id && tempDataList.length > 0) {
      // 检查是否已经有选中的模版
      const currentTemplateId = form.getFieldValue(['extInfo', 'templateId']);
      if (!currentTemplateId) {
        // 默认选中自定义模版（id为'0'）
        form.setFieldsValue({
          extInfo: {
            templateId: '0',
          },
        });
      }
    }
  }, [id, tempDataList, form]);

  const resetAuthStatus = () => {
    setIsAuthPassed(false);
    setIsMultiTable(false);
    onAuthStatusChange?.(false);
  };

  const extractSqlVariables = e => {
    let resultList = e.match(/\$\{(.*?)\}/g);
    if (Array.isArray(resultList)) {
      resultList = resultList
        .filter(item => !item.toLocaleLowerCase().includes('date'))
        .map(ele => ele.slice(2, -1));
    }
    onChangeSqlVariables(resultList);
    setSqlParams(resultList);
  };

    // SQL语法检查函数（使用现有的sqlLinter逻辑）
  const checkSqlSyntax = (sql) => {
    if (!sql || !sql.trim()) {
      return [];
    }

    // 使用现有的sqlLinter函数进行检查
    const lintErrors = sqlLinter(sql);

    // 将lint结果转换为简单的错误信息数组
    return lintErrors.map(error => error.message);
  };

  // 执行授权校验
  const performAuthCheck = (sql) => {
    setLoading(true);
    checkCrowdCircleSqlAuth({ sql }).then(res => {
      // 检查是否是多表SQL的特殊情况
      if (res.success && (res.code === '1001' || res.code === 1001)) {
        message.warning('检测到多表SQL，请注意相关限制');
        setIsAuthPassed(true);
        setIsMultiTable(true);
      } else if (res.data && res.success) {
        message.success('校验通过！');
        setIsAuthPassed(true);
        setIsMultiTable(false);
      } else if (!res.data && res.success) {
        message.warning('SQL中的表未授权,请先进行表授权操作!');
        setIsAuthPassed(false);
        setIsMultiTable(false);
      } else if (!res.success) {
        message.error(res.msg || '授权校验失败');
        setIsAuthPassed(false);
        setIsMultiTable(false);
      }
      setLoading(false);
    }).catch(error => {
      message.error(`权限校验过程中发生错误：${error.message || '未知错误'}`);
      setIsAuthPassed(false);
      setIsMultiTable(false);
      setLoading(false);
    });
  };

  return (
    <Spin spinning={loading} tip="校验中，预计需要30s">
            <Form.Item
        label="SQL模版"
        labelCol={{ span: 3 }}
        wrapperCol={{ span: 21 }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <Form.Item
            name={['extInfo', 'templateId']}
            rules={[{ required: true, message: '请选择SQL模版' }]}
            style={{ flex: 1, margin: 0 }}
          >
            <Select
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              onChange={e => {
                // 先清理旧的SQL参数表单字段
                sqlParams.forEach(param => {
                  form.setFieldsValue({ [param]: undefined });
                });

                const d = tempDataList.find(item => item.id == e).templateSql;
                form.setFieldsValue({
                  extInfo: {
                    templateSql: d,
                  },
                });

                if (e !== '0') {
                  extractSqlVariables(d);
                  // 选择非自定义模版时，直接设置为授权通过状态
                  setIsAuthPassed(true);
                  setIsMultiTable(false);
                  // 立即通知父组件授权状态已通过
                  onAuthStatusChange?.(true);
                } else {
                  // 选择自定义模版时，重置授权状态和参数
                  setSqlParams([]);
                  resetAuthStatus();
                }
              }}
              placeholder="请选择SQL模版"
            >
              {tempDataList.map(item => (
                <Option key={item.id}>{item.templateName}</Option>
              ))}
            </Select>
          </Form.Item>

        </div>
      </Form.Item>
      <Form.Item
        noStyle
        shouldUpdate={(prevValues, currentValues) =>
          prevValues.extInfo?.templateId !== currentValues.extInfo?.templateId
        }
      >
        {({ getFieldValue }) => {
          const templateValue = getFieldValue(['extInfo', 'templateId']);
          return (
            <Form.Item
              label="SQL语句"
              required
              labelCol={{ span: 3 }}
              wrapperCol={{ span: 21 }}
            >
              <Form.Item
                name={['extInfo', 'templateSql']}
                rules={[{ required: true, message: '请输入SQL语句' }]}
                noStyle
              >
                {templateValue === '0' ? (
                  <CodeMirror
                    mode="text/x-sql"
                    onBlur={() => {
                      const sql = form.getFieldValue(['extInfo', 'templateSql']);
                      const isHaveDate = isHasDate(sql);
                      dispatch({
                        type: 'crowdImport/updateState',
                        payload: {
                          isHaveDate,
                        },
                      });
                    }}
                    onChange={() => {
                      resetAuthStatus();
                    }}
                    style={{ height: '300px', width: '100%' }}
                  />
                ) : (
                  <Input.TextArea style={{ height: '300px', lineHeight: '20px' }} disabled={true} />
                )}
              </Form.Item>
            </Form.Item>
          );
        }}
      </Form.Item>

      {/* 授权校验按钮 */}
      <Form.Item
        noStyle
        shouldUpdate={(prevValues, currentValues) =>
          prevValues.extInfo?.templateId !== currentValues.extInfo?.templateId
        }
      >
        {({ getFieldValue }) => {
          const templateValue = getFieldValue(['extInfo', 'templateId']);
          return templateValue === '0' && (
            <Form.Item
              labelCol={{ span: 3 }}
              wrapperCol={{ span: 21 }}
              label=" "
              colon={false}
            >
              <Button
                onClick={() => {
                  const sql = form.getFieldValue(['extInfo', 'templateSql']);
                  if (!sql || !sql.trim()) {
                    message.warning('请先输入SQL语句');
                    return;
                  }

                  // 检查SQL语法
                  const syntaxErrors = checkSqlSyntax(sql);

                  if (syntaxErrors.length > 0) {
                    // 如果有语法错误，弹出确认对话框
                    Modal.confirm({
                      title: '检测到SQL语法问题',
                      icon: null,
                      content: (
                        <div>
                          <p style={{ marginBottom: '12px', color: '#fa8c16' }}>
                            <strong>发现以下可能的语法问题：</strong>
                          </p>
                          <ul style={{ paddingLeft: '20px', margin: 0 }}>
                            {syntaxErrors.map((error, index) => (
                              <li key={index} style={{ color: '#8c8c8c', marginBottom: '4px' }}>
                                {error}
                              </li>
                            ))}
                          </ul>
                                                      <p style={{ marginTop: '12px', color: '#8c8c8c' }}>
                              建议检查并修复这些问题后再进行授权校验，或者您也可以选择继续进行校验。
                              <br />
                             由于语法检测是前端实现，存在不准确的情况。如果您认为语法没有问题，也建议反馈给 @牧熵，进一步优化这里的校验能力。
                            </p>
                        </div>
                      ),
                      okText: '继续校验',
                      cancelText: '取消',
                      onOk: () => {
                        performAuthCheck(sql);
                      },
                      onCancel: () => {
                        // 用户取消，不执行任何操作
                      },
                    });
                  } else {
                    // 没有语法错误，直接执行授权校验
                    performAuthCheck(sql);
                  }
                }}
                type="primary"
              >
                授权校验
              </Button>
            </Form.Item>
          );
        }}
      </Form.Item>

      {/* 多表SQL注意事项提示 */}
      {isMultiTable && (
        <div style={{
          marginTop: 16,
          marginBottom: 16,
          padding: '12px 16px',
          backgroundColor: '#fff7e6',
          border: '1px solid #ffd591',
          borderRadius: '6px',
          marginLeft: 'calc(12.5% + 8px)',
          width: 'calc(87.5% - 16px)'
        }}>
          <div style={{ color: '#fa8c16', fontWeight: 'bold', marginBottom: '8px' }}>
            注意事项：
          </div>
          <div style={{ color: '#8c8c8c', fontSize: '13px', lineHeight: '1.6' }}>
            1. 建议使用单表 sql，平台原则上不支持多表 sql，此时调度时会设置最低优先级，可能需要 4 小时以上运行时间
            <br />
            2. 多表产生的 sql 报错（含授权问题）请自行解决，平台仅会对单表 sql 前置进行授权校验检测
          </div>
        </div>
      )}

      <Form.Item
        noStyle
        shouldUpdate={(prevValues, currentValues) =>
          prevValues.extInfo?.templateId !== currentValues.extInfo?.templateId
        }
      >
        {({ getFieldValue }) => {
          const templateValue = getFieldValue(['extInfo', 'templateId']);
          if (templateValue === '0' || !templateValue) {
            return null;
          }
          return (
            <Form.Item labelCol={{ span: 4 }} wrapperCol={{ span: 16 }} label="SQL参数">
              {sqlParams.map((ele, index) => (
                <Form.Item
                  key={`${templateValue}-${ele}-${index}`}
                  label={ele}
                  name={ele}
                  rules={[{ required: true, message: `请输入${ele}` }]}
                >
                  <Input
                    onChange={() => {
                      // 只有自定义模板才需要重置授权状态
                      const currentTemplateId = form.getFieldValue(['extInfo', 'templateId']);
                      if (currentTemplateId === '0') {
                        resetAuthStatus();
                      }
                    }}
                  />
                </Form.Item>
              ))}
            </Form.Item>
          );
        }}
      </Form.Item>
      <Form.Item
        noStyle
        shouldUpdate={(prevValues, currentValues) =>
          prevValues.extInfo?.templateId !== currentValues.extInfo?.templateId ||
          prevValues.extInfo?.templateSql !== currentValues.extInfo?.templateSql
        }
      >
        {({ getFieldValue }) => {
          const templateValue = getFieldValue(['extInfo', 'templateId']);
          const sqlValue = getFieldValue(['extInfo', 'templateSql']);

          if (templateValue === '0' && sqlValue && sqlValue.trim()) {
            if (!isAuthPassed) {
              return (
                <div style={{ marginTop: 16, padding: '8px 12px', backgroundColor: '#fff7e6', border: '1px solid #ffd591', borderRadius: '4px', marginLeft: 'calc(12.5% + 8px)', width: 'calc(87.5% - 16px)' }}>
                  <span style={{ color: '#fa8c16' }}>⚠️ 请先点击"授权校验"按钮进行权限验证，通过后才能提交人群</span>
                </div>
              );
            } else {
              if (isMultiTable) {
                return (
                  <div style={{ marginTop: 16, padding: '8px 12px', backgroundColor: '#fff7e6', border: '1px solid #ffd591', borderRadius: '4px', marginLeft: 'calc(12.5% + 8px)', width: 'calc(87.5% - 16px)' }}>
                    <span style={{ color: '#fa8c16' }}>⚠️ 当前 sql 包含多张 odps 表，请自行保障已提前做好授权操作后，再进行提交</span>
                  </div>
                );
              } else {
                return (
                  <div style={{ marginTop: 16, padding: '8px 12px', backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: '4px', marginLeft: 'calc(12.5% + 8px)', width: 'calc(87.5% - 16px)' }}>
                    <span style={{ color: '#52c41a' }}>✅ 授权校验已通过，可以提交人群</span>
                  </div>
                );
              }
            }
          }
          return null;
        }}
      </Form.Item>
    </Spin>
  );
};

export default connect(state => ({
  crowdImport: state.crowdImport,
}))(CustomizeSql);
