.detailRow {
  display: flex;
  .leftCol {
    flex: 1;
    margin-right: 20px;
    :global {
      .ant-card-body {
        display: flex;
        justify-content: center;
      }
    }
    .content {
      width: 780px;
      .cardList {
        display: flex;
        justify-content: center;
        margin-bottom: 20px;
        .card {
          width: 260px;
          padding: 20px;
          color: rgba(0, 0, 0, 0.85);
          font-size: 14px;
          border: 1px solid #d9d9d9;
          border-radius: 2px;
          cursor: pointer;

          .name {
            margin-bottom: 10px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
          }

          .desc {
            margin: 0;
            color: rgba(0, 0, 0, 0.45);
            font-size: 12px;
          }
        }

        .checked {
          border: 1px solid #1890ff;

          .name {
            color: #1890ff;
          }
        }
      }

      .odpsTableHeader {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        padding: 8px;
        background-color: #e5e5e5;
      }
    }
  }
  .rightCol {
    width: 400px;
    background-color: #fff;
    :global {
      .ant-card-body {
        padding-top: 10px;
      }
      .ant-form-item-label {
        padding: 0;
      }
    }
    .rightBtn {
      width: 100%;
      margin-top: 40px;
      text-align: center;
    }
  }
}
