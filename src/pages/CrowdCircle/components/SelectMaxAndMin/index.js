import React from 'react';
import { InputNumber } from 'antd';

class SelectMaxAndMin extends React.PureComponent {
  handleMinChange = values => {
    const { value } = this.props;
    const min = parseInt(values, 10);
    if (Number.isNaN(min)) {
      return;
    }
    this.triggerChange({ min, max: value?.max ?? 0 });
  };

  handleMaxChange = values => {
    const { value } = this.props;
    const max = parseInt(values, 10);
    if (Number.isNaN(max)) {
      return;
    }
    this.triggerChange({ max, min: value?.min ?? 0 });
  };

  triggerChange = changedValue => {
    const { onChange, value } = this.props;
    if (onChange) {
      onChange({
        ...value,
        min: changedValue.min ?? 0,
        max: changedValue.max ?? 0,
      });
    }
  };

  render() {
    const value = this.props.value || {};
    const { max = 0, min = 0 } = value;
    return (
      <div>
        <InputNumber min={0} value={min} onChange={this.handleMinChange} />
        <span style={{ marginLeft: 4 }}>最大值：</span>
        <InputNumber min={0} value={max} onChange={this.handleMaxChange} />
      </div>
    );
  }
}

export default SelectMaxAndMin;
