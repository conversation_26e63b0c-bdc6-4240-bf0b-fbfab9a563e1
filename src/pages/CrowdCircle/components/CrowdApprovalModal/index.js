import React, { useState, useEffect } from 'react';
import { Modal, Form, Row, Col, Button, Input, message } from 'antd';

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

const { TextArea } = Input;
const FormItem = Form.Item;

const CrowdApprovalModal = props => {
  const { visible, onSubmit, close, title, reasonLabel, loading } = props
  const [form] = Form.useForm();

  const onFinish = async (values) => {
    onSubmit(values)
  };


  const onCancel = () => {
    form.resetFields()
    close()
  }

  return (
    <Modal title='提示' width={500} visible={visible} onCancel={onCancel} footer={null} destroyOnClose>
      <div style={{color: 'red'}}>
        {title}
      </div>
      <Form form={form} onFinish={onFinish} layout="vertical">
        <FormItem name="applyReason" label={reasonLabel} rules={[{ required: true, message:  reasonLabel }]}>
          <TextArea rows={4} />
        </FormItem>
        <Row gutter={24}>
          <Col span={4} />
          <Col span={14} style={{ textAlign: 'center' }}>
            <Button onClick={onCancel} style={{ marginRight: 15 }}>
              取消
            </Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              提交
            </Button>
          </Col>
        </Row>
      </Form>
    </Modal>
  )
}

export default CrowdApprovalModal