import { Form, Modal } from 'antd';
import GetUser from '@/components/GetUser';
import { connect } from 'dva';

const UpdateOwner = props => {
  const {
    crowdCircle: { modelConfig },
    form,
    dispatch,
    onSubmit,
  } = props;

  const handleUpdateManager = values => {
    onSubmit(values);
  };

  return (
    <Modal
      title={modelConfig.isOwner ? '负责人' : '管理员'}
      visible={modelConfig.visible}
      onCancel={() => {
        dispatch({
          type: 'crowdCircle/updateState',
          payload: {
            modelConfig: {
              visible: false,
              isOwner: false,
              record: {},
            },
          },
        });
      }}
      onOk={form.submit}
    >
      <Form
        form={form}
        onFinish={handleUpdateManager}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
      >
        <Form.Item
          label={modelConfig.isOwner ? '负责人' : '管理员'}
          name="owner"
          rules={[{ required: true, message: '请选择' }]}
        >
          {modelConfig.visible && (
            <GetUser mode={modelConfig.isOwner ? 'default' : 'multiple'} />
          )}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default connect(state => ({
  crowdCircle: state.crowdCircle,
}))(UpdateOwner);
