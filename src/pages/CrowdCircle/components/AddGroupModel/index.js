import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { PlusOutlined, CloseCircleOutlined } from '@ant-design/icons';
import {
  Modal,
  DatePicker,
  Row,
  Col,
  Button,
  Input,
  Popconfirm,
  notification,
  message,
  Table,
  Tag,
  Popover
} from 'antd';
import { CROWD_PROGRESS_STATUS } from '@/constants';
import dayjs from 'dayjs';
import styles from './index.less';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};
const formItemLayoutWithOutLabel = {
  wrapperCol: {
    xs: { span: 24, offset: 0 },
    sm: { span: 20, offset: 4 },
  },
};

const FormItem = Form.Item;
let id = 0;
@Form.create()
class AddGroupModel extends React.PureComponent {
  state = { checked: true, keys: [], tipVisible: false,childCrowdList: [], reload: false };

  handleSubmit = () => {
    const {
      form: { validateFields },
      groupModeFormData,
      dispatch,
      bucketLoading,
    } = this.props;

    // 防止重复点击
    if (bucketLoading) {
      return;
    }

    validateFields((err, values) => {
      if (!err) {
        const sun = values.names.reduce((prev, cur) => {
          return Number(prev) + Number(cur);
        });
        let childCrowdList = []
        values.names.forEach((ele,i)=>{
          let obj = {}
          if (i === 0) {
            obj = {
              childCrowdIndex: 1,
              bucketPercent: Number(ele),
              bucketRanges: [0 ,Number(ele) - 1]
            }
            childCrowdList.push(obj)
          } else {
            obj = {
              childCrowdIndex: i + 1,
              bucketPercent: Number(ele),
              bucketRanges: [childCrowdList[i - 1].bucketRanges[1] + 1,childCrowdList[i - 1].bucketRanges[1] + Number(ele)]
            }
            childCrowdList.push(obj)
          }
        })
        this.setState({
          childCrowdList
        })
        if (sun < 100) {
          this.setState({
            tipVisible: true,
          });
        } else if (sun > 100) {
          message.warning('输入框总和大于100，请重新分配流量');
        } else if (sun == 100 ) {
          const data = {
            childCrowdList,
            parentId:groupModeFormData.parentId,
            operator:groupModeFormData.operator,
          }
          
          dispatch({
            type: 'crowdCircle/crowdBucket',
            payload: data,
          });
        }
      }
    });
  };

  onToCancel = () => {
    const { keys } = this.state;
    const {
      onCancel,
      form: { getFieldDecorator, getFieldValue },
    } = this.props;
    onCancel();
    this.setState({ checked: true, keys: [], reload: false });
    id = 0;
  };

  remove = k => {
    const { form } = this.props;
    const { keys } = this.state;

    if (keys.length === 1) {
      return;
    }

    this.setState({
      keys: keys.filter(key => key !== k),
    });
  };

  add = () => {
    const { form } = this.props;
    const { keys } = this.state;
    const nextKeys = keys.concat(id++);
    this.setState({
      keys: nextKeys,
    });
  };

  tipHandleCancel = () => {
    this.setState({
      tipVisible: false,
    });
  };

  // 小于等于100%事的继续执行
  tipHandleOk = (key, values) => {
    const { groupModeFormData, dispatch, bucketLoading } = this.props;
    const { childCrowdList } = this.state

    // 防止重复点击
    if (bucketLoading) {
      return;
    }

    this.setState({
      tipVisible: false,
    });
    const data = {
      childCrowdList,
      parentId:groupModeFormData.parentId,
      operator:groupModeFormData.operator,
    }
    
    dispatch({
      type: 'crowdCircle/crowdBucket',
      payload: data,
    });
  };

  // 重新分组
  onRegroup = () => {
    const { onCancel,onRefresh } = this.props;
    this.setState({
      reload: true
    })
  };

  crowdBucketInfoType = (crowdBucketInfo) => {
    if (!crowdBucketInfo) {
      return true
    } else {
      if (Object.keys(crowdBucketInfo).length > 0) {
        return false
      } else {
        return true
      }
    }
  }

  render() {
    const {
      addGroupVisible,
      onCancel,
      groupModeFormData,
      form: { getFieldDecorator, getFieldValue },
    } = this.props;

    const { checked, keys, tipVisible, reload } = this.state;
    const { crowdBucketInfo } = groupModeFormData
 
    const formItems =
      keys &&
      keys.length > 0 &&
      keys.map((k, index) => {
        return (
          <Form.Item label={`组${index + 1}`} required={false} key={k} {...formItemLayout}>
            {getFieldDecorator(`names[${k}]`, {
              rules: [
                {
                  required: true,
                  pattern: new RegExp(/(^[1-9][0-9]$)|(^100&)|(^[1-9]$)$/, 'g'),
                  message: '请输入数值1-99任意整数',
                },
              ],
            })(
              <Input
                placeholder="请输入数值1-99任意整数"
                style={{ width: '60%', marginRight: 8 }}
                suffix={'%'}
              />
            )}
            {keys.length > 1 ? (
              <Popconfirm
                title="是否继续执行删除"
                onConfirm={() => this.remove(k)}
                onCancel={() => this.cancel()}
                okText="继续执行"
                cancelText="取消"
              >
                <CloseCircleOutlined />
              </Popconfirm>
            ) : null}
          </Form.Item>
        );
      });

    let dataSource = []

    if (crowdBucketInfo && crowdBucketInfo.childCrowdList && crowdBucketInfo.childCrowdList.length > 0){
      dataSource = crowdBucketInfo.childCrowdList
    }


    const columns = [
      {
        title: '人群包ID',
        dataIndex: 'childCrowdId',
        key: 'childCrowdId',
      },
      {
        title: '人群包名称',
        dataIndex: 'childCrowdName',
        key: 'childCrowdName',
        render: (text, record, index) => {
          return <Popover content={text}>{text}</Popover>;
      },
      },
      {
        title: '分组比例',
        dataIndex: 'bucketPercent',
        key: 'bucketPercent',
        render: (text, record, index) => {
            return <div>{text}%</div>;
        },
      },
      {
        title: '人群规模',
        dataIndex: 'childCrowdAmount',
        key: 'childCrowdAmount',
      },
      {
        title: '状态',
        dataIndex: 'childCrowdStatus',
        key: 'childCrowdStatus',
        render: (text, record, index) => {
          const obj = CROWD_PROGRESS_STATUS[text];
          if (!record.childCrowdId) {
            return null
          }
          return <Tag color={obj.color}>{obj.text}</Tag>;
        },
      },
    ];

    return (
      <>
        <Modal
          title="实验分组"
          footer={null}
          open={addGroupVisible}
          onCancel={() => this.onToCancel()}
          width={'600px'}
        >
          {(reload || this.crowdBucketInfoType(crowdBucketInfo)) ? (
            checked ? (
              <div style={{ display: 'flex', justifyContent: 'center', padding: '30px 0' }}>
                <Button
                  type="primary"
                  size="large"
                  onClick={() => {
                    this.setState({ checked: false });
                  }}
                >
                  创建分组
                </Button>
              </div>
            ) : (
              <Form>
                {formItems}
                {id < 10 ? (
                  <Form.Item {...formItemLayoutWithOutLabel}>
                    <Button type="dashed" onClick={this.add} style={{ width: '60%' }}>
                      <PlusOutlined /> 添加分组
                    </Button>
                  </Form.Item>
                ) : null}
                <Row gutter={24}>
                  <Col span={4} />
                  <Col span={14} style={{ textAlign: 'center' }}>
                    <Button onClick={() => this.onToCancel()} style={{ marginRight: 15 }}>
                      取消
                    </Button>
                    <Button 
                      type="primary" 
                      onClick={this.handleSubmit}
                      loading={this.props.bucketLoading}
                      disabled={this.props.bucketLoading}
                    >
                      保存
                    </Button>
                  </Col>
                </Row>
              </Form>
            )
          ) : (
            <div className={styles.tableName}>
              <Table dataSource={dataSource} columns={columns} pagination={false}/>
              <Row gutter={24} style={{marginTop:'20px'}}>
                <Col span={4} />
                <Col span={14} style={{ textAlign: 'center' }}>
                  <Popconfirm
                    title="重新分组系统会自动删除已有分组人群包，请谨慎执行！！！"
                    onConfirm={this.onRegroup}
                    onCancel={this.onCancel}
                    okText="继续执行"
                    cancelText="取消"
                  >
                    <Button type="primary">
                      重新分组
                    </Button>
                  </Popconfirm>
                </Col>
              </Row>
            </div>
          )}
        </Modal>
        <Modal title="提示" visible={tipVisible} footer={null} style={{ top: 20 }} onCancel={() => this.tipHandleCancel()}>
          <p>输入框总和小于100,确认是否继续执行</p>
          <Row gutter={24}>
            <Col span={4} />
            <Col span={14} style={{ textAlign: 'center' }}>
              <Button onClick={() => this.tipHandleCancel()} style={{ marginRight: 15 }}>
                取消
              </Button>
              <Button 
                type="primary" 
                onClick={this.tipHandleOk}
                loading={this.props.bucketLoading}
                disabled={this.props.bucketLoading}
              >
                继续执行
              </Button>
            </Col>
          </Row>
        </Modal>
      </>
    );
  }
}

export default AddGroupModel;
