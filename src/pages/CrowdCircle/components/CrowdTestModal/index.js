import React from 'react';
import { Form } from '@ant-design/compatible';
import { get, debounce } from 'lodash';
import '@ant-design/compatible/assets/index.css';
import { queryCrowd, isUserInCrowd, queryMatchIdTypes, singleMatch } from '@/services/api';
import { Modal, Row, Col, Button, Input, Select, message, Typography } from 'antd';

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

const FormItem = Form.Item;
const { Option } = Select;
const { Text } = Typography;

// 用户名称文案
const USER_LABEL_TEXT = {
  TAOBAO_USER: '淘宝ID',
  SCRM_USER: 'Scrm-Oneid',
  DEVICE: '输入ID',
};

// 用户描述文案
const USER_PLACEHOLDER_TEXT = {
  TAOBAO_USER: '淘宝ID',
  SCRM_USER: 'Scrm-Oneid',
  DEVICE: '设备id',
};

@Form.create()
class CrowdTestModal extends React.PureComponent {
  constructor(props) {
    super(props);
    this.fetchCrowd = debounce(this.fetchCrowd, 500);
    this.state = {
      crowdList: [],
      result: null,
      inputTypeOpts: [],
    };
  }

  componentWillReceiveProps(nextProps) {
    if (get(nextProps, 'editFormData.physicalProfileCode', null) === 'DEVICE' && get(nextProps, 'editModalVisible', false) && (get(nextProps, 'editModalVisible', false) !== this.props.editModalVisible)) {
      this.fetchInputType()
    }
  }

  // 查询输入类型枚举
  fetchInputType = () => {
    queryMatchIdTypes().then(res => {
      if (res.success) {
        this.setState({
          inputTypeOpts: get(res, 'data', []).map(ele => ({ label: ele, value: ele }))
        })
      } else {
        message.error(res.msg)
      }
    })
  };


  // 设备id标签圈人测试提交
  onDeviceOneIdSubmit = (values) => {
    const { userId, sourceIdType, crowdIds = [] } = values;
    if (crowdIds.length > 1) {
      message.error('设备id只允许选择一条人群信息')
      return
    }
    singleMatch({
      sourceIdType: sourceIdType,
      sourceId: userId,
      groupId: crowdIds.map(item => item.key).join(','),
    })
      .then(res => {
        if (res.code === '200') {
          if (res.data) {
            message.success('查询成功');
            this.setState({
              result: res.data,
            });
          }
        } else {
          message.error(res.msg);
        }
      })
      .catch(() => {
        message.error('请求失败');
      });
  }

  onTestSubmit = (values) => {
    const { userId, crowdIds = [] } = values;
    isUserInCrowd({
      userId,
      crowdIds: crowdIds.map(item => item.key),
    })
      .then(res => {
        if (res.code === '200') {
          if (res.data) {
            message.success('查询成功');
            this.setState({
              result: res.data,
            });
          }
        } else {
          message.error(res.msg);
        }
      })
      .catch(() => {
        message.error('请求失败');
      });
  }


  handleSubmit = () => {
    const {
      form: { validateFields },
      editFormData,
    } = this.props;
    const { physicalProfileCode } = editFormData;

    validateFields((err, values) => {
      if (!err) {
        if (['DEVICE'].includes(physicalProfileCode)) {
          this.onDeviceOneIdSubmit(values)
        } else {
          this.onTestSubmit(values)
        }
      }
    });
  };

  fetchCrowd = value => {
    queryCrowd({
      pageNum: 1,
      pageSize: 10,
      type: 'all',
      crowdName: value,
    })
      .then(res => {
        const data = (res && res.data && res.data.rows) || [];
        this.setState({
          crowdList: data,
        });
      })
      .catch(() => {
        message.error('请求失败');
      });
  };

  onCancel = () => {
    const { onCancel = () => { }, form } = this.props;

    form.resetFields();
    this.setState({
      crowdList: [],
      result: null,
    });
    onCancel();
  };

  // 查询结果
  resultRender = (profileType, inputCrowdList, inputUserInfo, result ) => {
    if (['TAOBAO_USER', 'SCRM_USER'].includes(profileType)) {
      return (
        <div>
          <div>{`用户：${inputUserInfo}`}</div>
          <div>
            {inputCrowdList.map(c => {
              const isExist = !!result[c.key];
              return (
                typeof result[c.key] !== 'undefined' && (
                  <div>
                    <span>{isExist ? '在' : '不在'}</span>
                    <span>
                      <span>人群</span>
                      <span style={{ color: 'red' }}>{` ${c.label} `}</span>
                      <span>中</span>
                    </span>
                  </div>
                )
              );
            })}
          </div>
        </div>
      );
    }

    if (['DEVICE'].includes(profileType)) {
      return <Text mark>查询到{result.oneIdTotal || 0}个唯一设备ID，其中有{result.oneIdMatched || 0}个唯一设备id 匹配</Text>
    }
  };

  render() {
    const {
      editModalVisible,
      form: { getFieldDecorator, getFieldValue },
      editFormData,
    } = this.props;
    const { crowdList, result, inputTypeOpts } = this.state;

    const defaultCrowd = [{ key: editFormData.id, label: editFormData.crowdName }];
    const inputUserInfo = getFieldValue('userId') || '';
    const inputCrowdList = getFieldValue('crowdIds');
    const { physicalProfileCode } = editFormData;
    return (
      <Modal
        width={800}
        title={
          <span>
            <span>用户命中人群查询</span>
            <span style={{ marginLeft: 8, fontSize: 13, color: 'red' }}>
              注：使用场景选择了人群匹配才支持查询
            </span>
          </span>
        }
        footer={null}
        open={editModalVisible}
        onCancel={this.onCancel}
      >
        <Form {...FORM_ITEM_LAYOUT}>
          {/* 设备类型才支持输入类型 */}
          {['DEVICE'].includes(physicalProfileCode) && (
            <FormItem label="输入类型">
              {getFieldDecorator('sourceIdType', {
                rules: [{ required: true, message: '请选择输入类型' }],
              })(<Select allowClear options={inputTypeOpts} placeholder='请选择输入类型' />)}
            </FormItem>
          )}
          <FormItem label={USER_LABEL_TEXT[physicalProfileCode]}>
            {getFieldDecorator('userId', {
              rules: [{ required: true, message: '请输入用户ID' }],
            })(<Input placeholder={`请输入${USER_PLACEHOLDER_TEXT[physicalProfileCode]}`} allowClear />)}
          </FormItem>
          <FormItem label="人群信息">
            {getFieldDecorator('crowdIds', {
              initialValue: defaultCrowd,
              rules: [{ required: true, message: '请选择人群信息' }],
            })(
              <Select
                labelInValue
                mode='multiple'
                showSearch
                allowClear
                showArrow
                filterOption={false}
                defaultActiveFirstOption={false}
                notFoundContent={null}
                onSearch={this.fetchCrowd}
              >
                {crowdList &&
                  crowdList.length > 0 &&
                  crowdList.map(item => (
                    <Option key={item.id} value={item.id}>
                      {item.crowdName}
                    </Option>
                  ))}
              </Select>
            )}
          </FormItem>
          <Row gutter={24}>
            <Col span={4} />
            <Col span={14} style={{ textAlign: 'center' }}>
              <Button onClick={this.onCancel} style={{ marginRight: 15 }}>
                取消
              </Button>
              <Button type="primary" onClick={this.handleSubmit}>
                查询
              </Button>
            </Col>
          </Row>
          {result !== null && <FormItem label="查询结果">
            {this.resultRender(physicalProfileCode, inputCrowdList, inputUserInfo, result)}
          </FormItem>}
        </Form>
      </Modal>
    );
  }
}

export default CrowdTestModal;
