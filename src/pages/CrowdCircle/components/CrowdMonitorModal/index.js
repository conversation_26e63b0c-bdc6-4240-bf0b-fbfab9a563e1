import React, { useState, useEffect } from 'react';
import { Modal, Checkbox, Input, Button, Table, Tooltip, Form, DatePicker } from 'antd';
import { EditOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import styles from './index.less';
import GetUser from '@/components/GetUser';

// 枚举定义
const ALERT_SCENARIO = {
  CROWD_EXPIRE: { key: 'CROWD_EXPIRE', label: '人群即将过期' },
  CROWD_BUILD_SUCCESS: { key: 'CROWD_BUILD_SUCCESS', label: '人群构建成功' },
  CROWD_BUILD_FAILED: { key: 'CROWD_BUILD_FAILED', label: '人群构建失败' },
  CROWD_BUILD_ERROR: { key: 'CROWD_BUILD_ERROR', label: '人群构建异常' },
  CROWD_BLACK_WHITE_LIST_CHANGE: {
    key: 'CROWD_BLACK_WHITE_LIST_CHANGE',
    label: '人群黑白名单变更',
  },
  CROWD_DATA_FLUCTUATE: { key: 'CROWD_DATA_FLUCTUATE', label: '人群数据量波动' },
  CROWD_BASE_LINE: { key: 'CROWD_BASE_LINE', label: '人群构建基线' },
};

const NOTIFY_METHOD = {
  ZHUGE_MSG: { key: 'ZHUGE_MSG', label: '站内信' },
  DING_TALK: { key: 'DING_TALK', label: '钉钉告警' },
  MAIL: { key: 'MAIL', label: '邮件告警' },
  SMS: { key: 'SMS', label: '短信告警' },
  PHONE: { key: 'PHONE', label: '电话告警' },
};

const NOTIFY_PERSON = {
  OWNER: { key: 'OWNER', label: '负责人' },
  MANAGER: { key: 'MANAGER', label: '管理员' },
  CUSTOM: { key: 'CUSTOM', label: '自定义' },
};

const initialData = [
  {
    sceneType: ALERT_SCENARIO.CROWD_BUILD_SUCCESS.key,
    alarmTypes: [NOTIFY_METHOD.MAIL.key],
    receiverTypes: [NOTIFY_PERSON.OWNER.key],
    customReceivers: [],
  },
  {
    sceneType: ALERT_SCENARIO.CROWD_BUILD_FAILED.key,
    alarmTypes: [NOTIFY_METHOD.DING_TALK.key],
    receiverTypes: [NOTIFY_PERSON.OWNER.key, NOTIFY_PERSON.MANAGER.key],
    customReceivers: [],
  },
  {
    sceneType: ALERT_SCENARIO.CROWD_BLACK_WHITE_LIST_CHANGE.key,
    alarmTypes: [NOTIFY_METHOD.ZHUGE_MSG.key],
    receiverTypes: [NOTIFY_PERSON.OWNER.key, NOTIFY_PERSON.MANAGER.key],
    customReceivers: [],
  },
  {
    sceneType: ALERT_SCENARIO.CROWD_EXPIRE.key,
    alarmTypes: [NOTIFY_METHOD.DING_TALK.key, NOTIFY_METHOD.MAIL.key],
    receiverTypes: [NOTIFY_PERSON.OWNER.key, NOTIFY_PERSON.MANAGER.key],
    customReceivers: [],
  },
  {
    sceneType: ALERT_SCENARIO.CROWD_BUILD_ERROR.key,
    alarmTypes: [NOTIFY_METHOD.DING_TALK.key, NOTIFY_METHOD.MAIL.key],
    receiverTypes: [NOTIFY_PERSON.OWNER.key, NOTIFY_PERSON.MANAGER.key],
    customReceivers: [],
  },
  {
    sceneType: ALERT_SCENARIO.CROWD_DATA_FLUCTUATE.key,
    alarmTypes: [NOTIFY_METHOD.ZHUGE_MSG.key, NOTIFY_METHOD.MAIL.key],
    receiverTypes: [NOTIFY_PERSON.OWNER.key],
    customReceivers: [],
    extInfo: {
      dataFlucDimMap: {
        YESTERDAY: 0,
        DAYS_7_BEFORE: 0,
        DAYS_7_AVG: 0,
      },
    },
  },
  {
    sceneType: ALERT_SCENARIO.CROWD_BASE_LINE.key,
    isValid: false,
    alarmTypes: [NOTIFY_METHOD.DING_TALK.key, NOTIFY_METHOD.ZHUGE_MSG.key],
    receiverTypes: [NOTIFY_PERSON.OWNER.key, NOTIFY_PERSON.MANAGER.key],
    customReceivers: [],
    extInfo: {
      baseLineTime: '10:00',
    },
  },
];

const CrowdMonitorModal = ({ visible, onCancel, onOk, crowdMonitorFormData = initialData }) => {
  const [form] = Form.useForm();
  // 状态：用于UI展示的数据
  const [displayData, setDisplayData] = useState([]);

  // 自定义通知人弹框状态
  const [customModalVisible, setCustomModalVisible] = useState(false);
  const [currentScenarioType, setCurrentScenarioType] = useState(null);

  

  useEffect(() => {
    if (crowdMonitorFormData && crowdMonitorFormData.length) {
      // 转换为UI展示所需的格式
      const uiData = crowdMonitorFormData.map(item => {
        const scenarioInfo = Object.values(ALERT_SCENARIO).find(s => s.key === item.sceneType);

        return {
          key: item.sceneType,
          id: item.sceneType,
          name: scenarioInfo ? scenarioInfo.label : item.sceneType,
          description: getDescriptionByType(item.sceneType),
          frequency: getFrequencyByType(item.sceneType),
          isValid: item.isValid,
          alarmTypes: (item.isValid && item.alarmTypes) || [],
          receiverTypes: (item.isValid && item.receiverTypes) || [],
          customReceivers: (item.isValid && item.customReceivers) || [],
          escalation: getEscalationByType(item.sceneType),
          extInfo: {
            dataFlucDimMap:
              (item.sceneType === ALERT_SCENARIO.CROWD_DATA_FLUCTUATE.key && {
                YESTERDAY: 0,
                DAYS_7_BEFORE: 0,
                DAYS_7_AVG: 0,
                ...(item.extInfo?.dataFlucDimMap || {}),
              }),
            baseLineTime:
              (item.sceneType === ALERT_SCENARIO.CROWD_BASE_LINE.key && 
                (item.extInfo?.baseLineTime || '10:00')),
          },
        };
      });

      setDisplayData(uiData);
    }
  }, [crowdMonitorFormData]);

  // 根据场景类型获取描述
  const getDescriptionByType = type => {
    switch (type) {
      case ALERT_SCENARIO.CROWD_BUILD_SUCCESS.key:
        return '人群创建成功且支持线上投放/分析';
      case ALERT_SCENARIO.CROWD_BUILD_FAILED.key:
        return '人群创建失败时';
      case ALERT_SCENARIO.CROWD_BLACK_WHITE_LIST_CHANGE.key:
        return '修改人群黑白名单信息';
      case ALERT_SCENARIO.CROWD_EXPIRE.key:
        return '人群有效期即将过期（7日后）';
      case ALERT_SCENARIO.CROWD_BUILD_ERROR.key:
        return '使用"已下线"标签、引用"已过期"人群、"ODPS等入"人群且日更新方式依赖底表的分区连续3天未产出';
      case ALERT_SCENARIO.CROWD_DATA_FLUCTUATE.key:
        return '';
      case ALERT_SCENARIO.CROWD_BASE_LINE.key:
        return '';
      default:
        return '';
    }
  };

  const getFrequencyByType = type => {
    if (
      [
        ALERT_SCENARIO.CROWD_EXPIRE.key,
        ALERT_SCENARIO.CROWD_BUILD_ERROR.key,
        ALERT_SCENARIO.CROWD_DATA_FLUCTUATE.key,
        ALERT_SCENARIO.CROWD_BASE_LINE.key,
      ].includes(type)
    ) {
      return '每日一次';
    }
    return '每次触发';
  };

  const getEscalationByType = type => {
    if (type === ALERT_SCENARIO.CROWD_BUILD_FAILED.key) {
      return '连续7天构建失败，将不再更新人群数据';
    } else if (type === ALERT_SCENARIO.CROWD_BUILD_ERROR.key) {
      return '超7天未处理，将不再更新人群数据';
    }
    return null;
  };

  // 更新场景启用状态
  const toggleScenario = id => {
    setDisplayData(
      displayData.map(item => {
        if (item.id === id) {
          const isValid = !item.isValid;

          // 如果禁用，清空所有选项
          if (!isValid) {
            return {
              ...item,
              isValid: false,
              alarmTypes: [],
              receiverTypes: [],
              customReceivers: [],
            };
          }

          // 启用时，从服务端返回的原始数据 crowdMonitorFormData 中找到正确的配置进行恢复
          const originalData = crowdMonitorFormData.find(serverItem => serverItem.sceneType === id);

          return {
            ...item,
            isValid: true,
            alarmTypes: originalData?.alarmTypes || [],
            receiverTypes: originalData?.receiverTypes || [],
            customReceivers: originalData?.customReceivers || [],
          };
        }
        return item;
      })
    );
  };

  // 更新通知方式
  const toggleNotifyMethod = (id, method) => {
    setDisplayData(
      displayData.map(item => {
        if (item.id === id) {
          const methods = [...item.alarmTypes];
          const index = methods.indexOf(method);

          if (index > -1) {
            methods.splice(index, 1);
          } else {
            methods.push(method);
          }

          // 如果没有通知方式，则禁用场景
          const isValid = methods.length > 0;

          // 如果禁用，清空所有选项
          if (!isValid) {
            return {
              ...item,
              isValid: false,
              sceneType: [],
              receiverTypes: [],
              customReceivers: [],
            };
          }

          return { ...item, alarmTypes: methods, isValid };
        }
        return item;
      })
    );
  };

  // 更新通知人
  const toggleNotifyPerson = (id, person) => {
    setDisplayData(
      displayData.map(item => {
        if (item.id === id) {
          const persons = [...item.receiverTypes];
          const index = persons.indexOf(person);

          if (index > -1) {
            persons.splice(index, 1);

            // 如果取消了自定义通知人，清空自定义通知人列表
            if (person === NOTIFY_PERSON.CUSTOM.key) {
              return { ...item, receiverTypes: persons, customReceivers: [] };
            }
          } else {
            persons.push(person);
          }

          // 如果没有选择任何通知人但有通知方式，确保场景仍然启用
          const isValid = persons.length > 0 || item.alarmTypes.length > 0;

          return { ...item, receiverTypes: persons, isValid };
        }
        return item;
      })
    );
  };

  // 打开自定义通知人弹框
  const openCustomModal = (id, customReceivers) => {
    form.setFieldsValue({ customPersonInput: customReceivers });
    setCurrentScenarioType(id);
    setCustomModalVisible(true);
  };

  // 添加自定义通知人
  const addCustomPerson = ({ customPersonInput }) => {
    if (customPersonInput) {
      setDisplayData(
        displayData.map(item => {
          if (item.id === currentScenarioType) {
            // 确保notifyPersons中包含CUSTOM
            let receiverTypes = [...item.receiverTypes];
            if (!receiverTypes.includes(NOTIFY_PERSON.CUSTOM.key)) {
              receiverTypes.push(NOTIFY_PERSON.CUSTOM.key);
            }

            return {
              ...item,
              isValid: true,
              receiverTypes,
              customReceivers: [...item.customReceivers, ...customPersonInput],
              // 如果没有通知方式，添加默认通知方式
              sceneType: (item.sceneType || []).length ? item.sceneType : [NOTIFY_METHOD.MAIL.key],
            };
          }
          return item;
        })
      );

      form.resetFields();
      setCustomModalVisible(false);
    }
  };

  // 更新百分比输入
  const updatePercentInput = (id, field, value) => {
    setDisplayData(
      displayData.map(item => {
        if (item.id === id && item.extInfo) {
          // 如果有输入值，确保场景启用
          const hasValue = value > 0;
          const isValid = hasValue || item.sceneType || item.receiverTypes.length > 0;

          return {
            ...item,
            isValid,
            extInfo: {
              ...item.extInfo,
              dataFlucDimMap: {
                ...item.extInfo.dataFlucDimMap,
                [field]: value,
              },
            },
            // 如果启用但没有通知方式，添加默认通知方式
            alarmTypes:
              isValid && !item.alarmTypes.length ? [NOTIFY_METHOD.MAIL.key] : item.alarmTypes,
            // 如果启用但没有通知人，添加默认通知人
            receiverTypes:
              isValid && !item.receiverTypes.length
                ? [NOTIFY_PERSON.OWNER.key]
                : item.receiverTypes,
          };
        }
        return item;
      })
    );
  };

  // 更新基线时间
  const updateBaseLineTime = (id, time) => {
    // 如果 time 为 null (用户清空了输入)，则使用默认值
    const timeString = time ? time.format('HH:mm') : '00:00';
    setDisplayData(
      displayData.map(item => {
        if (item.id === id) {
          return {
            ...item,
            extInfo: {
              ...item.extInfo,
              baseLineTime: timeString,
            },
          };
        }
        return item;
      })
    );
  };

  // 更新提交数据
  const updateSubmitData = () => {
    return displayData.map(item => {
      const result = {
        sceneType: item.id,
        alarmTypes: item.isValid ? item.alarmTypes : [],
        isValid: item.isValid,
        receiverTypes: item.receiverTypes,
        customReceivers: item.customReceivers,
      };

      // 构建 extInfo 对象
      const extInfo = {};
      if (item.extInfo?.dataFlucDimMap) {
        extInfo.dataFlucDimMap = item.extInfo.dataFlucDimMap;
      }
      if (item.extInfo?.baseLineTime) {
        extInfo.baseLineTime = item.extInfo.baseLineTime;
      }

      // 如果有 extInfo 数据，则添加到结果中
      if (Object.keys(extInfo).length > 0) {
        result.extInfo = extInfo;
      }

      return result;
    });
  };

  // 处理确认按钮
  const handleOk = () => {
    // 最后更新一次提交数据，确保数据是最新的
    const result = updateSubmitData();
    // 提交数据给父组件
    onOk(result);
  };

  // 表格列配置
  const columns = [
    {
      title: '告警场景',
      dataIndex: 'name',
      key: 'name',
      width: 148,
      fixed: 'left',
      render: (text, record) => (
        <Checkbox checked={record.isValid} onChange={() => toggleScenario(record.id)}>
          {text}
        </Checkbox>
      ),
    },
    {
      title: '场景描述',
      dataIndex: 'description',
      key: 'description',
      width: 150,
      render: (text, record) => (
        <div>
          {text}
          {record.id === ALERT_SCENARIO.CROWD_DATA_FLUCTUATE.key && record.extInfo && (
            <>
              <div className={styles.percent_row}>
                <span className={styles.percent_label}>对比昨天 波动超过</span>
                <Input
                  className={styles.percent_input}
                  value={record.extInfo.dataFlucDimMap.YESTERDAY}
                  onChange={e => updatePercentInput(record.id, 'YESTERDAY', Number(e.target.value))}
                  suffix="%"
                />
              </div>
              <div className={styles.percent_row}>
                <span className={styles.percent_label}>对比7天前 波动超过</span>
                <Input
                  className={styles.percent_input}
                  value={record.extInfo.dataFlucDimMap.DAYS_7_BEFORE}
                  onChange={e =>
                    updatePercentInput(record.id, 'DAYS_7_BEFORE', Number(e.target.value))
                  }
                  suffix="%"
                />
              </div>
              <div className={styles.percent_row}>
                <span className={styles.percent_label}>对比前7天均值 波动超过</span>
                <Input
                  className={styles.percent_input}
                  value={record.extInfo.dataFlucDimMap.DAYS_7_AVG}
                  onChange={e =>
                    updatePercentInput(record.id, 'DAYS_7_AVG', Number(e.target.value))
                  }
                  suffix="%"
                />
              </div>
            </>
          )}
          {record.id === ALERT_SCENARIO.CROWD_BASE_LINE.key && record.extInfo && (
            <div style={{ display: 'flex', flexDirection: 'column' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <span>人群在指定时间</span>
                <DatePicker
                  picker="time"
                  value={
                    record.extInfo.baseLineTime
                      ? dayjs(record.extInfo.baseLineTime, 'HH:mm')
                      : dayjs('00:00', 'HH:mm')
                  }
                  format="HH:mm"
                  onChange={time => updateBaseLineTime(record.id, time)}
                  placeholder="选择时间"
                  style={{ width: 100, marginLeft: 8 }}
                  showNow={false}
                />
              </div>
              <div style={{ marginTop: 4 }}>前未构建成功</div>
            </div>
          )}
        </div>
      ),
    },
    {
      title: '通知频率',
      dataIndex: 'frequency',
      key: 'frequency',
      width: 90,
    },
    {
      title: '告警方式',
      key: 'alarmTypes',
      width: 250,
      render: (_, record) => (
        <div style={{ display: 'flex', flexWrap: 'wrap' }}>
          {[
            ALERT_SCENARIO.CROWD_BUILD_FAILED.key,
            ALERT_SCENARIO.CROWD_EXPIRE.key,
            ALERT_SCENARIO.CROWD_BUILD_ERROR.key,
            ALERT_SCENARIO.CROWD_DATA_FLUCTUATE.key,
            ALERT_SCENARIO.CROWD_BASE_LINE.key,
          ].includes(record.id) && (
            <Checkbox
              checked={record.alarmTypes.includes(NOTIFY_METHOD.DING_TALK.key)}
              onChange={() => toggleNotifyMethod(record.id, NOTIFY_METHOD.DING_TALK.key)}
            >
              钉钉
            </Checkbox>
          )}
          <Checkbox
            checked={record.alarmTypes.includes(NOTIFY_METHOD.ZHUGE_MSG.key)}
            onChange={() => toggleNotifyMethod(record.id, NOTIFY_METHOD.ZHUGE_MSG.key)}
          >
            站内信
          </Checkbox>
          <Checkbox
            checked={record.alarmTypes.includes(NOTIFY_METHOD.MAIL.key)}
            onChange={() => toggleNotifyMethod(record.id, NOTIFY_METHOD.MAIL.key)}
          >
            邮件
          </Checkbox>
          {[
            ALERT_SCENARIO.CROWD_BUILD_FAILED.key,
            ALERT_SCENARIO.CROWD_EXPIRE.key,
            ALERT_SCENARIO.CROWD_BUILD_ERROR.key,
            ALERT_SCENARIO.CROWD_BASE_LINE.key,
          ].includes(record.id) && (
            <>
              <Checkbox
                checked={record.alarmTypes.includes(NOTIFY_METHOD.SMS.key)}
                onChange={() => toggleNotifyMethod(record.id, NOTIFY_METHOD.SMS.key)}
                style={{
                  margin: 0,
                }}
              >
                短信
              </Checkbox>
              <Checkbox
                checked={record.alarmTypes.includes(NOTIFY_METHOD.PHONE.key)}
                onChange={() => toggleNotifyMethod(record.id, NOTIFY_METHOD.PHONE.key)}
              >
                电话
              </Checkbox>
            </>
          )}
        </div>
      ),
    },
    {
      title: '通知人',
      key: 'receiverTypes',
      width: 280,
      render: (_, record) => (
        <div>
          <div direction="vertical" size="small">
            <Checkbox
              checked={record.receiverTypes.includes(NOTIFY_PERSON.OWNER.key)}
              onChange={() => toggleNotifyPerson(record.id, NOTIFY_PERSON.OWNER.key)}
            >
              负责人
            </Checkbox>
            <Checkbox
              checked={record.receiverTypes.includes(NOTIFY_PERSON.MANAGER.key)}
              onChange={() => toggleNotifyPerson(record.id, NOTIFY_PERSON.MANAGER.key)}
            >
              管理员
            </Checkbox>
            <div className={styles.custom_person_row}>
              <Checkbox
                checked={record.receiverTypes.includes(NOTIFY_PERSON.CUSTOM.key)}
                onChange={() => toggleNotifyPerson(record.id, NOTIFY_PERSON.CUSTOM.key)}
              >
                自定义
              </Checkbox>
              {record.receiverTypes.includes(NOTIFY_PERSON.CUSTOM.key) && (
                <EditOutlined
                  className={styles.edit_icon}
                  onClick={() => openCustomModal(record.id, record.customReceivers)}
                />
              )}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '升级处理',
      dataIndex: 'escalation',
      key: 'escalation',
      width: 120,
      fixed: 'right',
    },
  ];

  return (
    <>
      <Modal
        title="监控告警"
        visible={visible}
        onCancel={onCancel}
        width={1200}
        footer={[
          <Button key="cancel" onClick={onCancel}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={handleOk}>
            确认
          </Button>,
        ]}
        className={styles.alert_monitor_modal}
      >
        <Table
          columns={columns}
          dataSource={displayData}
          pagination={false}
          bordered={false}
          scroll={{ x: 'max-content' }}
          className={styles.alert_table}
        />
      </Modal>

      {/* 自定义通知人弹框 */}
      <Modal
        title="添加自定义通知人"
        visible={customModalVisible}
        onCancel={() => setCustomModalVisible(false)}
        onOk={form.submit}
      >
        <Form
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
          onFinish={addCustomPerson}
          form={form}
        >
          <Form.Item label="通知人" name="customPersonInput">
            <GetUser mode="multiple" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default CrowdMonitorModal;
