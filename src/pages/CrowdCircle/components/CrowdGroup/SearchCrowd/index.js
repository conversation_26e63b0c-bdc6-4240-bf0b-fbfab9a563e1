import { useState, useEffect } from 'react';
import { uniqBy, debounce } from 'lodash';
import { queryCrowdKeyword } from '@/services/api';
import { message, Select, Spin } from 'antd';
import { connect } from 'dva';

const SearchCrowd = props => {
  const { handleChangeCrowd, tag, detailCrowdList, crowdGroups, i, index, dispatch } = props;
  const [loading, setLoading] = useState(false);
  const [crowdList, setCrowdList] = useState([]);
  const [value, setValue] = useState(undefined);


  useEffect(() => {
    setValue(tag?.name ? Number(tag.name) : undefined);
  },[tag])

  useEffect(() => {
    if (detailCrowdList && detailCrowdList.length > 0) {
      setCrowdList(detailCrowdList);
    }
  }, [detailCrowdList]);

  const fetchCrowd = e => {
    if (!e) return;
    setLoading(true);
    queryCrowdKeyword({
      pageNum: 1,
      pageSize: 10,
      keyword: e,
    })
      .then(res => {
        if (!res.success) {
          message.error(res.msg);
          return;
        }
        if (res.data && res.data.rows && res.data.rows.length > 0) {
          setCrowdList(res.data.rows);
        }
      })
      .catch(() => {
        message.error('请求人群失败');
      });
    setLoading(false);
  };

  const handleChange = (e, option) => {
    const crowdGroup = crowdGroups[i];
    const selectedTags = crowdGroup.selectedTags || [];

    const result = selectedTags.find(item => Number(item.name) === e);
    if (result) {
      message.warning('同一组内，不能重复人群');
      setValue("")
      return
    }

    dispatch({
      type: 'crowdGroup/checkOperateCntCrowdCircleStatus',
      payload: {
        crowdId: e,
        operateCnt: 5,
      },
    }).then(res => {
      if (res) {
        dispatch({
          type: 'crowdGroup/checkCrowdIdStatus',
          payload: { crowdId: e },
        }).then(result => {
          if (result) {
            setValue(e);
            handleChangeCrowd(e, option);
          } else {
            setValue("")
          }
        });
      } else {
        setValue("")
      }
    });
  };

  return (
    <Select
      value={value}
      onChange={(value, option) => {
        handleChange(value, option);
      }}
      style={{ width: 330 }}
      placeholder="请输入人群id或人群名称"
      onSearch={debounce(fetchCrowd, 500)}
      // allowClear
      showSearch
      defaultActiveFirstOption={false}
      filterOption={false}
      showArrow={false}
      notFoundContent={loading ? <Spin size="small" /> : null}
    >
      {crowdList.map(c => (
        <Select.Option key={c.id + `/${c.realTime}` + `_${c.crowdAmount ?? 0}`} value={c.id}>
          {c.crowdName + `(${c.id})`}
        </Select.Option>
      ))}
    </Select>
  );
};

export default connect(state => ({
  crowdGroup: state.crowdGroup,
}))(SearchCrowd);
