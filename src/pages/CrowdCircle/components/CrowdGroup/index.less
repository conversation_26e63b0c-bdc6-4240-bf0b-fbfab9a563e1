.labelGroup {
  width: 600px;
  .numberContainer {
    display: flex;
    align-items: center;
    .crowdCountText {
      font-weight: bolder;
    }
  }
  .expression {
    margin-left: 20px;
  }
  .tagGroupTop {
    margin-top: 20px;
  }

  .tagGroupContainer {
    display: flex;
    align-items: center;
    .tagGroup {
      position: relative;
      display: flex;
      flex-direction: column;
      width: 100%;
      padding: 5px 10px 5px 10px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;

      .tagOrder {
        display: inline-block;
        flex-shrink: 0;
        width: 26px;
        height: 26px;
        margin-right: 10px;
        color: #fff;
        font-size: 12px;
        line-height: 26px;
        text-align: center;
        background: #b5c5d4;
        border-radius: 50%;
      }

      .tag {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin: 5px 0;
        width: 100%;
      }
      .tagCrowdCount {
        margin-left: 10px;
        color: #818181;
      }
      .tagDelIcon {
        margin-left: 5px;
        cursor: pointer;
      }
    }

    .tagGroupDelIcon {
      margin-left: 5px;
      cursor: pointer;
    }
  }
}
