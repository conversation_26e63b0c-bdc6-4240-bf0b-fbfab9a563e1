import React, { useState, Fragment, useEffect } from 'react';
import styles from './index.less';
import { Radio, Tag, Button, message, Select, Input, Spin } from 'antd';
import { DeleteFilled, ExportOutlined } from '@ant-design/icons';
import { cloneDeep, debounce, uniqBy } from 'lodash';
import { setTagGroup } from '../../common/tag';
import { setCrowdGroup, extractValues, transform } from '../../common/crowdGroup';
import { getExpression, getCode } from '../../common/utils';
import { connect } from 'dva';
import SearchCrowd from './SearchCrowd';

const { Group: RadioGroup, Button: RadioButton } = Radio;

const OPERATOR_MAP = {
  and: 'AND',
  or: 'OR',
  '-': 'NOTIN',
};

function hasMoreThanOneValidValue(obj) {
  const validValuesCount = Object.values(obj).filter(
    value => value !== null && value !== undefined && value !== ''
  ).length;

  return validValuesCount > 1;
}

const CrowdGroup = props => {
  const {
    value,
    onChange,
    detailCrowdList,
    id,
    dispatch,
    crowdGroup: { checkLoading },
  } = props;
  const [calculating, setCalculating] = useState(false);
  const [expression, setExpression] = useState('');
  const [crowdGroups, setCrowdGroups] = useState([
    {
      selectedTags: [{ operator: 'and' }],
    },
  ]);

  useEffect(() => {
    if (value) {
      setCrowdGroups(transform(value));
    }
  }, [value]);

  useEffect(() => {
    const result = getExpression(crowdGroups);
    setExpression(result);
  }, [crowdGroups]);

  const handleAddTag = index => {
    const selectedTags = crowdGroups[index].selectedTags;

    for (let i = 0; i < selectedTags.length; i++) {
      if (Object.keys(selectedTags[i]).length <= 1) {
        message.warning('请先选择人群值再进行添加！');
        return;
      }
    }

    const updatedList = [...crowdGroups];
    updatedList[index].selectedTags.push({ operator: 'and' });
    triggerChange(updatedList);
  };

  const handleAddParentTag = () => {
    for (let i = 0; i < crowdGroups.length; i++) {
      for (let j = 0; j < crowdGroups[i].selectedTags.length; j++) {
        if (Object.keys(crowdGroups[i].selectedTags[j]).length <= 1) {
          message.warning('请先选择人群值再进行添加！');
          return;
        }
      }
    }
    const updatedList = [...crowdGroups, { selectedTags: [{ operator: 'and' }], operator: 'and' }];
    triggerChange(updatedList);
  };

  const removeTag = (i, index) => {
    const updatedList = [...crowdGroups];
    updatedList[i].selectedTags.splice(index, 1);
    triggerChange(updatedList);
  };

  const removeTagGroup = i => {
    const updatedList = crowdGroups.filter((_, index) => index !== i);
    triggerChange(updatedList);
  };

  const triggerChange = (crowdGroup) => {
    const { group, outerExpression } = setCrowdGroup(crowdGroup, OPERATOR_MAP);
    setCrowdGroups(crowdGroup);
    if (!outerExpression) {
      message.error('人群不能为空');
      return;
    }

    const conditions = { group, expression: outerExpression };

    if (onChange) onChange(conditions);
  };

  const handleTagOperatorChange = (i, index, e) => {
    const crowdGroup = crowdGroups[i];
    if (crowdGroup && crowdGroup.selectedTags) {
      crowdGroup.selectedTags[index].operator = e.target.value;
      triggerChange(crowdGroups);
      const result = getExpression(crowdGroups);
      setExpression(result);
    }
  };

  const onTagGroupOperatorChange = (i, e) => {
    const crowdGroup = crowdGroups[i];
    if (crowdGroup) {
      crowdGroup.operator = e.target.value;
      triggerChange(crowdGroups);
      const result = getExpression(crowdGroups);
      setExpression(result);
    }
  };

  const handleChangeCrowd = (i, index, e, option) => {
    const crowdGroup = crowdGroups[i];
    const selectedTags = crowdGroup.selectedTags || [];
    const otherValue = extractValues(option.key);
    if (otherValue) {
      crowdGroup.selectedTags[index].name = otherValue.name;
      crowdGroup.selectedTags[index].crowdAmount = otherValue.crowdAmount;
      crowdGroup.selectedTags[index].realTime = otherValue.realTime === "0" ? 0 : 1;
    }
    triggerChange(crowdGroups);
  };

  return (
    <Spin spinning={checkLoading} tip="校验中，请稍等...">
      <div className={styles.labelGroup}>
        <div className={styles.numberContainer}>
          <div className={styles.crowdCountText}>
            <span style={{ color: '#ff4d4f' }}>*</span>人群组合关系：
          </div>
          <div className={styles.expression}>{expression}</div>
        </div>
        {crowdGroups.map((t, i) => (
          <Fragment key={`tagGroup_${i}`}>
            {i !== 0 && (
              <div style={{ width: '100%', margin: '5px 0 5px 120px' }}>
                <RadioGroup value={t.operator} onChange={e => onTagGroupOperatorChange(i, e)}>
                  <RadioButton value="and">并且</RadioButton>
                  <RadioButton value="or">或者</RadioButton>
                  <RadioButton value="-">排除</RadioButton>
                </RadioGroup>
              </div>
            )}
            <div className={styles.tagGroupContainer}>
              <div
                className={i === 0 ? `${styles.tagGroupTop} ${styles.tagGroup}` : styles.tagGroup}
              >
                {t.selectedTags.map((tag, index) => {
                  return (
                    <Fragment key={`tag_${tag.id}_${index}`}>
                      {index !== 0 && (
                        <div style={{ width: '100%', marginLeft: 120 }}>
                          <RadioGroup
                            value={tag.operator}
                            onChange={e => handleTagOperatorChange(i, index, e)}
                          >
                            <RadioButton value="and">并且</RadioButton>
                            <RadioButton value="or">或者</RadioButton>
                            <RadioButton value="-">排除</RadioButton>
                          </RadioGroup>
                        </div>
                      )}
                      <div className={styles.tag}>
                        <div className={styles.tagOrder}>
                          {getCode(i, index, t.selectedTags.length)}
                        </div>
                        <SearchCrowd
                          detailCrowdList={(detailCrowdList || []).filter(
                            item => String(item.id) === tag.name
                          )}
                          handleChangeCrowd={(e, option) => handleChangeCrowd(i, index, e, option)}
                          tag={tag}
                          i={i}
                          index={index}
                          crowdGroups={crowdGroups}
                        />
                        <div className={styles.tagDelIcon} onClick={() => removeTag(i, index)}>
                          <DeleteFilled />
                        </div>
                        <div className={styles.tagCrowdCount}>
                          人群数量：
                          {String(tag.realTime) === '1'
                            ? '实时不展示'
                            : !tag.crowdAmount || tag.crowdAmount === '暂无'
                            ? '暂无'
                            : tag.crowdAmount}
                        </div>
                        {tag.name && (
                          <a
                            onClick={() => {
                              dispatch({
                                type: 'crowdGroup/queryCrowdRouterDetail',
                                payload: tag.name,
                              });
                            }}
                            style={{ marginLeft: 4 }}
                          >
                            <ExportOutlined />
                          </a>
                        )}
                      </div>
                    </Fragment>
                  );
                })}
                <div>
                  {!t.selectedTags.length && (
                    <div className={styles.tagOrder}>{String.fromCharCode(65 + i)}</div>
                  )}
                  <Button shape="round" onClick={() => handleAddTag(i)}>
                    + 人群
                  </Button>
                </div>
              </div>
              <div className={styles.tagGroupDelIcon} onClick={() => removeTagGroup(i)}>
                <DeleteFilled />
              </div>
            </div>
          </Fragment>
        ))}
        <Button onClick={handleAddParentTag} type="round" style={{ marginTop: 8 }}>
          + 添加人群组
        </Button>
      </div>
    </Spin>
  );
};

export default connect(state => ({
  crowdGroup: state.crowdGroup,
}))(CrowdGroup);
