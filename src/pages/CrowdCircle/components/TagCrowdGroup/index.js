import React, { useState, Fragment, useEffect } from 'react';
import styles from './index.less';
import { Tooltip, Spin, Table, Radio, Tag, Button, message } from 'antd';
import { LoadingOutlined, DeleteFilled } from '@ant-design/icons';
import { getExpression, getCode, setTagShow } from '../../common/utils';
import { cloneDeep, isNumber } from 'lodash';
import { connect } from 'dva';
import SelectEnumValueModal from '../SelectEnumValueModal';
import {
  getSelectEnumData,
  isHsfCustom,
  setTagGroup,
  setHsfOrSqlEnumValue,
} from '../../common/tag';
import { transform } from '@/utils/crowd';
import { getUrlParam } from '@/utils/utils';

const { Group: RadioGroup, Button: RadioButton } = Radio;

const OPERATOR_MAP = {
  and: 'AND',
  or: 'OR',
  '-': 'NOTIN',
};

const loadingIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;

function hasMoreThanOneValidValue(obj) {
  const validValuesCount = Object.values(obj).filter(
    value => value !== null && value !== undefined && value !== ''
  ).length;

  return validValuesCount > 1;
}

const TagCrowdGroup = ({
  dispatch,
  tagCircle,
  value,
  onChange,
  crowdAmount,
  changeConditions,
  id,
}) => {
  const {
    selectTag,
    selectIndex,
    tagGroups,
    selectEnumValueModalVisible,
    calculating,
    crowdCount,
    isRealTime,
  } = tagCircle;
  const [expression, setExpression] = useState('');

  const count = typeof crowdCount !== 'undefined' && crowdCount !== null ? crowdCount : crowdAmount;

  useEffect(() => {
    return () => {
      dispatch({
        type: 'tagCircle/handleReset',
      });
    };
  }, []);

  useEffect(() => {
    if (value) {
      dispatch({
        type: 'tagCircle/handleUpdateTagGroups',
        payload: transform(value),
      });
    }
  }, [value, dispatch]);

  useEffect(() => {
    const result = getExpression(tagGroups);
    setExpression(result);
  }, [tagGroups]);

  const renderTable = () => {
    const columns = [
      {
        title: '数据级',
        dataIndex: 'data',
        key: 'data',
      },
      {
        title: '误差系数',
        dataIndex: 'error',
        key: 'error',
      },
    ];

    const dataSource = [
      { key: '1', data: '100万以上', error: '<0.1%' },
      { key: '2', data: '10万～100万', error: '1%' },
      { key: '3', data: '1万～10万', error: '1%~5%' },
      { key: '4', data: '1万以下', error: '>10%' },
    ];

    return (
      <div>
        <div style={{ color: 'red' }}>预估值，存在误差</div>
        <p>汇总后的误差</p>
        <Table dataSource={dataSource} columns={columns} pagination={false} />
      </div>
    );
  };

  const handleAddTag = index => {
    const selectedTags = tagGroups[index].selectedTags;

    for (let i = 0; i < selectedTags.length; i++) {
      if (Object.keys(selectedTags[i]).length <= 1) {
        message.warning('请先选择标签值再进行添加！');
        return;
      }
    }

    const updatedList = [...tagGroups];
    updatedList[index].selectedTags.push({ operator: 'and' });
    triggerChange(false, updatedList, selectIndex.childrenIndex + 1, index);
  };

  const handleAddParentTag = () => {
    const updatedList = [...tagGroups, { selectedTags: [{ operator: 'and' }], operator: 'and' }];
    triggerChange(false, updatedList, 0, selectIndex.parentIndex + 1);
  };

  const removeTag = (i, index) => {
    if (tagGroups.length === 1 && tagGroups[0].selectedTags.length === 1) {
      triggerChange(false,[
        {
          selectedTags: [{}],
        },
      ]);
      return;
    }
    if (tagGroups[i].selectedTags.length === 1) {
      const updatedList = [...tagGroups];
      updatedList.splice(i, 1);
      triggerChange(false, updatedList, 0, 0);
      return
    }
    const updatedList = [...tagGroups];
    updatedList[i].selectedTags.splice(index, 1);
    triggerChange(true, updatedList, index - 1, selectIndex.parentIndex);
  };

  const removeTagGroup = i => {
    if (tagGroups.length === 1 && i === 0) {
      triggerChange(false,[
        {
          selectedTags: [{}],
        },
      ], 0, 0);
      ;
      return
    }
    const updatedList = [...tagGroups];
    updatedList.splice(i, 1);
    triggerChange(true, updatedList, 0, i - 1);
  };

  const isShow = tag => {
    if (tag?.dataType === 'NONE_TYPE') {
      if (!tag?.sourceConfig?.sqlConfig?.sqlParams) {
        if (tag?.editFormData && !(JSON.stringify(tag?.editFormData) === '{}')) {
          return false;
        }
        if (getUrlParam('crowdParams') && tag?.sqlParams?.length === 0) {
        return false
        }
        return true;
      }
    }
    return false;
  };

  const triggerChange = (needCalculateCrowdCount, updatedList = [], childrenIndex, parentIndex) => {
    const list = updatedList.length > 0 ? updatedList : tagGroups;
    const { group, outerExpression } = setTagGroup(list, OPERATOR_MAP);
    if (!outerExpression && group.length > 1) {
      message.error('圈人条件不能为空');
      return;
    }
    const conditions = { group, expression: outerExpression };
    if (onChange) onChange(conditions);
    const expression = getExpression(updatedList);
    setExpression(expression);
    changeConditions(conditions);
    if (isNumber(childrenIndex) || isNumber(childrenIndex)) {
      dispatch({
        type: 'tagCircle/handleSelectIndex',
        payload: { childrenIndex, parentIndex },
      });
    }

    if (needCalculateCrowdCount) {
      dispatch({
        type: 'tagCircle/getQueryIsRealTime',
        payload: {
          conditions,
          circleType: 'LABEL_CROWD',
        },
      });
    }
  };

  const onSelectedEnumValue = record => {
    const tagGroup = tagGroups[selectIndex.parentIndex];
    if (tagGroup && tagGroup.selectedTags) {
      const selectedTag = tagGroup.selectedTags[selectIndex.childrenIndex];
      Object.assign(selectedTag, {
        ...selectTag,
        operator: 'and',
        dimMetaId: selectTag.dimEnumMetaId,
      });
      selectedTag.editFormData = getSelectEnumData(record);

      const data = getSelectEnumData(record);
      if (selectedTag.dataType === 'DATE') handleDateType(selectedTag, data);
      else if (['ENUM', 'MULTI_VALUE'].includes(selectedTag.dataType))
        selectedTag.values = data.enumValues;
      else if (selectedTag.dataType === 'NUMBER') handleNumberType(selectedTag, data);
      else if (selectedTag.dataType === 'NONE_TYPE')
        selectedTag.values = setHsfOrSqlEnumValue(data);
      else if (selectedTag.dataType === 'KV') handleKVType(selectedTag, data);
      else if (selectedTag.dataType === 'JSON_OBJECT')
        selectedTag.values = data.data[0]?.paramLabel || [];

      if (isHsfCustom(selectedTag)) {
        selectedTag.hsfEditFormData = record.hsfData;
        selectedTag.hsfValues =
          record.hsfData && record.mock
            ? setHsfOrSqlEnumValue({ ...record.hsfData, mock: record.mock })
            : [];
      }

      dispatch({ type: 'tagCircle/handleCancelModal', payload: {} });
      triggerChange(true);
    }
  };

  const handleDateType = (tag, data) => {
    const { dateType, startDate, endDate, pastTime, futureTime } = data;
    tag.values =
      dateType === 'fixed'
        ? [
            { label: `开始时间:${startDate}`, value: startDate },
            { label: `结束时间:${endDate}`, value: endDate },
          ]
        : dateType === 'relativePast'
        ? [{ label: `最近${pastTime}天`, value: -parseInt(pastTime, 10) }]
        : [{ label: `未来${futureTime}天`, value: futureTime }];
  };

  const handleNumberType = (tag, data) => {
    const { numberValue } = data;
    const { min = 0, max = 0 } = numberValue;

    tag.values = [
      { label: `最小值:${min}`, value: min },
      { label: `最大值:${max}`, value: max },
    ];
  };

  const handleKVType = (tag, data) => {
    const { targets } = data;
    tag.values = targets.map(t => ({
      label: `${t.label}：${data[`${t.value}_weight`].join('~')}`,
      value: `${t.value} ${t.label} ${data[`${t.value}_weight`].join(' ')}`,
    }));
  };

  const openSelectEnumValueModal = tag => {
    dispatch({
      type: 'tagCircle/handleUpdateVisibleAndData',
      payload: {
        visible: true,
        tag,
      },
    });
  };

  const handleTagOperatorChange = (i, index, e) => {
    const tagGroup = tagGroups[i];
    if (tagGroup && tagGroup.selectedTags) {
      tagGroup.selectedTags[index].operator = e.target.value;
      triggerChange(true);
    }
  };

  const handleTagGroupOperatorChange = (i, e) => {
    const tagGroup = tagGroups[i];
    if (tagGroup) {
      tagGroup.operator = e.target.value;
      triggerChange(true);
    }
  };

  return (
    <div className={styles.labelGroup}>
      <div className={styles.numberContainer}>
        <div className={styles.crowdCountText}>人群数量预估：</div>
        <Spin indicator={loadingIcon} spinning={calculating}>
          <Tooltip title={renderTable()} placement="bottom">
            <span style={{ fontSize: 16, color: '#ffa940', fontWeight: 'bolder' }}>
              {calculating
                ? '计算中...'
                : isRealTime
                ? '实时人群不展示'
                : count === -1
                ? '暂无'
                : count}
            </span>
          </Tooltip>
        </Spin>
      </div>
      <div className={styles.expression}>{expression}</div>
      {tagGroups.map((t, i) => (
        <Fragment key={`tagGroup_${i}`}>
          {i !== 0 && (
            <div style={{ width: '100%', textAlign: 'center', margin: '5px 0' }}>
              <RadioGroup value={t.operator} onChange={e => handleTagGroupOperatorChange(i, e)}>
                <RadioButton value="and">并且</RadioButton>
                <RadioButton value="or">或者</RadioButton>
                <RadioButton value="-">排除</RadioButton>
              </RadioGroup>
            </div>
          )}
          <div className={styles.tagGroupContainer}>
            <div className={i === 0 ? `${styles.tagGroupTop} ${styles.tagGroup}` : styles.tagGroup}>
              {t.selectedTags.map((tag, index) => (
                <Fragment key={`tag_${tag.id}_${index}`}>
                  {index !== 0 && (
                    <div style={{ width: '100%', textAlign: 'center' }}>
                      <RadioGroup
                        value={tag.operator}
                        onChange={e => handleTagOperatorChange(i, index, e)}
                      >
                        <RadioButton value="and">并且</RadioButton>
                        <RadioButton value="or">或者</RadioButton>
                        <RadioButton value="-">排除</RadioButton>
                      </RadioGroup>
                    </div>
                  )}
                  <div className={styles.tag}>
                    <div className={styles.tagOrder}>
                      {getCode(i, index, t.selectedTags.length)}
                    </div>
                    <div
                      className={styles.tagInfo}
                      style={{
                        border: `1px solid ${
                          selectIndex.childrenIndex === index && selectIndex.parentIndex === i
                            ? '#5c9bff'
                            : '#e0e0e0'
                        }`,
                      }}
                      onClick={() =>
                        dispatch({
                          type: 'tagCircle/handleSelectIndex',
                          payload: { childrenIndex: index, parentIndex: i },
                        })
                      }
                    >
                      {hasMoreThanOneValidValue(tag) ? (
                        <Fragment>
                          <Tooltip
                            title={`${tag.name || tag.propertyDescription}(${tag.code ||
                              tag.propertyName})`}
                          >
                            <span className={styles.tagName}>
                              {`${tag.name || tag.propertyDescription}(${tag.code ||
                                tag.propertyName})` || ''}
                            </span>
                          </Tooltip>
                          <span className={styles.manzuText}> 满足 </span>
                          {isShow(tag) ? (
                            <div style={{ display: 'flex', flex: 1, justifyContent: 'center' }}>
                              无需选择参数
                            </div>
                          ) : (
                            <div
                              className={styles.enumValue}
                              onClick={e => {
                                // e.stopPropagation();
                                openSelectEnumValueModal(tag);
                              }}
                            >
                              {setTagShow(tag).map(v => (
                                <Tooltip
                                  key={`value_${i}_${tag.id}_${v.label}`}
                                  title={v.label?.replace('&gt;', '>')}
                                >
                                  <Tag className={styles.myTag} color="cyan">
                                    {v.label?.replace('&gt;', '>')}
                                  </Tag>
                                </Tooltip>
                              ))}
                            </div>
                          )}
                        </Fragment>
                      ) : (
                        <p className={styles.placeholderText}>请选择左边标签进行添加</p>
                      )}
                    </div>
                    <div className={styles.tagDelIcon} onClick={() => removeTag(i, index)}>
                      <DeleteFilled />
                    </div>
                  </div>
                </Fragment>
              ))}
              <div>
                {!t.selectedTags.length && (
                  <div className={styles.tagOrder}>{String.fromCharCode(65 + i)}</div>
                )}
                <Button shape="round" onClick={() => handleAddTag(i)}>
                  + 标签
                </Button>
              </div>
            </div>
            <div className={styles.tagGroupDelIcon} onClick={() => removeTagGroup(i)}>
              <DeleteFilled />
            </div>
          </div>
        </Fragment>
      ))}
      <Button onClick={handleAddParentTag} type="round" style={{ marginTop: 8 }}>
        + 添加标签组
      </Button>

      <SelectEnumValueModal
        title={selectTag ? selectTag.name || selectTag.propertyDescription : '请选择'}
        visible={selectEnumValueModalVisible}
        tag={selectTag}
        onCancel={() => dispatch({ type: 'tagCircle/handleCancelModal', payload: {} })}
        onOk={onSelectedEnumValue}
      />
    </div>
  );
};

export default connect(state => ({
  tagCircle: state.tagCircle,
}))(TagCrowdGroup);
