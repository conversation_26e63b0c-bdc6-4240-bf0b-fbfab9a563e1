.labelGroup {
  .numberContainer {
    display: flex;
    align-items: center;
    .crowdCountText {
      font-weight: bolder;
    }
  }
  .expression {
    margin-left: 20px;
  }
  .tagGroupTop {
    margin-top: 20px;
  }

  .tagGroupContainer {
    display: flex;
    align-items: center;
    .tagGroup {
      position: relative;
      display: flex;
      flex-direction: column;
      width: 100%;
      padding: 5px 10px 5px 10px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;

      .tagOrder {
        display: inline-block;
        flex-shrink: 0;
        width: 26px;
        height: 26px;
        margin-right: 10px;
        color: #fff;
        font-size: 12px;
        line-height: 26px;
        text-align: center;
        background: #b5c5d4;
        border-radius: 50%;
      }

      .tag {
        display: flex;
        flex-direction: row;
        align-items: center;
        width: 100%;
      }

      .tagInfo {
        display: flex;
        flex: 1;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        margin: 5px 0;
        padding: 5px;
        border-radius: 4px;
        cursor: pointer;

        .tagName {
          display: inline-block;
          width: 71px;
          height: 24px;
          margin-right: 10px;
          overflow: hidden;
          color: #666;
          font-size: 12px;
          line-height: 24px;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .manzuText {
          width: 40px;
          height: 25px;
          color: #a0a2ad;
          font-size: 12px;
          line-height: 25px;
          text-align: center;
          background-color: #f2f3f7;
          border-radius: 2px;
        }

        .enumValue {
          display: flex;
          flex: 1;
          flex-wrap: wrap;
          align-items: center;
          min-height: 30px;
          margin-left: 10px;
          padding: 2px 4px;
          font-size: 12px;
          line-height: 30px;
          border: 1px solid #e0e0e0;
          border-radius: 4px;

          .myTag {
            max-width: 96px;
            height: 24px;
            margin: 2px;
            overflow: hidden;
            line-height: 24px;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }

        .placeholderText {
          width: 100%;
          margin-bottom: 0;
          text-align: center;
        }
      }

      .tagDelIcon {
        margin-left: 5px;
        cursor: pointer;
      }
    }

    .tagGroupDelIcon {
      margin-left: 5px;
      cursor: pointer;
    }
  }
}
