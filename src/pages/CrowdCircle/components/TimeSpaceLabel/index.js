import React, { Fragment, useEffect } from 'react';
import { Form, Input, Select, Button, Row, Col, DatePicker, Radio, Divider, message } from 'antd';
import { cloneDeep } from 'lodash';
import dayjs from 'dayjs';

import TimeComponent from '../TimeComponent';
import BehaviorSelect from '../BehaviorSelect';
import AdditionalConditions from '../AdditionalConditions';

const { RangePicker } = DatePicker;
const { Option } = Select;

const TimeSpaceLabel = props => {
  const {
    tag,
    conditionOptions,
    behaviorTypeOptions,
    timeTypeOptions,
    onChangeConditionOptions,
    onChangeBehaviorType,
    form,
  } = props;

  useEffect(() => {
    if (tag?.editFormData?.timeType) {
      onChangeBehaviorType(tag?.editFormData?.timeType);
    }
  }, [tag]);

  const onChangeTimeType = values => {
    const { onChangeBehaviorType } = props;
    onChangeBehaviorType(values);
    form.setFieldsValue({ additional: [], keys: [], behaviorCode: undefined });
  };

  if (!tag) {
    return null;
  }

  const { editFormData } = tag;

  const disabledDate = current => {
    const today = dayjs();
    const beforeThirtyDays = today.clone().subtract(30, 'days');
    const afterThirtyDays = today.clone().add(30, 'days');
    return current && (current < beforeThirtyDays || current > afterThirtyDays);
  };

  return (
    <Fragment>
      <h3>必填条件</h3>
      <Row>
        <Col span={24}>
          <Form.Item
            label="时间"
            extra={
              <span style={{ color: '#666666', fontSize: 12 }}>
                使用说明：1.时间区间：闭区间；2.过去N天：过去N天；3.今天：当天自然日；4.未来N天：今天+未来N天
              </span>
            }
            name="dateType"
            rules={[{ required: true, message: '请选择类型' }]}
          >
            <Radio.Group>
              <Radio value="fixed">时间区间</Radio>
              <Radio value="relativePast">过去N天</Radio>
              <Radio value="intraday">当天</Radio>
              <Radio value="relativeFuture">未来N天</Radio>
            </Radio.Group>
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, curValues) => prevValues.dateType !== curValues.dateType}
          >
            {({ getFieldValue }) => {
              const dateTypeValue = getFieldValue('dateType');
              if (dateTypeValue === 'fixed') {
                return (
                  <Form.Item
                    label="日期"
                    name="date"
                    rules={[{ required: true, message: '请选择日期' }]}
                  >
                    <RangePicker disabledDate={disabledDate} />
                  </Form.Item>
                );
              }
              if (dateTypeValue === 'relativePast') {
                return (
                  <Form.Item
                    label="天数"
                    name="pastTime"
                    rules={[{ required: true, message: '请选择天数' }]}
                  >
                    <TimeComponent start="过去" max={30} />
                  </Form.Item>
                );
              }
              if (dateTypeValue === 'relativeFuture') {
                return (
                  <Form.Item
                    label="天数"
                    name="futureTime"
                    rules={[{ required: true, message: '请选择天数' }]}
                  >
                    <TimeComponent start="未来" max={30} />
                  </Form.Item>
                );
              }
            }}
          </Form.Item>
        </Col>
      </Row>
      <Col span={22}>
        <Form.Item
          label="时间类型"
          name="timeType"
          rules={[{ required: true, message: '请选择时间类型' }]}
        >
          <Select
            placeholder="请选择时间类型"
            showSearch
            filterOption={(input, option) => option.props.children.indexOf(input) >= 0}
            onChange={onChangeTimeType}
          >
            {timeTypeOptions &&
              timeTypeOptions.length > 0 &&
              timeTypeOptions.map(o => (
                <Option key={o.value} value={o.value}>
                  {o.label}
                </Option>
              ))}
          </Select>
        </Form.Item>
      </Col>
      <Col span={22}>
        <Form.Item
          label="行为"
          name="behaviorCode"
          rules={[{ required: true, message: '请选择行为' }]}
        >
          <BehaviorSelect behaviorTypeOptions={cloneDeep(behaviorTypeOptions)} />
        </Form.Item>
      </Col>
      <Divider />
      <Form.Item
        noStyle
        shouldUpdate={(prevValues, curValues) => prevValues.behaviorCode !== curValues.behaviorCode}
      >
        {({ getFieldValue }) => {
          const behaviorCodeValue = getFieldValue('behaviorCode');
          return (
            <AdditionalConditions
              form={form}
              behaviorCode={behaviorCodeValue}
              value={editFormData?.additional || []}
              conditionOptions={conditionOptions}
              onChangeConditionOptions={onChangeConditionOptions}
            />
          );
        }}
      </Form.Item>
    </Fragment>
  );
};

export default TimeSpaceLabel;
