import React, { useState, useEffect, useCallback } from 'react';
import {
  Modal,
  Input,
  message,
  Select,
  Radio,
  Switch,
  DatePicker,
  Button,
  Drawer,
  Form,
  Space,
} from 'antd';
import { debounce } from 'lodash';
import dayjs from 'dayjs';
import { queryCrowdKeyword } from '@/services/api';
import GetUser from '@/components/GetUser';
import { connect } from 'dva';

const NOOP = () => {};
const FORM_ITEM_LAYOUT = {
  labelCol: { span: 7 },
  wrapperCol: { span: 14 },
};
const { TextArea } = Input;
const { Option } = Select;

const CreateOperateCrowdModal = props => {
  const {
    editModalVisible = false,
    editFormData = {},
    onSubmit = NOOP,
    onCancel = NOOP,
    user: { currentUser, isSuperAdmin },
  } = props;
  const [form] = Form.useForm();
  const [crowdList, setCrowdList] = useState([]);
  const [crowdNum1, setCrowdNum1] = useState(editFormData.leftCrowdNum);
  const [crowdNum2, setCrowdNum2] = useState(editFormData.rightCrowdNum);
  const [crowdIsRealTime1, setCrowdIsRealTime1] = useState(editFormData.crowdLeftRealTime || false);
  const [crowdIsRealTime2, setCrowdIsRealTime2] = useState(
    editFormData.crowdRightRealTime || false
  );

  // 使用useEffect替代componentWillReceiveProps
  useEffect(() => {
    if (!editFormData || !editFormData.crowdName) {
      form.resetFields();
    }
    if (editFormData) {
      form.setFieldsValue({
        ...editFormData,
        expiredDate: dayjs(editFormData.expiredDate),
        isLoss: editFormData.crowdTags &&
              (editFormData.crowdTags.length > 0 &&
              editFormData.crowdTags.includes('LOSS_OF_ASSETS')
                ? 1
                : 0)
      });
    }
  }, [editFormData, form]);

  // 使用useCallback包装fetchCrowd函数
  const fetchCrowd = useCallback(
    debounce((params = {}) => {
      queryCrowdKeyword({
        pageNum: 1,
        pageSize: 10,
        ...params,
      })
        .then(res => {
          if (!res.success) {
            message.error(res.msg);
            return;
          }

          if (res.data && res.data.rows && res.data.rows.length > 0) {
            setCrowdList(res.data.rows);
          }
        })
        .catch(() => {
          message.error('请求人群失败');
        });
    }, 300),
    []
  );

  const handleCrowdSearch = value => {
    fetchCrowd({ keyword: value });
  };

  const handleCrowd1Change = value => {
    if (!value) return;

    const crowd = crowdList.find(item => item.id === value.key);
    if (crowd) {
      setCrowdNum1(crowd.crowdAmount);
      setCrowdIsRealTime1(crowd.realTime === 1 || crowd.realTime === 2);
    }
  };

  const handleCrowd2Change = value => {
    if (!value) return;

    const crowd = crowdList.find(item => item.id === value.key);
    if (crowd) {
      setCrowdNum2(crowd.crowdAmount);
      setCrowdIsRealTime2(crowd.realTime === 1 || crowd.realTime === 2);
    }
  };

  const handleSubmit = () => {
    form
      .validateFields()
      .then(values => {
        const payload = { ...values };
        const {
          needUpdate,
          expiredDate,
          isLoss,
          crowdLeft,
          crowdRight,
          operateCrowdType,
        } = payload;

        payload.crowdType = 'OPERATE_CROWD';
        payload.needUpdate = needUpdate ? 1 : 0;
        payload.expiredDate = expiredDate.valueOf();
        payload.crowdTags = [];
        payload.profileType = editFormData.profileType || 'TAOBAO_USER';

        // 涉及资损
        if (isLoss === 1) {
          payload.crowdTags.push('LOSS_OF_ASSETS');
        }

        const prevExtInfo = payload.extInfo;
        payload.extInfo = {
          ...prevExtInfo,
          OPERATE_CROWD_LEFT: crowdLeft && crowdLeft.key,
          OPERATE_CROWD_RIGHT: crowdRight && crowdRight.key,
          OPERATE_CROWD_TYPE: operateCrowdType,
        };

        if (payload.needUpdate) {
          if (!payload.extInfo.UPDATE_MODEL) {
            payload.extInfo.UPDATE_MODEL = {};
          }
          payload.extInfo.UPDATE_MODEL.updateType =
            payload.updateType === 1 ? 'dependence' : 'every_day';
          payload.extInfo.UPDATE_MODEL.dependenceBizOption =
            payload.updateType === 1 ? payload.updateDependenceApp : undefined;
        } else if (payload.extInfo.UPDATE_MODEL) {
          delete payload.extInfo.UPDATE_MODEL.updateType;
          delete payload.extInfo.UPDATE_MODEL.dependenceBizOption;
        }

        // 提交审核信息
        if (payload.applyApprovalReason && payload.qualityOwner) {
          payload.crowdApprovalInfo = {
            applyApprovalReason: payload.applyApprovalReason,
            applyOwner: { empId: currentUser.workId, nickName: currentUser.name },
            qualityOwner: payload.qualityOwner,
            needSubmit: true,
          };
        }

        if (payload.applyApprovalReason) {
          delete payload.applyApprovalReason;
        }

        if (payload.qualityOwner) {
          delete payload.qualityOwner;
        }

        onSubmit(payload);
      })
      .catch(err => {
        console.log('Validate Failed:', err);
      });
  };

  const handleCrowdIsExist = (_, value) => {
    if (!value) return Promise.resolve();

    const crowdLeft = form.getFieldValue('crowdLeft');
    if (value.key === crowdLeft?.key) {
      return Promise.reject(new Error('逻辑运算人群不能为同一人群'));
    }

    return Promise.resolve();
  };

  const handleTopChange = data => {
    const { type, value } = data;
    const formData = form.getFieldsValue();

    if (formData) {
      if (!formData.extInfo) {
        formData.extInfo = {};
      }

      if (type === 'needUpdate') {
        if (value) {
          if (!formData.extInfo.UPDATE_MODEL) {
            formData.extInfo.UPDATE_MODEL = {};
          }
          formData.extInfo.UPDATE_MODEL.updateType = formData.updateType;
          formData.extInfo.UPDATE_MODEL.dependenceBizOption =
            formData.updateType === 1 ? formData.updateDependenceApp : undefined;
        } else if (formData.extInfo.UPDATE_MODEL) {
          delete formData.extInfo.UPDATE_MODEL.updateType;
          delete formData.extInfo.UPDATE_MODEL.dependenceBizOption;
        }
      }
    }

    if (value && type === 'needUpdate') {
      Modal.confirm({
        title: '每日更新计算资源消耗较大，请确认是否是必要选项',
        okText: '确认',
        cancelText: '取消',
        onOk() {
          form.setFieldValue('needUpdate', true);
        },
        onCancel() {
          form.setFieldValue('needUpdate', false);
        },
      });
    }

    form.setFieldValue('extInfo', formData.extInfo);
  };

  const handleCancel = () => {
    setCrowdNum1(0);
    setCrowdNum2(0);
    onCancel();
  };

  const isSubmitDisabled = () => {
    // 管理者和创建者及超级管理员能编辑
    const { creator = {}, operator = [] } = editFormData;
    const owners = [...operator, creator];

    const isGranted = owners.map(o => o.empId).includes(currentUser.workId);

    if (!editFormData.id || isSuperAdmin || isGranted) {
      return false;
    }
    return true;
  };

  const disabledDate = current =>
    current < dayjs().startOf('day') || current > dayjs().add(90, 'day');

  return (
    <Drawer
      title="人群逻辑运算"
      placement="right"
      open={editModalVisible}
      destroyOnClose
      width={800}
      onClose={handleCancel}
      footer={
        <div style={{ textAlign: 'right' }}>
          <Space>
            <Button onClick={handleCancel}>取消</Button>
            <Button type="primary" onClick={handleSubmit} disabled={isSubmitDisabled()}>
              保存
            </Button>
          </Space>
        </div>
      }
    >
      <Form
        {...FORM_ITEM_LAYOUT}
        form={form}
        initialValues={{
          updateDependenceApp: 'huokebao',
        }}
      >
        <Form.Item
          name="crowdName"
          label="结果人群名称"
          rules={[{ required: true, message: '请输入人群名称' }]}
        >
          <Input placeholder="请输入人群名称" />
        </Form.Item>

        <Form.Item
          name="crowdDescription"
          label="人群描述"
          rules={[{ required: true, message: '请输入人群描述' }]}
        >
          <TextArea placeholder="请输入人群描述" />
        </Form.Item>

        <Form.Item
          name="operator"
          label="管理员"
          rules={[{ required: true, message: '请选择管理员' }]}
        >
          <GetUser mode="multiple" />
        </Form.Item>

        <Form.Item name="needUpdate" label="是否更新" valuePropName="checked">
          <Switch onChange={checked => handleTopChange({ type: 'needUpdate', value: checked })} />
        </Form.Item>

        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.needUpdate !== currentValues.needUpdate
          }
        >
          {({ getFieldValue }) =>
            getFieldValue('needUpdate') ? (
              <Form.Item name="updateType" label="更新类型">
                <Radio.Group
                  onChange={e => handleTopChange({ type: 'updateType', value: e.target.value })}
                >
                  <Radio value={0}>每天更新</Radio>
                  <Radio value={1}>依赖更新</Radio>
                </Radio.Group>
              </Form.Item>
            ) : null
          }
        </Form.Item>

        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.needUpdate !== currentValues.needUpdate ||
            prevValues.updateType !== currentValues.updateType
          }
        >
          {({ getFieldValue }) =>
            getFieldValue('needUpdate') && getFieldValue('updateType') === 1 ? (
              <Form.Item name="updateDependenceApp" label="依赖更新方">
                <Select defaultValue="huokebao">
                  <Option value="huokebao">获客宝</Option>
                </Select>
              </Form.Item>
            ) : null
          }
        </Form.Item>

        <Form.Item
          name="isLoss"
          label="涉及资损"
          rules={[{ required: true, message: '请选择是否涉及资损' }]}
        >
          <Radio.Group>
            <Radio value={1}>是</Radio>
            <Radio value={0}>否</Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item label="人群类型">
          {editFormData.profileType === 'SCRM_USER' ? 'Scrm-Oneid人群包' : '淘宝id'}
        </Form.Item>

        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) => prevValues.isLoss !== currentValues.isLoss}
        >
          {({ getFieldValue }) =>
            getFieldValue('isLoss') === 1 ? (
              <>
                <Form.Item
                  name="applyApprovalReason"
                  label="详细描述"
                  rules={[{ required: true, message: '请输入详细描述' }]}
                  extra={
                    <span style={{ color: 'red' }}>
                      注：涉及资损的人群包将通过审批流获取额外测试保障，测试通过将后自动上线
                    </span>
                  }
                >
                  <TextArea placeholder="请详细描述下可能发生资损的场景" allowClear />
                </Form.Item>

                <Form.Item
                  name="qualityOwner"
                  label="QA"
                  rules={[{ required: true, message: '请输入业务对应的测试同学' }]}
                >
                  <GetUser mode="default" />
                </Form.Item>
              </>
            ) : null
          }
        </Form.Item>

        <Form.Item
          name="crowdLeft"
          label="人群1"
          rules={[{ required: true, message: '请选择人群' }]}
          extra={
            <span>
              {crowdIsRealTime1
                ? '实时不展示'
                : `人群数量：${crowdNum1 || editFormData.leftCrowdNum || '暂无'}`}
            </span>
          }
        >
          <Select
            labelInValue
            allowClear
            placeholder="请选择人群"
            showSearch
            onSearch={handleCrowdSearch}
            defaultActiveFirstOption={false}
            filterOption={false}
            onChange={handleCrowd1Change}
            showArrow={false}
          >
            {crowdList.map(c => (
              <Option key={c.id} value={c.id}>
                {c.crowdName}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="crowdRight"
          label="人群2"
          rules={[{ required: true, message: '请选择人群' }, { validator: handleCrowdIsExist }]}
          extra={
            <span>
              {crowdIsRealTime2
                ? '实时不展示'
                : `人群数量：${crowdNum2 || editFormData.rightCrowdNum || '暂无'}`}
            </span>
          }
        >
          <Select
            labelInValue
            allowClear
            placeholder="请选择人群"
            showSearch
            onSearch={handleCrowdSearch}
            defaultActiveFirstOption={false}
            filterOption={false}
            onChange={handleCrowd2Change}
            showArrow={false}
          >
            {crowdList.map(c => (
              <Option key={c.id} value={c.id}>
                {c.crowdName}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="operateCrowdType"
          label="运算规则"
          rules={[{ required: true, message: '请选择运算规则' }]}
        >
          <Radio.Group>
            <Radio value="AND">交集</Radio>
            <Radio value="OR">并集</Radio>
            <Radio value="NOTIN">差集</Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          name="crowdApplyScene"
          label="人群使用场景"
          rules={[{ required: true, message: '请选择人群使用场景' }]}
          extra={
            <span style={{ color: 'red' }}>
              注：非PUSH场景请选中人群匹配，否则会导致无法判断人群
            </span>
          }
        >
          <Select placeholder="请选择人群使用场景" mode="multiple" showSearch>
            {crowdIsRealTime1 || crowdIsRealTime2 ? null : <Option key="PUSH">PUSH</Option>}
            <Option key="MATCH">人群匹配</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="expiredDate"
          label="人群过期时间"
          rules={[{ required: true, message: '请选择人群人群过期时间' }]}
        >
          <DatePicker
            disabledDate={disabledDate}
            format="YYYY-MM-DD HH:mm:ss"
            showTime={{
              defaultValue: dayjs('23:59:59', 'HH:mm:ss'),
            }}
          />
        </Form.Item>

        <Form.Item name="extInfo" label="扩展信息" hidden>
          <Input />
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default connect(({ user }) => ({
  user,
}))(CreateOperateCrowdModal);
