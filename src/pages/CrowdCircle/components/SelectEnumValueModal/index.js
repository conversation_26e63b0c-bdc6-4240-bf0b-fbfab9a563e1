import React, { useEffect, useState, Fragment } from 'react';
import dayjs from 'dayjs';
import {
  Form,
  Modal,
  Radio,
  Checkbox,
  message,
  DatePicker,
  Tag,
  Spin,
  Select,
  Slider,
  Row,
  Col,
  Input,
} from 'antd';
import { cloneDeep, get, pick, set } from 'lodash';
import {
  queryLabelEnumValueBuildTree,
  queryLabelByName,
  queryTimeType,
  queryBehaviorType,
  checkCrowdId,
  queryCrowdCircle,
} from '@/services/api';
import TimeComponent from '../TimeComponent';
import SelectMaxAndMin from '../SelectMaxAndMin';
import DateTimePicker from '../DateTimePicker';
import TimeSpaceLabel from '../TimeSpaceLabel';
import {
  hsfOrSqlParams,
  isHsfCustom,
  getHsfParams,
  getFlattenParams,
  getHsfServiceParams,
  timeSpaceLabelSubmit,
  setTimeSpaceLabelParams,
} from '../../common/tag';

const { RangePicker } = DatePicker;
const { Option } = Select;
const CheckboxGroup = Checkbox.Group;
const RadioGroup = Radio.Group;

const isDayjs = (obj) => obj instanceof dayjs;

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

const SelectEnumValueModal = props => {
  const { title = '', visible, tag, onCancel, onOk } = props;
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [editWeightRange, setEditWeightRange] = useState([]);
  const [mock, setMock] = useState([]);
  const [dataConfig, setDataConfig] = useState({});
  const [hsfParamsList, setHsfParamsList] = useState([]);
  const [timeTypeOptions, setTimeTypeOptions] = useState([]);
  const [behaviorTypeOptions, setBehaviorTypeOptions] = useState([]);
  const [conditionOptions, setConditionOptions] = useState([]);

  const [isHsf, setIsHsf] = useState(false);

  const [form] = Form.useForm();

  useEffect(() => {
    if (tag) {
      const { dataType, editFormData, sourceConfig } = tag;
      setIsHsf(isHsfCustom(tag));
      switch (dataType) {
        case 'ENUM':
        case 'MULTI_VALUE':
          getEnumMateIdOrDataValue(tag);
          form.setFieldsValue({
            checkedList: editFormData?.checkedList,
          });
          break;
        case 'KV':
          getEnumMateIdOrDataValue(tag);
          form.setFieldsValue({
            ...editFormData,
            targets: editFormData?.targets.map(item => item.value),
          });
          break;
        case 'NUMBER':
          form.setFieldsValue(editFormData);
          if (editFormData?.numberValue) {
            getEnumMateIdOrDataValue(tag);
          }
          break;
        case 'DATE':
          if (editFormData) {
            form.setFieldsValue({
              ...editFormData,
              date: [dayjs(editFormData.startDate), dayjs(editFormData.endDate)],
            });
            if (editFormData?.dateType) {
              getEnumMateIdOrDataValue(tag);
            }
          }
          break;
        case 'NONE_TYPE':
          setLoading(true);
          form.setFieldsValue(editFormData);
          if (editFormData) {
            getEnumMateIdOrDataValue(tag);
          }
          const sqlParams = sourceConfig ? tag?.sqlConfig?.sqlParams : tag?.sqlParams;
          hsfOrSqlParams(sqlParams, params => {
            setMock(cloneDeep(params));
            setLoading(false);
          });
          break;
        case 'JSON_OBJECT':
          form.setFieldsValue(editFormData);
          if (editFormData) {
            getEnumMateIdOrDataValue(tag);
          }
          const timeSpaceLabel = tag?.editFormData?.data;
          if (timeSpaceLabel) {
            tag.editFormData = setTimeSpaceLabelParams(timeSpaceLabel)?.editFormData;
          }
          getTimeType();
          break;
        default:
          break;
      }

      if (isHsfCustom(tag)) {
        const hsfParams = getHsfParams(tag);
        const flattenParams = getFlattenParams(getHsfServiceParams(tag));
        hsfOrSqlParams(hsfParams, params => {
          const paramsWithValueSource = params?.map(ele => {
            const valueSource = flattenParams?.find(item => item?.paramName === ele?.name)?.valueSource;
            if (valueSource) {
              return {
                ...ele,
                valueSource,
              };
            }
            return ele;
          });
          setHsfParamsList(cloneDeep(paramsWithValueSource));
        });
        if (tag.hsfValues) {
          let result = {}
          tag.hsfValues.forEach(item => {
            if (['ENUM', 'SINGLE_ENUM'].includes(item.paramType)) {
              if (Array.isArray(item.value)) {
                result[item.name] = item.value.map(val => String(val)?.split(" ")[0]);
              } else {
                result[item.name] = String(item.value)?.split(" ")[0];
              }
            } else {
              result[item.name] = String(item.value)?.split(" ")[0];
            }
          })
          form.setFieldsValue(result);
        }
      }
    }
  }, [tag]);

  // 获取枚举或者多值类型
  const getEnumMateIdOrDataValue = tag => {
    const { dimEnumMetaId, dataConfig, propertyName } = tag;
    const { dataValue } = dataConfig || {};
    if (dimEnumMetaId && !dataValue) getEnumValues(dimEnumMetaId);
    if (propertyName) getLabel(propertyName);
    if (dataValue) setOptions(getDataValueEnum(dataValue));
    setDataConfig(dataConfig);
  };

  const getTimeType = async () => {
    const res = await queryTimeType();
    if (res.success) {
      setTimeTypeOptions(
        Object.keys(res.data || {}).map(ele => ({
          label: res.data[ele],
          value: ele,
        }))
      );
    } else {
      message.error(res.msg);
    }
  };

  const getEnumValues = async dimEnumMetaId => {
    setLoading(true);
    const res = await queryLabelEnumValueBuildTree({ dimMetaId: dimEnumMetaId });
    if (res.success) {
      const o = res.data?.root?.children || [];
      setOptions(o.map(item => ({ label: item.name, value: item.enumCode })));
    } else {
      message.error(res.msg);
    }
    setLoading(false);
  };

  const getLabel = async name => {
    const res = await queryLabelByName({ name });
    if (res.success) {
      const { extInfo, dataConfig } = res.data;
      const { weightRange } = extInfo || {};
      const { kvWeightRange } = dataConfig || {};

      tag.description = res.data.description;

      if (dataConfig?.dataValue) {
        setOptions(getDataValueEnum(dataConfig.dataValue));
      }
      setEditWeightRange(kvWeightRange ?? (weightRange || []));
      setDataConfig(dataConfig);
    } else {
      message.error(res.msg);
    }
  };

  const getDataValueEnum = dataValue => {
    if (!dataValue) return [];
    return Object.entries(dataValue)
      .map(([key, value]) => (value ? { label: value, value: key } : null))
      .filter(Boolean);
  };

  const onCancelClick = () => {
    setOptions([]);
    setMock([]);
    setHsfParamsList([]);
    onCancel();
    form.resetFields();
  };

  // 行为类型枚举接口
  const getBehaviorType = timeType => {
    queryBehaviorType({ filterNoDest: false, timeType }).then(res => {
      if (!res.success) {
        message.error(res.msg);
        return;
      }

      if (res?.data) {
        const result = Object.keys(res?.data || {}).map(ele => ({
          label: res?.data[ele],
          value: ele,
        }));
        setBehaviorTypeOptions(result);
      }
    });
  };

  const renderContent = () => {
    if (!tag) return null;
    const { dataType } = tag;

    switch (dataType) {
      case 'NUMBER':
        return getNumberTypeContent();
      case 'ENUM':
      case 'MULTI_VALUE':
        return getEnumTypeContent();
      case 'DATE':
        return getDateTypeContent();
      case 'KV':
        return getKVTypeContent();
      case 'NONE_TYPE':
        return getSqlTypeContent();
      case 'JSON_OBJECT':
        return (
          <TimeSpaceLabel
            conditionOptions={conditionOptions}
            behaviorTypeOptions={behaviorTypeOptions}
            timeTypeOptions={timeTypeOptions}
            onChangeConditionOptions={conditionOptions => {
              setConditionOptions(conditionOptions);
            }}
            tag={tag}
            form={form}
            onChangeBehaviorType={timeType => {
              getBehaviorType(timeType);
            }}
          />
        );
      default:
        return '暂不支持此类型';
    }
  };

  // 数值类型下所渲染的表单
  const getNumberTypeContent = () => {
    return (
      <Form.Item
        label="数值范围"
        name="numberValue"
        rules={[{ required: true, message: '请输入最大值最小值' }]}
      >
        <SelectMaxAndMin />
      </Form.Item>
    );
  };

  // 枚举或者多值类型下所渲染的表单
  const getEnumTypeContent = () => {
    return (
      <Form.Item
        label="目标"
        name="checkedList"
        rules={[{ required: true, message: '请选择目标' }]}
      >
        <Select
          mode="multiple"
          placeholder="请选择目标"
          showSearch
          style={{ width: 500 }}
          filterOption={(input, option) => option.props.children.indexOf(input) >= 0}
        >
          {(options || []).map(o => (
            <Option key={o.value} value={o.value}>
              {/*  判断如果包含&gt; 替换为 >  */}
              {o.label ? (o.label.includes('&gt;') ? o.label.replace('&gt;', '>') : o.label) : null}
            </Option>
          ))}
        </Select>
      </Form.Item>
    );
  };

  // 日期类型下所渲染的表单
  const getDateTypeContent = () => {
    return (
      <Fragment>
        <Form.Item
          label="类型"
          extra={
            <span style={{ color: '#666666', fontSize: 12 }}>
              使用说明：1.时间区间：闭区间；2.过去N天：过去N天；3.未来N天：今天+未来N天
            </span>
          }
          name="dateType"
          rules={[{ required: true, message: '请选择类型' }]}
        >
          <RadioGroup>
            <Radio value="fixed">时间区间</Radio>
            <Radio value="relativePast">过去N天</Radio>
            <Radio value="relativeFuture">未来N天</Radio>
          </RadioGroup>
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) => prevValues.dateType !== curValues.dateType}
        >
          {({ getFieldValue }) => {
            const dateType = getFieldValue('dateType');
            if (dateType === 'fixed') {
              return (
                //[startDate, endDate]
                <Form.Item
                  name="date"
                  label="日期"
                  rules={[{ required: true, message: '请选择日期' }]}
                >
                  <RangePicker />
                </Form.Item>
              );
            } else if (dateType === 'relativePast' || dateType === 'relativeFuture') {
              return (
                <Form.Item
                  name={dateType === 'relativePast' ? 'pastTime' : 'futureTime'}
                  label="天数"
                  rules={[{ required: true, message: '请选择天数' }]}
                >
                  <TimeComponent start={dateType === 'relativePast' ? '过去' : '未来'} />
                </Form.Item>
              );
            }
          }}
        </Form.Item>
      </Fragment>
    );
  };

  // KV类型下所渲染的表单
  const getKVTypeContent = () => {
    const { extInfo } = tag;
    const { weightRange } = extInfo || {};

    // 更新时候的步长
    const [min, max, step] = editWeightRange;

    const { kvDisplayKey, kvDisplayValue, kvWeightRange } = dataConfig || {
      kvDisplayKey: '目标',
      kvDisplayValue: '',
      kvWeightRange: [],
    };

    const [minValue, maxValue, stepLength] = kvWeightRange ?? (weightRange || []);

    return (
      <Spin tip="加载中..." spinning={loading}>
        <Form.Item
          label={kvDisplayKey}
          name="targets"
          rules={[{ required: true, message: '请选择目标' }]}
        >
          <Select
            mode="multiple"
            placeholder="请选择目标"
            showSearch
            style={{ width: 400 }}
            filterOption={(input, option) => option.props.children.indexOf(input) >= 0}
          >
            {(options || []).map(o => (
              <Option key={o.value} value={o.value}>
                {/*  判断如果包含&gt; 替换为 >  */}
                {o.label
                  ? o.label.includes('&gt;')
                    ? o.label.replace('&gt;', '>')
                    : o.label
                  : null}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) => prevValues.targets !== curValues.targets}
        >
          {({ getFieldValue }) => {
            const targets = getFieldValue('targets');
            if (!targets || targets.length === 0) {
              return null;
            }
            return targets.map((item, index) => {
              let opts = options.find(o => o.value === item);
              let opt = cloneDeep(opts);
              if (opt) {
                opt.value = opt.value.includes('.') ? opt.value.replaceAll('.', '$') : opt.value;
              }
              if (!opt) {
                return null;
              }
              return (
                <Form.Item
                  label={`${opt.label}-${kvDisplayValue ?? '权重'}`}
                  key={`${opt.value}_weight`}
                  name={`${opt.value}_weight`}
                  rules={[{ required: true, message: '请输入权重' }]}
                >
                  <Slider
                    className="ant-pro-sider-menu-sider"
                    range
                    style={{ width: 400 }}
                    marks={{
                      0: minValue ? parseFloat(minValue) : min,
                      [maxValue ? parseFloat(maxValue) : max]: {
                        style: {
                          left: '100%',
                        },
                        label: maxValue ? parseFloat(maxValue) : max,
                      },
                    }}
                    step={stepLength || step}
                    min={minValue ? parseFloat(minValue) : min}
                    max={maxValue ? parseFloat(maxValue) : max}
                  />
                </Form.Item>
              );
            });
          }}
        </Form.Item>
      </Spin>
    );
  };

  // 自定义类型下所渲染的表单
  const getSqlTypeContent = () => {
    const list = hsfParamsList.length ? hsfParamsList : mock;
    return (
      <Spin spinning={loading}>
        {(list || []).map((item, index) => {
          if (item.paramType === 'TEXT') {
            return (
              <Form.Item
                label={item.description}
                key={index}
                name={item.name}
                rules={[{ required: true, message: '请输入' }]}
              >
                <Input placeholder="请输入文本" />
              </Form.Item>
            );
          } else if (['ENUM', 'SINGLE_ENUM']?.includes(item.paramType)) {
            return (
              <Form.Item
                label={item.description}
                key={index}
                name={item.name}
                rules={[{ required: true, message: '请选择' }]}
              >
                <Select
                  mode={item.paramType !== 'SINGLE_ENUM' ? 'multiple' : undefined}
                  placeholder="请选择目标"
                  showSearch
                  style={{ width: 500 }}
                  filterOption={(input, option) => option.props.children.indexOf(input) >= 0}
                >
                  {(item.enumList || []).map(o => (
                    <Option key={o.value} value={o.value}>
                      {o.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            );
          } else if (item.paramType === 'DATE') {
            return (
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                  prevValues[item.name] !== curValues[item.name]
                }
              >
                {({ getFieldValue }) => {
                  const date = getFieldValue(item.name);
                  if (date && !isDayjs(date)) {
                    form.setFieldsValue({
                      [item.name]: dayjs(date, 'YYYYMMDD'),
                    });
                  }
                  return (
                    <Form.Item
                      label={item.description}
                      key={index}
                      name={item.name}
                      rules={[{ required: true, message: '请选择日期' }]}
                    >
                      <DatePicker format="YYYYMMDD" />
                    </Form.Item>
                  );
                }}
              </Form.Item>
            );
          } else if (item.paramType === 'NUMBER') {
            return (
              <Form.Item
                label={item.description}
                key={index}
                name={item.name}
                rules={[
                  {
                    required: true,
                    pattern: new RegExp(/^[0-9]\d*$/, 'g'),
                    message: '请输入数字',
                  },
                ]}
              >
                <Input placeholder="请输入数字" />
              </Form.Item>
            );
          } else if (item.paramType === 'TIME') {
            return (
              <Form.Item
                label={item.description}
                key={index}
                name={item.name}
                rules={[{ required: true, message: '请选择时间' }]}
              >
                <DateTimePicker />
              </Form.Item>
            );
          }
        })}
      </Spin>
    );
  };

  const handleOk = async () => {
    try {
      form.validateFields().then(async data => {
        let isValid = true;
        let params = {};

        if (tag.name === '诸葛指定人群匹配' && data.crowd_id) {
          const result = await queryCrowdCircle(data.crowd_id);
          if (!result.data) {
            message.error('人群不存在');
            return;
          }
          if (result.data.timeType && result.data.timeType === 'ONLINE') {
            message.error('仅支持离线人群id');
            return;
          }

          const res = await checkCrowdId({ crowdId: Number(data.crowd_id) });

          if (!res.success) {
            message.error(res.msg);
            isValid = false;
            return;
          }
        }

        if (tag && isValid) {
          const { dataType } = tag;

          if (dataType === 'ENUM' || dataType === 'MULTI_VALUE') {
            params.enumValues = options.filter(o => data.checkedList.includes(o.value));
          } else if (dataType === 'JSON_OBJECT') {
            params.data = timeSpaceLabelSubmit(
              data,
              conditionOptions,
              behaviorTypeOptions,
              timeTypeOptions
            );
          } else if (dataType === 'NUMBER' && data.numberValue) {
            if (data.numberValue.min > data.numberValue.max) {
              message.error('最小值不能大于最大值');
              return;
            }
            Object.assign(params, data);
          } else {
            if (data.date) {
              const [startDate, endDate] = data.date;
              data.startDate = startDate.format('YYYY-MM-DD');
              data.endDate = endDate.format('YYYY-MM-DD');
              delete data.date;
            }

            if (dataType === 'KV') {
              data.targets = options.filter(o => data.targets.includes(o.value));
            }

            if (dataType === 'NONE_TYPE') {
              Object.keys(data).forEach(key => {
                if (dayjs.isDayjs(data[key])) data[key] = data[key].format('YYYYMMDD');
              });
              data.mock = mock;
            }

            Object.assign(params, data);
          }

          if (isHsfCustom(tag)) {
            const hsfParams = getHsfParams(tag);
            const flattenParams = getFlattenParams(getHsfServiceParams(tag));
            const hsfData = pick(
              data,
              hsfParams.map(ele => ele.name)
            );
            Object.keys(hsfData).forEach(key => {
              if (dayjs.isDayjs(hsfData[key])) hsfData[key] = hsfData[key].format('YYYYMMDD');
            });

            onOk({ hsfData, otherData: params, mock: hsfParamsList });
          } else {
            onOk(params);
          }
          form.resetFields();
        }
      });
    } catch (error) {
      message.error('请将数据填写完整');
    }
  };

  return (
    <Modal visible={visible} onCancel={onCancelClick} onOk={handleOk} width={800} title={title}>
      <div style={{ background: '#EFEFEF', padding: 10, marginBottom: 10 }}>
        标签描述: {tag.description ?? ''}
      </div>
      <Form {...FORM_ITEM_LAYOUT} form={form}>
        {renderContent()}
        {isHsf && getSqlTypeContent()}
      </Form>
    </Modal>
  );
};

export default SelectEnumValueModal;
