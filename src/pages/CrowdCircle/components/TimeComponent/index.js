import React, { Fragment } from 'react';
import { InputNumber } from 'antd';

class TimeComponent extends React.PureComponent {
  handleChange = value => {
    this.triggerChange(value);
  };

  triggerChange = changedValue => {
    const { onChange } = this.props;
    if (onChange) {
      onChange(changedValue);
    }
  };

  render() {
    const { start, value, max } = this.props;
    return (
      <Fragment>
        <span>{start} </span>
        <InputNumber placeholder="天数" min={1} max={max || undefined}  value={value} onChange={this.handleChange} />
        <span> 天</span>
      </Fragment>
    );
  }
}

export default TimeComponent;
