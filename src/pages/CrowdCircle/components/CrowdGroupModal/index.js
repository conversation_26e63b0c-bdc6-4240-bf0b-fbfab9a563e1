import { Modal, Form, Select } from 'antd';

const CrowdGroupModal = (props) => {
    const { updateCrowdGroupConfig, setUpdateCrowdGroupConfig, crowdGroupForm, dataList, handleUpdateCrowdGroup } = props;
    return (
        <Modal
        title="人群分组"
        visible={updateCrowdGroupConfig.visible}
        onCancel={() => {
          setUpdateCrowdGroupConfig({
            visible: false,
            record: {},
          });
        }}
        onOk={() => {
          crowdGroupForm.submit();
        }}
      >
        <Form
          onFinish={handleUpdateCrowdGroup}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          form={crowdGroupForm}
        >
          <Form.Item name="groupIdList" label="人群分组">
            <Select mode="multiple" style={{ width: 280 }} placeholder="请选择人群分组">
              {(dataList || []).map(item => (
                <Select.Option key={item.key} value={item.key}>
                  {item.title}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    )
}

export default CrowdGroupModal