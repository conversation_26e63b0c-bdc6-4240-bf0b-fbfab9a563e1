import React, { useEffect, useState } from 'react';
import { Select } from 'antd'; // 假设你在使用 antd 组件库

const { Option } = Select;

const DateInput = props => {
  const { size, value = { updatePeriod: 'DAY', updateTime: '12:00:00' }, onChange } = props;

  // 使用 useState 来管理日期数组
  const dateArray = new Array(24).fill(null).map((_, index) => ({
    id: index,
    value: `${index}:00:00`,
  }));

  // 处理变化
  const handleChange = changedValue => {
    if (onChange) {
      onChange({
        ...value,
        updateTime: changedValue,
      });
    }
  };

  return (
    <span>
      <Select value={value && value.updatePeriod} style={{ width: '60px' }}>
        <Option value="DAY">天</Option>
      </Select>
      <Select value={value && value.updateTime} style={{ width: '120px' }} onChange={handleChange}>
        {dateArray.map(item => (
          <Option key={item.id} value={item.value}>
            {item.value}
          </Option>
        ))}
      </Select>
    </span>
  );
};

export default DateInput;
