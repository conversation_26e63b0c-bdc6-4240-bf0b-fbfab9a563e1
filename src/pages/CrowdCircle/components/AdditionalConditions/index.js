import React, { Fragment, useEffect, useState } from 'react';
import { Input, Select, Button, Row, message, Popconfirm, Col, Form } from 'antd';
import { MinusCircleOutlined, PlusOutlined, CloseCircleOutlined } from '@ant-design/icons';
import styles from './index.less';

import { getContent } from './content';
import { queryConditionType, queryBehaviorType } from '@/services/api';

const { Option } = Select;

const AdditionalConditions = (props) => {
  const { form, behaviorCode, conditionOptions, value: initialValue, onChangeConditionOptions } = props;

  const [value, setValue] = useState(initialValue || []);
  const [behaviorTypeOptions, setBehaviorTypeOptions] = useState([]);
  const [list, setList] = useState([]);

  useEffect(() => {
    if (behaviorCode) {
      fetchData(behaviorCode);
    }
    getSameDestBehavior();
  }, [behaviorCode]);

  useEffect(() => {
    setValue(initialValue || []);
    setList(initialValue?.map((ele, index) => index));
  }, [initialValue]);

  // 用来查询附加条件
  const fetchData = (value) => {
    if (!value) return;
    queryConditionType({ behaviorType: value }).then((res) => {
      if (!res?.success) {
        message.error(res?.msg);
        return;
      }
      onChangeConditionOptions(res?.data || []);
    });
  };

  // 单击增加按钮
  const add = () => {
    const nextKeys = list.concat(list.length);
    setList(nextKeys);
  };

  // 查询行为类型
  const getSameDestBehavior = () => {
    queryBehaviorType({ filterNoDest: true }).then((res) => {
      if (!res.success) {
        message.error(res.msg);
        return;
      }

      if (res?.data) {
        const options = Object.keys(res.data || {}).map((ele) => ({
          label: res.data[ele],
          value: ele,
        }));
        setBehaviorTypeOptions(options);
      }
    });
  };

  const getConditions = (opts, code) => {
    if (code && opts?.length) {
      return opts?.find(ele => ele?.paramCode === code || `${ele.paramCode}-${ele.paramDesc}` === code) || {};
    }
  };

  const remove = (k) => {
    setList(list.filter((key) => key !== k))
  };

  const setDisabled = (o) => {
    const list = form.getFieldValue('additional') || [];
    const newList = list.map((ele) => ele.paramCode).filter((i) => i !== 'valid-同目的地行为');
    return newList.includes(o);
  };

  const setParamCodeValue = (o) => {
    return o.paramDesc === '同目的地行为' ? `${o.paramCode}-${o.paramDesc}` : o.paramCode;
  };

  const formItems = (list || []).map((k, index) => (
    <Form.Item required={false} key={k} wrapperCol={{ span: 24 }} style={{ marginBottom: 0 }}>
      <Row type="flex" style={{ width: '100%', marginBottom: 0 }}>
        <Col span={6} style={{ marginRight: 10 }}>
          <Form.Item
            name={["additional", k, "paramCode"]}
            noStyle
          >
            <Select
              placeholder="请选择条件"
              showSearch
              filterOption={(input, option) => option.props.children.indexOf(input) >= 0}
              style={{ width: '100%' }}
              onChange={() => {
                form.setFieldsValue({ [`additional[${k}].values`]: undefined });
              }}
            >
              {conditionOptions &&
                conditionOptions.length > 0 &&
                conditionOptions.map((o) => (
                  <Option key={o.paramCode} value={setParamCodeValue(o)} disabled={setDisabled(setParamCodeValue(o))}>
                    {o.paramDesc}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        </Col>
        <Form.Item noStyle shouldUpdate={(prevValues, curValues) => prevValues.additional?.[k]?.paramCode !== curValues.additional?.[k]?.paramCode}>
          {({ getFieldValue }) => {
            const paramCode = getFieldValue(["additional", k, "paramCode"]);
            return (
              <Col span={15} style={{ marginRight: 12 }}>
                {getContent(
                  k,
                  getConditions(conditionOptions, paramCode),
                  behaviorTypeOptions,
                )}
              </Col>
            );
          }}
        </Form.Item>
        <Col span={2} style={{ display: 'flex', alignItems: 'center', justifyContent: 'center',height: 32 }}>
          <Popconfirm
            title="是否继续执行删除"
            onConfirm={() => remove(k)}
            onCancel={() => {}}
            okText="继续执行"
            cancelText="取消"
          >
            <CloseCircleOutlined />
          </Popconfirm>
        </Col>
      </Row>
    </Form.Item>
  ));

  return (
    <Fragment>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <h3 style={{ margin: '0px 10px 0px 0' }}>附加条件</h3>
        <Button type="primary" onClick={add} disabled={behaviorCode === 'VISA'}>
          <PlusOutlined /> 增加
        </Button>
        <span style={{ color: 'red' }}>（不同附加条件为“且”的逻辑）</span>
      </div>
      <div className={styles.additionalForm}>{formItems}</div>
    </Fragment>
  );
};

export default AdditionalConditions;
