import React from 'react';
import {
  Input,
  Select,
  Button,
  Row,
  message,
  Popconfirm,
  Col,
  Card,
  InputNumber,
  Form,
} from 'antd';

import ConditionSelect from '../ConditionSelect';
import BehaviorSelect from '../BehaviorSelect';

// 是否是同行为标签，例如：同目的地行为
const sameBehaviorOpts = [
  {
    label: '有同目的地行为',
    value: 'AND',
  },
  {
    label: '没有同目的地行为',
    value: 'NOTIN',
  },
];

// 时间偏差模式
const paramOffsetModeOpts = [
  {
    label: '绝对值偏移',
    value: 1,
  },
  {
    label: '向前偏移',
    value: 2,
  },
  {
    label: '向后偏移',
    value: 3,
  },
];

export const getContent = (k, conditions, behaviorTypeOptions) => {
  if (conditions?.paramDesc === '同目的地行为') {
    return (
      <Card
        bodyStyle={{
          padding: '10px 10px 0px',
          marginBottom: '-6px',
        }}
      >
        <Row type="flex" gutter={[16, 16]}>
          <Col span={12}>
            <Form.Item
              name={['additional', k, 'logicRelation']}
              rules={[{ required: true, message: '请选择有无同目的地行为' }]}
            >
              <Select
                placeholder="有无同目的地行为"
                showSearch
                filterOption={(input, option) => option.props.children.indexOf(input) >= 0}
                style={{ width: '100%' }}
              >
                {sameBehaviorOpts.map(o => (
                  <Option key={o.value} value={o.value}>
                    {o.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, curValues) =>
                prevValues.behaviorCode !== curValues.behaviorCode
              }
            >
              {({ getFieldValue }) => {
                const behaviorCodeValue = getFieldValue('behaviorCode');
                return (
                  <Form.Item
                    name={['additional', k, 'behaviorCode']}
                    rules={[{ required: true, message: '请选择行为' }]}
                  >
                    <BehaviorSelect
                      behaviorTypeOptions={behaviorTypeOptions}
                      behaviorCode={behaviorCodeValue}
                    />
                  </Form.Item>
                );
              }}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name={['additional', k, 'paramOffsetMode']}
              rules={[{ required: true, message: '请选择时间偏差类型' }]}
            >
              <Select
                placeholder="按出发（开始）时间偏差类型"
                showSearch
                filterOption={(input, option) => option.props.children.indexOf(input) >= 0}
                style={{ width: '100%' }}
              >
                {paramOffsetModeOpts.map(o => (
                  <Option key={o.value} value={o.value}>
                    {o.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name={['additional', k, 'paramOffsetTs']}
              rules={[{ required: true, message: '请输入时长' }]}
            >
              <InputNumber
                max={168}
                style={{ width: '100%' }}
                formatter={value => `${value}h`}
                parser={value => value?.replace('h', '')}
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    );
  }

  switch (conditions?.paramDataType) {
    case 'SINGLE_ENUM':
      return (
        <Form.Item
          name={['additional', k, 'values']}
          rules={[{ required: true, message: '请选择' }]}
        >
          <ConditionSelect
            dimEnumMetaId={
              conditions?.paramDimEnumMetaId === 0 ? '0' : conditions?.paramDimEnumMetaId
            }
          />
        </Form.Item>
      );
  }
};
