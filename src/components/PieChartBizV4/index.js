import React, { Component } from 'react';
import {
  Chart,
  Interval,
  Tooltip,
  Axis,
  Coordinate,
  Interaction,
  Legend
} from 'bizcharts';

class PieChart extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  render() {

    const { data = [], height = 200} = this.props
    let allCount = 0
    data.map(item => {
      allCount += item.count
    })

    return (
      <Chart height={height} 
        data={data} 
        scale={    
          {count: {
            formatter: val => {
          return `${val}（${(val * 100 / allCount).toFixed(2)}%）`
          },
        }	
        }}
        autoFit>
        <Coordinate type="theta" radius={0.75} />
        <Tooltip showTitle={false} />
        <Axis visible={false} />
        <Legend
          flipPage={false}
        />
        <Interval
          position="count"
          adjust="stack"
          color="item"
          style={{
            lineWidth: 1,
            stroke: '#fff',
          }}
          label={['count', {
            content: (data) => {
              return `${data.item}：${data.count}`;
            },
          }]}
        />
        <Interaction type='element-single-selected' />
      </Chart>
    );
  }
}

export default PieChart;
