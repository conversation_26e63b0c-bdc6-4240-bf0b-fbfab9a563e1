/**
 * 算法选品任务弹窗
 * */
import React, { PureComponent } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, Row, Col, Button, Select, Input, Switch, DatePicker, } from 'antd';
import isArray from 'lodash/isArray';
import dayjs from 'dayjs';
import GetUser from '@/components/GetUser';
// import { createFormItems } from '@/utils/createFormItem';
import styles from './index.less';

const { Option } = Select;
const FORM_ITEM_LAYOUT = {
  labelCol: { span: 9 },
  wrapperCol: { span: 12 },
};
const noop = () => {};
const FormItem = Form.Item;

const isDayjs = (obj) => obj instanceof dayjs;

@Form.create()
class CreateEnclosedModal extends PureComponent {
  static defaultProps = {
    editFormData: {}
  };

  constructor(props) {
    super(props);
    this.state = {
      currentItemType: '',
      itemBizType: [],
      algoModelName: [],
      itemType: [],
    };
  }

  componentWillReceiveProps(nextProps) {
    if (
      (!nextProps.editFormData || !nextProps.editFormData.name) &&
      nextProps.editFormData !== this.props.editFormData
    ) {
      this.props.form.resetFields();
    }
    if (nextProps.itemBizType.length !== this.state.itemBizType.length) {
      // itemBizType={itemBizType}
      // algoModelName={algoModelName}
      // itemType={itemType}
      this.setState({
        itemBizType: nextProps.itemBizType,
        algoModelName: nextProps.algoModelName,
        itemType: nextProps.itemType,
      });
    }

    // isEdit={isEditChooseModal}
    // itemBizType={itemBizType}
    // algoModelName={algoModelName}
    // itemType={itemType}
  }

  handleSubmit = () => {
    const { form, onSubmit = noop } = this.props;

    form.validateFields((err, values) => {
      if (!err) {
        const payload = values;
        // 预处理表单数据
        Object.keys(payload).forEach(k => {
          const t = payload[k];

          if (isDayjs(t)) {
            payload[k] = t.format('YYYY-MM-DD');
          }
          // 判断是否为datepicker的表单值 是将其转为需要的格式
          if (isArray(t) && t.length === 2 && isDayjs(t[0]) && isDayjs(t[1])) {
            payload[k] = t.map(m => m.format('YYYY-MM-DD'));
          }

          // filelist类型 geng
          if (isArray(t) && t.length && (t[0].url || t[0].response)) {
            // eslint-disable-next-line prefer-destructuring

            // 更新的时候
            const file = t[t.length - 1];
            payload[k] = file.response ? file.response.url : file.url;
          }
        });

        onSubmit({
          ...payload,
          needUpdate: payload.needUpdate ? 1 : 0,
          approval_status: 'INIT',
        });
      }
    });
  };

  onAlgoTypeChange = () => {
    // this.setState({
    //   currentModelName: value,
    // });
  };

  onItemTypeChange = value => {
    this.setState({
      currentItemType: value,
    });

    const {
      algoModelName
    } = this.state;
    const matchNames = algoModelName.filter(item => {
      const keys = Object.keys(item);
      const key = keys[0];
      return key.includes(value);
    }).map(item => {
      return Object.keys(item)[0];
    });
    const showName = matchNames && matchNames[0];

    this.props.form.setFieldsValue({
      algoModelName: showName
    });
  }

  renderForm = () => {
    const {
      form: { getFieldDecorator },
      editFormData,
      isEdit,
    } = this.props;
    const {
      itemBizType,
      algoModelName,
      itemType,
      currentItemType,
    } = this.state;

    return (
      <Form
        {...FORM_ITEM_LAYOUT}
        className={!isEdit ? styles.formReadonly : ''}
        onSubmit={this.handleSubmit}
      >
        <FormItem label="素材名称">
          {getFieldDecorator('itemTaskName', {
            initialValue: editFormData.itemTaskName,
            rules: [{ required: true, message: '请输入素材名称' }],
          })(<Input placeholder="请输入素材名称" />)}
        </FormItem>
        <FormItem label="使用场景">
          {getFieldDecorator('bizSceneType', {
            initialValue: editFormData.bizSceneType,
            rules: [{ required: true, message: '请选择使用场景' }],
          })(
            <Select
              filterOption={false}
              labelInValue={false}
              placeholder="请选择使用场景"
              onChange={this.onAlgoTypeChange}
            >
              {itemBizType &&
                itemBizType.length > 0 &&
                itemBizType
                  .map(m => {
                    const key = Object.keys(m)[0];
                    return <Option key={key} value={key}>{m[key]}</Option>
                  })}
            </Select>
          )}
        </FormItem>
        <FormItem label="素材类型">
          {getFieldDecorator('itemType', {
            initialValue: editFormData.itemType,
            rules: [{ required: true, message: '请选择素材类型' }],
          })(
            <Select
              filterOption={false}
              labelInValue={false}
              placeholder="请选择素材类型"
              onChange={this.onItemTypeChange}
            >
            {
              itemType &&
              itemType.length > 0 &&
              itemType
                .map(m => {
                  const key = Object.keys(m)[0];
                  return <Option key={key} value={key}>{m[key]}</Option>
                })
            }
            </Select>
          )}
        </FormItem>
        <FormItem label="算法模型">
          {getFieldDecorator('algoModelName', {
            initialValue: editFormData.algoModelName,
            rules: [{ required: true, message: '请选择算法模型' }],
          })(
            <Select
              filterOption={false}
              labelInValue={false}
              placeholder="请选择算法模型"
              onChange={this.onAlgoTypeChange}
            >
              {
                algoModelName &&
                algoModelName.length > 0 &&
                algoModelName
                  .filter(obj => {
                    const key = Object.keys(obj)[0];
                    return key.includes(currentItemType);
                  })
                  .map(m => {
                    const key = Object.keys(m)[0];
                    return <Option key={key} value={key}>{m[key]}</Option>
                  })
              }
            </Select>
          )}
        </FormItem>
        <FormItem label="人群包ID">
          {getFieldDecorator('crowdId', {
            initialValue: editFormData.crowdId,
            rules: [{ required: true, message: '请设置人群包ID' }],
          })(<Input />)}
        </FormItem>
        <FormItem label="素材池ID">
          {getFieldDecorator('itemPoolId', {
            initialValue: editFormData.itemPoolId,
            rules: [{ required: true, message: '请设置素材池ID' }],
          })(<Input />)}
        </FormItem>
        <FormItem label="管理员">
          {getFieldDecorator('operator', {
            initialValue: editFormData.operator,
            rules: [{ required: true, message: '请输入管理员' }],
          })(<GetUser />)}
        </FormItem>
        <FormItem label="过期时间">
          {getFieldDecorator('gmtExpiredDate', {
            initialValue: editFormData.gmtExpiredDate ? dayjs(editFormData.gmtExpiredDate) : undefined,
            rules: [{ required: true, message: '请选择过期时间' }],
          })(
            <DatePicker
              format="YYYY-MM-DD HH:mm:ss"
              showTime={{
                defaultValue: dayjs('23:59:59', 'HH:mm:ss'),
              }}
            />
          )}
        </FormItem>

        <FormItem label="是否更新">
          {getFieldDecorator('needUpdate', {
            initialValue: !!editFormData.needUpdate,
            valuePropName: 'checked',
          })(<Switch />)}
        </FormItem>

        <FormItem hidden>
          {getFieldDecorator('id', {
            initialValue: editFormData.id,
          })(<Input />)}
        </FormItem>
        {isEdit ? (
          <Row gutter={24}>
            <Col span={4} />
            <Col span={14} style={{ textAlign: 'center' }}>
              <Button onClick={() => this.onCancel()} style={{ marginRight: 15 }}>
                取消
              </Button>
              <Button type="primary" onClick={this.handleSubmit}>
                保存
              </Button>
            </Col>
          </Row>
        ) : null}
      </Form>
    );
  };

  onCancel = () => {
    const { onCancel = noop } = this.props;
    onCancel();
    this.setState({
      curAlgoModel: null,
    });
  };

  render() {
    const { editModalVisible } = this.props;

    return (
      <Modal
        visible={editModalVisible}
        footer={null}
        title="新增个性化素材任务"
        onCancel={this.onCancel}
        width={750}
      >
        {this.renderForm()}
      </Modal>
    );
  }
}

export default CreateEnclosedModal;
