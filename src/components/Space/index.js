import React, { PureComponent } from 'react'
import PropTypes from 'prop-types'

export default class Space extends PureComponent {
  render() {
    const { distance, direction } = this.props
    const directionList = [
      {
        type: 'row',
        props: {
          width: `${distance}px`,
          height: '1px',
        },
      },
      {
        type: 'column',
        props: {
          height: `${distance}px`,
          width: '100%',
        },
      },
    ]

    const stylesProps =
      directionList.filter(item => item.type === direction)[0] ||
      directionList[1]

    return <div style={stylesProps.props} />
  }
}

Space.propTypes = {
  distance: PropTypes.number,
  direction: PropTypes.oneOf(['row', 'column']),
}

Space.defaultProps = {
  distance: 10,
  direction: 'column',
}
