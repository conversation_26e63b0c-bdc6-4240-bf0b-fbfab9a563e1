import React from 'react';
import { FormattedMessage } from 'umi';
import { Link } from 'umi';
import {  Tabs, Typography, Button } from 'antd';
import { connect } from 'dva';
import classNames from 'classnames';
import MenuContext from '@/layouts/MenuContext';
import { YuqueOutlined } from '@ant-design/icons';
import GridContent from './GridContent';
import styles from './index.less';
import { conversionBreadcrumbList } from './breadcrumb';
import { hideSideBar } from '../../utils/nav';
import { getUrlParam } from '../../utils/utils';
import { PageHeader } from '@ant-design/pro-layout'; 

const { Title } = Typography;

/**
 * render Footer tabList
 * In order to be compatible with the old version of the PageHeader
 * basically all the functions are implemented.
 */
const renderFooter = ({ tabList, tabActiveKey, onTabChange, tabBarExtraContent }) => {
  return tabList && tabList.length ? (
    <Tabs
      className={styles.tabs}
      activeKey={tabActiveKey}
      onChange={key => {
        if (onTabChange) {
          onTabChange(key);
        }
      }}
      tabBarExtraContent={tabBarExtraContent}
    >
      {tabList.map(item => (
        <Tabs.TabPane tab={item.tab} key={item.key} />
      ))}
    </Tabs>
  ) : null;
};

const PageHeaderWrapper = ({
  children,
  contentWidth,
  wrapperClassName,
  top,
  title,
  content,
  logo,
  extraContent,
  toDocumentLink = "https://yuque.antfin.com/qnwrq9/kzl68s",
  hiddenBreadcrumb = getUrlParam('isFrame') ? true : false,
  ...restProps
}) => {
  return (
    <div style={{ margin: '-24px -24px 0' }} className={classNames(wrapperClassName, styles.main)}>
      {top}
      <MenuContext.Consumer>
        {value => {
          return (
            !hiddenBreadcrumb &&
            <PageHeader
              wide={contentWidth === 'Fixed'}
              title={
                title && (
                  <Title
                    level={4}
                    style={{
                      margin: 0,
                    }}
                  >
                    {title}
                  </Title>
                )
              }
              subTitle=""
              extra={[
                <Button key="3" type="dashed">
                  <a
                    // href="https://yuque.antfin-inc.com/fbrain/zhuge-doc" //旧文档
                    href={toDocumentLink} //新文档
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <YuqueOutlined /> 使用文档
                  </a>
                </Button>,
              ]}
              key="pageheader"
              {...restProps}
              breadcrumb={
                !hiddenBreadcrumb &&
                !hideSideBar &&
                conversionBreadcrumbList({
                  ...value,
                  ...restProps,
                })
              }
              className={styles.pageHeader}
              linkElement={Link}
              footer={renderFooter(restProps)}
            >
              <div className={styles.detail}>
                {logo && <div className={styles.logo}>{logo}</div>}
                <div className={styles.main}>
                  <div className={styles.row}>
                    {content && <div className={styles.content}>{content}</div>}
                    {extraContent && <div className={styles.extraContent}>{extraContent}</div>}
                  </div>
                </div>
              </div>
            </PageHeader>
          );
        }}
      </MenuContext.Consumer>
      {children ? (
        <div className={!hiddenBreadcrumb && styles['children-content']}>
          <GridContent>{children}</GridContent>
        </div>
      ) : null}
    </div>
  );
};

export default connect(({ setting }) => ({
  contentWidth: setting.contentWidth,
}))(PageHeaderWrapper);
