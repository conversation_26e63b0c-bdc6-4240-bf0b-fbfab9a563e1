import React, { useState } from 'react';
import { Modal, Row, Col } from 'antd';
import CheckCard from '@/components/CheckCard'
import { get, cloneDeep } from 'lodash'

const CROWD_TYPE_MAP = [{
  name: '标签圈人',
  desc: '通过标签组合的与或非的方式进行人群圈选',
  value: 'LABEL_CROWD',
  isCommonUse: true
}, {
  name: 'SQL圈人',
  desc: '通过导入SQL语句的方式进行人群圈选',
  value: 'ODPS_SQL_CROWD'
}, {
  name: '文件上传',
  desc: '通过上传txt文件的形式生成固定人群包',
  value: 'FILE_CROWD'
}, {
  name: 'ODPS导入',
  desc: '通过导入授权诸葛的视图表指定字段进行人群权限',
  value: 'ODPS_TABLE_CROWD'
}, {
  name: '人群逻辑运算',
  desc: '进行同类型人群包之间的匹配去除等',
  value: 'OPERATE_CROWD',
}, {
  name: '跃迁圈人',
  desc: '通过监控人群/标签的变化用户生成人群包',
  value: 'TRANSITION_CROWD',
}]

const POPULATION_TYPE = [{
  name: '淘宝id',
  value: 'TAOBAO_USER'
}, {
  name: 'Scrm-Oneid',
  value: 'SCRM_USER'
}, {
  name: '唯一设备id',
  desc: '飞猪综合定义Device Oneid',
  value: 'DEVICE'
}]

const CreateCrowdModal = (props) => {
  const { onCancel, onSubmit, editModalVisible } = props;
  const [ crowdTypeMap, setCrowdTypeMap] = useState(CROWD_TYPE_MAP)
  const [ crowdPack, setCrowdPack] = useState('TAOBAO_USER')
  const [ crowdType, setCrowdType] = useState('LABEL_CROWD')

  const handleOk = () => {
    onSubmit(crowdPack, crowdType)
    onReset()
  };

  const handleCancel = () => {
    onCancel()
    onReset()
  }

  const onReset = () => {
    setCrowdPack('TAOBAO_USER')
    setCrowdType('LABEL_CROWD')
    setCrowdTypeMap(CROWD_TYPE_MAP)
  }

  const onCheckCrowdPack = (item) => {
    if (item.value === 'SCRM_USER') {
      setCrowdTypeMap(CROWD_TYPE_MAP.slice(0,CROWD_TYPE_MAP.length - 1))
      setCrowdType('LABEL_CROWD')
    } else if (item.value === 'DEVICE') {
      setCrowdTypeMap(CROWD_TYPE_MAP.slice(0,1))
      setCrowdType('LABEL_CROWD')
    } else {
      setCrowdTypeMap(CROWD_TYPE_MAP)
    }
    setCrowdPack(item.value)
  }

  const onCheckCrowdType = (item) => {
    setCrowdType(item.value)
  }

  return (
    <Modal
      title="创建人群"
      visible={editModalVisible}
      destroyOnClose={true}
      width={700}
      onOk={handleOk}
      onCancel={handleCancel}
    >
      <h3>人群包类型</h3>
      <Row gutter={[10, 10]}>
        {POPULATION_TYPE.map((item) => (
          <Col span={8} key={item.value} onClick={() => onCheckCrowdPack(item)}>
            <CheckCard
              name={item.name}
              desc={item.desc}
              disabled={item.value === crowdPack}
            />
          </Col>
        ))}
      </Row>
      <h3>圈选方式</h3>
      <Row gutter={[10, 10]}>
        {crowdTypeMap && crowdTypeMap.length > 0 && crowdTypeMap.map((item) => (
          <Col span={8} key={item.value} onClick={() => onCheckCrowdType(item)}>
            <CheckCard
              name={item.name}
              desc={item.desc}
              isCommonUse={item.isCommonUse}
              disabled={item.value === crowdType}
            />
          </Col>
        ))}
      </Row>
    </Modal>
  );
};

export default CreateCrowdModal;
