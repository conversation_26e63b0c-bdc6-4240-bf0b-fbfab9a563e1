import React from 'react';
import { Tag } from 'antd';

const NOOP = () => {};
const { CheckableTag } = Tag;

class MyCheckableTag extends React.Component {
  state = { checked: false };

  handleChange = checked => {
    const { handleChange = NOOP } = this.props;
    this.setState({ checked });

    handleChange(checked);
  };

  render() {
    const { handleChange, ...resProps } = this.props;

    return <CheckableTag style={{width: '224px',overflow: 'hidden',textOverflow: 'ellipsis'}} {...resProps} checked={this.state.checked} onChange={this.handleChange} />;
  }
}

export default MyCheckableTag;
