/* eslint-disable no-useless-escape */
/**
 * 人群放大弹窗
 * */
import React, { PureComponent } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, Row, Col, Button, Select, Input, DatePicker, Switch, Radio, message, Drawer } from 'antd';
import dayjs from 'dayjs';
import debounce from 'lodash/debounce';
import { queryOdpsTable, queryOdpsTableColumn } from '@/services/api';
import GetUser from '@/components/GetUser';
import { connect } from 'dva';

const { Option } = Select;
const { TextArea } = Input;
const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const FORM_ITEM_LAYOUT = {
  labelCol: { span: 7 },
  wrapperCol: { span: 14 },
};
const noop = () => {};

@Form.create()
@connect(state => ({ user: state.user }))
export default class CreateOdpsCrowdModal extends PureComponent {
  constructor(props) {
    super(props);
    this.fetchOdpsTable = debounce(this.fetchOdpsTable, 300);
    this.state = {
      tableColumns: [],
      odpsTables: [],
      keyword: '',
    };
  }

  componentWillReceiveProps(nextProps) {
    if (
      (!nextProps.editFormData || !nextProps.editFormData.crowdName) &&
      nextProps.editFormData !== this.props.editFormData
    ) {
      this.props.form.resetFields();
    }
  }

  onCancel = () => {
    const { onCancel = noop } = this.props;
    onCancel();
  };

  handleSubmit = () => {
    const { form, onSubmit = noop , editFormData, user: { currentUser }} = this.props;

    form.validateFields((err, values) => {
      if (!err) {
        const {
          accessId,
          accessKey,
          expiredDate,
          isLoss,
          needUpdate,
          where,
          uid,
          partition,
          tableName,
        } = values;

        const payload = {
          ...values,
          needUpdate: needUpdate ? 1 : 0,
          expiredDate: expiredDate.valueOf(),
        };

        payload.crowdType = 'ODPS_TABLE_CROWD';
        payload.crowdTags = [];
        payload.profileType = editFormData.profileType || 'TAOBAO_USER'

        // 涉及资损
        if (isLoss === 1) {
          payload.crowdTags.push('LOSS_OF_ASSETS');
        }

        const prevExtInfo = payload.extInfo;
        payload.extInfo = {
          ...prevExtInfo,
          ODPS_TABLE: tableName,
          ODPS_TABLE_WHERE: where, // 可以为空，一个或多个
          ODPS_TABLE_UID: uid, // 用户ID字段
          ODPS_TABLE_PARTITION: partition, // 日期分区字段

          // AccessID/KEY 如果未指定则用系统账号
          ODPS_ACCESS_ID: accessId,
          ODPS_ACCESS_KEY: accessKey,
        };

        if (payload.needUpdate) {
          if (!payload.extInfo.UPDATE_MODEL) {
            payload.extInfo.UPDATE_MODEL = {};
          }
          payload.extInfo.UPDATE_MODEL.updateType = payload.updateType === 1 ? 'dependence' : 'every_day';
          payload.extInfo.UPDATE_MODEL.dependenceBizOption = payload.updateType === 1 ? payload.updateDependenceApp : undefined;
        } else if (payload.extInfo.UPDATE_MODEL) {
          delete payload.extInfo.UPDATE_MODEL.updateType
          delete payload.extInfo.UPDATE_MODEL.dependenceBizOption
        }

        // 提交审核信息
        if (payload.applyApprovalReason && payload.qualityOwner) {
          payload.crowdApprovalInfo = {
            applyApprovalReason: payload.applyApprovalReason,
            applyOwner:{empId: currentUser.workId, nickName: currentUser.name},
            qualityOwner: payload.qualityOwner,
            needSubmit: true
          }
        }
        
        if (payload.applyApprovalReason) {
          delete payload.applyApprovalReason
        }
        if (payload.qualityOwner) {
          delete payload.qualityOwner
        }

        onSubmit(payload);
      }
    });
  };

  fetchOdpsTable = keyword => {
    queryOdpsTable({ keyword }).then(res => {
      if (!res.success) {
        message.error(res.msg);
        return;
      }

      this.setState({
        odpsTables: res.data,
      });
    });
  };

  onTableSearch = val => {
    this.setState({
      keyword: val,
    });
    this.fetchOdpsTable(val);
  };

  onTableChange = val => {
    this.fetchTableColumns(val);
  };

  fetchTableColumns = tableGuid => {
    queryOdpsTableColumn({ tableGuid }).then(res => {
      if (!res.success) {
        message.error(res.msg);
        return;
      }

      this.setState({
        tableColumns: res.data,
      });
    });
  };

  renderForm = () => {
    const {
      form: { getFieldDecorator, getFieldValue },
      editFormData,
    } = this.props;
    const { odpsTables, keyword, tableColumns } = this.state;
    const disabledDate = current =>
      current < dayjs().startOf('day') || current > dayjs() + 90 * 24 * 60 * 60 * 1000;

    const topHander = data => {
      const {
        type,
        value,
      } = data;
      const formData = this.props.form.getFieldsValue();
      if (formData) {
        if (!formData.extInfo) {
          formData.extInfo = {};
        }
        if (type === 'needUpdate') {
          if (value) {
            if (!formData.extInfo.UPDATE_MODEL) {
              formData.extInfo.UPDATE_MODEL = {};
            }
            formData.extInfo.UPDATE_MODEL.updateType = formData.updateType;
            formData.extInfo.UPDATE_MODEL.dependenceBizOption = formData.updateType === 1 ? formData.updateDependenceApp : undefined;
          } else if (formData.extInfo.UPDATE_MODEL) {
            delete formData.extInfo.UPDATE_MODEL.updateType
            delete formData.extInfo.UPDATE_MODEL.dependenceBizOption
          }
        }
      }

      if (value && type === 'needUpdate') {
        const {
          form: { setFieldsValue },
        } = this.props;
        Modal.confirm({
          title: '每日更新计算资源消耗较大，请确认是否是必要选项',
          okText:"确认",
          cancelText:"取消",
          onOk() {
            setFieldsValue({needUpdate: true})
          },
          onCancel() {
            setFieldsValue({needUpdate: false})
          },
        });
      }

      this.props.form.setFieldsValue({
        extInfo: formData.extInfo,
      });
    }
    return (
      <Form {...FORM_ITEM_LAYOUT} onSubmit={this.handleSubmit}>
        <FormItem label="人群(任务)名称">
          {getFieldDecorator('crowdName', {
            initialValue: editFormData.crowdName,
            rules: [{ required: true, message: '请输入人群名称' }],
          })(<Input placeholder="请输入人群名称" />)}
        </FormItem>
        <FormItem label="人群描述">
          {getFieldDecorator('crowdDescription', {
            initialValue: editFormData.crowdDescription,
            rules: [{ required: true, message: '请输入人群描述' }],
          })(<TextArea placeholder="请输入人群描述" />)}
        </FormItem>
        <FormItem label="管理员">
          {getFieldDecorator('operator', {
            initialValue: editFormData.operator,
            rules: [{ required: true, message: '请选择管理员' }],
          })(<GetUser />)}
        </FormItem>
        
        <FormItem label="是否涉及资损">
          {getFieldDecorator('isLoss', {
            initialValue:
              editFormData.crowdTags &&
              (editFormData.crowdTags.length > 0 &&
              editFormData.crowdTags.includes('LOSS_OF_ASSETS')
                ? 1
                : 0),
            rules: [{ required: true, message: '请选择是否涉及资损' }],
          })(
            <RadioGroup>
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </RadioGroup>
          )}
        </FormItem>

        <FormItem
          label="人群类型"
        >
          {editFormData.profileType === 'SCRM_USER' ? 'Scrm-Oneid人群包' : '淘宝id'}
        </FormItem>

        {
          getFieldValue('isLoss') === 1 && (<FormItem 
            label="详细描述"
            extra={
              <span style={{ color: 'red' }}>
                注：涉及资损的人群包将通过审批流获取额外测试保障，测试通过将后自动上线
              </span>
            }
          >
            {getFieldDecorator('applyApprovalReason', {
              initialValue: editFormData?.crowdApprovalInfo?.applyApprovalReason,
              rules: [{ required: true, message: '请输入详细描述' }],
            })(<Input.TextArea placeholder="请详细描述下可能发生资损的场景" allowClear />)}
          </FormItem>)
        }

        {
          getFieldValue('isLoss') === 1 && (
            <FormItem label="QA">
              {getFieldDecorator('qualityOwner', {
                initialValue: editFormData?.crowdApprovalInfo?.qualityOwner,
                rules: [{ required: true, message: '请输入业务对应的测试同学' }],
              })(<GetUser mode="default"/>)}
            </FormItem>
          )

        }

        <FormItem label="是否更新">
          {getFieldDecorator('needUpdate', {
            initialValue: !!editFormData.needUpdate,
            valuePropName: 'checked',
          })(<Switch onChange={checked => topHander({ type: 'needUpdate', value: checked })} />)}
        </FormItem>

        <FormItem label="更新类型" hidden={!getFieldValue('needUpdate')}>
          {
            getFieldDecorator('updateType', {
              initialValue: (
                editFormData.extInfo
                && editFormData.extInfo.UPDATE_MODEL
                && editFormData.extInfo.UPDATE_MODEL.updateType === 'dependence' ? 1 : 0
              )
            })(
              <RadioGroup onChange={e => topHander({ type: 'updateType', value: e.target.value })}>
                <Radio value={0}>每天更新</Radio>
                <Radio value={1}>依赖更新</Radio>
              </RadioGroup>
            )
          }
        </FormItem>

        <FormItem
          label="依赖更新方"
          hidden={!(
            getFieldValue('needUpdate')
            && (
              getFieldValue('updateType') === 1
            ))}>
          {getFieldDecorator('updateDependenceApp', {
            initialValue: 'huokebao'
          })(
            <Select defaultValue="huokebao">
              <Option value="huokebao">获客宝</Option>
            </Select>
          )
        }
        </FormItem>

        <FormItem
          label="人群使用场景"
          extra={
            <span style={{ color: 'red' }}>
              注：非PUSH场景请选中人群匹配，否则会导致无法判断人群
            </span>
          }
        >
          {getFieldDecorator('crowdApplyScene', {
            initialValue: editFormData.crowdApplyScene,
            rules: [{ required: true, message: '请选择人群使用场景' }],
          })(
            <Select placeholder="请选择人群使用场景" mode="multiple" showSearch>
              <Option key="PUSH">PUSH</Option>
              <Option key="MATCH">人群匹配</Option>
              {/* <Option key="ANALYSIS">人群分析</Option> */}
            </Select>
          )}
        </FormItem>
        <FormItem
          label="ODPS表权限账号"
          extra={
            <span>
              <span>授权示例：</span>
              <span style={{ fontSize: 12 }}>
                add user aliyun$<EMAIL>; GRANT Describe,Select ON TABLE
                表名称 TO USER ALIYUN$<EMAIL>; GRANT LABEL 2 ON TABLE
                表名称 TO USER ALIYUN$<EMAIL>;
              </span>
            </span>
          }
        >
          {getFieldDecorator('odpsAccessAccount', {
            initialValue: !editFormData.accessId ? 1 : 0,
            rules: [{ required: true, message: '请选择是否涉及资损' }],
          })(
            <RadioGroup>
              <Radio value={1}>系统默认账号</Radio>
              <Radio value={0} disabled>
                个人账号
              </Radio>
            </RadioGroup>
          )}
        </FormItem>
        {getFieldValue('odpsAccessAccount') === 0 && (
          <>
            <FormItem label="access id">
              {getFieldDecorator('accessId', {
                initialValue: editFormData.accessId,
                rules: [{ required: true, message: '请输入access id' }],
              })(<Input placeholder="请输入access id" />)}
            </FormItem>
            <FormItem label="access key">
              {getFieldDecorator('accessKey', {
                initialValue: editFormData.accessKey,
                rules: [{ required: true, message: '请输入access key' }],
              })(
                <Input.Password
                  visibilityToggle={!editFormData.id}
                  placeholder="请输入access key"
                />
              )}
            </FormItem>
          </>
        )}

        <FormItem label="ODPS表名称">
          {getFieldDecorator('tableName', {
            initialValue: editFormData.tableName,
            rules: [{ required: true, message: '请输入ODPS表名称' }],
          })(
            <Select
              showSearch
              onSearch={this.onTableSearch}
              onChange={this.onTableChange}
              placeholder="选择ODPS表名称"
              optionLabelProp="value"
              showArrow={false}
            >
              {odpsTables.length && // 高亮显示
                odpsTables.map(t => {
                  const index = t.indexOf(keyword);
                  const beforeStr = t.substr(0, index);
                  const afterStr = t.substr(index + keyword.length);
                  const content =
                    index > -1 ? (
                      <span>
                        {beforeStr}
                        <span style={{ color: '#f50' }}>{keyword}</span>
                        {afterStr}
                      </span>
                    ) : (
                      <span>{t}</span>
                    );
                  return (
                    <Option title={t} key={t}>
                      {content}
                    </Option>
                  );
                })}
            </Select>
          )}
        </FormItem>
        <FormItem label="用户ID字段">
          {getFieldDecorator('uid', {
            initialValue: editFormData.uid,
            rules: [{ required: false, message: '请选择用户ID字段' }], // 服务端数据问题先放开做测试
          })(
            <Select showSearch placeholder="请选择用户ID字段">
              {tableColumns.length &&
                tableColumns.map(c => <Option key={c.columnName}>{c.columnName}</Option>)}
            </Select>
          )}
        </FormItem>
        <FormItem label="where条件">
          {getFieldDecorator('where', {
            initialValue: editFormData.where && editFormData.where.replace(/\&\#39\;/gi, "'"),
          })(<Input placeholder="请输入where条件" />)}
        </FormItem>
        <FormItem label="分区字段">
          {getFieldDecorator('partition', {
            initialValue: editFormData.partition,
          })(
            <Select showSearch placeholder="请输入分区字段">
              {tableColumns.length &&
                tableColumns.map(c => <Option key={c.columnName}>{c.columnName}</Option>)}
            </Select>
          )}
        </FormItem>
        <FormItem label="人群过期时间">
          {getFieldDecorator('expiredDate', {
            initialValue: editFormData.expiredDate ? dayjs(editFormData.expiredDate) : undefined,
            rules: [{ required: true, message: '请选择人群人群过期时间' }],
          })(
            <DatePicker
              disabledDate={disabledDate}
              format="YYYY-MM-DD HH:mm:ss"
              showTime={{
                defaultValue: dayjs('23:59:59', 'HH:mm:ss'),
              }}
            />
          )}
        </FormItem>

        <FormItem hidden>
          {getFieldDecorator('extInfo', {
            initialValue: editFormData.extInfo
          })(<Input />)}
        </FormItem>

      </Form>
    );
  };

  render() {
    const { 
      editModalVisible,
      editFormData,
      user: { currentUser, isSuperAdmin } 
    } = this.props;

    const isSubmit = () => {
      // 管理者和创建者及超级管理员能编辑
      const { creator = {}, operator = [] } = editFormData;
      const owners = [...operator, creator];

      const isGranted = owners.map(o => o.empId).includes(currentUser.workId);
      
      if(!editFormData.id || isSuperAdmin || isGranted) {
        return false
      }
      return true
    }

    return (
      <Drawer
        title='ODPS导入人群'
        placement="right"
        visible={editModalVisible}
        destroyOnClose
        width="800"
        onClose={this.onCancel}
        footer={
          <div
            style={{
              textAlign: 'right',
            }}
          >
            <Button onClick={() => this.onCancel()} style={{ marginRight: 15 }}>
              取消
            </Button>
            <Button type="primary" onClick={this.handleSubmit} disabled={isSubmit()}>
              保存
            </Button>
          </div>
        }
      >
        {this.renderForm()}
      </Drawer>
    );
  }
}
