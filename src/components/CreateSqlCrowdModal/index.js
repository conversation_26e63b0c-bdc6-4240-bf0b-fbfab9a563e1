/**
 * 人群放大弹窗
 * */
import React, { PureComponent } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, Row, Col, Button, Select, Input, DatePicker, Radio, Drawer, Switch, Tooltip } from 'antd';
import dayjs from 'dayjs';
import debounce from 'lodash/debounce';
import GetUser from '@/components/GetUser';
import Editor from '@monaco-editor/react';
import CodeMirror from '@/components/CodeMirror/CodeMirrorInput';
import { queryTemplate } from '@/services/api';
import { get, uniq } from 'lodash'
import { QuestionCircleOutlined } from '@ant-design/icons';
import styles from './index.less';
import { connect } from 'dva';


const { Option } = Select;
const { TextArea } = Input;
const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const FORM_ITEM_LAYOUT = {
  labelCol: { span: 7 },
  wrapperCol: { span: 14 },
};
const noop = () => {};

export class DateInput extends React.Component {

  dateArray = new Array(24).fill({}).map((item, index)=>{
    return {
      id: index,
      value: index+':00:00'
    }
  })

  handleChange = (changedValue)=>{
    const { onChange, value } = this.props;
    if (onChange) {
      onChange({
        ...value,
        updateTime: changedValue,
      });
    }
  }
  render() {
    const { size, value } = this.props;
    return (
      <span>
        <Select
          value={value && value.updatePeriod}
          style={{ width: '60px'}}
        >
          <Option value='DAY'>天</Option>
        </Select>
        <Select
          value={value && value.updateTime}
          style={{ width: '120px' }}
          onChange={this.handleChange}
        >
          {this.dateArray.map(item=><Option key={item.id} value={item.value}>{item.value}</Option>)}
        </Select>
      </span>
    );
  }
}

@Form.create()
class SqlCrowd extends PureComponent {
  constructor(props) {
    super(props);
    this.fetchTemplate();
    this.state = {
      tempData: [],
      VariableList: Object.keys(get(this.props.editFormData, 'extInfo.templateParams', {}))
    };
  }
  EditorRef = React.createRef();
  componentDidMount(){
    if(this.props.editFormData.extInfo && this.props.editFormData.extInfo.templateSql){
      this.extractSqlVariables(this.props.editFormData.extInfo.templateSql)
    }
  }
  fetchTemplate = ()=>{
    queryTemplate().then(res => {
        if (res.success) {          
          this.setState({tempData: [{id: 0, templateName: '自定义模版'}, ...res.data.rows]})
          return;
        }
      })
      .catch(() => {
        message.error('请求模版失败');
      });
  }
  componentWillReceiveProps(nextProps) {
    if (
      (!nextProps.editFormData || !nextProps.editFormData.crowdName) &&
      nextProps.editFormData !== this.props.editFormData
    ) {
      this.props.form.resetFields();
    }
  }
  // 正则提取SQL中变量
  extractSqlVariables = (strData='')=>{  
    let VariableList = strData.match(/\$\{(.*?)\}/g);
    if(Array.isArray(VariableList)){
      VariableList = VariableList.filter(item=>!item.toLocaleLowerCase().includes('date')).map(ele=>ele.slice(2,-1))
    }
    this.setState({ VariableList })
  }

  handleSubmit = () => {
    const { form, onSubmit = noop, user: { currentUser }, editFormData } = this.props;

    form.validateFields((err, values) => {
      if (!err) {
        const { expiredDate, isLoss, sql, template, needUpdate, isAllow } = values;

        const payload = {
          ...values,
          expiredDate: expiredDate.valueOf(),
        };
        payload.needUpdate = needUpdate ? 1 : 0;
        payload.crowdType = 'ODPS_SQL_CROWD';
        payload.account = true;
        payload.crowdTags = [];
        if(!needUpdate){
          payload.scheduleModel = {}
        }

        payload.profileType = editFormData.profileType || 'TAOBAO_USER'

        // 涉及资损
        if (isLoss === 1) {
          payload.crowdTags.push('LOSS_OF_ASSETS');
        }
        let str = sql;
        if(template != '0'){
          // 选择模版
          const matchData = str.match(/\$\{(.*?)\}/g) && str.match(/\$\{(.*?)\}/g).filter(item=>!item.includes('date')) || [];
          this.state.VariableList.forEach((ele,idx)=>{         
            // str = str.replaceAll(matchData[idx], values[ele])
            str = str.split(matchData[idx]).join(values[ele])
          })
          let templateParams = {};
          uniq(this.state.VariableList).forEach(ele=>{
            templateParams[ele] = payload[ele];
            delete payload[ele];
          })
          payload.extInfo = {
            ODPS_SQL: str.replace(';',''),
            templateId: template, // 模版ID
            templateSql: sql, // 模版SQL
            templateParams, // 模版参数
            isAllow
          };
        }else{
          // 自定义模版
          this.state.VariableList.forEach(ele=>{
            delete payload[ele];
          })
          payload.extInfo = {
            ODPS_SQL: str.replace(';',''),
            templateId: template, // 模版ID
            isAllow
          };
        }
        // 提交审核信息
        if (payload.applyApprovalReason && payload.qualityOwner) {
          payload.crowdApprovalInfo = {
            applyApprovalReason: payload.applyApprovalReason,
            applyOwner:{empId: currentUser.workId, nickName: currentUser.name},
            qualityOwner: payload.qualityOwner,
            needSubmit: true
          }
        }

        if (payload.applyApprovalReason) {
          delete payload.applyApprovalReason
        }
        if (payload.qualityOwner) {
          delete payload.qualityOwner
        }
  
        delete payload.isLoss;
        delete payload.template;
        delete payload.sql;

        onSubmit(payload);
      }
    });
  };

  render(){
    const {
      form: { getFieldDecorator, getFieldValue, setFieldsValue },
      editFormData,
      onCancel,
      user: { currentUser, isSuperAdmin } 
    } = this.props;
    const { tempData, VariableList} = this.state;
  
    const disabledDate = current =>
      current < dayjs().startOf('day') || current > dayjs() + 90 * 24 * 60 * 60 * 1000;
  
    const handleSwitch = (checked)=>{
      if(isHasData(getFieldValue('sql'))){
        setFieldsValue({needUpdate: checked})
      }
    }
    const isHasData = (value) => {
      if(!value){
        return false
      }
      if(value.includes('${date}') || value.includes('${date+') || value.includes('${date-') || value.includes('${date +') || value.includes('${date -')){
        return true;
      }
      return false;
    }

    const isSubmit = () => {
      // 管理者和创建者及超级管理员能编辑
      const { creator = {}, operator = [] } = editFormData;
      const owners = [...operator, creator];

      const isGranted = owners.map(o => o.empId).includes(currentUser.workId);
      
      if(!editFormData.id || isSuperAdmin || isGranted) {
        return false
      }
      return true
    }

    return (
      <Form {...FORM_ITEM_LAYOUT} onSubmit={this.handleSubmit}>
        <FormItem label="人群(任务)名称">
          {getFieldDecorator('crowdName', {
            initialValue: editFormData.crowdName,
            rules: [{ required: true, message: '请输入人群名称' }],
          })(<Input placeholder="请输入人群名称" />)}
        </FormItem>
        <FormItem label="人群描述">
          {getFieldDecorator('crowdDescription', {
            initialValue: editFormData.crowdDescription,
            rules: [{ required: true, message: '请输入人群描述' }],
          })(<TextArea placeholder="请输入人群描述" />)}
        </FormItem>
        <FormItem
          label="人群使用场景"
          extra={
            <span style={{ color: 'red' }}>
              注：非PUSH场景请选中人群匹配，否则会导致无法判断人群
            </span>
          }
        >
          {getFieldDecorator('crowdApplyScene', {
            initialValue: editFormData.crowdApplyScene,
            rules: [{ required: true, message: '请选择人群使用场景' }],
          })(
            <Select placeholder="请选择人群使用场景" mode="multiple" showSearch>
              <Option key="PUSH">PUSH</Option>
              <Option key="MATCH">人群匹配</Option>
              {/* <Option key="ANALYSIS">人群分析</Option> */}
            </Select>
          )}
        </FormItem>
        <FormItem label="管理员">
          {getFieldDecorator('operator', {
            initialValue: editFormData.operator,
            rules: [{ required: true, message: '请选择管理员' }],
          })(<GetUser />)}
        </FormItem>
        <FormItem label="涉及资损">
          {getFieldDecorator('isLoss', {
            initialValue:
              editFormData.crowdTags &&
              (editFormData.crowdTags.length > 0 &&
              editFormData.crowdTags.includes('LOSS_OF_ASSETS')
                ? 1
                : 0),
            rules: [{ required: true, message: '请选择是否涉及资损' }],
          })(
            <RadioGroup>
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </RadioGroup>
          )}
        </FormItem>
        <FormItem
          label="人群类型"
        >
          {editFormData.profileType === 'SCRM_USER' ? 'Scrm-Oneid人群包' : '淘宝id'}
        </FormItem>
        {
          getFieldValue('isLoss') === 1 && (<FormItem 
            label="详细描述"
            extra={
              <span style={{ color: 'red' }}>
                注：涉及资损的人群包将通过审批流获取额外测试保障，测试通过将后自动上线
              </span>
            }
          >
            {getFieldDecorator('applyApprovalReason', {
              initialValue: editFormData?.crowdApprovalInfo?.applyApprovalReason,
              rules: [{ required: true, message: '请输入详细描述' }],
            })(<Input.TextArea placeholder="请详细描述下可能发生资损的场景" allowClear />)}
          </FormItem>)
        }

        {
          getFieldValue('isLoss') === 1 && (
            <FormItem label="QA">
              {getFieldDecorator('qualityOwner', {
                initialValue: editFormData?.crowdApprovalInfo?.qualityOwner,
                rules: [{ required: true, message: '请输入业务对应的测试同学' }],
              })(<GetUser mode="default"/>)}
            </FormItem>
          )

        }

        {/* <FormItem
          label="使用账号"
          extra={
            <span style={{ color: 'rgba(0,0,0,0.65)' }}>
              使用系统账号请确保*********************************有表的读权限
            </span>
          }
        >
          {getFieldDecorator('account', {
            initialValue: true,
            rules: [{ required: true }],
          })(<Radio checked>系统账号</Radio>)}
        </FormItem> */}
        <FormItem label="SQL模版">
          {getFieldDecorator('template', {
            initialValue: editFormData.template,
            rules: [{ required: true, message: '请选择SQL模版' }],
          })(
            <Select 
              placeholder="请选择SQL模版" 
              showSearch 
              onChange={(value)=>{ 
                const d = tempData.find(item=>item.id==value).templateSql;
                VariableList.forEach(ele=>{
                  delete editFormData[ele]
                })
                setFieldsValue({sql: d})
                if(value!=='0'){
                  this.extractSqlVariables(d)
                  // 判断是否包含date变量
                  if(isHasData(d)){
                  }else{
                    setFieldsValue({needUpdate: false});
                  }
                }else{
                  setFieldsValue({needUpdate: false});
                }
              }}
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {tempData.map(item=><Option key={item.id} >{item.templateName}</Option>)}
            </Select>
          )}
        </FormItem>
        <FormItem label="SQL语句">
          {getFieldDecorator('sql', {
            initialValue: editFormData.sql,
            rules: [{ required: true, message: '请输入SQL语句' }],
          })(getFieldValue('template')==='0'? 
          // <Editor
          //   defaultValue='select distinct xxx as user_id from ...'
          //   onMount={(editor, monaco)=>{
          //     this.EditorRef.current=editor;
          //     this.EditorRef.current.onDidBlurEditorText(()=>{
          //       if(isHasData(getFieldValue('sql'))){
          //       }else{
          //         setFieldsValue({needUpdate: false})
          //       }
          //     })
          //   }}          
          //   width="100%"
          //   height="70px"
          //   language="sql"
          //   theme="vs-dark"
          // />
          <CodeMirror mode="sql" 
            style={{height:'70px', lineHeight: '20px'}}
            onBlur={()=>{
              if(isHasData(getFieldValue('sql'))){
              }else{
                setFieldsValue({needUpdate: false})
              }
            }
        }/> : <TextArea style={{height: '70px'}} disabled={true}/>)}
        </FormItem>
        {
          getFieldValue('template')!=='0' && getFieldValue('template') !== undefined?
          (<FormItem label='SQL参数'>
            <Row gutter={24}>
            { uniq(VariableList).map(ele=>
              <Col span={12}>
                <FormItem key={ele} label={ele}>
                  {getFieldDecorator(ele, {
                    initialValue: editFormData[ele],
                    rules: [{ required: true, message: `请输入${ele}` }],
                  })(<Input style={{width: '140px'}} placeholder='多个值用逗号隔开' />)}
                </FormItem>
              </Col>)
            }
            </Row>
          </FormItem>
        )
        : null
        }      
        <FormItem label="是否更新">
          {getFieldDecorator('needUpdate', {
            initialValue: !!editFormData.needUpdate,
            valuePropName: 'checked',
          })(<div><Switch checked={getFieldValue('needUpdate')} onChange={handleSwitch} /><span style={{color: 'red'}}>&nbsp;SQL语句中含有日期变量参数，才可以打开"更新"</span></div>)}
        </FormItem>
        {
          !!getFieldValue('needUpdate') && 
          <FormItem label="运行周期">
            {getFieldDecorator('scheduleModel', {
              initialValue: editFormData.scheduleModel && Object.keys(editFormData.scheduleModel).length > 0? editFormData.scheduleModel: {updatePeriod: 'DAY', updateTime: '0:00:00'}
            })(<DateInput />)}
          </FormItem>
        }
        <FormItem label="人群过期时间">
          {getFieldDecorator('expiredDate', {
            initialValue: editFormData.expiredDate ? dayjs(editFormData.expiredDate) : undefined,
            rules: [{ required: true, message: '请选择人群人群过期时间' }],
          })(
            <DatePicker
              disabledDate={disabledDate}
              format="YYYY-MM-DD HH:mm:ss"
              showTime={{
                defaultValue: dayjs('23:59:59', 'HH:mm:ss'),
              }}
            />
          )}
        </FormItem>
        <FormItem label={<span>授权 
          <Tooltip overlayClassName={styles.tip_show} title={
            <div>
              <div>授权示例：</div>
              <div>add user aliyun$<EMAIL>;</div>
              <div>GRANT Describe,Select ON TABLE 表名称 TO USER ALIYUN$<EMAIL>;</div>
              <div>GRANT LABEL 2 ON TABLE 表名称 TO USER ALIYUN$<EMAIL>;</div>
            </div>
          }>
            <QuestionCircleOutlined />
          </Tooltip></span>}
        >
          {getFieldDecorator('isAllow', {
              initialValue: editFormData.isAllow,
              rules: [{ required: true, message: '请选择授权' }],
            })(<Radio.Group>
                <Radio value={true}>已赋权给诸葛系统账号读取所使用表权限</Radio>
            </Radio.Group>)}
        </FormItem>
        <Row gutter={24}>
          <Col span={4} />
          <Col span={14} style={{ textAlign: 'center' }}>
            <Button onClick={() => onCancel()} style={{ marginRight: 15 }}>
              取消
            </Button>
            <Button type="primary" onClick={this.handleSubmit} disabled={isSubmit()}>
              保存
            </Button>
          </Col>
        </Row>
      </Form>
    );
  }
};


@connect(state => ({ user: state.user }))
export default class CreateSqlCrowdModal extends PureComponent {

  onCancel = () => {
    const { onCancel = noop } = this.props;
    onCancel();
  };

  render() {
    const { editModalVisible } = this.props;

    return (
      <Drawer
        visible={editModalVisible}
        footer={null}
        width="800"
        destroyOnClose
        title="自定义SQL圈人"
        onClose={this.onCancel}
        width={800}
      >
        {/* {this.renderForm()} */}
        <SqlCrowd {...this.props} onCancel={this.onCancel} />
      </Drawer>
    );
  }
}
