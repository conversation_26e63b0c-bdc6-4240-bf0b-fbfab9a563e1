import React, { Component, Fragment } from 'react';
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import { Tree, Input } from 'antd';
import { getParentKey } from '@/utils/tree';
import styles from './index.less';

const { TreeNode, DirectoryTree } = Tree;
const { Search } = Input;
const NOOP = () => {};

class SearchTree extends Component {
  constructor(props) {
    super(props);
    this.dataList = [];
    this.state = {
      expandedKeys: [],
      searchValue: '',
      autoExpandParent: true,
    };
  }

  componentDidMount() {
    this.generateList(this.props.dataSource);
  }

  generateList = (data = []) => {
    // eslint-disable-next-line no-plusplus
    for (let i = 0; i < data.length; i++) {
      const node = data[i];
      const { id, name } = node;
      const isLeaf = !node.children || !node.children.length;
      this.dataList.push({ id, name, isLeaf });

      if (!isLeaf) {
        this.generateList(node.children);
      }
    }
  };

  onChange = e => {
    const { dataSource } = this.props;
    const { value } = e.target;
    const expandedKeys = this.dataList
      .map(item => {
        if (item.name.indexOf(value) > -1) {
          return getParentKey(item.id, dataSource);
        }
        return null;
      })
      .filter((item, i, self) => item && self.indexOf(item) === i);

    this.setState({
      expandedKeys,
      searchValue: value,
      autoExpandParent: true,
    });
  };

  onExpand = expandedKeys => {
    this.setState({
      expandedKeys,
      autoExpandParent: false,
    });
  };

  onSelect = selectedKeys => {
    const { onSelect = NOOP } = this.props;
    if (selectedKeys && selectedKeys.length) {
      const key = selectedKeys[0];

      const node = this.dataList.find(item => String(item.id) === key);

      if (node) {
        if (node.isLeaf) {
          onSelect(node);
        }
      }
    }
  };

  onFold = () => {
    const { toggleFold = NOOP } = this.props;
    toggleFold(true);
  };

  onUnFold = () => {
    const { toggleFold = NOOP } = this.props;
    toggleFold(false);
  };

  render() {
    const { dataSource, isFold } = this.props;
    const { searchValue, expandedKeys, autoExpandParent } = this.state;

    const loop = data =>
      data.map(item => {
        const index = item.name.indexOf(searchValue);
        const beforeStr = item.name.substr(0, index);
        const afterStr = item.name.substr(index + searchValue.length);
        const name =
          index > -1 ? (
            <span>
              {beforeStr}
              <span style={{ color: '#f50' }}>{searchValue}</span>
              {afterStr}
            </span>
          ) : (
            <span>{item.name}</span>
          );
        if (item.children && item.children.length > 0) {
          return (
            <TreeNode key={item.id} title={name}>
              {loop(item.children)}
            </TreeNode>
          );
        }
        // 叶子节点
        return <TreeNode key={item.id} title={name} isLeaf />;
      });

    return (
      <div className={styles.container}>
        <div className={styles.treeTool}>
          {isFold ? (
            <MenuUnfoldOutlined className={styles.foldIcon} onClick={this.onUnFold} />
          ) : (
            <MenuFoldOutlined className={styles.foldIcon} onClick={this.onFold} />
          )}
        </div>
        {!isFold && (
          <Fragment>
            <Search
              style={{ marginBottom: 8 }}
              placeholder="输入条件进入搜索"
              onChange={this.onChange}
            />
            <DirectoryTree
              blockNode
              onSelect={this.onSelect}
              onExpand={this.onExpand}
              expandedKeys={expandedKeys}
              autoExpandParent={autoExpandParent}
            >
              {loop(dataSource)}
            </DirectoryTree>
          </Fragment>
        )}
      </div>
    );
  }
}

export default SearchTree;
