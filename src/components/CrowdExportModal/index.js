import React, { useState, Fragment, useEffect } from 'react';
import { Modal, Select, Form, Row, Col, Button, Radio, DatePicker, message, Tag } from 'antd';
import dayjs from 'dayjs';
import debounce from 'lodash/debounce';
import get from 'lodash/get';
import { connect } from 'dva';
import {
  queryLabel,
  queryExportCrowdTask,
  syncCrowdCenter,
  exportApprovalJudge,
} from '@/services/api';
import { EXPORT_TASK_STATUS, EXPORT_SYNC_STATUS } from '@/constants';
import { ExportOutlined } from '@ant-design/icons';

import CrowdEmpowerModal from '@/components/CrowdEmpowerModal';
import CrowdApprovalModal from '@/pages/GatherPerson/GpByTag/components/CrowdApprovalModal';
import { approvalApplySceneEnum } from '@/pages/GatherPerson/constants';
import { getBpmsUrl } from '@/utils/utils';
import styles from './index.less';

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const NOOP = () => {};
const { Option } = Select;

const CrowdExportModal = ({
  editCrowdExportFormData,
  editModalVisible = false,
  onCancel = NOOP,
  onSubmit = NOOP,
  isRealTime,
  isOwner,
  currentUser,
}) => {
  const [form] = Form.useForm();
  const [labels, setLabels] = useState([]);
  const [instance, setInstance] = useState(null);
  const [groupId, setGroupId] = useState(''); // 群组id
  const [syncStatus, setSyncStatus] = useState(''); // 同步状态
  const [empowerVisible, setEmpowerVisible] = useState(false); // 授权页面弹窗
  const [syncBtnLoading, setSyncBtnLoading] = useState(false); // 同步按钮
  const [exportUidType, setExportUidType] = useState('');
  const [isApproval, setIsApproval] = useState(false); // 发布审核
  const crowdName = editCrowdExportFormData.crowdName || '';
  const crowdId = editCrowdExportFormData.id || '';
  const [showLabelSelect, setShowLabelSelect] = useState(false);

  const onFinish = async values => {
    const res = await exportApprovalJudge({ crowdId });
    if (!res.success) {
      message.error(res?.msg);
      return;
    }
    if (res?.data) {
      setIsApproval(true);
      return;
    }
    const data = getFormData(values);
    onSubmit(data);
  };

  const getFormData = values => {
    const { exportEnd, exportLabelOption, ...restValues } = values;
    const data = { 
      ...restValues, 
      exportCrowdId: crowdId, 
      exportEnd: exportEnd && exportEnd.valueOf(),
      exportLabel: exportLabelOption === 'NO' ? [] : values.exportLabel
    };
    if (instance && instance.id !== -1) {
      data.id = instance.id;
    }

    return data;
  };

  const fetchLabels = debounce(params => {
    queryLabel({
      deleted: 0,
      type: 'OFFLINE',
      source: 'ODPS',
      pageNum: 1,
      pageSize: 100000,
      status: 'ACTIVATE',
      bizRegionCode: 'public_region',
      physicalProfileCode: editCrowdExportFormData.physicalProfileCode === 'SCRM_USER' ? 'scrm_up' : 'tbup',
      ...params,
    }).then(res => {
      if (!res.success) {
        message.error(res.msg);
        return;
      }

      setLabels(res.data.rows);
    });
  }, 200);

  const onSearch = value => {
    // fetchLabels({
    //   propertyDescription: value,
    // });
  };

  const fetchCrowdExportTask = id => {
    queryExportCrowdTask({ crowdId: id })
      .then(res => {
        if (!res.success) {
          message.error(res.msg);
          return;
        }

        if (res.data) {
          const { exportEnd, exportLabel, exportUidType } = res.data;
          setExportUidType(exportUidType);
          form.setFieldsValue({
            ...res.data,
            exportLabel: exportLabel || [],
            exportEnd: exportEnd && dayjs(exportEnd),
            exportPeriod: 'DAY',
          });
          setInstance(res.data);
          setGroupId(get(res, 'data.extInfo.syncInfo.crowdId', ''));
          setSyncStatus(get(res, 'data.extInfo.syncInfo.status', ''));
        }
      })
      .catch(err => {
        console.error(err);
      });
  };

  const disabledDate = current =>
    current < dayjs().startOf('day') ||
    current > dayjs() + 90 * 24 * 60 * 60 * 1000 ||
    current > editCrowdExportFormData.expiredDate;

  const validator = ({ getFieldValue }) => ({
    validator(rule, value) {
      const time = getFieldValue('exportEnd');
      const crowdExpiredTime = editCrowdExportFormData.expiredDate;
      if (!value || (time && time.valueOf() < crowdExpiredTime)) {
        return Promise.resolve();
      }
      // eslint-disable-next-line prefer-promise-reject-errors
      return Promise.reject('导出任务过期时间需要小于人群有效期');
    },
  });

  useEffect(() => {
    if (crowdId && editModalVisible) {
      fetchCrowdExportTask(crowdId);
      fetchLabels('');
    }

    return () => {
      form.resetFields();
      setInstance(null);
      setExportUidType('');
      setShowLabelSelect(false);
    };
  }, [crowdId, editModalVisible]);

  useEffect(() => {
    if (instance && instance.exportLabel && instance.exportLabel.length > 0) {
      setShowLabelSelect(true);
      form.setFieldsValue({ exportLabelOption: 'YES' });
    }
  }, [instance]);

  // 打开人群授权弹窗
  const openEmpowerModal = () => {
    if (!isOwner) {
      message.error('人群创建者和管理者才可以进行授权');
      return;
    }
    setEmpowerVisible(true);
  };

  // 同步人群中心
  const onSyncClick = () => {
    if (!isOwner) {
      message.error('人群创建者和管理者才可以进行授权');
      return;
    }
    setSyncBtnLoading(true);
    syncCrowdCenter({ crowdId }).then(res => {
      if (res.success) {
        setGroupId(res.data);
        // 请求成功直接将状态变为同步中
        setSyncStatus('SYNCING');
      } else {
        message.error(res.msg);
        setGroupId('');
        setSyncStatus('');
      }
      setSyncBtnLoading(false);
    });
  };

  // 同步人群按钮置灰
  const isSyncDisabled = instance => {
    if (instance.exportStatus !== 'SUCCESS') {
      return true;
    }
    if (syncStatus === 'SYNCING') {
      return true;
    }
    return false;
  };

  // 关闭人群授权弹窗
  const closeEmpowerModal = () => {
    setEmpowerVisible(false);
  };

  // 切换用户主键
  const onExportUidTypeChange = e => {
    setExportUidType(e.target.value);
  };

  // 审核
  const onApprovalSubmit = async values => {
    const data = getFormData(form.getFieldsValue());
    const payload = {
      ...data,
      approvalInfoVO: {
        approvalCreator: { empId: currentUser?.workId, nickName: currentUser?.name },
        applyScene: approvalApplySceneEnum.CROWD_EXPORT,
        entityType: 'TAOBAO_USER',
        approvalInfo: values,
      },
    };
    const res = await onSubmit(payload);
    return res;
  };

  // 审核标签
  const getApprovalTag = () => {
    const approvalInfo = get(instance, 'extInfo.approvalInfo', undefined);

    if (approvalInfo?.approvalStatus && approvalInfo?.approvalStatus !== 'APPROVAL_SUCCESS') {
      return (
        <Tag
          onClick={() => {
            if (
              [
                'APPROVAL_RUNNING',
                'APPROVAL_REJECTED',
                'APPROVAL_CANCELLED',
                'APPROVAL_ERROR',
              ].includes(approvalInfo?.approvalStatus)
            ) {
              if (approvalInfo?.approvalId) {
                window.open(getBpmsUrl(approvalInfo?.approvalId));
              }
            }
          }}
          style={{ cursor: 'pointer' }}
          className={
            ['APPROVAL_RUNNING', 'APPROVAL_REJECTED']?.includes(approvalInfo?.approvalStatus) &&
            styles.exportTag
          }
          color={
            EXPORT_TASK_STATUS[approvalInfo?.approvalStatus] &&
            EXPORT_TASK_STATUS[approvalInfo?.approvalStatus].status
          }
        >
          {EXPORT_TASK_STATUS[approvalInfo?.approvalStatus] &&
            EXPORT_TASK_STATUS[approvalInfo?.approvalStatus].text}
          {['APPROVAL_RUNNING', 'APPROVAL_REJECTED']?.includes(approvalInfo?.approvalStatus) && (
            <ExportOutlined style={{ marginLeft: 10 }} />
          )}
        </Tag>
      );
    }

    return (
      instance?.exportStatus && (
        <Tag
          color={
            EXPORT_TASK_STATUS[instance.exportStatus] &&
            EXPORT_TASK_STATUS[instance.exportStatus].status
          }
        >
          {EXPORT_TASK_STATUS[instance.exportStatus] &&
            EXPORT_TASK_STATUS[instance.exportStatus].text}
        </Tag>
      )
    );
  };

  const handleExportLabelOptionChange = e => {
    setShowLabelSelect(e.target.value === 'YES');
    if (e.target.value === 'NO') {
      form.setFieldsValue({ exportLabel: [] });
    }
  };

  const title = `人群导出到ODPS：${crowdName}(人群ID：${crowdId})`;
  return (
    <>
      <Modal title={title} width={800} visible={editModalVisible} onCancel={onCancel} footer={null}>
        <Form name="exportForm" {...FORM_ITEM_LAYOUT} form={form} onFinish={onFinish}>
          <FormItem
            label="导出类型"
            name="exportType"
            rules={[{ required: true, message: '请选择导出类型' }]}
            extra={<span style={{ color: 'red' }}>注：周期导出过期时间跟人群过期时间保持一致</span>}
          >
            <RadioGroup >
              <Radio key="SINGLE" value="SINGLE">
                单次导出
              </Radio>
              <Radio key="CYCLE" value="CYCLE">
                周期导出
              </Radio>
              {editCrowdExportFormData.realTime === 2 && (
                <Radio key="SUBSCRIBE" value="SUBSCRIBE">
                  实时导出发push
                </Radio>
              )}
            </RadioGroup>
          </FormItem>
          <FormItem
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.exportType !== currentValues.exportType
            }
          >
            {({ getFieldValue }) =>
              (getFieldValue('exportType') === 'CYCLE' ||
                getFieldValue('exportType') === 'SUBSCRIBE') && (
                <Fragment>
                  {/* <FormItem
                    label="过期时间"
                    name="exportEnd"
                    rules={[{ required: true, message: '请选择过期时间' }, validator]}
                    extra={<span style={{ color: 'red' }}>导出任务过期时间需要小于人群有效期</span>}
                  >
                    <DatePicker
                      disabledDate={disabledDate}
                      format="YYYY-MM-DD HH:mm:ss"
                      showTime={{
                        defaultValue: dayjs('23:59:59', 'HH:mm:ss'),
                      }}
                    />
                  </FormItem> */}
                  {getFieldValue('exportType') === 'SUBSCRIBE' || (
                    <FormItem
                      label="任务周期"
                      name="exportPeriod"
                      initialValue="DAY"
                      extra={<span style={{ color: 'red' }}>注：默认依赖人群更新后再导出</span>}
                      rules={[{ required: true, message: '请选择任务周期' }]}
                    >
                      <RadioGroup value="DAY">
                        <Radio value="DAY">日</Radio>
                      </RadioGroup>
                    </FormItem>
                  )}
                </Fragment>
              )
            }
          </FormItem>
          <FormItem
            name="exportUidType"
            label="用户主键"
            rules={[{ required: true, message: '请选择用户主键' }]}
          >
            <RadioGroup onChange={onExportUidTypeChange}>
              {editCrowdExportFormData.physicalProfileCode === 'TAOBAO_USER' && (
                <Radio value="TAOBAO">淘宝ID</Radio>
              )}
              <Radio disabled value="ALIPAY">
                支付宝ID
              </Radio>
              {editCrowdExportFormData.physicalProfileCode === 'SCRM_USER' && (
                <Radio value="SCRM">Scrm-Oneid</Radio>
              )}
            </RadioGroup>
          </FormItem>

          {isRealTime || (
            <>
              <FormItem
                label="导出标签"
                name="exportLabelOption"
                initialValue="NO"
                extra={<span>非必要不需要导出标签，仅用于查看用户标签值需要</span>}
              >
                <RadioGroup onChange={handleExportLabelOptionChange}>
                  <Radio value="YES">是</Radio>
                  <Radio value="NO">否</Radio>
                </RadioGroup>
              </FormItem>
              
              {showLabelSelect && (
                <FormItem
                  label="标签选择"
                  name="exportLabel"
                >
                  <Select
                    allowClear
                    mode="multiple"
                    placeholder="请选择导出标签"
                    optionFilterProp="label"
                    showArrow={false}
                    showSearch
                    onSearch={onSearch}
                    style={{ width: '60%' }}
                    filterOption={(input, option) =>
                      option.children.toLowerCase().includes(input.toLowerCase())
                    }
                  >
                    {labels &&
                      labels.length > 0 &&
                      labels.map(l => {
                        const { code, name } = l;
                        const label = `${name} ${code}`;
                        return (
                          <Option title={label} key={code} value={code}>
                            {label}
                          </Option>
                        );
                      })}
                  </Select>
                </FormItem>
              )}
            </>
          )}
          <FormItem
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.exportType !== currentValues.exportType ||
              prevValues.exportUidType !== currentValues.exportUidType
            }
          >
            {({ getFieldValue }) => {
              const exportTypeValue = getFieldValue('exportType');
              const exportUidTypeValue = getFieldValue('exportUidType');
              if (exportTypeValue && exportUidTypeValue ) {
                return (
                  <FormItem label=" " colon={false}>
                    <span>
                      <span style={{ marginRight: '10px' }}>提示：导出到表名称为 </span>
                      {getApprovalTag()}
                    </span>
                    <a
                      href={`https://dmc.dw.alibaba-inc.com/dm/table/odps.trip_profile.dwd_fliggy_zhuge_crowd_profile_export_${crowdId}/detail/col`}
                      target="_blank"
                    >{` trip_profile.dwd_fliggy_zhuge_crowd_profile_export_${crowdId}`}</a>
                  </FormItem>
                );
              }
              return null;
            }}
          </FormItem>
          {editCrowdExportFormData.extInfo && editCrowdExportFormData.extInfo.CROWD_DATA_BIZ_DATE && (
            <FormItem label=" " colon={false}>
              <span>导出时间：</span>
              <span>{editCrowdExportFormData.extInfo.CROWD_DATA_BIZ_DATE}</span>
            </FormItem>
          )}
          {exportUidType !== 'SCRM' && instance && (
            <FormItem label=" " colon={false}>
              <Button
                type="primary"
                onClick={openEmpowerModal}
                style={{ marginRight: 15 }}
                disabled={instance.exportStatus !== 'SUCCESS'}
              >
                授权
              </Button>
              <Button
                type="primary"
                onClick={onSyncClick}
                style={{ marginRight: 15 }}
                loading={syncBtnLoading}
                disabled={isSyncDisabled(instance)}
              >
                同步人群中心
              </Button>
              {groupId && <span style={{ marginRight: '15px' }}>群组ID：{groupId}</span>}
              {syncStatus && (
                <Tag
                  color={EXPORT_SYNC_STATUS[syncStatus] && EXPORT_SYNC_STATUS[syncStatus].status}
                >
                  {EXPORT_SYNC_STATUS[syncStatus] && EXPORT_SYNC_STATUS[syncStatus].text}
                </Tag>
              )}
            </FormItem>
          )}

          <Row gutter={24}>
            <Col span={4} />
            <Col span={14} style={{ textAlign: 'center' }}>
              <Button onClick={onCancel} style={{ marginRight: 15 }}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                disabled={['APPROVAL_RUNNING']?.includes(
                  get(instance, 'extInfo.approvalInfo.approvalStatus', undefined)
                )}
              >
                导出
              </Button>
            </Col>
          </Row>
        </Form>
      </Modal>
      <CrowdEmpowerModal
        empowerVisible={empowerVisible}
        closeEmpowerModal={closeEmpowerModal}
        crowdName={crowdName}
        crowdId={crowdId}
      />

      <CrowdApprovalModal
        title="当前人群涉及高敏标签数据，需数据安全审核过后方可执行导出"
        reasonLabel="请填写导出理由"
        visible={isApproval}
        onSubmit={onApprovalSubmit}
        close={() => {
          setIsApproval(false);
        }}
      />
    </>
  );
};

export default connect(({ user }) => ({
  currentUser: user.currentUser,
}))(CrowdExportModal);
