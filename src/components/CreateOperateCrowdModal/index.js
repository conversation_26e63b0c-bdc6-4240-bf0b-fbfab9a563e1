/**
 * 人群逻辑运算
 */
import React, { PureComponent } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Modal,
  Input,
  message,
  Select,
  Radio,
  Switch,
  DatePicker,
  Row,
  Col,
  <PERSON><PERSON>,
  Drawer,
  Card,
  Spin,
} from 'antd';
import debounce from 'lodash/debounce';
import dayjs from 'dayjs';
import { queryCrowd, queryCrowdKeyword } from '@/services/api';
import GetUser from '@/components/GetUser';
import { connect } from 'dva';

const NOOP = () => {};
const FORM_ITEM_LAYOUT = {
  labelCol: { span: 7 },
  wrapperCol: { span: 14 },
};
const FormItem = Form.Item;
const { TextArea } = Input;
const { Option } = Select;
const RadioGroup = Radio.Group;

@Form.create()
@connect(state => ({ user: state.user }))
class CreateOperateCrowdModal extends PureComponent {
  constructor(props) {
    super(props);
    this.fetchCrowd = debounce(this.fetchCrowd, 300);
    this.state = {
      crowdList: [],
    };
  }

  componentWillReceiveProps(nextProps) {
    if (
      (!nextProps.editFormData || !nextProps.editFormData.crowdName) &&
      nextProps.editFormData !== this.props.editFormData
    ) {
      this.props.form.resetFields();
    }
  }

  fetchCrowd = (params = {}) => {
    queryCrowdKeyword({
      pageNum: 1,
      pageSize: 10,
      ...params,
    })
      .then(res => {
        if (!res.success) {
          message.error(res.msg);
          return;
        }

        if (res.data && res.data.rows && res.data.rows.length > 0) {
          this.setState({
            crowdList: res.data.rows,
          });
        }
      })
      .catch(() => {
        message.error('请求人群失败');
      });
  };

  handleCrowdSearch = value => {
    this.fetchCrowd({ keyword: value });
  };

  handleCrowd1Change = value => {
    if (!value) return;
    const { crowdList } = this.state;

    const crowd = crowdList.find(item => item.id === value.key);
    if (crowd) {
      this.setState({
        crowdNum1: crowd && crowd.crowdAmount,
      });
    }
  };

  handleCrowd2Change = value => {
    if (!value) return;
    const { crowdList } = this.state;

    const crowd = crowdList.find(item => item.id === value.key);
    if (crowd) {
      this.setState({
        crowdNum2: crowd && crowd.crowdAmount,
      });
    }
  };

  handleSubmit = () => {
    const {
      form,
      onSubmit = NOOP,
      user: { currentUser },
      editFormData,
    } = this.props;

    form.validateFields((err, values) => {
      if (!err) {
        const payload = values;
        const {
          needUpdate,
          expiredDate,
          isLoss,
          crowdLeft,
          crowdRight,
          operateCrowdType,
        } = payload;

        payload.crowdType = 'OPERATE_CROWD';
        payload.needUpdate = needUpdate ? 1 : 0;
        payload.expiredDate = expiredDate.valueOf();
        payload.crowdTags = [];
        payload.profileType = editFormData.profileType || 'TAOBAO_USER';

        // 涉及资损
        if (isLoss === 1) {
          payload.crowdTags.push('LOSS_OF_ASSETS');
        }

        const prevExtInfo = payload.extInfo;
        payload.extInfo = {
          ...prevExtInfo,
          OPERATE_CROWD_LEFT: crowdLeft && crowdLeft.key,
          OPERATE_CROWD_RIGHT: crowdRight && crowdRight.key,
          OPERATE_CROWD_TYPE: operateCrowdType,
        };
        if (payload.needUpdate) {
          if (!payload.extInfo.UPDATE_MODEL) {
            payload.extInfo.UPDATE_MODEL = {};
          }
          payload.extInfo.UPDATE_MODEL.updateType =
            payload.updateType === 1 ? 'dependence' : 'every_day';
          payload.extInfo.UPDATE_MODEL.dependenceBizOption =
            payload.updateType === 1 ? payload.updateDependenceApp : undefined;
        } else if (payload.extInfo.UPDATE_MODEL) {
          delete payload.extInfo.UPDATE_MODEL.updateType;
          delete payload.extInfo.UPDATE_MODEL.dependenceBizOption;
        }

        // 提交审核信息
        if (payload.applyApprovalReason && payload.qualityOwner) {
          payload.crowdApprovalInfo = {
            applyApprovalReason: payload.applyApprovalReason,
            applyOwner: { empId: currentUser.workId, nickName: currentUser.name },
            qualityOwner: payload.qualityOwner,
            needSubmit: true,
          };
        }
        if (payload.applyApprovalReason) {
          delete payload.applyApprovalReason;
        }
        if (payload.qualityOwner) {
          delete payload.qualityOwner;
        }

        onSubmit(values);
      }
    });
  };

  handleCrowdIsExist = (rule, val, callback) => {
    if (!val) callback();
    const {
      form: { getFieldValue },
    } = this.props;
    const crowdLeft = getFieldValue('crowdLeft') || {};
    if (val.key === crowdLeft.key) {
      callback('逻辑运算人群不能为同一人群');
    }

    callback();
  };

  renderForm = () => {
    const {
      form: { getFieldDecorator, getFieldValue },
      editFormData,
    } = this.props;
    const { crowdList, crowdNum1, crowdNum2 } = this.state;
    const disabledDate = current =>
      current < dayjs().startOf('day') || current > dayjs() + 90 * 24 * 60 * 60 * 1000;

    const topHander = data => {
      const { type, value } = data;
      const formData = this.props.form.getFieldsValue();
      if (formData) {
        if (!formData.extInfo) {
          formData.extInfo = {};
        }
        if (type === 'needUpdate') {
          if (value) {
            if (!formData.extInfo.UPDATE_MODEL) {
              formData.extInfo.UPDATE_MODEL = {};
            }
            formData.extInfo.UPDATE_MODEL.updateType = formData.updateType;
            formData.extInfo.UPDATE_MODEL.dependenceBizOption =
              formData.updateType === 1 ? formData.updateDependenceApp : undefined;
          } else if (formData.extInfo.UPDATE_MODEL) {
            delete formData.extInfo.UPDATE_MODEL.updateType;
            delete formData.extInfo.UPDATE_MODEL.dependenceBizOption;
          }
        }
      }

      if (value && type === 'needUpdate') {
        const {
          form: { setFieldsValue },
        } = this.props;
        Modal.confirm({
          title: '每日更新计算资源消耗较大，请确认是否是必要选项',
          okText: '确认',
          cancelText: '取消',
          onOk() {
            setFieldsValue({ needUpdate: true });
          },
          onCancel() {
            setFieldsValue({ needUpdate: false });
          },
        });
      }

      this.props.form.setFieldsValue({
        extInfo: formData.extInfo,
      });
    };
    return (
      <Form {...FORM_ITEM_LAYOUT} onSubmit={this.handleSubmit}>
        <FormItem label="结果人群名称">
          {getFieldDecorator('crowdName', {
            initialValue: editFormData.crowdName,
            rules: [{ required: true, message: '请输入人群名称' }],
          })(<Input placeholder="请输入人群名称" />)}
        </FormItem>
        <FormItem label="人群描述">
          {getFieldDecorator('crowdDescription', {
            initialValue: editFormData.crowdDescription,
            rules: [{ required: true, message: '请输入人群描述' }],
          })(<TextArea placeholder="请输入人群描述" />)}
        </FormItem>
        <FormItem label="管理员">
          {getFieldDecorator('operator', {
            initialValue: editFormData.operator,
            rules: [{ required: true, message: '请选择管理员' }],
          })(<GetUser />)}
        </FormItem>
        <FormItem label="是否更新">
          {getFieldDecorator('needUpdate', {
            initialValue: !!editFormData.needUpdate,
            valuePropName: 'checked',
          })(<Switch onChange={checked => topHander({ type: 'needUpdate', value: checked })} />)}
        </FormItem>

        <FormItem label="更新类型" hidden={!getFieldValue('needUpdate')}>
          {getFieldDecorator('updateType', {
            initialValue:
              editFormData.extInfo &&
              editFormData.extInfo.UPDATE_MODEL &&
              editFormData.extInfo.UPDATE_MODEL.updateType === 'dependence'
                ? 1
                : 0,
          })(
            <RadioGroup onChange={e => topHander({ type: 'updateType', value: e.target.value })}>
              <Radio value={0}>每天更新</Radio>
              <Radio value={1}>依赖更新</Radio>
            </RadioGroup>
          )}
        </FormItem>

        <FormItem
          label="依赖更新方"
          hidden={!(getFieldValue('needUpdate') && getFieldValue('updateType') === 1)}
        >
          {getFieldDecorator('updateDependenceApp', {
            initialValue: 'huokebao',
          })(
            <Select defaultValue="huokebao">
              <Option value="huokebao">获客宝</Option>
            </Select>
          )}
        </FormItem>

        <FormItem label="涉及资损">
          {getFieldDecorator('isLoss', {
            initialValue:
              editFormData.crowdTags &&
              (editFormData.crowdTags.length > 0 &&
              editFormData.crowdTags.includes('LOSS_OF_ASSETS')
                ? 1
                : 0),
            rules: [{ required: true, message: '请选择是否涉及资损' }],
          })(
            <RadioGroup>
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </RadioGroup>
          )}
        </FormItem>
        <FormItem label="人群类型">
          {editFormData.profileType === 'SCRM_USER' ? 'Scrm-Oneid人群包' : '淘宝id'}
        </FormItem>
        {getFieldValue('isLoss') === 1 && (
          <FormItem
            label="详细描述"
            extra={
              <span style={{ color: 'red' }}>
                注：涉及资损的人群包将通过审批流获取额外测试保障，测试通过将后自动上线
              </span>
            }
          >
            {getFieldDecorator('applyApprovalReason', {
              initialValue: editFormData?.crowdApprovalInfo?.applyApprovalReason,
              rules: [{ required: true, message: '请输入详细描述' }],
            })(<Input.TextArea placeholder="请详细描述下可能发生资损的场景" allowClear />)}
          </FormItem>
        )}

        {getFieldValue('isLoss') === 1 && (
          <FormItem label="QA">
            {getFieldDecorator('qualityOwner', {
              initialValue: editFormData?.crowdApprovalInfo?.qualityOwner,
              rules: [{ required: true, message: '请输入业务对应的测试同学' }],
            })(<GetUser mode="default" />)}
          </FormItem>
        )}

        <FormItem
          label="人群1"
          extra={<span>{`人群数量：${crowdNum1 || editFormData.leftCrowdNum || '暂无'}`}</span>}
        >
          {getFieldDecorator('crowdLeft', {
            initialValue: editFormData.crowdLeft,
            rules: [{ required: true, message: '请选择人群' }],
          })(
            <Select
              labelInValue
              allowClear
              placeholder="请选择人群"
              showSearch
              onSearch={this.handleCrowdSearch}
              defaultActiveFirstOption={false}
              filterOption={false}
              onChange={this.handleCrowd1Change}
              showArrow={false}
            >
              {crowdList &&
                crowdList.length > 0 &&
                crowdList.map(c => (
                  <Option key={c.id} value={c.id}>
                    {c.crowdName}
                  </Option>
                ))}
            </Select>
          )}
        </FormItem>
        <FormItem
          label="人群2"
          extra={<span>{`人群数量：${crowdNum2 || editFormData.rightCrowdNum || '暂无'}`}</span>}
        >
          {getFieldDecorator('crowdRight', {
            initialValue: editFormData.crowdRight,
            rules: [
              { required: true, message: '请选择人群' },
              {
                validator: this.handleCrowdIsExist,
              },
            ],
          })(
            <Select
              labelInValue
              allowClear
              placeholder="请选择人群"
              showSearch
              onSearch={this.handleCrowdSearch}
              defaultActiveFirstOption={false}
              filterOption={false}
              onChange={this.handleCrowd2Change}
              showArrow={false}
            >
              {crowdList &&
                crowdList.length > 0 &&
                crowdList.map(c => (
                  <Option key={c.id} value={c.id}>
                    {c.crowdName}
                  </Option>
                ))}
            </Select>
          )}
        </FormItem>
        <FormItem label="运算规则">
          {getFieldDecorator('operateCrowdType', {
            initialValue: editFormData.operateCrowdType,
            rules: [{ required: true, message: '请选择运算规则' }],
          })(
            <RadioGroup>
              <Radio value="AND">交集</Radio>
              <Radio value="OR">并集</Radio>
              <Radio value="NOTIN">差集</Radio>
            </RadioGroup>
          )}
        </FormItem>
        <FormItem
          label="人群使用场景"
          extra={
            <span style={{ color: 'red' }}>
              注：非PUSH场景请选中人群匹配，否则会导致无法判断人群
            </span>
          }
        >
          {getFieldDecorator('crowdApplyScene', {
            initialValue: editFormData.crowdApplyScene,
            rules: [{ required: true, message: '请选择人群使用场景' }],
          })(
            <Select placeholder="请选择人群使用场景" mode="multiple" showSearch>
              <Option key="PUSH">PUSH</Option>
              <Option key="MATCH">人群匹配</Option>
              {/* <Option key="ANALYSIS">人群分析</Option> */}
            </Select>
          )}
        </FormItem>
        <FormItem label="人群过期时间">
          {getFieldDecorator('expiredDate', {
            initialValue: editFormData.expiredDate ? dayjs(editFormData.expiredDate) : undefined,
            rules: [{ required: true, message: '请选择人群人群过期时间' }],
          })(
            <DatePicker
              disabledDate={disabledDate}
              format="YYYY-MM-DD HH:mm:ss"
              showTime={{
                defaultValue: dayjs('23:59:59', 'HH:mm:ss'),
              }}
            />
          )}
        </FormItem>
        <FormItem hidden>
          {getFieldDecorator('extInfo', {
            initialValue: editFormData.extInfo,
          })(<Input />)}
        </FormItem>
      </Form>
    );
  };

  onCancel = () => {
    const { onCancel = NOOP } = this.props;
    this.setState({
      crowdNum1: 0,
      crowdNum2: 0,
    });
    onCancel();
  };

  render() {
    const {
      editModalVisible = false,
      editFormData,
      user: { currentUser, isSuperAdmin },
      detailLoading = false,
    } = this.props;

    const isSubmit = () => {
      // 管理者和创建者及超级管理员能编辑
      const { creator = {}, operator = [] } = editFormData;
      const owners = [...operator, creator];

      const isGranted = owners.map(o => o.empId).includes(currentUser.workId);

      if (!editFormData.id || isSuperAdmin || isGranted) {
        return false;
      }
      return true;
    };

    return (
      <Drawer
        title="人群逻辑运算"
        placement="right"
        visible={editModalVisible}
        destroyOnClose
        width="800"
        onClose={this.onCancel}
        footer={
          <div
            style={{
              textAlign: 'right',
            }}
          >
            <Button onClick={() => this.onCancel()} style={{ marginRight: 15 }}>
              取消
            </Button>
            <Button type="primary" onClick={this.handleSubmit} disabled={isSubmit()}>
              保存
            </Button>
          </div>
        }
      >
        <Spin spinning={detailLoading}>{this.renderForm()}</Spin>
      </Drawer>
    );
  }
}

export default CreateOperateCrowdModal;
