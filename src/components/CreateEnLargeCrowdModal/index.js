/**
 * 人群放大弹窗
 * */
import React, { PureComponent } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Modal,
  Row,
  Col,
  Button,
  Select,
  Input,
  DatePicker,
  Switch,
  InputNumber,
  Radio,
  message,
  Drawer,
  Tooltip
} from 'antd';
import dayjs from 'dayjs';
import { connect } from 'dva';
import isArray from 'lodash/isArray';
import debounce from 'lodash/debounce';
import { createFormItems } from '@/utils/createFormItem';
import GetUser from '@/components/GetUser';
import { queryCrowd } from '@/services/api';
import { QuestionCircleOutlined } from '@ant-design/icons';

const { Option } = Select;
const { TextArea } = Input;
const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const FORM_ITEM_LAYOUT = {
  labelCol: { span: 7 },
  wrapperCol: { span: 14 },
};
const NOOP = () => { };
const isDayjs = (obj) => obj instanceof dayjs;

@Form.create()
@connect(state => ({ user: state.user }))
export default class CreateEnLargeCrowdModal extends PureComponent {
  constructor(props) {
    super(props);
    this.fetchCrowd = debounce(this.fetchCrowd, 300);
    this.state = {
      curAlgoModel: null,
      crowdList: [],
    };
  }

  componentWillReceiveProps(nextProps) {
    if (
      (!nextProps.editFormData || !nextProps.editFormData.crowdName) &&
      nextProps.editFormData !== this.props.editFormData
    ) {
      this.props.form.resetFields();
    }
    if (
      nextProps.editFormData &&
      nextProps.editFormData.modelName &&
      nextProps.editFormData.modelName !== this.props.editFormData.modelName
    ) {
      const model = this.props.algoModels.find(
        m => String(m.modelName) === String(nextProps.editFormData.modelName)
      );
      this.setState({
        curAlgoModel: model,
      });
    }
  }

  onAlgoTypeChange = value => {
    const { algoModels } = this.props;
    const model = algoModels.find(m => String(m.modelName) === String(value));
    this.setState({
      curAlgoModel: model,
    });
  };

  fetchCrowd = (params = {}) => {
    queryCrowd({
      pageNo: 1,
      pageSize: 1000,
      type: 'all',
      deleted: 0,
      ...params,
    })
      .then(res => {
        if (!res.success) {
          message.error(res.msg);
          return;
        }

        if (res.data && res.data.rows && res.data.rows.length > 0) {
          this.setState({
            crowdList: res.data.rows,
          });
        }
      })
      .catch(() => {
        message.error('请求人群失败');
      });
  };

  handleCrowdSearch = value => {
    this.fetchCrowd({ crowdName: value });
  };

  onCancel = () => {
    const { onCancel = NOOP } = this.props;
    onCancel();
    this.setState({
      curAlgoModel: null,
    });
  };

  handleSubmit = () => {
    const { form, onSubmit = NOOP, user: { currentUser }} = this.props;
    const { curAlgoModel } = this.state;

    form.validateFields((err, values) => {
      if (!err) {
        const payload = values;
        const { needUpdate, expiredDate, isLoss } = payload;

        payload.crowdType = 'ALGO_ENLARGE_CROWD';
        payload.needUpdate = needUpdate ? 1 : 0;
        payload.expiredDate = expiredDate.valueOf();
        payload.crowdTags = [];

        // 涉及资损
        if (isLoss === 1) {
          payload.crowdTags.push('LOSS_OF_ASSETS');
        }
        // 预处理表单数据
        Object.keys(payload).forEach(k => {
          const t = payload[k];

          if (isDayjs(t)) {
            payload[k] = t.format('YYYY-MM-DD');
          }
          // 判断是否为datepicker的表单值 是将其转为需要的格式
          if (isArray(t) && t.length === 2 && isDayjs(t[0]) && isDayjs(t[1])) {
            payload[k] = t.map(m => m.format('YYYY-MM-DD'));
          }

          // filelist类型 geng
          if (isArray(t) && t.length && (t[0].url || t[0].response)) {
            // eslint-disable-next-line prefer-destructuring

            // 更新的时候
            const file = t[t.length - 1];
            payload[k] = file.response ? file.response.url : file.url;
          }
        });

        const inputExtInfo = [];
        const outputExtInfo = [];
        const { inputArgs, outputArgs } = curAlgoModel || {};

        // eslint-disable-next-line no-unused-expressions
        inputArgs &&
          inputArgs.length > 0 &&
          inputArgs.forEach(i => {
            inputExtInfo.push({ key: i.argName, value: values[i.argName] });
            delete payload[i.argName];
          });

        // eslint-disable-next-line no-unused-expressions
        outputArgs &&
          outputArgs.length > 0 &&
          outputArgs.forEach(o => {
            outputExtInfo.push({
              key: o.argName,
              value: values[o.argName],
            });
            delete payload[o.argName];
          });

          const prevExtInfo = payload.extInfo;
        payload.extInfo = {
          ...prevExtInfo,
          FILTER_ORIGIN_CROWD: payload.filterOriginCrowd, //是否包含种子人群传参
          //放大类型不需要了
          // ALGO_MODEL_META_INFO_ID: curAlgoModel.id, // 算法模型
          RESULT_CROWD_AMOUNT_LIMIT: payload.resultAmount, // 人群限制
          ALGO_ENLARGE_SEED_CROWD_ID: payload.seedCrowdId && parseInt(payload.seedCrowdId.key, 10), // 种子人群ID
          ALGO_ENLARGE_EXCEPT_CROWD_ID:
            payload.exceptCrowdId && parseInt(payload.exceptCrowdId.key, 10), // 【非必须】过滤人群
          inputExtInfo,
          outputExtInfo,
        };
        if (payload.needUpdate) {
          if (!payload.extInfo.UPDATE_MODEL) {
            payload.extInfo.UPDATE_MODEL = {};
          }
          payload.extInfo.UPDATE_MODEL.updateType = payload.updateType === 1 ? 'dependence' : 'every_day';
          payload.extInfo.UPDATE_MODEL.dependenceBizOption = payload.updateType === 1 ? payload.updateDependenceApp : undefined;
        } else if (payload.extInfo.UPDATE_MODEL) {
          delete payload.extInfo.UPDATE_MODEL.updateType
          delete payload.extInfo.UPDATE_MODEL.dependenceBizOption
        }

        // 提交审核信息
        if (payload.applyApprovalReason && payload.qualityOwner) {
          payload.crowdApprovalInfo = {
            applyApprovalReason: payload.applyApprovalReason,
            applyOwner:{empId: currentUser.workId, nickName: currentUser.name},
            qualityOwner: payload.qualityOwner,
            needSubmit: true
          }
        }
        
        if (payload.applyApprovalReason) {
          delete payload.applyApprovalReason
        }
        if (payload.qualityOwner) {
          delete payload.qualityOwner
        }

        delete payload.resultAmount;
        delete payload.modelName;
        delete payload.filterOriginCrowd
        onSubmit(payload);
      }
    });
  };

  handleSeedCrowdChange = value => {
    if (!value) return;
    const { crowdList } = this.state;

    const crowd = crowdList.find(item => item.id === value.key);
    if (crowd) {
      this.setState({
        limitCrowdLimitNum: crowd && crowd.crowdAmount,
      });
    }
  };

  handleEnumValueCodeIsExist = (rule, val, callback) => {
    const { limitCrowdLimitNum } = this.state;
    const { editFormData } = this.props;
    const num = limitCrowdLimitNum || editFormData.limitCrowdLimitNum;

    if (!num) {
      callback('请选择种子人群或该种子人群无人群数量');
    }

    if (num >= val) {
      callback('放大人群数量需大于种子人群数量');
    }

    callback();
  };

  renderForm = () => {
    const {
      form: { getFieldDecorator, getFieldValue },
      algoModels,
      editFormData,
    } = this.props;

    const { curAlgoModel, crowdList, limitCrowdLimitNum } = this.state;

    const num = limitCrowdLimitNum || editFormData.limitCrowdLimitNum;

    const disabledDate = current =>
      current < dayjs().startOf('day') || current > dayjs() + 90 * 24 * 60 * 60 * 1000;


    const topHander = data => {
      const {
        type,
        value,
      } = data;
      const formData = this.props.form.getFieldsValue();
      if (formData) {
        if (!formData.extInfo) {
          formData.extInfo = {};
        }
        if (type === 'needUpdate') {
          if (value) {
            if (!formData.extInfo.UPDATE_MODEL) {
              formData.extInfo.UPDATE_MODEL = {};
            }
            formData.extInfo.UPDATE_MODEL.updateType = formData.updateType;
            formData.extInfo.UPDATE_MODEL.dependenceBizOption = formData.updateType === 1 ? formData.updateDependenceApp : undefined;
          } else if (formData.extInfo.UPDATE_MODEL) {
            delete formData.extInfo.UPDATE_MODEL.updateType
            delete formData.extInfo.UPDATE_MODEL.dependenceBizOption
          }
        }
      }

      if (value && type === 'needUpdate') {
        const {
          form: { setFieldsValue },
        } = this.props;
        Modal.confirm({
          title: '每日更新计算资源消耗较大，请确认是否是必要选项',
          okText:"确认",
          cancelText:"取消",
          onOk() {
            setFieldsValue({needUpdate: true})
          },
          onCancel() {
            setFieldsValue({needUpdate: false})
          },
        });
      }

      this.props.form.setFieldsValue({
        extInfo: formData.extInfo,
      });
    }
    return (
      <Form {...FORM_ITEM_LAYOUT} onSubmit={this.handleSubmit}>
        <FormItem label="人群(任务)名称">
          {getFieldDecorator('crowdName', {
            initialValue: editFormData.crowdName,
            rules: [{ required: true, message: '请输入人群名称' }],
          })(<Input placeholder="请输入人群名称" />)}
        </FormItem>
        <FormItem label="人群描述">
          {getFieldDecorator('crowdDescription', {
            initialValue: editFormData.crowdDescription,
            rules: [{ required: true, message: '请输入人群描述' }],
          })(<TextArea placeholder="请输入人群描述" />)}
        </FormItem>
        <FormItem label="管理员">
          {getFieldDecorator('operator', {
            initialValue: editFormData.operator,
            rules: [{ required: true, message: '请选择管理员' }],
          })(<GetUser />)}
        </FormItem>
        <FormItem label="种子人群" extra={<span>{`人群数量：${num || '暂无'}`}</span>}>
          {getFieldDecorator('seedCrowdId', {
            initialValue: editFormData.seedCrowdId,
            rules: [{ required: true, message: '请选择种子人群' }],
          })(
            <Select
              labelInValue
              allowClear
              placeholder="请选择种子人群"
              showSearch
              onSearch={this.handleCrowdSearch}
              defaultActiveFirstOption={false}
              filterOption={false}
              onChange={this.handleSeedCrowdChange}
              showArrow={false}
            >
              {crowdList &&
                crowdList.length > 0 &&
                crowdList.map(c => (
                  <Option key={c.id} value={c.id}>
                    {c.crowdName}
                  </Option>
                ))}
            </Select>
          )}
        </FormItem>
        <FormItem label="过滤人群">
          {getFieldDecorator('exceptCrowdId', {
            initialValue: editFormData.exceptCrowdId,
          })(
            <Select
              labelInValue
              allowClear
              placeholder="请选择过滤人群"
              showSearch
              onSearch={this.handleCrowdSearch}
              defaultActiveFirstOption={false}
              filterOption={false}
              showArrow={false}
            >
              {crowdList &&
                crowdList.length > 0 &&
                crowdList.map(c => (
                  <Option key={c.id} value={c.id}>
                    {c.crowdName}
                  </Option>
                ))}
            </Select>
          )}
        </FormItem>
        <FormItem label={
            <span>
              是否包含种子人群&nbsp;
              <Tooltip title="数据统计超过6倍会不准">
                <QuestionCircleOutlined />
              </Tooltip>
            </span>
          }
 >
          {getFieldDecorator('filterOriginCrowd', {
            initialValue: editFormData.filterOriginCrowd,
            rules: [{ required: true, message: '请选择是否包含种子人群' }],
          })(
          <RadioGroup>
            <Radio value={true}>是</Radio>
            <Radio value={false}>否</Radio>
          </RadioGroup>)}
        </FormItem>
        <FormItem 
          label="放大人数"
          extra={
            <span style={{ color: 'red' }}>
              参考：14亿用户平均每人有6.7个关联人
            </span>
          }
        >
          {getFieldDecorator('resultAmount', {
            initialValue: editFormData.resultAmount,
            rules: [
              { required: true, message: '请输入返回数据量' },
              {
                validator: this.handleEnumValueCodeIsExist,
              },
            ],
          })(<InputNumber min={0} placeholder="请输入返回数据量" style={{ width: 150 }} />)}
        </FormItem>
        <FormItem label="是否更新">
          {getFieldDecorator('needUpdate', {
            initialValue: !!editFormData.needUpdate,
            valuePropName: 'checked',
          })(<Switch onChange={checked => topHander({ type: 'needUpdate', value: checked })} />)}
        </FormItem>

        <FormItem label="更新类型" hidden={!getFieldValue('needUpdate')}>
          {
            getFieldDecorator('updateType', {
              initialValue: (
                editFormData.extInfo
                && editFormData.extInfo.UPDATE_MODEL
                && editFormData.extInfo.UPDATE_MODEL.updateType === 'dependence' ? 1 : 0
              )
            })(
              <RadioGroup onChange={e => topHander({ type: 'updateType', value: e.target.value })}>
                <Radio value={0}>每周更新</Radio>
                <Radio value={1}>依赖更新</Radio>
              </RadioGroup>
            )
          }
        </FormItem>

        <FormItem
          label="依赖更新方"
          hidden={!(
            getFieldValue('needUpdate')
            && (
              getFieldValue('updateType') === 1
            ))}>
          {getFieldDecorator('updateDependenceApp', {
            initialValue: 'huokebao'
          })(
            <Select defaultValue="huokebao">
              <Option value="huokebao">获客宝</Option>
            </Select>
          )
        }
        </FormItem>

        <FormItem label="涉及资损">
          {getFieldDecorator('isLoss', {
            initialValue:
              editFormData.crowdTags &&
              (editFormData.crowdTags.length > 0 &&
              editFormData.crowdTags.includes('LOSS_OF_ASSETS')
                ? 1
                : 0),
            rules: [{ required: true, message: '请选择是否涉及资损' }],
          })(
            <RadioGroup>
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </RadioGroup>
          )}
        </FormItem>

        {
          getFieldValue('isLoss') === 1 && (<FormItem 
            label="详细描述"
            extra={
              <span style={{ color: 'red' }}>
                注：涉及资损的人群包将通过审批流获取额外测试保障，测试通过将后自动上线
              </span>
            }
          >
            {getFieldDecorator('applyApprovalReason', {
              initialValue: editFormData?.crowdApprovalInfo?.applyApprovalReason,
              rules: [{ required: true, message: '请输入详细描述' }],
            })(<Input.TextArea placeholder="请详细描述下可能发生资损的场景" allowClear />)}
          </FormItem>)
        }

        {
          getFieldValue('isLoss') === 1 && (
            <FormItem label="QA">
              {getFieldDecorator('qualityOwner', {
                initialValue: editFormData?.crowdApprovalInfo?.qualityOwner,
                rules: [{ required: true, message: '请输入业务对应的测试同学' }],
              })(<GetUser mode="default"/>)}
            </FormItem>
          )

        }

        <FormItem
          label="人群使用场景"
          extra={
            <span style={{ color: 'red' }}>
              注：非PUSH场景请选中人群匹配，否则会导致无法判断人群
            </span>
          }
        >
          {getFieldDecorator('crowdApplyScene', {
            initialValue: editFormData.crowdApplyScene,
            rules: [{ required: true, message: '请选择人群使用场景' }],
          })(
            <Select placeholder="请选择人群使用场景" mode="multiple" showSearch>
              <Option key="PUSH">PUSH</Option>
              <Option key="MATCH">人群匹配</Option>
              {/* <Option key="ANALYSIS">人群分析</Option> */}
            </Select>
          )}
        </FormItem>
        {/* <FormItem label="放大类型">
          {getFieldDecorator('modelName', {
            initialValue: editFormData.modelName,
            rules: [{ required: true, message: '请选择算法类型' }],
          })(
            <Select
              filterOption={false}
              labelInValue={false}
              placeholder="请选择算法类型"
              onChange={this.onAlgoTypeChange}
            >
              {algoModels &&
                algoModels.length > 0 &&
                algoModels
                  .filter(item => item.businessType === 'ENLARGE_GROUP')
                  .map(m => <Option key={m.modelName}>{m.modelName}</Option>)}
            </Select>
          )}
        </FormItem> */}
        <FormItem label="人群过期时间">
          {getFieldDecorator('expiredDate', {
            initialValue: editFormData.expiredDate ? dayjs(editFormData.expiredDate) : undefined,
            rules: [{ required: true, message: '请选择人群人群过期时间' }],
          })(
            <DatePicker
              disabledDate={disabledDate}
              format="YYYY-MM-DD HH:mm:ss"
              showTime={{
                defaultValue: dayjs('23:59:59', 'HH:mm:ss'),
              }}
            />
          )}
        </FormItem>

        <FormItem hidden>
          {getFieldDecorator('extInfo', {
            initialValue: editFormData.extInfo
          })(<Input />)}
        </FormItem>

        {/* {createFormItems(curAlgoModel, getFieldDecorator, editFormData)} */}
      </Form>
    );
  };

  render() {
    const { 
      editModalVisible,
      editFormData,
      user: { currentUser, isSuperAdmin } 
    } = this.props;

    const isSubmit = () => {
      // 管理者和创建者及超级管理员能编辑
      const { creator = {}, operator = [] } = editFormData;
      const owners = [...operator, creator];

      const isGranted = owners.map(o => o.empId).includes(currentUser.workId);
      
      if(!editFormData.id || isSuperAdmin || isGranted) {
        return false
      }
      
      return true
    }

    return (
      <Drawer
        title='人群放大圈人'
        placement="right"
        visible={editModalVisible}
        destroyOnClose
        width="800"
        onClose={this.onCancel}
        footer={
          <div
            style={{
              textAlign: 'right',
            }}
          >
            <Button onClick={() => this.onCancel()} style={{ marginRight: 15 }}>
              取消
            </Button>
            <Button type="primary" onClick={this.handleSubmit} disabled={isSubmit()}>
              保存
            </Button>
          </div>
        }
      >
        {this.renderForm()}
      </Drawer>
    );
  }
}
