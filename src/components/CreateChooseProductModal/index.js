/**
 * 算法选品任务弹窗
 * */
import React, { PureComponent } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, Row, Col, Button, Select, Input, InputNumber } from 'antd';
import isArray from 'lodash/isArray';
import dayjs from 'dayjs';
import GetUser from '@/components/GetUser';
import { createFormItems } from '@/utils/createFormItem';
import styles from './index.less';

const { Option } = Select;
const FORM_ITEM_LAYOUT = {
  labelCol: { span: 9 },
  wrapperCol: { span: 12 },
};
const noop = () => {};
const FormItem = Form.Item;

const isDayjs = (obj) => obj instanceof dayjs;

@Form.create()
class CreateChooseModal extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      curAlgoModel: null,
    };
  }

  componentWillReceiveProps(nextProps) {
    if (
      (!nextProps.editFormData || !nextProps.editFormData.name) &&
      nextProps.editFormData !== this.props.editFormData
    ) {
      this.props.form.resetFields();
    }

    if (
      nextProps.editFormData &&
      nextProps.editFormData.modelName &&
      nextProps.editFormData.modelName !== this.props.editFormData.modelName
    ) {
      const model = this.props.algoModels.find(
        m => String(m.modelName) === String(nextProps.editFormData.modelName)
      );
      this.setState({
        curAlgoModel: model,
      });
    }
  }

  handleSubmit = () => {
    const { form, onSubmit = noop } = this.props;
    const { curAlgoModel } = this.state;

    form.validateFields((err, values) => {
      if (!err) {
        const payload = values;
        // 预处理表单数据
        Object.keys(payload).forEach(k => {
          const t = payload[k];

          if (isDayjs(t)) {
            payload[k] = t.format('YYYY-MM-DD');
          }
          // 判断是否为datepicker的表单值 是将其转为需要的格式
          if (isArray(t) && t.length === 2 && isDayjs(t[0]) && isDayjs(t[1])) {
            payload[k] = t.map(m => m.format('YYYY-MM-DD'));
          }


          // filelist类型 geng
          if (isArray(t) && t.length && (t[0].url || t[0].response)) {
            // eslint-disable-next-line prefer-destructuring

            // 更新的时候
            const file = t[t.length - 1];
            payload[k] = file.response ? file.response.url : file.url;
          }
        });

        payload.inputExtInfo = [];
        payload.outputExtInfo = [];
        const { inputArgs, outputArgs } = curAlgoModel || {};

        // eslint-disable-next-line no-unused-expressions
        inputArgs &&
          inputArgs.length > 0 &&
          inputArgs.forEach(i => {
            payload.inputExtInfo.push({ key: i.argName, value: values[i.argName] });
            delete payload[i.argName];
          });

        // eslint-disable-next-line no-unused-expressions
        outputArgs &&
          outputArgs.length > 0 &&
          outputArgs.forEach(o => {
            payload.outputExtInfo.push({
              key: o.argName,
              value: values[o.argName],
            });
            delete payload[o.argName];
          });

        onSubmit({
          ...payload,
          approval_status: 'INIT',
        });
      }
    });
  };

  onAlgoTypeChange = value => {
    const { algoModels } = this.props;
    const model = algoModels.find(m => String(m.modelName) === String(value));
    this.setState({
      curAlgoModel: model,
    });
  };

  renderForm = () => {
    const {
      form: { getFieldDecorator },
      algoModels,
      editFormData,
      isEdit,
    } = this.props;
    const { curAlgoModel } = this.state;

    return (
      <Form
        {...FORM_ITEM_LAYOUT}
        className={!isEdit ? styles.formReadonly : ''}
        onSubmit={this.handleSubmit}
      >
        <FormItem label="任务名称">
          {getFieldDecorator('name', {
            initialValue: editFormData.name,
            rules: [{ required: true, message: '请输入任务类型' }],
          })(<Input placeholder="请输入任务名称" />)}
        </FormItem>
        <FormItem label="算法类型">
          {getFieldDecorator('modelName', {
            initialValue: editFormData.modelName,
            rules: [{ required: true, message: '请选择算法类型' }],
          })(
            <Select
              filterOption={false}
              labelInValue={false}
              placeholder="请选择算法类型"
              onChange={this.onAlgoTypeChange}
            >
              {algoModels &&
                algoModels.length > 0 &&
                algoModels
                  .filter(item => item.businessType === 'ITEM_GROUP')
                  .map(m => <Option key={m.modelName}>{m.modelName}</Option>)}
            </Select>
          )}
        </FormItem>
        <FormItem label="管理员">
          {getFieldDecorator('operator', {
            initialValue: editFormData.operator,
            rules: [{ required: true, message: '请选择管理员' }],
          })(<GetUser />)}
        </FormItem>
        <FormItem label="限制商品数量">
          {getFieldDecorator('resultAmount', {
            initialValue: editFormData.resultAmount || 10000,
            rules: [{ required: true, message: '请输入返回数据量' }],
          })(<InputNumber min={0} placeholder="默认10000" style={{ width: 150 }} />)}
        </FormItem>
        {createFormItems(curAlgoModel, getFieldDecorator, editFormData)}
        {isEdit ? (
          <Row gutter={24}>
            <Col span={4} />
            <Col span={14} style={{ textAlign: 'center' }}>
              <Button onClick={() => this.onCancel()} style={{ marginRight: 15 }}>
                取消
              </Button>
              <Button type="primary" onClick={this.handleSubmit}>
                保存
              </Button>
            </Col>
          </Row>
        ) : null}
      </Form>
    );
  };

  onCancel = () => {
    const { onCancel = noop } = this.props;
    onCancel();
    this.setState({
      curAlgoModel: null,
    });
  };

  render() {
    const { editModalVisible } = this.props;

    return (
      <Modal
        visible={editModalVisible}
        footer={null}
        title="新增选品任务"
        onCancel={this.onCancel}
        width={750}
      >
        {this.renderForm()}
      </Modal>
    );
  }
}

export default CreateChooseModal;
