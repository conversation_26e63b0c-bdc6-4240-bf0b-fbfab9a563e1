import React from 'react';
import { Cascader } from 'antd';

class MyCascader extends React.Component {
  onChange = value => {
    this.triggerChange(value);
  };

  triggerChange = changedValue => {
    // Should provide an event to pass value to Form.
    const { onChange } = this.props;
    if (onChange) {
      onChange(changedValue);
    }
  };

  render() {
    const { value, treeData, changeOnSelect, style, ...restProps } = this.props;
    return (
      <Cascader
        placeholder="请选择标签分类"
        style={{ ...style }}
        fieldNames={{ label: 'name', value: 'id', children: 'children' }}
        value={value}
        options={treeData}
        onChange={this.onChange}
        changeOnSelect={changeOnSelect}
        {...restProps}
      />
    );
  }
}

export default MyCascader;
