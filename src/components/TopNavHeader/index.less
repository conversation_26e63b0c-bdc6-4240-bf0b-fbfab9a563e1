
.head {
  position: relative;
  width: 100%;
  height: 64px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  transition: background 0.3s, width 0.2s;
  :global {
    .ant-menu-submenu.ant-menu-submenu-horizontal {
      height: 100%;
      line-height: 64px;
      .ant-menu-submenu-title {
        height: 100%;
      }
    }
  }
  &.light {
    background-color: #fff;
  }
  .main {
    display: flex;
    height: 64px;
    padding-left: 24px;
    &.wide {
      max-width: 1200px;
      margin: auto;
      padding-left: 0;
    }
    .left {
      display: flex;
      flex: 1;
    }
    .right {
      width: 324px;
    }
  }
}

.logo {
  position: relative;
  width: 165px;
  height: 64px;
  overflow: hidden;
  line-height: 64px;
  transition: all 0.3s;
  img {
    display: inline-block;
    height: 32px;
    vertical-align: middle;
  }
  h1 {
    display: inline-block;
    margin: 0 0 0 12px;
    font-weight: 700;
    color: #1b98dc;
    font-size: 16px;
    vertical-align: top;
  }
}

.light {
  h1 {
    color: #002140;
  }
}

.menu {
  height: 64px;
  line-height: 64px;
  border: none;
  :global{
    .anticon {
      margin-right: 0 !important;
    }
    .ant-menu-submenu-selected {
      background-color: transparent !important;
    }
  }
}
