import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, Input, Row, Col, Button } from 'antd';
import MyCascader from '@/components/Cascader';

const NOOP = () => {};
const FormItem = Form.Item;

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

@Form.create({
  onValuesChange: (props, values) => props.onValuesChange && props.onValuesChange(values),
})
class EditModal extends React.PureComponent {
  componentWillReceiveProps(nextProps) {
    if (nextProps.editFormData !== this.props.editFormData) {
      this.props.form.resetFields();
    }
  }

  walk = data => [
    {
      id: 0,
      name: '根目录',
      children: data,
    },
  ];

  onSubmit = () => {
    const {
      form: { validateFields },
      onSubmit = NOOP,
      editFormData,
    } = this.props;

    validateFields((err, values) => {
      if (!err) {
        const payload = values;

        if (editFormData.id) {
          payload.id = editFormData.id;
        }

        onSubmit(payload);
      }
    });
  };

  render() {
    const {
      modalVisible,
      // curPath = '/'
      treeData = [],
      onCancel = () => {},
      form: { getFieldDecorator },
      editFormData,
      onPathChange,
    } = this.props;

    return (
      <Modal visible={modalVisible} onCancel={onCancel} width={700} footer={null}>
        <Form {...FORM_ITEM_LAYOUT}>
          <FormItem label="层级">
            {getFieldDecorator('path', {
              initialValue: editFormData.curPath,
              rules: [{ required: true, message: '请选择目录' }],
            })(
              <MyCascader treeData={this.walk(treeData)} onChange={onPathChange} changeOnSelect />
            )}
          </FormItem>
          <FormItem label="显示名">
            {getFieldDecorator('enumDesc', {
              initialValue: editFormData.enumDesc,
              rules: [{ required: true, message: '请输入节点信息' }],
            })(<Input placeholder="请输入节点信息" allowClear />)}
          </FormItem>
          <FormItem label="实际值">
            {getFieldDecorator('enumCode', {
              initialValue: editFormData.enumCode,
              rules: [{ required: true, message: '请输入实际值' }],
            })(<Input placeholder="请输入实际值" allowClear />)}
          </FormItem>
          <Row gutter={24}>
            <Col span={4} />
            <Col span={14} style={{ textAlign: 'center' }}>
              <Button onClick={onCancel} style={{ marginRight: 15 }}>
                取消
              </Button>
              <Button type="primary" onClick={this.onSubmit}>
                保存
              </Button>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
}

export default EditModal;
