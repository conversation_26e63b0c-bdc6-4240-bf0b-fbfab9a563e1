import React, { Component, Fragment } from 'react';
import ReactDOM from 'react-dom';

import {
  DeleteOutlined,
  EditOutlined,
  FileAddOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  SyncOutlined,
} from '@ant-design/icons';
import { connect } from 'dva';
import { Tree, Input, Menu, Modal, message } from 'antd';
import { getParentKey, getPath } from '@/utils/tree';
import EditModal from './components/EditModal';
import styles from './index.less';

const { TreeNode, DirectoryTree } = Tree;
const { Search } = Input;
const NOOP = () => { };

@connect(state => ({}))
class EditableTree extends Component {
  constructor(props) {
    super(props);
    this.dataList = [];
    this.state = {
      expandedKeys: [],
      searchValue: '',
      autoExpandParent: true,
      editModalVisible: false,
      editFormData: {
        curPath: [0],
      },
      selectedNode: null,
      selectedKeys: [],
      dataSource: props.dataSource,
    };
  }

  componentWillUnmount() {
    if (this.cmContainer) {
      ReactDOM.unmountComponentAtNode(this.cmContainer);
      document.body.removeChild(this.cmContainer);
      this.cmContainer = null;
    }
  }

  componentDidMount() {
    this.getContainer();
    this.generateList(this.props.dataSource);
  }

  generateList = (data = []) => {
    // eslint-disable-next-line no-plusplus
    for (let i = 0; i < data.length; i++) {
      const node = data[i];
      const { id, name, enumCode, children } = node;
      const isLeaf = !node.children || !node.children.length;
      this.dataList.push({ id, name, enumCode, children, isLeaf });

      if (!isLeaf) {
        this.generateList(node.children);
      }
    }
  };

  onChange = e => {
    const { dataSource } = this.state;
    const { value } = e.target;
    const expandedKeys = this.dataList
      .map(item => {
        if (item.name.indexOf(value) > -1) {
          return getParentKey(item.id, dataSource);
        }
        return null;
      })
      .filter((item, i, self) => item && self.indexOf(item) === i);
    this.setState({
      expandedKeys,
      searchValue: value,
      autoExpandParent: true,
    });
  };

  onExpand = expandedKeys => {
    this.setState({
      expandedKeys,
      autoExpandParent: false,
    });
  };

  onSelect = (selectedKeys, e) => {
    // 销毁右键菜单
    this.destroyContextMenu();

    const { onSelect = NOOP } = this.props;
    const { selectedNodes } = e;
    const [selectedNode] = selectedNodes;

    if (selectedKeys && selectedKeys.length) {
      // 得到选中的key
      const [key] = selectedKeys;
      // 得到路径
      const curPath = this.getPath(key);

      this.setState(({ editFormData }) => ({
        selectedKeys,
        selectedNode,
        editFormData: {
          ...editFormData,
          // eslint-disable-next-line react/no-unused-state
          curPath,
        },
      }));

      const node = this.dataList.find(item => String(item.id) === String(key));

      if (node) {
        onSelect(node);
      }
    }
  };

  // 回溯得到路径
  getPath = key => {
    const { dataSource } = this.state;

    if (!key) {
      return;
    }

    // eslint-disable-next-line consistent-return
    return getPath(key, dataSource);
  };

  // 选择路径改变
  onPathChange = val => {
    this.setState(({ editFormData }) => ({
      editFormData: {
        ...editFormData,
        curPath: val,
      },
    }));
  };

  onFold = () => {
    const { toggleFold = NOOP } = this.props;
    toggleFold(true);
  };

  onUnFold = () => {
    const { toggleFold = NOOP } = this.props;
    toggleFold(false);
  };

  // 打开新增窗口
  onOpenAddModal = () => {
    this.setState({
      editModalVisible: true,
    });
  };

  // 打开编辑弹窗
  onOpenEditModal = () => {
    this.setState(({ editFormData }) => {
      const { curPath } = editFormData;
      const cur = curPath && curPath.pop();

      const node = this.dataList.find(item => String(item.id) === String(cur));

      if (!node) {
        return {
          editFormData,
        };
      }

      return {
        editModalVisible: true,
        editFormData: {
          ...editFormData,
          curPath,
          id: cur,
          enumCode: node.enumCode,
          enumDesc: node.name,
        },
      };
    });
  };

  // 关闭弹窗
  onCancelEditModal = () => {
    this.setState(({ editFormData }) => ({
      editModalVisible: false,
      editFormData: {
        curPath: editFormData.curPath,
      },
    }));
  };

  // 动态增加节点
  appendNode = (treeNode, newNodeData) =>
    new Promise(resolve => {
      const { children = [] } = treeNode.props.dataRef;
      setTimeout(() => {
        // eslint-disable-next-line no-param-reassign
        treeNode.props.dataRef.children = [...children, newNodeData];

        this.setState(({ dataSource }) => ({
          dataSource: [...dataSource],
        }));

        this.dataList.push(newNodeData);

        resolve();
      }, 1000);
    });

  onEdit = values => {
    const { onEdit = NOOP } = this.props;

    const { path } = values;
    const len = path.length;
    // eslint-disable-next-line no-param-reassign
    values.parentId = path[len - 1];

    // 编辑接口返回
    onEdit(values).then(res => {
      if (res.success) {
        const { data } = res;
        this.setState({
          editModalVisible: false,
        });

        if (!data.isUpdate) {
          this.appendNode(this.state.selectedNode, {
            id: data.id,
            name: data.enumDesc,
            enumCode: data.enumCode,
            isLeaf: 1,
            children: [],
          });
        } else {
          const node = this.dataList.find(item => String(item.id) === String(values.id));
          const newNode = Object.assign({}, node, {
            name: values.enumDesc,
            enumCode: values.enumCode,
          });

          this.updateNode(this.state.dataSource, newNode, values.path[values.path.length - 1]);
        }
        this.onRefresh()
      }
    });
  };

  onDelete = () =>
    new Promise(resolve => {
      const { onDelete = NOOP } = this.props;
      const { selectedKeys } = this.state;
      if (selectedKeys && selectedKeys.length > 0) {
        const [key] = selectedKeys;

        onDelete({ id: key }).then(res => {
          if (res && res.success) {
            this.removeNode(this.state.dataSource, key);
            resolve({
              success: true,
            });
            this.onRefresh()
          }
        });
      }
    });

  // 删除节点
  removeNode = (treeData, id) => {
    const recurveDel = tree => {
      if (!tree || !tree.length) {
        return;
      }

      // 广度遍历树删除节点
      // eslint-disable-next-line no-plusplus
      for (let i = 0; i < tree.length; i++) {
        const n = tree[i];

        if (String(n.id) === String(id)) {
          tree.splice(i, 1);
          return;
        }

        if (n.children && n.children.length > 0) {
          recurveDel(n.children);
        }
      }
    };

    recurveDel(treeData);

    this.generateList(treeData);
    this.setState({
      dataSource: treeData.slice(),
    });
    this.onRefresh()
  };

  // 动态更新节点
  updateNode = (treeData, updateNode, parentId) => {
    const recurveDel = tree => {
      if (!tree || !tree.length) {
        return;
      }

      // 广度遍历树删除节点
      // eslint-disable-next-line no-plusplus
      for (let i = 0; i < tree.length; i++) {
        const n = tree[i];

        if (String(n.id) === String(updateNode.id)) {
          tree.splice(i, 1);
          return;
        }

        if (n.children && n.children.length > 0) {
          recurveDel(n.children);
        }
      }
    };

    const recurveAdd = tree => {
      if (!tree || !tree.length) {
        return;
      }

      // 广度遍历树删除节点
      // eslint-disable-next-line no-plusplus
      for (let i = 0; i < tree.length; i++) {
        const n = tree[i];

        if (String(n.id) === String(parentId)) {
          n.children = [...(n.children || []), updateNode];
          return;
        }

        if (n.children && n.children.length > 0) {
          recurveAdd(n.children);
        }
      }
    };

    // 分开删除和增加
    recurveDel(treeData);
    recurveAdd(treeData);

    this.generateList(treeData);
    this.setState({
      dataSource: treeData.slice(),
    });
  };

  // 刷新事件
  onRefresh = () => {
    const { onRefresh = NOOP, dispatch } = this.props;
    this.setState({
      selectedKeys: [],
    })
    onRefresh();
    dispatch({
      type: 'enumValueDetail/updateState',
      payload: {
        enumValues: [],
      },
    })
  };

  // 表单改变事件
  onValuesChange = values => {
    this.setState(({ editFormData }) => ({ editFormData: { ...editFormData, ...values } }));
  };

  // 右键菜单的点击新增事件
  onCMAdd = selectedNode => {
    // 得到选中的key
    const [key] = this.state.selectedKeys;
    // 得到路径
    const curPath = this.getPath(key);

    this.setState(
      ({ editFormData }) => ({
        selectedNode,
        editFormData: {
          ...editFormData,
          // eslint-disable-next-line react/no-unused-state
          curPath,
        },
        editModalVisible: true,
      }),
      () => {
        this.destroyContextMenu();
      }
    );
  };

  // 右键菜单的点击编辑事件
  onCMEdit = selectedNode => {
    // 得到选中的key
    const [key] = this.state.selectedKeys;
    // 得到路径
    const curPath = this.getPath(key);

    this.setState(
      ({ editFormData }) => ({
        selectedNode,
        editFormData: {
          ...editFormData,
          // eslint-disable-next-line react/no-unused-state
          curPath,
        },
      }),
      () => {
        this.destroyContextMenu();
        this.onOpenEditModal();
      }
    );
  };

  onCMDelete = () => {
    this.onDelete()
      .then(res => {
        if (res.success) {
          this.destroyContextMenu();
        }
      })
      .catch(() => {});
  };

  // 右键点击事件
  onRightClick = info => {
    this.setState({ selectedKeys: [info.node.props.eventKey] });
    this.renderCm(info);
  };

  getContainer() {
    if (!this.cmContainer) {
      this.cmContainer = document.createElement('div');
      document.body.appendChild(this.cmContainer);
    }
    return this.cmContainer;
  }

  // 销毁右键菜单
  destroyContextMenu = () => {
    if (this.contextMenu) {
      ReactDOM.unmountComponentAtNode(this.cmContainer);
      this.contextMenu = null;
    }
  };

  // 渲染右键菜单
  renderCm(info) {
    this.destroyContextMenu();
    this.contextMenu = (
      <Menu style={{ minWidth: 100, borderRadius: 5, border: '1px solid #eee' }}>
        <Menu.Item key="0" onClick={() => this.onCMAdd(info.node)}>
          添加子节点
        </Menu.Item>
        <Menu.Divider />
        <Menu.Item key="1" onClick={() => this.onCMEdit(info.node)}>
          编辑当前节点
        </Menu.Item>
        <Menu.Item key="2" onClick={() => this.onCMConfirm()}>
          删除当前节点
        </Menu.Item>
      </Menu>
    );

    const container = this.getContainer();

    Object.assign(this.cmContainer.style, {
      position: 'absolute',
      left: `${info.event.pageX}px`,
      top: `${info.event.pageY}px`,
      zIndex: 100,
    });

    ReactDOM.render(this.contextMenu, container);
  }

  // 拖拽事件
  onDrop = info => {
    const { onEdit = NOOP } = this.props;
    const dropKey = info.node.props.eventKey;
    const dragKey = info.dragNode.props.eventKey;
    const dropPos = info.node.props.pos.split('-');
    const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]);
    let parentId; // 父节点

    if (dropKey && info.dropToGap) {
      parentId = getParentKey(dropKey, this.state.dataSource) || 0;
    }

    // console.log('parentId', parentId);

    const values = {
      parentId: typeof parentId === 'undefined' ? dropKey : parentId, // 父节点的id
      id: dragKey, // 拖动节点的id
    };

    // 调用更新接口
    onEdit(values).then(res => {
      if (res.success) {
        const loop = (data, key, callback) => {
          // eslint-disable-next-line consistent-return
          data.forEach((item, index, arr) => {
            if (item.id === key) {
              return callback(item, index, arr);
            }
            if (item.children) {
              return loop(item.children, key, callback);
            }
          });
        };

        const { dataSource } = this.state;
        const data = [...dataSource];

        // 寻找拖拽的节点
        let dragObj;
        loop(data, dragKey, (item, index, arr) => {
          arr.splice(index, 1);
          dragObj = item;
        });

        if (!info.dropToGap) {
          // Drop on the content
          loop(data, dropKey, item => {
            // eslint-disable-next-line no-param-reassign
            item.children = item.children || [];
            // where to insert 示例添加到尾部，可以是随意位置
            item.children.push(dragObj);
          });
        } else if (
          (info.node.props.children || []).length > 0 && // Has children
          info.node.props.expanded && // Is expanded
          dropPosition === 1 // On the bottom gap
        ) {
          loop(data, dropKey, item => {
            // eslint-disable-next-line no-param-reassign
            item.children = item.children || [];
            // where to insert 示例添加到头部，可以是随意位置
            item.children.unshift(dragObj);
          });
        } else {
          let ar;
          let i;
          loop(data, dropKey, (item, index, arr) => {
            ar = arr;
            i = index;
          });
          if (dropPosition === -1) {
            ar.splice(i, 0, dragObj);
          } else {
            ar.splice(i + 1, 0, dragObj);
          }
        }

        this.setState({
          dataSource: data,
        });
      }
    });
  };

  // 确认删除
  onConfirm = () => {
    const { selectedKeys } = this.state;
    if(!selectedKeys || selectedKeys.length === 0) {
      message.error('请选择要删除的节点!');
      return;
    }
    Modal.confirm({
      title: '是否删除',
      content: '确定要删除这个枚举值么',
      okText: '确认',
      cancelText: '取消',
      onOk: this.onDelete,
    });
  };

  // 右键菜单删除确认
  onCMConfirm = () => {
    Modal.confirm({
      title: '是否删除',
      content: '确定要删除这个枚举值么',
      okText: '确认',
      cancelText: '取消',
      onOk: this.onCMDelete,
    });
  };

  render() {
    const { isFold } = this.props;
    const {
      searchValue,
      expandedKeys,
      autoExpandParent,
      editModalVisible,
      editFormData,
      dataSource,
      selectedKeys,
    } = this.state;

    const loop = data =>
      data.map(item => {
        const index = item.name.indexOf(searchValue);
        const beforeStr = item.name.substr(0, index);
        const afterStr = item.name.substr(index + searchValue.length);
        const name =
          index > -1 ? (
            <span>
              {beforeStr}
              <span style={{ color: '#f50' }}>{searchValue}</span>
              {afterStr}
            </span>
          ) : (
            <span>{item.name}</span>
          );

        if (item.children && item.children.length > 0) {
          return (
            <TreeNode key={item.id} title={name} dataRef={item}>
              {loop(item.children)}
            </TreeNode>
          );
        }
        // 叶子节点
        return <TreeNode key={item.id} title={name} isLeaf dataRef={item} />;
      });

    return (
      <div className={styles.container}>
        <div className={styles.foldIconWrap}>
          {isFold ? (
            <MenuUnfoldOutlined className={styles.foldIcon} onClick={this.onUnFold} />
          ) : (
            <MenuFoldOutlined className={styles.foldIcon} onClick={this.onFold} />
          )}
        </div>
        {!isFold && (
          <Fragment>
            <Search
              style={{ marginBottom: 8 }}
              placeholder="输入条件进入搜索"
              onChange={this.onChange}
            />
            <div className={styles.treeTool}>
              <FileAddOutlined className={styles.actionIcon} onClick={this.onOpenAddModal} />
              <DeleteOutlined className={styles.actionIcon} onClick={this.onConfirm} />
              <EditOutlined className={styles.actionIcon} onClick={this.onOpenEditModal} />
              <SyncOutlined className={styles.actionIcon} onClick={this.onRefresh} />
            </div>
            <DirectoryTree
              selectedKeys={selectedKeys}
              onSelect={this.onSelect}
              onExpand={this.onExpand}
              expandedKeys={expandedKeys}
              autoExpandParent={autoExpandParent}
              onRightClick={this.onRightClick}
              draggable
              onDrop={this.onDrop}
            >
              {loop(dataSource)}
            </DirectoryTree>
          </Fragment>
        )}

        <EditModal
          treeData={dataSource}
          modalVisible={editModalVisible}
          onCancel={this.onCancelEditModal}
          editFormData={editFormData}
          onPathChange={this.onPathChange}
          onSubmit={this.onEdit}
          onValuesChange={this.onValuesChange}
        />
      </div>
    );
  }
}

export default EditableTree;
