/**
 * 洞察任务modal
 */
/* eslint-disable no-case-declarations */
import React, { Fragment } from 'react';
import {
  Modal,
  Form,
  Row,
  Col,
  Button,
  Input,
  DatePicker,
  Select,
  InputNumber,
  Radio,
  TreeSelect,
} from 'antd';
import dayjs from 'dayjs';
import useSuggests from '@/hooks/useSuggests';
import styles from './index.less';

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const { Option } = Select;
const { SHOW_PARENT } = TreeSelect;
const NOOP = () => {};

export const CrowdInsightTaskForm = ({
  editFormData = {},
  disabled = false,
  onCancel = NOOP,
  onSubmit = NOOP,
  onSave = NOOP,
  colLimit = 24
}) => {
  const [form] = Form.useForm();
  const [
    { allPoiSuggests, categorySuggests, citySuggests, playTagSuggests },
    {
      fetchCitySuggests,
      fetchPlayTagSuggests,
      fetchNativePoiSuggests,
      fetchPoiSuggests,
      fetchAllPoi,
    },
    restPoiPlaySuggests,
  ] = useSuggests();

  const handlerData = values => {
    const {
      category = [],
      autoUpdate,
      limitCount,
      expireTime,
      play,
      poi,
      name,
      scene,
      remarks,
      dest,
    } = values;

    const payload = {
      id: editFormData.id,
      autoUpdate,
      category: category.map(item => ({ content: item.label, id: item.value })),
      dest: (dest || []).map(item => ({
        city: item.key,
        cityName: item.label,
      })),
      expireTime: expireTime.valueOf(),
      limitCount,
      name,
      play: (play || []).map(item => {
        const { key = '', label } = item;
        const [tagId, city, cityName] = key.split('_');
        return { city, cityName, tagId, tagName: label };
      }),
      poi: (poi || []).map(item => {
        const { key = '', label } = item;
        const [poiId, city, cityName] = key.split('_');
        return {
          poiId,
          poiName: label,
          city,
          cityName,
        };
      }),
      remarks,
      scene,
      // status: 'string',
    };
    return payload;
  }
  const onFinish = values => {
    const payload = handlerData(values);
    onSubmit(payload);
  };
  const onSaveForm = () => {
    const values = form.getFieldsValue();
    const payload = handlerData(values);
    onSave(payload);
  }

  const citySuggest = value => {
    fetchCitySuggests({
      query: value,
    });
  };

  const playTagSuggest = value => {
    const cities = form.getFieldValue('dest');

    fetchPlayTagSuggests({
      cities: (cities || []).map(item => item.key),
      query: value,
    });
  };

  const poiSuggest = value => {
    const cities = form.getFieldValue('dest');
    // 两份数据
    fetchAllPoi({ cities: (cities || []).map(item => item.key), query: value });
  };

  const onSelectCityChange = value => {
    if (!value || !value.length) {
      restPoiPlaySuggests();
      return;
    }
    const cities = value.map(item => item.key);
    fetchAllPoi({
      cities,
    });
    fetchPlayTagSuggests({
      cities,
    });
  };

  const topHandler = type => {
    switch (type) {
      case 'city':
        fetchCitySuggests({
          limit: form.getFieldValue('destTop'),
        }).then(data => {
          form.setFieldsValue({
            dest: (data || []).map(item => ({ key: item.city, label: item.cityName })),
          });
        });
        break;
      case 'native-poi':
        fetchNativePoiSuggests({
          cities: (form.getFieldValue('dest') || []).map(item => item.key),
          limit: form.getFieldValue('poiTop'),
        }).then(data => {
          form.setFieldsValue({
            poi: (data || []).map(item => {
              const { city, cityName, poiId, poiName } = item;
              return { key: `${poiId}_${city}_${cityName}`, label: poiName };
            }),
          });
        });
        break;
      case 'non-native-poi':
        fetchPoiSuggests({
          cities: (form.getFieldValue('dest') || []).map(item => item.key),
          limit: form.getFieldValue('poiTop'),
        }).then(data => {
          form.setFieldsValue({
            poi: (data || []).map(item => {
              const { city, cityName, poiId, poiName } = item;
              return { key: `${poiId}_${city}_${cityName}`, label: poiName };
            }),
          });
        });
        break;
      case 'play':
        fetchPlayTagSuggests({
          cities: (form.getFieldValue('dest') || []).map(item => item.key),
          limit: form.getFieldValue('playTop'),
        }).then(data => {
          form.setFieldsValue({
            play: (data || []).map(item => {
              const { city, cityName, tagId, tagName } = item;
              return { key: `${tagId}_${city}_${cityName}`, label: tagName };
            }),
          });
        });
        break;
      default:
        break;
    }
  };

  const disabledDate = current =>
    current < dayjs().startOf('day') || current > dayjs() + 90 * 24 * 60 * 60 * 1000;

  const isAddStatus = Object.keys(editFormData || {}).length === 0;
  return (
    <Form
      name="exportForm"
      {...FORM_ITEM_LAYOUT}
      initialValues={editFormData}
      form={form}
      onFinish={onFinish}>
      <Row gutter={24}>
        <Col span={colLimit}>
          <FormItem
            label="任务名称"
            name="name"
            rules={[{ required: true, message: '请输入任务名称' }]}
          >
            <Input placeholder="请输入任务名称" disabled={disabled} />
          </FormItem>
        </Col>
        <Col span={colLimit}>
          <FormItem
            label="使用场景"
            name="scene"
            rules={[{ required: true, message: '请输入使用场景' }]}
          >
            <Input placeholder="请输入使用场景" disabled={disabled} />
          </FormItem>
        </Col>
        <Col span={colLimit}>
          <FormItem
            label="备注说明"
            name="remarks"
            rules={[{ required: true, message: '请输入备注说明' }]}
          >
            <Input placeholder="请输入备注说明" disabled={disabled} />
          </FormItem>
        </Col>

        <Col span={colLimit}>
          <FormItem
            label="过期时间"
            name="expireTime"
            rules={[{ required: true, message: '请选择过期时间' }]}
          >
            <DatePicker
              disabled={disabled}
              disabledDate={disabledDate}
              format="YYYY-MM-DD HH:mm:ss"
            />
          </FormItem>
        </Col>

        <Col span={colLimit}>
          <FormItem
            label="目的地"
            name="dest"
            rules={[{ required: true, message: '请选择目的地' }]}
            extra={
              <span>
                <Button onClick={() => topHandler('city')} disabled={disabled} style={{ marginTop: 6 }}>
                  查看top
                </Button>
                <FormItem noStyle name="destTop" initialValue={10} style={{ marginTop: 6 }}>
                  <InputNumber min={0} className={styles.searchInput} allowClear disabled={disabled} />
                </FormItem>
              </span>
            }
          >
          <Select
            mode="multiple"
            rules={[{ required: true, message: '请选择目的地' }]}
            placeholder="请选择目的地"
            allowClear
            showSearch
            disabled={disabled}
            onSearch={citySuggest}
            defaultActiveFirstOption={false}
            filterOption={false}
            showArrow={false}
            onChange={onSelectCityChange}
            labelInValue
          >
            {citySuggests &&
              citySuggests.length > 0 &&
              citySuggests.map(c => {
                const { city, cityName } = c;
                return <Option key={city}>{cityName}</Option>;
              })}
          </Select>
        </FormItem>
        </Col>

        <Col span={colLimit}>
          <FormItem
          noStyle
          shouldUpdate={(prevValues, curValues) => {
            const shouldUpdate = prevValues.dest !== curValues.dest;
            if (shouldUpdate) {
              form.setFieldsValue({
                poi: [],
                play: []
              });
            }
            return shouldUpdate;
          }}
        >
          {({ getFieldValue }) => {
            const dest = getFieldValue('dest');
            return (
              dest &&
              dest.length > 0 && (
                <Fragment>
                  <FormItem
                    label="POI"
                    name="poi"
                    extra={
                      <span>
                        <Button
                          style={{ marginRight: 10 }}
                          size="small"
                          disabled={disabled}
                          onClick={() => topHandler('native-poi')}
                        >
                          查看本地top
                        </Button>
                        <Button size="small" onClick={() => topHandler('non-native-poi')} disabled={disabled}>
                          查看异地top
                        </Button>
                        <FormItem noStyle name="poiTop" initialValue={10}>
                          <InputNumber min={0} className={styles.searchInput} allowClear disabled={disabled} />
                        </FormItem>
                      </span>
                    }
                  >
                    <Select
                      mode="multiple"
                      placeholder="请选择POI"
                      allowClear
                      showSearch
                      disabled={disabled}
                      onSearch={poiSuggest}
                      defaultActiveFirstOption={false}
                      filterOption={false}
                      showArrow={false}
                      labelInValue
                    >
                      {allPoiSuggests &&
                        allPoiSuggests.length > 0 &&
                        allPoiSuggests.map(p => {
                          const { poiId, poiName, city, cityName } = p;
                          return (
                            <Option key={`${poiId}_${city}_${cityName}`}>
                              {poiName}
                            </Option>
                          );
                        })}
                    </Select>
                  </FormItem>
                  <FormItem
                    label="玩法"
                    name="play"
                    extra={
                      <span>
                        <Button size="small" onClick={() => topHandler('play')}>
                          查看top
                        </Button>
                        <FormItem noStyle name="playTop" initialValue={10}>
                          <InputNumber min={0} className={styles.searchInput} allowClear />
                        </FormItem>
                      </span>
                    }
                  >
                    <Select
                      mode="multiple"
                      placeholder="请选择玩法"
                      allowClear
                      showSearch
                      disabled={disabled}
                      onSearch={playTagSuggest}
                      defaultActiveFirstOption={false}
                      filterOption={false}
                      showArrow={false}
                      labelInValue
                    >
                      {playTagSuggests &&
                        playTagSuggests.length > 0 &&
                        playTagSuggests.map(t => {
                          const { tagId, tagName, city, cityName } = t;
                          return <Option key={`${tagId}_${city}_${cityName}`}>{tagName}</Option>;
                        })}
                    </Select>
                  </FormItem>
                </Fragment>
              )
            );
          }}
        </FormItem>
        </Col>

        <Col span={colLimit}>
          <FormItem name="category" label="类目">
            <TreeSelect
              allowClear
              showSearch
              disabled={disabled}
              treeData={categorySuggests}
              treeCheckable
              showCheckedStrategy={SHOW_PARENT}
              treeNodeFilterProp="title"
              labelInValue
              placeholder="请选择类目"
            />
          </FormItem>
        </Col>

        <Col span={colLimit}>
          <FormItem name="limitCount" label="商品数量" rules={[{ required: true, message: '请设置商品数量' }]}>
            <InputNumber min={0} allowClear disabled={disabled} />
          </FormItem>
        </Col>

        <Col span={colLimit}>
          <FormItem name="autoUpdate" label="自动更新" rules={[{ required: true, message: '请选择是否自动更新' }]}>
            <RadioGroup disabled={disabled}>
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </RadioGroup>
          </FormItem>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={4} />
        {
          !disabled ? (
            <Col span={18} style={{ textAlign: 'center' }}>
              <Button onClick={onCancel} style={{ marginRight: 15 }}>
                取消
              </Button>
              <Button type="primary" onClick={onSaveForm} style={{ marginRight: 15 }}>
                保存
              </Button>
              <Button type="primary" htmlType="submit" disabled={isAddStatus}>提交</Button>
              <span style={{ marginLeft: 12, color: 'red' }}>注: 保存后才能提交，提交后不可修改!</span>
            </Col>
          ) : null
        }
      </Row>
    </Form>
  );
};

function WrapModal(props) {
  const {
    editInsightTaskVisible = false,
    onCancel = NOOP,
  } = props;
  return (
    <Modal
      title="新增洞察任务"
      width={800}
      visible={editInsightTaskVisible}
      onCancel={onCancel}
      footer={null}
      destroyOnClose
      className={styles.container}
    >
      <CrowdInsightTaskForm {...props} />
    </Modal>
  );
}


export default WrapModal;

/**
* 反向解析到标准字段
* 因为表单在生成服务端字段时做了转换
* 这里重新反向生成回去，方便渲染
* @param {}
*/
export const validataData = data => {
  if (!data) return {};
  const result = {};
  if (data.category) {
    result.category = data.category
      .filter(d => d.id)
      .map(d => ({
        label: d.content,
        value: d.id
      }));
  }
  if (data.dest) {
    result.dest = data.dest
      .filter(d => d.city)
      .map(d => ({
        key: d.city,
        label: d.cityName
      }));
  }
  if (data.play) {
    result.play = data.play
      .filter(d => d.cityName)
      .map(d => ({
        key: `${d.tagId}_${d.city}_${d.cityName}`,
        label: d.tagName,
      }));
  }
  if (data.poi) {
    result.poi = data.poi
      .filter(d => d.poiName)
      .map(d => ({
        key: `${d.poiId}_${d.city}_${d.cityName}`,
        label: d.poiName
      }));
  }
  if (data.expireTime) {
    result.expireTime = dayjs(data.expireTime);
  }

  return {
    ...data,
    ...result
  };
}
