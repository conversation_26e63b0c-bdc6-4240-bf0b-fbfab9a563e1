import React, { Component } from 'react';
import { Empty } from 'antd';
import { <PERSON>, <PERSON>eom, <PERSON>, Too<PERSON><PERSON>, Co<PERSON>, Legend } from 'bizcharts'
import DataSet from '@antv/data-set';

class PieChart extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  render() {
    const { DataView } = DataSet;
    const { data = [], title, padding = [10, 10, 10, 10] } = this.props;

    let allCount = 0;
    for (let i = 0, len = data.length; i < len; i++) {
      allCount += (data[i] && data[i].count) || 0;
    }
    // const flagTen = allCount * 0.05

    // const data = [
    //   {
    //     item: "事例一",
    //     count: 41
    //   },
    //   {
    //     item: "事例二",
    //     count: 40
    //   },
    //   {
    //     item: "事例三",
    //     count: 20
    //   }
    // ];
    const dv = new DataView();
    dv.source(data).transform({
      type: 'percent',
      field: 'count',
      dimension: 'item',
      as: 'percent',
    });
    return (
      <div>
        {title ? (
          <h2 style={{ fontSize: 14, color: '#5e5e5e', textAlign: 'center' }}>{title}</h2>
        ) : null}
        {!data || !data.length ? <Empty /> : null}
        {data && data.length ? (
          <Chart width={384} height={220} data={dv} padding={padding} forceFit={true}>
            <Coord type="theta" radius={0.9} />
            <Legend
              position="bottom"
              useHtml={true}
              offsetY={-14}
              itemTpl={
                '<li title="{value}" class="g2-legend-list-item item-{index} {checked}" data-color="{originColor}" data-value="{originValue}" style="cursor: pointer;font-size: 14px; display: inline-block; min-width: 50px; max-width: 90px; overflow: hidden; white-space:nowrap; text-overflow: ellipsis;margin-right: 14px;margin-bottom: 2px;">' +
                '<i class="g2-legend-marker" style="width:10px;height:10px;border-radius:50%;display:inline-block;margin-right:10px;background-color: {color};"></i>' +
                '<span class="g2-legend-text">' +
                '{value}' +
                '</span>' +
                '</li>'
              }
              g2-legend-list-item={{
                marginRight: 14,
                marginBottom: 2,
              }}
            />
            <Tooltip
              showTitle={false}
              itemWidth={40}
              itemTpl="<li><span style='background-color:{color};' class='g2-tooltip-marker'></span>{name}: {value}</li>"
            />
            <Geom
              type="intervalStack"
              position="percent"
              color="item"
              tooltip={[
                'item*percent',
                (item, percent) => {
                  percent = (percent * 100).toFixed(2) + '%';
                  return {
                    name: item,
                    value: percent,
                  };
                },
              ]}
              style={{
                lineWidth: 1,
                stroke: '#fff',
              }}
            >
              <Label
                content="name"
                offset={-16}
                formatter={(text, item, index) => {
                  // text 为每条记录 x 属性的值
                  // item 为映射后的每条数据记录，是一个对象，可以从里面获取你想要的数据信息
                  // index 为每条记录的索引
                  var point = item.point; // 每个弧度对应的点
                  var percent = point['percent'];
                  percent = (percent * 100).toFixed(0);
                  return percent >= 5 ? percent + '%' : '';
                }}
              />
            </Geom>
          </Chart>
        ) : null}
      </div>
    );
  }
}

export default PieChart;
