import React, { PureComponent } from 'react';
import { FormattedMessage, formatMessage } from 'umi';

import {
  CloseCircleOutlined,
  LogoutOutlined,
  QuestionCircleOutlined,
  SettingOutlined,
  UserOutlined,
  AlertOutlined,
} from '@ant-design/icons';
import { connect } from 'dva';
import { Spin, Menu, Avatar, Tooltip, Badge } from 'antd';
import HeaderDropdown from '../HeaderDropdown';
import styles from './index.less';
import MessageModal from './MessageModal';

@connect(state => ({ message: state.message }))
export default class GlobalHeaderRight extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
    };
  }

  componentDidMount() {
    this.props.dispatch({
      type: 'message/fetchUnreadCount',
    });
    setInterval(() => {
      this.props.dispatch({
        type: 'message/fetchUnreadCount',
      });
    }, 1000 * 60 * 5);
  }

  render() {
    const {
      currentUser,
      onMenuClick,
      theme,
      message: { unReadCount },
      dispatch,
    } = this.props;

    const menu = (
      <Menu className={styles.menu} selectedKeys={[]} onClick={onMenuClick}>
        <Menu.Item key="userCenter">
          <UserOutlined />
          <FormattedMessage id="menu.account.center" defaultMessage="account center" />
        </Menu.Item>
        <Menu.Item key="userinfo">
          <SettingOutlined />
          <FormattedMessage id="menu.account.settings" defaultMessage="account settings" />
        </Menu.Item>
        <Menu.Item key="triggerError">
          <CloseCircleOutlined />
          <FormattedMessage id="menu.account.trigger" defaultMessage="Trigger Error" />
        </Menu.Item>
        <Menu.Divider />
        <Menu.Item key="logout">
          <LogoutOutlined />
          <FormattedMessage id="menu.account.logout" defaultMessage="logout" />
        </Menu.Item>
      </Menu>
    );
    let className = styles.right;
    if (theme === 'dark') {
      className = `${styles.right}  ${styles.dark}`;
    }
    return (
      <div className={className}>
        <span
          onClick={() => {
            this.setState({ visible: true });
          }}
          className={styles.action}
        >
          <Badge count={unReadCount} size="small">
            <AlertOutlined />
          </Badge>
        </span>
        <Tooltip title={formatMessage({ id: 'component.globalHeader.help' })}>
          <a
            target="_blank"
            href="https://aliyuque.antfin.com/qnwrq9/kzl68s"
            rel="noopener noreferrer"
            className={styles.action}
          >
            <QuestionCircleOutlined />
          </a>
        </Tooltip>
        {currentUser.name ? (
          // <HeaderDropdown overlay={menu}>
          <span className={`${styles.action} ${styles.account}`}>
            <Avatar size="small" className={styles.avatar} src={currentUser.avatar} alt="avatar" />
            <span className={styles.name}>{currentUser.name}</span>
          </span>
        ) : (
          // </HeaderDropdown>
          <Spin size="small" style={{ marginLeft: 8, marginRight: 8 }} />
        )}
        {/* <SelectLang className={styles.action} /> */}
        <MessageModal
          visible={this.state.visible}
          onCancel={() => {
            this.setState({ visible: false });
          }}
        />
      </div>
    );
  }
}
