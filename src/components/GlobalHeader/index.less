
@pro-header-hover-bg: rgba(0, 0, 0, 0.025);

.header {
  position: relative;
  height: 64px;
  padding: 0;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.logo {
  display: inline-block;
  height: 64px;
  padding: 0 0 0 24px;
  font-size: 20px;
  line-height: 64px;
  vertical-align: top;
  cursor: pointer;
  img {
    display: inline-block;
    vertical-align: middle;
  }
}

.menu {
  :global(.anticon) {
    margin-right: 8px;
  }
  :global(.ant-dropdown-menu-item) {
    min-width: 160px;
  }
}

.trigger {
  height: 64px;
  padding: ~'calc((64px - 20px) / 2)' 24px;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s, padding 0s;
  &:hover {
    background: @pro-header-hover-bg;
  }
}

.right {
  float: right;
  height: 100%;
  overflow: hidden;
  display: flex;
  .action {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 38px;
    height: 100%;
    padding: 0 12px;
    cursor: pointer;
    transition: all 0.3s;
    :global {
      .ant-badge {
        overflow: visible;
      }
      .ant-badge-count {
        min-width: 10px;
        height: 10px;
        padding: 0 4px;
        font-size: 6px;
        line-height: 10px;
      }
    }
    > i {
      color: var(--text-color);
      vertical-align: middle;
    }
    &:hover {
      background: @pro-header-hover-bg;
    }
    &:global(.opened) {
      background: @pro-header-hover-bg;
    }
  }
  .search {
    padding: 0 12px;
    &:hover {
      background: transparent;
    }
  }
  .account {
    width: 100px;
    .avatar {
      margin: ~'calc((64px - 24px) / 2)' 0;
      margin-right: 8px;
      color: @primary-color;
      vertical-align: top;
      background: rgba(255, 255, 255, 0.85);
    }
  }
}

.dark {
  height: 64px;
  .action {
    color: rgba(255, 255, 255, 0.85);
    > i {
      color: rgba(255, 255, 255, 0.85);
    }
    &:hover,
    &:global(.opened) {
      background:var(--primary-color) ;
    }
    :global(.ant-badge) {
      color: rgba(255, 255, 255, 0.85);
    }
  }
}

.messageModal {
  margin: 0;
  margin-left: auto;
  :global {
    .ant-modal-content {
      padding: 0;
    }
    .ant-modal-body {
      padding: 0;
    }
  }
  .flex {
    display: flex;
    align-items: center;
    padding: 4px;
  }
  .header {
    justify-content: space-between;
    height: 32px;
    border-bottom: 1px solid #e5e5e5;
    box-shadow: none;
  }
  .list {
    min-height: 150px;
    max-height: 240px;
    padding: 12px;
    overflow: hidden;
    overflow-y: auto;
    .item {
      margin-bottom: 12px;
      cursor: pointer;
      .itemHeader {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 4px;
        .title {
          display: flex;
          align-items: center;
          .point {
            display: inline-block;
            width: 4px;
            height: 4px;
            background: red;
            border-radius: 50%;
          }
          .text {
            width: 132px;
            margin-left: 2px;
            overflow: hidden;
            font-weight: bold;
            font-size: 14px;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        .time {
          font-size: 12px;
        }
      }
      .content {
        max-height: 36px;
        display: -webkit-box;
        overflow: hidden;

        overflow: hidden;
        font-size: 12px;

        text-overflow: ellipsis;

        -webkit-line-clamp: 2;

        /*! autoprefixer: off */

        -webkit-box-orient: vertical;
      }
    }
    .empty {
      margin-top: 40px;
      margin-bottom: 0;
      font-weight: 400;
      text-align: center;
    }
  }
  .footer {
    position: relative;
    justify-content: center;
    border-top: 1px solid #e5e5e5;
    a {
      position: absolute;
      top: 4px;
      right: 4px;
    }
    span {
      font-weight: 500;
      cursor: pointer;
    }
  }
}

.allMessageModal {
  :global {
    .ant-modal-title {
      display: flex;
      justify-content: space-between;
      a {
        font-size: 14px;
      }
    }
  }
  .search {
    display: flex;
    justify-content: space-between;
  }
  .list {
    :global {
      .ant-list-items {
        height: 400px;
        overflow-y: auto;
        .ant-list-item {
          cursor: pointer;
          .ant-list-item-meta {
            margin-bottom: 8px;
            .ant-list-item-meta-title {
              display: flex;
              justify-content: space-between;
              margin-bottom: 0;
            }
          }
        }
      }
    }
    .titleContainer {
      display: flex;
      align-items: center;
      .point {
        display: inline-block;
        margin-right: 2px;
        width: 4px;
        height: 4px;
        background: red;
        border-radius: 50%;
      }
    }
    .content {
      display: -webkit-box;
      overflow: hidden;
    
      overflow: hidden;
    
      text-overflow: ellipsis;
    
      -webkit-line-clamp: 1;
    
      /*! autoprefixer: off */
    
      -webkit-box-orient: vertical;
    }
    .time {
      font-weight: 400;
      font-size: 14px;
    }
  }
}

.detailDrawer {
  .drawerHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    .title {
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 132px;
      font-weight: 500;
    }
  }
}

@media only screen and (max-width: 768px) {
  .header {
    :global(.ant-divider-vertical) {
      vertical-align: unset;
    }
    .name {
      display: none;
    }
    i.trigger {
      padding: 22px 12px;
    }
    .logo {
      position: relative;
      padding-right: 12px;
      padding-left: 12px;
    }
    .right {
      position: absolute;
      top: 0;
      right: 12px;
      background: #fff;
      .account {
        .avatar {
          margin-right: 0;
        }
      }
    }
  }
}
