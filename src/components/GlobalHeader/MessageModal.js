import { Modal, Select, Spin, Button, Radio, Input, List, Drawer, Tooltip } from 'antd';
import styles from './index.less';
import { title } from '@/defaultSettings';
import { useEffect, useState, useRef } from 'react';
import { connect } from 'dva';
import dayjs from 'dayjs';

const { Search } = Input;

const IsolatedContent = ({ htmlContent, className }) => {
  const containerRef = useRef(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    let shadow = container.shadowRoot;
    if (!shadow) {
      shadow = container.attachShadow({ mode: 'open' });
    }

    shadow.innerHTML = htmlContent;

    const pElements = shadow.querySelectorAll('p');
    pElements.forEach(p => {
      p.style.margin = '0';
    });

    return () => {
      if (shadow) {
        shadow.innerHTML = '';
      }
    };
  }, [htmlContent]);

  return <div ref={containerRef} className={className} />;
}

const MessageModal = props => {
  const {
    visible,
    onCancel,
    dispatch,
    message: { messageList, loading, tabCntMap, unReadCount },
  } = props;
  const [open, setOpen] = useState(false);
  const [openDetail, setOpenDetail] = useState(false);
  const [detail, setDetail] = useState({});
  const [tab, setTab] = useState('ALL');

  const fetchData = data => {
    dispatch({
      type: 'message/fetchData',
      payload: {
        pageNo: data.pageNo ? data.pageNo : 1,
        pageSize: data.pageSize ? data.pageSize : 10,
        tab: data.tab || 'ALL',
        ...data,
      },
    });
  };

  useEffect(() => {
    if (visible) {
      fetchData({ pageSize: 20 });
    }
  }, [visible]);

  const updateRead = (id, pageSize) => {
    dispatch({
      type: 'message/readMessage',
      payload: {
        id,
        pageSize,
      },
    });
  };

  const updateReadAll = payload => {
    Modal.confirm({
      title: '确认全部设为已读吗？',
      onOk() {
        dispatch({
          type: 'message/readAllMessage',
          payload,
        });
      },
    });
  };

  return (
    <>
      <Modal
        visible={visible}
        style={{
          top: 64,
          right: 10,
        }}
        width={300}
        className={styles.messageModal}
        closable={false}
        header={false}
        footer={null}
        mask={false}
        onCancel={() => {
          onCancel();
        }}
      >
        <div className={`${styles.header} ${styles.flex}`}>
          <Select
            bordered={false}
            defaultValue={'ALL'}
            size="small"
            onChange={e => {
              setTab(e);
              fetchData({ tab: e });
            }}
            options={[
              { label: `全部消息`, value: 'ALL' },
              { label: `已读消息`, value: 'READ' },
              { label: `未读消息`, value: 'UNREAD' },
            ]}
          />
          <a
            style={{
              cursor: unReadCount <= 0 ? 'not-allowed' : 'pointer',
              color: unReadCount <= 0 ? '#ccc' : '#1890ff',
            }}
            onClick={() => {
              if (unReadCount <= 0) {
                return;
              }
              updateReadAll({
                pageSize: 20,
              });
            }}
          >
            全部设为已读
          </a>
        </div>
        <div className={styles.list}>
          <Spin spinning={loading}>
            {messageList.length > 0 ? (
              messageList.map(item => {
                return (
                  <div
                    key={item.id}
                    style={{
                      opacity: item.isRead ? 0.5 : 1,
                    }}
                    className={styles.item}
                    onClick={() => {
                      onCancel();
                      setOpenDetail(true);
                      setDetail(item);
                      if (!item.isRead) {
                        updateRead(item.id, 20);
                      }
                    }}
                  >
                    <div className={styles.itemHeader}>
                      <div className={styles.title}>
                        {item.isRead ? null : <span className={styles.point} />}
                        <Tooltip title={item.title && item.title.length > 9 ? item.title : ''}>
                          <span className={styles.text}>{item.title}</span>
                        </Tooltip>
                      </div>
                      <span className={styles.time}>
                        {dayjs(item.gmtCreate).format('YYYY-MM-DD HH:mm:ss')}
                      </span>
                    </div>
                    {/* <div
                      className={styles.content}
                      dangerouslySetInnerHTML={{ __html: item.content }}
                    /> */}
                    <IsolatedContent htmlContent={item.content} className={styles.content} />
                  </div>
                );
              })
            ) : (
              <p className={styles.empty}>暂无消息</p>
            )}
          </Spin>
        </div>
        <div className={`${styles.footer} ${styles.flex}`}>
          <span
            onClick={() => {
              setTab('ALL');
              fetchData({});
              setOpen(true);
              onCancel();
            }}
          >
            查看全部消息
          </span>
          <a
            onClick={() => {
              onCancel();
            }}
          >
            关闭
          </a>
        </div>
      </Modal>
      <Modal
        title={
          <>
            <span className={styles.title}>消息列表</span>
            <a
              onClick={() => {
                if (unReadCount <= 0) {
                  return;
                }
                updateReadAll({
                  pageSize: 10,
                  tab: 'ALL',
                });
              }}
              style={{
                cursor: unReadCount <= 0 ? 'not-allowed' : 'pointer',
                color: unReadCount <= 0 ? '#ccc' : '#1890ff',
              }}
              className={styles.count}
            >
              全部设为已读
            </a>
          </>
        }
        className={styles.allMessageModal}
        visible={open}
        onCancel={() => setOpen(false)}
        closeIcon={false}
        closable={false}
        style={{
          top:20
        }}
        width={1000}
        footer={<Button onClick={() => setOpen(false)}>关闭</Button>}
      >
        <div className={styles.search}>
          <Radio.Group
            defaultValue={'ALL'}
            value={tab}
            onChange={e => {
              setTab(e.target.value);
              fetchData({ tab: e.target.value });
            }}
            optionType="button"
          >
            <Radio.Button value="ALL">全部{`(${tabCntMap['ALL']})`}</Radio.Button>
            <Radio.Button value="READ">已读{`(${tabCntMap['READ']})`}</Radio.Button>
            <Radio.Button value="UNREAD">未读{`(${tabCntMap['UNREAD']})`}</Radio.Button>
          </Radio.Group>
          <Search
            placeholder="请输入"
            onSearch={e => {
              fetchData({ keyword: e });
            }}
            style={{ width: 300 }}
          />
        </div>
        <List
          itemLayout="vertical"
          size="large"
          pagination={{
            onChange: page => {
              fetchData({ pageNo: page });
            },
            pageSize: 10,
            total: tabCntMap[tab],
          }}
          className={styles.list}
          dataSource={messageList}
          renderItem={item => (
            <List.Item
              onClick={() => {
                setOpen(false);
                setDetail(item);
                setOpenDetail(true);
                if (!item.isRead) {
                  updateRead(item.id, 10);
                }
              }}
              key={item.title}
              style={{
                opacity: item.isRead ? 0.5 : 1,
              }}
            >
              <List.Item.Meta
                title={
                  <>
                    <div className={styles.titleContainer}>
                      {item.isRead ? null : <span className={styles.point} />}
                      <span>{item.title}</span>
                    </div>
                    <span className={styles.time}>
                      {dayjs(item.gmtCreate).format('YYYY-MM-DD HH:mm:ss')}
                    </span>
                  </>
                }
              />
              <IsolatedContent htmlContent={item.content} className={styles.content} />
              {/* <div
                className={styles.content}
                dangerouslySetInnerHTML={{
                  __html: item.content,
                }}
              /> */}
            </List.Item>
          )}
        />
      </Modal>
      <Drawer
        width={400}
        title="消息详情"
        visible={openDetail}
        onClose={() => setOpenDetail(false)}
        className={styles.detailDrawer}
      >
        <div className={styles.drawerHeader}>
          <Tooltip title={detail && detail.title && detail.title.length > 9 ? detail.title : ''}>
            <span className={styles.title}>{detail && detail.title ? detail.title : '空'}</span>
          </Tooltip>
          <span className={styles.time}>
            {detail && detail.gmtCreate
              ? dayjs(detail.gmtCreate).format('YYYY-MM-DD HH:mm:ss')
              : ''}
          </span>
        </div>
        {detail && detail.content && (
          <IsolatedContent htmlContent={detail.content}  />
          // <div
          //   dangerouslySetInnerHTML={{
          //     __html: detail.content,
          //   }}
          // />
        )}
        <div
          style={{
            position: 'absolute',
            right: 12,
            bottom: 12,
          }}
        >
          <Button onClick={() => setOpenDetail(false)} style={{ marginRight: 8 }}>
            取消
          </Button>
        </div>
      </Drawer>
    </>
  );
};

export default connect(({ message }) => ({
  message,
}))(MessageModal);
