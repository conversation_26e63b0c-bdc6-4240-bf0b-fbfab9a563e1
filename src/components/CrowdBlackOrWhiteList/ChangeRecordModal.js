import React, { useState, useEffect } from 'react';
import { Modal, Table, Button, Tag, message, Typography, Drawer } from 'antd';
import { HistoryOutlined, EyeOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { queryBlackWhiteListRecords } from '@/services/api';

const { Paragraph } = Typography;

/**
 * 黑白名单变更记录组件
 */
const ChangeRecordModal = ({ visible, onCancel, groupId }) => {
  const [loading, setLoading] = useState(false);
  const [records, setRecords] = useState([]);
  const [diffVisible, setDiffVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState(null);

  useEffect(() => {
    if (visible && groupId) {
      fetchRecords();
    }
  }, [visible, groupId]);

  const fetchRecords = async () => {
    setLoading(true);
    try {
      const response = await queryBlackWhiteListRecords({ groupId });
      if (response.success && response.data) {
        setRecords(response.data);
      }
    } catch (error) {
      message.error('获取变更记录失败');
    } finally {
      setLoading(false);
    }
  };

  const showDiff = (record) => {
    setCurrentRecord(record);
    setDiffVisible(true);
  };

  const formatDateTime = (dateTime) => {
    return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss');
  };

  const getOperationType = (beforeData, afterData) => {
    if (!beforeData) {
      return { type: '新增', color: 'green' };
    }
    if (!afterData) {
      return { type: '删除', color: 'red' };
    }
    return { type: '修改', color: 'orange' };
  };

  const columns = [
    {
      title: '操作时间',
      dataIndex: 'gmtCreate',
      key: 'gmtCreate',
      render: (text) => formatDateTime(text),
      width: 160,
    },
    {
      title: '操作人',
      dataIndex: ['operator', 'nickName'],
      key: 'operator',
      width: 120,
    },
    {
      title: '操作类型',
      key: 'operationType',
      render: (_, record) => {
        const { type, color } = getOperationType(record.beforeData, record.afterData);
        return <Tag color={color}>{type}</Tag>;
      },
      width: 100,
    },
    {
      title: '变更内容',
      key: 'content',
      render: (_, record) => {
        const beforeData = record.beforeData?.groupBlackWhiteList;
        const afterData = record.afterData?.groupBlackWhiteList;

        let content = '';
        if (!beforeData && afterData) {
          content = '新增黑白名单配置';
        } else if (beforeData && !afterData) {
          content = '删除黑白名单配置';
        } else if (beforeData && afterData) {
          const changes = [];
          if (beforeData.blackList !== afterData.blackList) {
            changes.push('黑名单');
          }
          if (beforeData.whiteList !== afterData.whiteList) {
            changes.push('白名单');
          }
          content = `修改了${changes.join('、')}`;
        }

        return content;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Button
          type="link"
          icon={<EyeOutlined />}
          onClick={() => showDiff(record)}
        >
          查看详情
        </Button>
      ),
      width: 100,
    },
  ];

  return (
    <>
      <Modal
        title={
          <div>
            <HistoryOutlined style={{ marginRight: 8 }} />
            黑白名单变更历史
          </div>
        }
        visible={visible}
        onCancel={onCancel}
        footer={null}
        width={800}
      >
        <Table
          columns={columns}
          dataSource={records}
          loading={loading}
          rowKey="id"
          size="small"
          pagination={{
            pageSize: 10,
            showSizeChanger: false,
            showTotal: (total) => `共 ${total} 条`,
          }}
        />
      </Modal>

      <DiffModal
        visible={diffVisible}
        onCancel={() => setDiffVisible(false)}
        record={currentRecord}
      />
    </>
  );
};

/**
 * 差异对比弹窗组件
 */
const DiffModal = ({ visible, onCancel, record }) => {
  const formatListForDisplay = (list) => {
    if (!list || !list.trim()) return '(空)';
    return list.split(',').join('\n');
  };

  const renderDiffContent = () => {
    if (!record) return null;

    const beforeData = record.beforeData?.groupBlackWhiteList;
    const afterData = record.afterData?.groupBlackWhiteList;

    if (!beforeData && afterData) {
      // 新增
      return (
        <div>
          <h4>新增黑白名单配置</h4>
          <div style={{ display: 'flex', gap: 20 }}>
            <div style={{ flex: 1 }}>
              <strong>黑名单：</strong>
              <div style={{
                border: '1px solid #d9d9d9',
                padding: 8,
                marginTop: 4,
                minHeight: 60,
                backgroundColor: '#f6ffed',
                whiteSpace: 'pre-line'
              }}>
                {formatListForDisplay(afterData.blackList)}
              </div>
            </div>
            <div style={{ flex: 1 }}>
              <strong>白名单：</strong>
              <div style={{
                border: '1px solid #d9d9d9',
                padding: 8,
                marginTop: 4,
                minHeight: 60,
                backgroundColor: '#f6ffed',
                whiteSpace: 'pre-line'
              }}>
                {formatListForDisplay(afterData.whiteList)}
              </div>
            </div>
          </div>
        </div>
      );
    }

    if (beforeData && !afterData) {
      // 删除
      return (
        <div>
          <h4>删除黑白名单配置</h4>
          <div style={{ display: 'flex', gap: 20 }}>
            <div style={{ flex: 1 }}>
              <strong>黑名单：</strong>
              <div style={{
                border: '1px solid #d9d9d9',
                padding: 8,
                marginTop: 4,
                minHeight: 60,
                backgroundColor: '#fff2f0',
                whiteSpace: 'pre-line'
              }}>
                {formatListForDisplay(beforeData.blackList)}
              </div>
            </div>
            <div style={{ flex: 1 }}>
              <strong>白名单：</strong>
              <div style={{
                border: '1px solid #d9d9d9',
                padding: 8,
                marginTop: 4,
                minHeight: 60,
                backgroundColor: '#fff2f0',
                whiteSpace: 'pre-line'
              }}>
                {formatListForDisplay(beforeData.whiteList)}
              </div>
            </div>
          </div>
        </div>
      );
    }

    if (beforeData && afterData) {
      // 修改
      return (
        <div>
          <h4>黑白名单变更对比</h4>

          <div style={{ marginBottom: 20 }}>
            <strong>黑名单变更：</strong>
            <div style={{ display: 'flex', gap: 10, marginTop: 8 }}>
              <div style={{ flex: 1 }}>
                <div style={{ marginBottom: 4, color: '#cf1322' }}>变更前：</div>
                <div style={{
                  border: '1px solid #d9d9d9',
                  padding: 8,
                  minHeight: 60,
                  backgroundColor: beforeData.blackList !== afterData.blackList ? '#fff2f0' : '#fafafa',
                  whiteSpace: 'pre-line'
                }}>
                  {formatListForDisplay(beforeData.blackList)}
                </div>
              </div>
              <div style={{ flex: 1 }}>
                <div style={{ marginBottom: 4, color: '#52c41a' }}>变更后：</div>
                <div style={{
                  border: '1px solid #d9d9d9',
                  padding: 8,
                  minHeight: 60,
                  backgroundColor: beforeData.blackList !== afterData.blackList ? '#f6ffed' : '#fafafa',
                  whiteSpace: 'pre-line'
                }}>
                  {formatListForDisplay(afterData.blackList)}
                </div>
              </div>
            </div>
          </div>

          <div>
            <strong>白名单变更：</strong>
            <div style={{ display: 'flex', gap: 10, marginTop: 8 }}>
              <div style={{ flex: 1 }}>
                <div style={{ marginBottom: 4, color: '#cf1322' }}>变更前：</div>
                <div style={{
                  border: '1px solid #d9d9d9',
                  padding: 8,
                  minHeight: 60,
                  backgroundColor: beforeData.whiteList !== afterData.whiteList ? '#fff2f0' : '#fafafa',
                  whiteSpace: 'pre-line'
                }}>
                  {formatListForDisplay(beforeData.whiteList)}
                </div>
              </div>
              <div style={{ flex: 1 }}>
                <div style={{ marginBottom: 4, color: '#52c41a' }}>变更后：</div>
                <div style={{
                  border: '1px solid #d9d9d9',
                  padding: 8,
                  minHeight: 60,
                  backgroundColor: beforeData.whiteList !== afterData.whiteList ? '#f6ffed' : '#fafafa',
                  whiteSpace: 'pre-line'
                }}>
                  {formatListForDisplay(afterData.whiteList)}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return null;
  };

  return (
    <Drawer
      title="变更详情对比"
      visible={visible}
      onClose={onCancel}
      width={800}
    >
      {record && (
        <div>
          <div style={{ marginBottom: 20, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
            <div><strong>操作时间：</strong>{dayjs(record.gmtCreate).format('YYYY-MM-DD HH:mm:ss')}</div>
            <div><strong>操作人：</strong>{record.operator?.nickName || record.operator?.empId || '未知'}</div>
          </div>
          {renderDiffContent()}
        </div>
      )}
    </Drawer>
  );
};

export default ChangeRecordModal;
