import React, { useState, Fragment, useEffect } from 'react';
import { Modal, Select, Form, Row, Col, Button, Radio, DatePicker, message, Tag, Input, Drawer } from 'antd';
import { HistoryOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import debounce from 'lodash/debounce';
import { queryLabel, queryExportCrowdTask } from '@/services/api';
import { EXPORT_TASK_STATUS } from '@/constants';
import ChangeRecordModal from './ChangeRecordModal';

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
  layout: 'vertical'
};
const formItemLayout = {
  labelCol: { span: 24 },
  wrapperCol: { span: 24 },
};

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const NOOP = () => {};
const { Option } = Select;
const { TextArea } = Input;


const CrowdBlackOrWhiteList = ({
  BlackOrWhiteListFormData,
  editModalVisible = false,
  onCancel = NOOP,
  onSubmit = NOOP,
  onInitialAction
}) => {
  const [form] = Form.useForm();
  const [recordModalVisible, setRecordModalVisible] = useState(false);
  const crowdId = BlackOrWhiteListFormData?.groupId;
  const black = BlackOrWhiteListFormData?.blackList;
  const white = BlackOrWhiteListFormData?.whiteList;

  // 监听表单字段变化，实时计算数量
  const blackListValue = Form.useWatch('blackList', form) || '';
  const whiteListValue = Form.useWatch('whiteList', form) || '';

  // 计算数量的辅助函数
  const getCount = (value) => {
    if (!value || !value.trim()) return 0;
    const arr = value.split(',').filter(item => item.trim());
    return arr.length;
  };

  // 去重处理函数
  const handleDeduplication = (value) => {
    if (!value || !value.trim()) return '';
    const arr = value.split(',')
      .map(item => item.trim())
      .filter(item => item)
      .filter((item, index, self) => self.indexOf(item) === index); // 去重
    return arr.join(',');
  };

  // 处理输入框变化
  const handleTextAreaChange = (fieldName, e) => {
    const value = e.target.value;
    const deduplicatedValue = handleDeduplication(value);

    // 如果去重后值发生变化，更新表单
    if (deduplicatedValue !== value) {
      form.setFieldsValue({
        [fieldName]: deduplicatedValue
      });
    }
  };

  useEffect(() => {
    form.setFieldsValue({
      blackList: black,
      whiteList: white,
    })
  }, [black, white, form])

  const onFinish = values => {
    const {blackList, whiteList} = values
    if(black === blackList && white === whiteList){
      message.info('黑白名单没有变化！');
      return;
    }
    // 都为空，则直接更新。
    if(blackList === '' && whiteList === ''){
      onSubmit(values);
      return;
    }
    const black_arr = blackList && blackList.split ? blackList.split(',') : [];
    const white_arr = whiteList && whiteList.split ? whiteList.split(','): [];

    // 找出重复的 id
    const duplicateIds = [];
    black_arr.forEach(ele => {
      if(white_arr.includes(ele)){
        duplicateIds.push(ele);
      }
    });

    // 如果有重复，显示具体重复的 id 并拦截提交
    if(duplicateIds.length > 0) {
      message.error(`黑名单和白名单的user_id出现重复：${duplicateIds.join(', ')}`);
      return;
    }

    onSubmit(values);
  };


  const validator = ({getFieldValue}) => ({
    validator(rule, value) {
      const reg = /^\d+(,\d+)*$/g;
      if(!value || reg.test(value)){
        return Promise.resolve();
      }
      return Promise.reject('只能包含数字和逗号哦，且开头和结尾都是数字！');
    },
  });

  return (
    <>
      <Drawer
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>黑白名单</span>
            <Button
              type="link"
              icon={<HistoryOutlined />}
              onClick={() => setRecordModalVisible(true)}
              style={{ padding: 0 }}
            >
              变更历史
            </Button>
          </div>
        }
        width={800}
        visible={editModalVisible}
        onClose={onCancel}
        footer={null}
      >
        <div style={{textAlign: 'center'}}>通过nick查询user_id的入口: <a target="_blank" href="https://itemtools.taobao.com/">https://itemtools.taobao.com/</a></div>
        <Form name="blackOrWhiteList" {...FORM_ITEM_LAYOUT} onFinish={onFinish} form={form}>
          <FormItem
            {...formItemLayout}
            label={`黑名单（${getCount(blackListValue)}个）`}
            name='blackList'
            rules={[ validator ]}
          >
            <TextArea
              placeholder="请输入user_id,请用 , 分隔"
              autoSize={{ minRows: 5, maxRows: 5 }}
              onBlur={(e) => handleTextAreaChange('blackList', e)}
            />
          </FormItem>

          <FormItem
            {...formItemLayout}
            name="whiteList"
            label={`白名单（${getCount(whiteListValue)}个）`}
            rules={[ validator ]}
          >
            <TextArea
              placeholder="请输入user_id,请用 , 分隔"
              autoSize={{ minRows: 5, maxRows: 5 }}
              onBlur={(e) => handleTextAreaChange('whiteList', e)}
            />
          </FormItem>
          <FormItem {...formItemLayout}>
            <div>1.黑白名单仅限用于测试，会影响人群所有渠道的在线匹配结果，<span style={{color: 'red'}}>上线前请一定删除；</span></div>
            <div>2.黑白名单配置后1分钟生效；</div>
            <div style={{color: 'red'}}>3.黑白名单只对当前人群有效，组合人群父人群不会查子人群配置的黑白名单，即子人群的黑白名单对父人群无效;</div>
          </FormItem>
          <Row gutter={24}>
            <Col span={4} />
            <Col span={14} style={{ textAlign: 'center' }}>
              <Button onClick={onCancel} style={{ marginRight: 15 }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
            </Col>
          </Row>
        </Form>
      </Drawer>

      <ChangeRecordModal
        visible={recordModalVisible}
        onCancel={() => setRecordModalVisible(false)}
        groupId={crowdId}
      />
    </>
  );
};

export default CrowdBlackOrWhiteList;
