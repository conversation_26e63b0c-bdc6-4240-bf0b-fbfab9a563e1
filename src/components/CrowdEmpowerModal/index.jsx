import React, { useState, useEffect } from 'react';
import { Modal, Select, Form, Row, Col, Button, Radio, DatePicker, message, Tag } from 'antd';
import dayjs from 'dayjs';
import debounce from 'lodash/debounce';
import get from 'lodash/get';
import { queryAccountInfos, authorize } from '@/services/api';
import { EXPORT_TASK_STATUS } from '@/constants';

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const FormItem = Form.Item;
const NOOP = () => { };
const { Option } = Select;

const CrowdEmpowerModal = ({
  empowerVisible,
  closeEmpowerModal = NOOP,
  crowdName,
  crowdId,
}) => {

  const [labelList, setLabelList] = useState([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (empowerVisible) {
      queryAccountInfos().then(res => {
        if (res?.success) {
          setLabelList(get(res,'data',[]))
        }
      })
    }
  },[empowerVisible])

  const [form] = Form.useForm();

  const onFinish = values => {
    setLoading(true)
    authorize({...values,crowdId}).then(res => {
      if (res.success) {
        message.success('授权成功')
        onCancel()
      } else {
        message.error(res.msg)
      }
      setLoading(false)
    })
  };


  const onCancel = () => {
    closeEmpowerModal()
    form.resetFields()
  }




  const title = `人群授权：${crowdName}(人群ID：${crowdId})`;
  return (
    <Modal title={title} width={800} visible={empowerVisible} onCancel={onCancel} footer={null} destroyOnClose>
      <Form {...FORM_ITEM_LAYOUT} form={form} onFinish={onFinish}>
        <FormItem name="account" label="人群授权" rules={[{ required: true, message: '请选择授权账号' }]}>
          <Select
            allowClear
            placeholder="请选择授权账号"
          >
            {labelList &&
              labelList.length > 0 &&
              labelList.map(l => {
                const { code, name } = l;
                return (
                  <Option title={name} key={code} value={code}>
                    {name}
                  </Option>
                );
              })}
          </Select>
        </FormItem>
        <Row gutter={24}>
          <Col span={4} />
          <Col span={14} style={{ textAlign: 'center' }}>
            <Button onClick={onCancel} style={{ marginRight: 15 }}>
              取消
            </Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              授权
            </Button>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default CrowdEmpowerModal;
