import React, { useState, useMemo } from 'react';
import { DownOutlined, PlusOutlined, UpOutlined } from '@ant-design/icons';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Table, Row, Col, Button, Select, Input, Radio, Checkbox } from 'antd';
import CreateModal from './createModal';
import styles from './index.less';

const FormItem = Form.Item;
const { Option } = Select;
const { TextArea } = Input;

const FORM_ITEM_LAYOUT = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

const DEFAULT_FORM_LAYOUT = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};

const getComponent = item => {
  let { component } = item;

  if (!component) {
    const { type, componentOptions, children } = item;
    switch (type) {
      case 'INPUT':
        component = <Input {...componentOptions} allowClear />;
        break;
      case 'TEXTAREA':
        component = <TextArea {...componentOptions} allowClear />;
        break;
      case 'SELECT':
        component = (
          <Select {...componentOptions} style={{ minWidth: 150 }}>
            {children &&
              children.length &&
              children.map(c => (
                <Option value={c.value} key={`${c.value}`}>
                  {c.name}
                </Option>
              ))}
          </Select>
        );
        break;
      case 'RADIO':
        component = (
          <Radio.Group>
            {children &&
              children.length &&
              children.map(c => (
                <Radio {...componentOptions} value={c.value} key={`${c.value}`}>
                  {c.name}
                </Radio>
              ))}
          </Radio.Group>
        );
        break;
      case 'CHECKBOX':
        component = (
          <Checkbox.Group>
            {children &&
              children.length &&
              children.map(c => (
                <Checkbox {...componentOptions} value={c.value} key={`${c.value}`}>
                  {c.name}
                </Checkbox>
              ))}
          </Checkbox.Group>
        );
        break;
      default:
        break;
    }
  }
  return component;
};

const SearchTable = ({
  form, // 表单对象
  columns = [], // table columns参数
  onSearch, // 查询
  onSubmit, // modal框表单提交事件
  onValuesChange, // modal框表单values改变事件
  onSelectedRowsChange, // 选择表格行改变事件
  modalVisible = false, // modal visible属性
  onModalVisible, // 控制modal
  onAction, // 表格批量操作事件
  searchFormItems = [], // 查询表单项
  createFormItems = [], // 新建表单项
  actionFormItems = [], // 操作表单项
  modalWidth, // modal宽度
  editFormData, // modal表单更新
  hideRowSelection, // 隐藏多选项
  ...others
}) => {
  const [expandForm, setExpandForm] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);

  const createDataSource = useMemo(
    () =>
      createFormItems.map(item => {
        const { field, label, isHidden, forItemOptions = {}, extra } = item;

        const ret = {
          title: label,
          dataKey: field,
          option: { ...forItemOptions },
          component: getComponent(item),
          formItemLayout: FORM_ITEM_LAYOUT,
          isHidden: !!isHidden,
          extra,
        };

        return ret;
      }),
    [createFormItems]
  );
  const { getFieldDecorator, getFieldsValue } = form;

  const rowSelection = {
    onChange: (selectedRowKeys, selectedRow) => {
      setSelectedRows(selectedRow);
      onSelectedRowsChange(selectedRowKeys, selectedRow);
    },
  };

  const getFormItems = formItems =>
    formItems &&
    formItems.length > 0 &&
    formItems.map(item => {
      const { label, field, formItemOptions = {}, extra } = item;

      const component = getComponent(item);

      return (
        component && (
          <Col md={8} sm={24} key={`${field}`}>
            <FormItem label={label || ''} extra={extra}>
              {getFieldDecorator(field, { ...formItemOptions })(component)}
            </FormItem>
          </Col>
        )
      );
    });
  const initSearchForm = () => {
    const formItems = expandForm ? searchFormItems : searchFormItems.slice(0, 2);
    return getFormItems(formItems);
  };

  const initActionForm = () => getFormItems(actionFormItems);

  // 搜索
  const handleSearch = () => {
    const fields = searchFormItems.map(i => i.field);
    const values = getFieldsValue(fields);
    onSearch(values);
  };

  // 表格操作
  const handleAction = () => {
    const fields = actionFormItems.map(i => i.field);

    const values = getFieldsValue(fields);
    onAction(values, selectedRows);
  };

  const toggleForm = () => {
    setExpandForm(!expandForm);
  };

  // 控制模态框
  const handleModalVisible = visible => {
    onModalVisible(visible);
  };

  return (
    <div className={styles.container}>
      {/* 查询表单 */}
      {searchFormItems && searchFormItems.length > 0 && (
        <Form layout="inline">
          <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
            {initSearchForm()}
            <Col md={8} sm={24}>
              <FormItem {...DEFAULT_FORM_LAYOUT}>
                <span className={styles.submitButtons}>
                  <Button type="primary" onClick={handleSearch}>
                    查询
                  </Button>
                  {searchFormItems.length > 2 &&
                    (!expandForm ? (
                      <a style={{ marginLeft: 8 }} onClick={toggleForm}>
                        展开 <DownOutlined />
                      </a>
                    ) : (
                      <a style={{ marginLeft: 8 }} onClick={toggleForm}>
                        收起 <UpOutlined />
                      </a>
                    ))}
                </span>
              </FormItem>
            </Col>
          </Row>
        </Form>
      )}

      {actionFormItems && actionFormItems.length > 0 && (
        <Form layout="inline">
          <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
            {initActionForm()}
            <Col md={8} sm={24}>
              <FormItem {...DEFAULT_FORM_LAYOUT}>
                <Button type="primary" onClick={handleAction}>
                  确定
                </Button>
              </FormItem>
            </Col>
          </Row>
        </Form>
      )}

      {createFormItems && createFormItems.length > 0 && (
        <div className={styles.tableListOperator} style={{ marginBottom: 20 }}>
          <Button icon={<PlusOutlined />} type="primary" onClick={() => handleModalVisible(true)}>
            新建
          </Button>
        </div>
      )}
      {hideRowSelection ? (
        <Table columns={columns} {...others} />
      ) : (
        <Table rowSelection={rowSelection} columns={columns} {...others} />
      )}

      {createFormItems && createFormItems.length > 0 && (
        <CreateModal
          modalWidth={modalWidth}
          dataSource={createDataSource}
          modalVisible={modalVisible}
          onValuesChange={onValuesChange}
          onSubmit={onSubmit}
          editFormData={editFormData}
          onCancel={() => handleModalVisible(false)}
        />
      )}
    </div>
  );
};

export default Form.create({
  onValuesChange: (props, values) =>
    props.onSearchValuesChange && props.onSearchValuesChange(values),
})(SearchTable);
