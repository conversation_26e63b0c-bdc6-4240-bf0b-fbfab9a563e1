import React from 'react'
import { Mo<PERSON>, <PERSON>, Col, Button } from 'antd'
import SubmitForm from '@/components/SubmitForm'

const noop = () => {}

const createModal = ({
  title = '',
  modalVisible = false,
  dataSource = [],
  onCancel = noop,
  onValuesChange = noop,
  onSubmit = noop,
  editFormData,
  modalWidth = 800,
}) => {
  const dataSetEditProps = {
    onValuesChange: values => onValuesChange(values),
    handleSubmit: values => onSubmit(values),
    dataSource,
    formData: editFormData,
    submit: {
      component: (
        <Row>
          <Col span={6} />
          <Col span={14} style={{ textAlign: 'center' }}>
            <Button onClick={() => onCancel()} style={{ marginRight: 15 }}>
              取消
            </Button>
            <Button type="primary" htmlType="submit">
              保存
            </Button>
          </Col>
        </Row>
      ),
    },
  }

  return (
    <Modal
      width={modalWidth}
      title={title}
      footer={null}
      visible={modalVisible}
      onCancel={onCancel}
    >
      <SubmitForm {...dataSetEditProps} />
    </Modal>
  )
}

export default createModal
