import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { Spin, Select, Avatar } from 'antd';
import { query } from '@/services/user';
import { debounce } from 'lodash';
import user from '@/models/user';

const { Option } = Select;

export default class GetUser extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      suggestList: [],
    };
  }

  onSearch = param => {
    this.setState({
      isLoading: true,
    });
    query(param)
      .then(({ data = [] }) => {
        const list = data.map(item => {
          return {
            empId: item.empEmployee.workNo,
            nickName: item.empEmployee.nickName,
            lastName: item.empEmployee.name,
          };
        });
        this.setState({
          suggestList: list,
          isLoading: false,
        });
      })
      .catch(() => {
        this.setState({
          suggestList: [],
          isLoading: false,
        });
      });
  };

  handleChange = (value) => {
    const { onChange, mode } = this.props;
    let v;

    if (mode === 'multiple') {
      if (value && Array.isArray(value)) {
        v = value.map(item => {
          const { empId, nickName } = JSON.parse(item);
          return {
            empId,
            nickName,
          };
        });
      } else {
        v = [];
      }
    } else {
      v = value ? JSON.parse(value) : null;
    }
    if (typeof onChange === 'function') {
      onChange(v);
    }
  };

  render() {
    const { isLoading, suggestList = [] } = this.state;
    //isAnalysis 是因为洞察分析单独处理，如果没有花名就传工号导致需要一个参数
    const { value, mode = 'default', style, placeholder, isAnalysis } = this.props;
    
    let newValue = undefined;

    if (mode === 'multiple') {
      newValue = (value || []).map((item) => ({
        label: item.nickName,
        key: JSON.stringify(item),
      }));
    } else {
      newValue = value ? {label: value.nickName, key: JSON.stringify(value)} : undefined;
    }

    return (
      <Select
        showSearch
        mode={mode}
        filterOption={false}
        value={newValue}
        notFoundContent={isLoading ? <Spin size="small" /> : null}
        onSearch={debounce(this.onSearch, 500)}
        onChange={this.handleChange}
        style={style}
        placeholder={placeholder || "请输入工号或花名"}
        allowClear
      >
        {suggestList.map(d => {
          const { empId, nickName, lastName } = d;
          let newValue = { empId, nickName: nickName || lastName };
          if (isAnalysis) {
            newValue = { empId, nickName };
          }
          return (
            <Option key={empId} value={JSON.stringify(newValue)}>
              <Avatar src={`https://work.alibaba-inc.com/photo/${empId}.220x220.jpg`} />
              &nbsp;
              {`${nickName || lastName}(${lastName})`}
              &nbsp;
              {empId}
            </Option>
          );
        })}
      </Select>
    );
  }
}

