import React from 'react';
import { Form } from '@ant-design/compatible';
import { Checkbox, Modal, Button, Input, Card, Row, Col } from 'antd';

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

const { TextArea } = Input;

@Form.create()
class SelectOperation extends React.PureComponent {
  state = {
    // checked: false,
    // checkedTwo: false,
    visible: false,
    application: null,
  }

  onCheckChange = (type,record) => {
    const { onCheckableChange} = this.props
    onCheckableChange(type ,record)
  }

  onCheckChangeTwo = (type,record) => {
    const { addCount} = this.props
    addCount(type ,record)
  }

  handleOk = e => {
    const {
      form: { validateFields },
      dispatch,
    } = this.props;

    validateFields((err, values) => {
      if (!err) {
        console.log('values===',values)
        this.setState({
          visible: false,
        });
      }
    })
  };

  handleCancel = e => {
    this.setState({
      visible: false,
    });
  };

  showModal = () => {
    this.setState({
      visible: true,
    });
  };

  render () {
    const {
      record,
      selectedTags,
      count,
      form: { getFieldDecorator }
    } = this.props
    const { checked,visible,application,checkedTwo} = this.state
    const {usePermission = '', name = ''} = record

    const resultOne = selectedTags.some(item => {
      if (item.id === record.id) {
        return true
      }
    })

    const resultTwo = count.some(item => {
      if (item.id === record.id) {
        return true
      }
    })

    return (
      <>
        {(usePermission && !resultOne) && <a type='link' onClick = {() => this.onCheckChange(true ,record)}>选择</a>}
        {(usePermission && resultOne) && <Checkbox onChange={() => this.onCheckChange(false ,record)} defaultChecked>已选择</Checkbox>}
        {(!usePermission && !resultTwo) && <a type='link' onClick={() => this.onCheckChangeTwo(true ,record)}>申请权限</a>}
        {(!usePermission && resultTwo) && <Checkbox onChange={() => this.onCheckChangeTwo(false ,record)} defaultChecked>已添加</Checkbox>}
        {/* 申请权限 */}
        {/* <Modal
          title={name}
          visible={visible}
          footer={null}
          onCancel={this.handleCancel} 
        >
           <Card bordered={false}>
            <Form {...FORM_ITEM_LAYOUT}>
              <Form.Item label="标签名称">
                <span>{name}</span>
              </Form.Item>
              <Form.Item label="申请原因">
                {getFieldDecorator('application', {
                  initialValue: application,
                  rules: [
                    {
                      required: true,
                      message: '请输入申请原因',
                    }
                  ],
                })(<TextArea/>)}
              </Form.Item>
              <a style = {{color:'red',paddingLeft:'30px',marginBottom:'30px',display:'block'}} href="https://bpms.alibaba-inc.com/portal/home#/fromMe" target="_blank">注：申请进度请到阿里内外我的任务中查看</a>
              <Row gutter={24}>
                <Col span={4} />
                <Col span={14} style={{ textAlign: 'center' }}>
                  <Button onClick={this.handleCancel} style={{ marginRight: 15 }}>
                    取消
                  </Button>
                  <Button type="primary" onClick={this.handleOk}>
                    保存
                  </Button>
                </Col>
              </Row>
            </Form>
           </Card>
        </Modal> */}
      </>
    )
    
  }
}

export default SelectOperation