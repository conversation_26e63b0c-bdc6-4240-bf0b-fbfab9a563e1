import React from 'react';
import { Tag } from 'antd';

import {queryBuildTree } from '@/services/api';

const { CheckableTag } = Tag;

class NewCascader extends React.Component {
  state = {
    selectedTags: [],
    treeData:[],
    tagsData:[],
    showTags:[], // 展示的数据
  };

  componentDidMount() {
    this.fetchTreeData()
  }

  componentWillReceiveProps(nextProps) {
    const { visible } = this.props
    if (this.props !== nextProps) {
      if (!visible) {
        this.setState({
          selectedTags: [],
          treeData:[],
          tagsData:[],
          showTags:[],
        })
        this.fetchTreeData()
      }
    }
  }


  fetchTreeData = () => {
    queryBuildTree({ bizEntityName: 'TAOBAO_USER' }).then(res => {
      if (!res.success) {
        message.error(res.msg);
        return;
      }

      if (res.data && res.data.root && res.data.root.children) {
        res.data.root.children.unshift({
          id: -1,
          name: '全部'
        })
        this.setState({
          treeData: (res.data && res.data.root && res.data.root.children) || [],
          showTags: [res.data.root.children]
        });
      }
    });
  };

  handleChange(tag, levelIndex,tagIndex) {
    const {showTags,treeData,selectedTags} = this.state;
    const {onChange} = this.props
    if (selectedTags[levelIndex]) {
      let newSelectedTags =  selectedTags.slice(0,levelIndex)
      newSelectedTags.push(tag)
      this.setState({
        selectedTags: newSelectedTags
      })
    } else {
      let checkedTags = selectedTags || []
      checkedTags.splice(levelIndex,0,tag)
      this.setState({
        selectedTags: checkedTags
      });
    }
    
    if (tag.children && tag.children.length > 0) {
      if (showTags[levelIndex + 1]) {
        let newShowTags =  showTags.slice(0,levelIndex + 1)
        this.setState({
          showTags: [...newShowTags,tag.children]
        });
      } else {
        this.setState({
          showTags: [...showTags,tag.children]
        });
      }
    } else {
      if (tag.id === -1) {
        let newShowTags =  showTags.slice(0,levelIndex + 1)
        // let newAllTags = []
        // treeData.filter(e => {
        //   return e.children && e.children.length > 0
        // }).forEach(elm => {
        //   newAllTags.push(...elm.children)
        // })
        onChange(tag.id)
        this.setState({
          showTags: [...newShowTags]
        });
      }
      onChange(tag.id)
    }



  }

  renderTag = (tagData)=>{
    return <CheckableTag
    key={tagData.value}
    checked={selectedTags.indexOf(tagData.value) > -1}
    onChange={(checked) => this.handleChange(tagData, checked)}
  >
    {tagData.label}
  </CheckableTag>
  }

  render() {
    const {tagsData ,selectedTags,showTags} = this.state;
    return (
      <>
        {showTags.map((tagLevel,levelIndex) => {
          return <div key = {levelIndex} style={{display: 'flex', flexDirection: 'row',marginBottom: 8}}>
              {levelIndex=== 0 ? <span style={{ marginRight: 8 ,whiteSpace: 'nowrap'}}>标签分类:</span> : <span style={{ marginRight: 8 }}>子分类:</span>}
              <div>
                {tagLevel.map((tag,tagIndex)=>{
                  return <CheckableTag
                  key={tag.id}
                  checked={selectedTags.indexOf(tag) > -1}
                  onChange={(checked) => this.handleChange(tag, levelIndex,tagIndex)}
                  key = {tagIndex}
                >
                  {tag.name}
                </CheckableTag>
                })}
              </div>
            </div>
        })}
      </>
    )
  }
}

export default NewCascader