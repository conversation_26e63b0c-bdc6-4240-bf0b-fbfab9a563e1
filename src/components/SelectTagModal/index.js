import React from 'react';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Modal, Card, Input, message, Spin, Popover, Table, Tag ,Badge, Button } from 'antd';
import { queryLabel, queryBuildTree, grant } from '@/services/api';
// import MyCascader from '@/components/Cascader';
import { history } from 'umi';
import NewCascader from './NewCascader';
import MyCheckableTag from '@/components/CheckableTag';
import MyIcon from '@/components/MyIcon';
import SelectOperation from './SelectOperation'
import styles from './index.less';
import { log } from 'lodash-decorators/utils';
import { LABEL_DATA_SOURCE } from '@/pages/Knowledge/CreateCrowdTag/common/constants'
import { get } from 'lodash'

const NOOP = () => {};
const { Search } = Input;

class SelectTagModal extends React.PureComponent {
  state = {
    labels: [],
    cascaderValue: undefined,
    propertyDescription: undefined,
    selectedTags: [],
    loading: false,
    treeData: [],
    total: undefined, 
    pageNum: undefined, 
    pageSize: undefined,
    count: [],
    checkVisible: false
  };

  componentDidMount() {
    const { showAll = true, profileCode } = this.props;
    // this.fetchTreeData();
    if (showAll) {
      this.request({
        name: '',
        pageNum: 1,
        pageSize: 10,
        profileCode
      });
    }
  }

  componentWillReceiveProps(nextProps) {
    if (this.props !== nextProps) {
      const { showAll = true , visible, profileCode} = nextProps;
      // 当添加标签页面打开时才会请求
      if (showAll && visible) {
        this.request({
          name: '',
          profileCode
        });
      }
    }
  }

  request = params => {
    this.setState({
      loading: true,
    });
    const { cascaderValue, propertyDescription, pageNum, pageSize } = this.state;
    const { profileCode } = this.props
    queryLabel({
      deleted: 0,
      bizEntityName: 'TAOBAO_USER',
      status: 'ACTIVATE',
      bizRegionCode: 'public_region',
      pageNum: pageNum,
      pageSize: pageSize,
      // categoryId: cascaderValue && cascaderValue[cascaderValue.length - 1],
      categoryId: cascaderValue,
      name:propertyDescription,
      profileCode,
      ...params,
    })
      .then(res => {
        if (!res.success) {
          message.error(res.msg);
          return;
        }

        this.setState({
          labels: res.data && res.data.rows,
          // labels: res.data && res.data.list,
          loading: false,
          total: res.data.totalNum,
          pageSize: res.data.pageSize,
          pageNum: res.data.pageNum
        });
      })
      .catch(() => {
        message.error('请求失败');
      });
  };

  onCascaderChange = value => {
    const { profileCode } = this.props
    const categoryId =  value === -1 ? undefined : value
    this.setState({
      cascaderValue: categoryId,
    });
    this.request({
      categoryId,
      pageNum: 1,
      profileCode
    });
  };
  
  onSearch = value => {
    const { profileCode } = this.props
    this.setState({
      propertyDescription: value,
    });

    this.request({
      name: value,
      pageNum: 1,
      profileCode
    });
  };

  onCheckableChange = (checked, tag) => {
    
    this.setState(({ selectedTags }) => {
      if (checked) {
        selectedTags.push(tag);
      } else {
        const index = selectedTags.findIndex(item => item.id === tag.id);
        selectedTags.splice(index, 1);
      }

      return {
        selectedTags: selectedTags.slice(),
      };
    });
  };

  onOk = () => {
    const { onOk = NOOP, isCircle } = this.props;
    const {count} = this.state

    if (count.length > 0) {
      message.error('选择了无权限的标签，请申请后重试。');
      return
    }

    if (isCircle && this.state.selectedTags.length > 1) {
      message.error('跃迁圈人标签只能选取一个标签');
      return
    }

    const res = onOk(this.state.selectedTags);

    if (res) {
      this.setState({
        labels: [],
        cascaderValue: undefined,
        searchValue: undefined,
        selectedTags: [],
        count: [],
        pageNum: 1
      });
    }
  };

  onSearchChange = e => {
    const { value } = e.target;
    this.setState({
      searchValue: value,
    });
  };

  clear = () => {
    this.setState({
      searchValue: undefined,
      cascaderValue: undefined,
      selectedTags: [],
      count: [],
      labels: [],
      pageNum: 1
    });
  };

  onCancel = () => {
    const { onCancel = NOOP } = this.props;
    this.clear();
    onCancel();
  };

  addCount = (checked, record) => {
    this.setState(({ count }) => {
      if (checked) {
        count.push(record);
      } else {
        const index = count.findIndex(item => item.id === record.id);
        count.splice(index, 1);
      }

      return {
        count: count.slice(),
      };
    });
  }

  handleTableChange = pagination => {
    this.request({
      pageNum: pagination?.current
    });
  };


  checkShowModal = () => {
    this.setState({
      checkVisible: true,
    });
  };

  checkHandleOk = () => {
    const {count } = this.state
    const _pnames = count.map(item => `zhuge_label_${item.code}_permission`).toString()

    // 判断是日常环境还是线上
    if (window.location.hostname.includes('.net') || window.location.hostname.includes('localhost')) {
      window.open(`http://acl-test.alibaba-inc.com/apply/cart/detail.htm?pnames=${_pnames}`,"_blank")
    } else if (window.location.hostname.includes('pre')) {
      window.open(`https://acl.alibaba-inc.com/apply/cart/detail.htm?pnames=${_pnames}`,"_blank")
    } else {
      window.open(`https://acl.alibaba-inc.com/apply/cart/detail.htm?pnames=${_pnames}`,"_blank")
    }
    this.setState({
      checkVisible: false,
    });
  };

  checkHandleCancel = () => {
    this.setState({
      checkVisible: false,
    });
  };

  render() {
    const { visible } = this.props;
    const { cascaderValue, labels = [], searchValue, loading, treeData ,total ,pageNum ,pageSize,count,selectedTags,checkVisible} = this.state;
    const showTotal = ((total) => {
      return `总共 ${total} 个标签`;
    })

    const pagination = {
      showTotal,
      current: pageNum,
      total
    }
    const columns = [
      {
        title: '标签名称',
        dataIndex: 'name',
        key: 'name',
        align: 'center',
        width:200,
        render: (text, record) => { 
          let tags = []
          if (record?.extInfo && record?.extInfo.tags && record?.extInfo.tags.length > 0) {
            tags = record?.extInfo.tags
          }
          return (
            <>
              {tags.includes('new') && (
                <MyIcon style={{ fontSize: 14, color: '#f5222d' }} type="icon-new" />
              )}
              <Popover content={text}>{text}</Popover>
            </>
          )
        },
      },
      {
        title: '标签描述',
        dataIndex: 'description',
        key: 'description',
        align: 'center',
        render: text => <Popover content={text}>{text}</Popover>,
      },
      {
        title: '最新数据日期',
        dataIndex: 'dataUpdateTime',
        key: 'dataUpdateTime',
        render: (text, record) => { 
          return (
            <Popover content={(!record.dataUpdateTime || record.dataUpdateTime === -28800000) ? '无' : dayjs(record.dataUpdateTime).format('YYYY-MM-DD HH:mm:ss')}>{(!record.dataUpdateTime || record.dataUpdateTime === -28800000) ? '无' : dayjs(record.dataUpdateTime).format('YYYY-MM-DD HH:mm:ss')}</Popover>
          )
        },
      },
      {
        title: '创建人',
        dataIndex: 'creator',
        key: 'creator',
        align: 'center',
        render: u => (
          <Tag color="green" key={u.empId}>
            {u.nickName}
          </Tag>
        ),
      },
      {
        title: '安全等级',
        dataIndex: 'securityLevel',
        width: 100,
        key: 'securityLevel',
        align: 'center',
        render: (text, record) => {
          return (
            <div>{`L${record.securityLevel}`}</div>
          )
        }
      },
      {
        title: <span>
              收费&nbsp;
              <Popover content={<a href='https://aliyuque.antfin.com/dl7v63/zwzgqr/subnu53efxtozyro?singleDoc# ' target='_blank'>查看收费规则</a>}>
                <QuestionCircleOutlined />
              </Popover>
            </span>,
        dataIndex: 'isPay',
        width: 80,
        key: 'isPay',
        render: (text, record) => {
          return (
            <div>{record && record.extInfo && record.extInfo.isPay ? '付费' : '免费'}</div>
          )
        }
      },
      {
        title: '标签来源',
        dataIndex: 'dataSource',
        key: 'dataSource',
        align: 'center',
        width: 100,
        render: (text, record) => { 
          const dataSource = get(record, 'extInfo.dataSourceConfig.dataSource', '')
          return dataSource ? LABEL_DATA_SOURCE[dataSource] : undefined
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        align: 'center',
        width: 140,
        render: (text, record) => { 
          return (
            <SelectOperation
              record = {record}
              selectedTags = {selectedTags}
              count = {count}
              addCount = {this.addCount}
              onCheckableChange = {this.onCheckableChange}
            />
          )
        },
      }
    ]

    return (
      <>
        <Modal
          visible={visible}
          width={'75vw'}
          style={{top: 0}}
          title="添加标签"
          onCancel={this.onCancel}
          onOk={this.onOk}
          destroyOnClose
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              flexDirection: 'row',
              justifyContent: 'space-between',
              marginBottom: 10,
            }}
          >
            <Search
              placeholder="按标签中文名搜索，不填默认全部"
              onSearch={this.onSearch}
              style={{ width: '45%' }}
              value={searchValue}
              onChange={this.onSearchChange}
              loading={loading}
            />

            <Badge count={count.length}>
              <Button type="primary" onClick = {() => this.checkShowModal()}>批量筛选列表</Button>
            </Badge>

            {/* <MyCascader
              style={{ width: '50%' }}
              treeData={treeData}
              onChange={this.onCascaderChange}
              value={cascaderValue}
            /> */}
          </div>
          <NewCascader
            onChange={this.onCascaderChange}
            visible = {visible}
          />
          {/* 老标签样式 */}
          {/* <Spin spinning={loading}>
            <Card>
              {labels &&
                labels.length > 0 &&
                labels.map(l => {
                  // const { propertyName, propertyDescription, id, detail, extInfo = {} } = l;
                  const { code, name, id, description, extInfo = {} } = l;
                  let { tags = [] } = extInfo;
                  if (!tags) {
                    tags = []
                  }
                  return (
                    <MyCheckableTag
                      key={`label_${id}`}
                      handleChange={checked => this.onCheckableChange(checked, l)}
                    >
                      {tags.includes('new') && (
                        <MyIcon style={{ fontSize: 14, color: '#f5222d' }} type="iconnew" />
                      )}
                      <Popover
                        content={
                          <div style={{maxWidth: '500px'}}>
                            <div>
                              <span style={{ fontWeight: 500 }}>标签名称：</span>
                              <span>{name}</span>
                            </div>
                            <div>
                              <span style={{ fontWeight: 500 }}>英文名称：</span>
                              <span>{code}</span>
                            </div>
                            <div>
                              <span style={{ fontWeight: 500 }}>描述：</span>
                              <p>{description}</p>
                            </div>
                            {name && name.includes('实时')? 
                            <Popover content='实时标签质量分建设中，敬请期待'>
                              <span style={{textDecoration: 'underline', color: 'gray'}}>标签质量分</span>
                            </Popover> 
                            : <div>
                                <a style={{textDecoration: 'underline'}} target="_blank" href={`https://go.alibaba-inc.com/trip_flywheel#/label_health_score?labelName=${code || ''}`}>标签质量分</a>
                              </div>}
                          </div>
                        }
                      >
                        <QuestionCircleOutlined />
                      </Popover>
                      <span style={{ marginLeft: 5 }}>{name}</span>
                    </MyCheckableTag>
                  );
                })}
            </Card>
          </Spin> */}
          <Card
            className={styles.container}
          >
            <Table
              loading={loading}
              rowKey={record => record.id}
              columns={columns}
              dataSource={labels}
              pagination={pagination}
              onChange={this.handleTableChange}
            />
          </Card>
        </Modal>
        <Modal
          title="批量申请标签列表"
          visible={checkVisible}
          okText='去申请'
          onOk={this.checkHandleOk}
          onCancel={this.checkHandleCancel}
        >
          {count &&
            count.length > 0 &&
            count.map(l => {
              return (
                <Tag>{l.name}</Tag>
              )
            })
          }
          <div><a style = {{color:'red'}} href="https://bpms.alibaba-inc.com/portal/home#/fromMe" target="_blank">注：申请进度请到阿里内外我的任务中查看</a></div>
        </Modal>
      </>
    );
  }
}

export default SelectTagModal;
