import React, { PureComponent } from 'react';
import classNames from 'classnames';
// import Icon from '@ant-design/icons';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Menu } from 'antd';
import { Link } from 'umi';
import { isUrl } from '@/utils/utils';
import IconFont from '@/components/IconFont';
import { urlToList } from '../_utils/pathTools';
import { getMenuMatches } from './SiderMenuUtils';
import styles from './index.less';
import AiAssistantSvg from './AiAssistant';
import { history } from 'umi';

const { SubMenu } = Menu;

// Allow menu.js config icon as string or ReactNode
//   icon: 'setting',
//   icon: 'icon-geren' #For Iconfont ,
//   icon: 'http://demo.com/icon.png',
const getIcon = icon => {
  if (typeof icon === 'string') {
    if (isUrl(icon)) {
      return <img src={icon} alt="icon" className={styles.icon} />;
    }
    if (icon.startsWith('icon-')) {
      return <IconFont type={icon} className={styles.iconFont} style={{ fontSize: 24 }} />;
    }
    return <LegacyIcon type={icon} style={{ marginRight: 10 }} />;
  }
  return icon;
};

export default class BaseMenu extends PureComponent {
  /**
   * 获得菜单子节点
   * @memberof SiderMenu
   */
  getNavMenuItems = (menusData, trueInMenu = false) => {
    if (!menusData) {
      return [];
    }
    return menusData
      .filter(item => item.name && !item.hideInMenu)
      .map(item => {
        if (!item.name || item.isHidden) {
          return null;
        }
        return this.getSubMenuOrItem(item, trueInMenu);
      })
      .filter(item => item);
  };

  // Get the currently selected menu
  getSelectedMenuKeys = pathname => {
    const { flatMenuKeys } = this.props;
    return urlToList(pathname).map(itemPath => getMenuMatches(flatMenuKeys, itemPath).pop());
  };

  /**
   * get SubMenu or Item
   */
  getSubMenuOrItem = (item, trueInMenu) => {
    // doc: add hideChildrenInMenu
    if (item.children && !item.hideChildrenInMenu && item.children.some(child => child.name)) {
      const { name } = item;
      return (
        <SubMenu
          title={
            item.icon ? (
              <span>
                {getIcon(item.icon)}
                <span>{name}</span>
              </span>
            ) : (
              name
            )
          }
          key={item.path}
        >
          {this.getNavMenuItems(item.children)}
        </SubMenu>
      );
    }
    if (trueInMenu && item.name === 'AI助手') {
      return (
        <Menu.Item
          key={item.path}
          onClick={() => {
            history.push(item.path);
          }}
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <AiAssistantSvg
            text={item.name}
            alwaysHover={window.location.hash.includes('ai-assistant')}
          />
        </Menu.Item>
      );
    }
    return <Menu.Item key={item.path}>{this.getMenuItemPath(item)}</Menu.Item>;
  };

  /**
   * 判断是否是http链接.返回 Link 或 a
   * Judge whether it is http link.return a or Link
   * @memberof SiderMenu
   */
  getMenuItemPath = item => {
    const { name } = item;
    const itemPath = this.conversionPath(item.path);
    const icon = getIcon(item.icon);
    const { target } = item;
    // Is it a http link
    if (/^https?:\/\//.test(itemPath)) {
      return (
        <a href={itemPath} target={target}>
          {icon}
          <span>{name}</span>
        </a>
      );
    }
    const { location, isMobile, onCollapse } = this.props;
    return (
      <Link
        to={itemPath}
        target={target}
        replace={itemPath === location.pathname}
        onClick={
          isMobile
            ? () => {
                onCollapse(true);
              }
            : () => {
                // 判断是否点击洞察分析路由，是的话重新赋值iframe的链接，重新加载iframe
                if (itemPath && itemPath.includes('insight-analysis')) {
                  const iFrame = document.getElementById('picassoIframe');
                  if (iFrame) {
                    iFrame.src = iFrame.src;
                  }
                }
              }
        }
      >
        {icon}
        {
          <span
            style={{
              marginInlineStart: 4,
            }}
          >
            {name}
          </span>
        }
      </Link>
    );
  };

  conversionPath = path => {
    if (path && path.indexOf('http') === 0) {
      return path;
    }
    return `/${path || ''}`.replace(/\/+/g, '/');
  };

  getPopupContainer = (fixedHeader, layout) => {
    if (fixedHeader && layout === 'topmenu') {
      return this.wrap;
    }
    return document.body;
  };

  getRef = ref => {
    this.wrap = ref;
  };

  render() {
    const {
      openKeys,
      theme,
      mode,
      location: { pathname },
      className,
      collapsed,
      fixedHeader,
      layout,
    } = this.props;

    // if pathname can't match, use the nearest parent's key
    let selectedKeys = this.getSelectedMenuKeys(pathname);
    if (!selectedKeys.length && openKeys) {
      selectedKeys = [openKeys[openKeys.length - 1]];
    }
    let props = {};
    if (openKeys && !collapsed) {
      props = {
        openKeys: openKeys.length === 0 ? [...selectedKeys] : openKeys,
      };
    }
    const { handleOpenChange, style, menuData } = this.props;
    const cls = classNames(className, {
      'top-nav-menu': mode === 'horizontal',
    });

    return (
      <>
        <Menu
          key="Menu"
          mode={mode}
          theme={theme}
          onOpenChange={handleOpenChange}
          selectedKeys={selectedKeys}
          style={style}
          className={cls}
          {...props}
          getPopupContainer={() => this.getPopupContainer(fixedHeader, layout)}
        >
          {this.getNavMenuItems(menuData, true)}
        </Menu>
        <div ref={this.getRef} />
      </>
    );
  }
}
