
@nav-header-height: 64px;

.logo {
  position: relative;
  height: @nav-header-height;
  overflow: hidden;
  line-height: @nav-header-height;
  background: #002140;
  transition: all 0.3s;
  img {
    display: inline-block;
    height: 32px;
    vertical-align: middle;
  }
  h1 {
    display: inline-block;
    margin: 0 0 0 12px;
    color: var(--primary-color);
    font-weight: 600;
    font-size: 20px;
    font-family: Avenir, 'Helvetica Neue', Arial, Helvetica, sans-serif;
    vertical-align: middle;
  }
}
.sider {
  position: relative;
  z-index: 10;
  min-height: 100vh;
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
  &.fixSiderBar {
    position: fixed;
    top: 0;
    left: 0;
    box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
    :global {
      .ant-menu-root {
        height: ~'calc(100vh - @{nav-header-height})';
        overflow-y: auto;
      }
      .ant-menu-inline {
        border-right: 0;
        .ant-menu-item,
        .ant-menu-submenu-title {
          width: 100%;
        }
      }
    }
  }
  &.light {
    background-color: white;
    box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
    .logo {
      background: white;
      box-shadow: 1px 1px 0 0 var(--border-color-split);
      h1 {
        color: var(--primary-color);
      }
    }
    :global(.ant-menu-light) {
      border-right-color: transparent;
    }
  }
}

.icon {
  width: 14px;
  vertical-align: baseline;
}

.iconFont {
  svg {
    height: 16px;
  }
}

.aiAssistant:hover{
  cursor: pointer;
}

:global {
  .top-nav-menu li.ant-menu-item {
    height: @nav-header-height;
    line-height: @nav-header-height;
  }
  .drawer .drawer-content {
    background: #001529;
  }
  .ant-menu-inline-collapsed {
    & > .ant-menu-item .sider-menu-item-img + span,
    &
      > .ant-menu-item-group
      > .ant-menu-item-group-list
      > .ant-menu-item
      .sider-menu-item-img
      + span,
    & > .ant-menu-submenu > .ant-menu-submenu-title .sider-menu-item-img + span {
      display: inline-block;
      max-width: 0;
      opacity: 0;
    }
  }
  .ant-menu-item .sider-menu-item-img + span,
  .ant-menu-submenu-title .sider-menu-item-img + span {
    opacity: 1;
    transition: opacity 0.3s var(--ease-in-out), width 0.3s var(--ease-in-out);
  }
  .ant-drawer-left {
    .ant-drawer-body {
      padding: 0;
    }
  }
}

.ai-logo {
  display: block;
  transition: all 0.3s ease;
}

.logo-element {
  transition: all 0.3s ease;
}

/* 悬停效果 */
.ai-logo:hover .logo-element,
.ai-logo.hover-active .logo-element {
  filter: brightness(1.4) contrast(1.2);
}

.ai-logo:hover .star-element,
.ai-logo.hover-active .star-element {
  filter: url(#starGlow) brightness(1.5);
}

.ai-logo:hover .text-element,
.ai-logo.hover-active .text-element {
  filter: url(#textGlow) brightness(1.4);
}

.ai-logo:hover .ai-text,
.ai-logo.hover-active .ai-text {
  filter: url(#aiShadow) brightness(1.4) drop-shadow(0 0 3px rgba(102, 181, 255, 0.8));
}

.ai-logo:hover .box-element,
.ai-logo.hover-active .box-element {
  stroke-width: 2.5;
}

@media (max-width: 576px) {
  .ai-logo {
    width: 100%;
    height: auto;
  }
}