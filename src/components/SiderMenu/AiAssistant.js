import React from 'react';
import './index.less'



const AiAssistantSvg = (props) => {
    const {
  text = 'AI助手',
  width = 80,
  height = 40,
  className = '',
  alwaysHover = false,
} = props
  // 将文本分解为AI和剩余部分
  const aiPart = text.startsWith('AI') ? 'AI' : text.substring(0, 2);
  const remainingText = text.startsWith('AI') ? text.substring(2) : text.substring(2);
  
  // 检查是否有"圈"字需要放入框中
  const hasBoxChar = remainingText.includes('圈');
  const textParts = remainingText.split('');
  
  // 根据alwaysHover属性决定是否添加hover-active类
  const hoverClass = alwaysHover ? 'hover-active' : '';
  
  return (
    <svg 
      width={width} 
      height={height} 
      viewBox="0 0 80 40" 
      xmlns="http://www.w3.org/2000/svg"
      className={`ai-logo ${className} ${hoverClass}`}
    >
      {/* 定义渐变和滤镜 */}
      <defs>
        <linearGradient id="starGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#a566ff" />
          <stop offset="40%" stopColor="#7a89ff" />
          <stop offset="70%" stopColor="#4da6f5" />
          <stop offset="100%" stopColor="#1b7dcf" />
        </linearGradient>
        
        <linearGradient id="textGradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" stopColor="#a566ff" />
          <stop offset="25%" stopColor="#8cc9ff" />
          <stop offset="50%" stopColor="#4da6f5" />
          <stop offset="75%" stopColor="#3a9eef" />
          <stop offset="100%" stopColor="#1b7dcf" />
        </linearGradient>
        
        <linearGradient id="boxGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#8cc9ff" />
          <stop offset="50%" stopColor="#4da6f5" />
          <stop offset="100%" stopColor="#1b7dcf" />
        </linearGradient>
        
        <filter id="aiShadow" x="-30%" y="-30%" width="160%" height="160%">
          <feGaussianBlur stdDeviation="1.2" result="blur" />
          <feFlood floodColor="#66b5ff" floodOpacity="0.8" result="color"/>
          <feComposite in="color" in2="blur" operator="in" result="shadow"/>
          <feComposite in="SourceGraphic" in2="shadow" operator="over"/>
        </filter>
        
        <filter id="textGlow" x="-20%" y="-20%" width="140%" height="140%">
          <feGaussianBlur stdDeviation="0.5" result="blur" />
          <feFlood floodColor="#66b5ff" floodOpacity="0.5" result="color"/>
          <feComposite in="color" in2="blur" operator="in" result="glow"/>
          <feComposite in="SourceGraphic" in2="glow" operator="over"/>
        </filter>
        
        <filter id="starGlow" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur stdDeviation="0.8" result="blur" />
          <feFlood floodColor="#a566ff" floodOpacity="0.9" result="glowColor"/>
          <feComposite in="glowColor" in2="blur" operator="in" result="softGlow"/>
          <feComposite in="SourceGraphic" in2="softGlow" operator="over"/>
        </filter>
        
        {/* 增强的悬停滤镜 */}
        <filter id="enhancedGlow" x="-30%" y="-30%" width="160%" height="160%">
          <feGaussianBlur stdDeviation="2" result="blur" />
          <feFlood floodColor="#a566ff" floodOpacity="1" result="glowColor"/>
          <feComposite in="glowColor" in2="blur" operator="in" result="softGlow"/>
          <feComposite in="SourceGraphic" in2="softGlow" operator="over"/>
          <feComponentTransfer>
            <feFuncR type="linear" slope="1.4" />
            <feFuncG type="linear" slope="1.4" />
            <feFuncB type="linear" slope="1.4" />
          </feComponentTransfer>
        </filter>
      </defs>
      
      {/* 星星装饰 */}
      <g className="logo-element">
        <path 
          d="M14,8 L15.5,11 L19,11.5 L16.5,14 L17,17.5 L14,15.8 L11,17.5 L11.5,14 L9,11.5 L12.5,11 Z" 
          fill="url(#starGradient)" 
          filter="url(#starGlow)" 
          className="star-element"
        />
        <path 
          d="M8,12 L9,14 L11,14.3 L9.5,16 L10,18 L8,17 L6,18 L6.5,16 L5,14.3 L7,14 Z" 
          fill="url(#starGradient)" 
          filter="url(#starGlow)" 
          className="star-element"
        />
      </g>
      
      {/* AI文字 - 带阴影 */}
      <text 
        x="22" 
        y="25" 
        fontSize="16" 
        fontWeight="bold" 
        fill="url(#textGradient)" 
        filter="url(#aiShadow)"
        className="logo-element ai-text"
      >
        {aiPart}
      </text>
      
      {/* 渲染剩余文字，如果是"圈"字则带框 */}
      {textParts.map((char, index) => {
        const xPosition = 42 + index * 20;
        
        if (char === '圈') {
          return (
            <g key={index} className="logo-element">
              <rect 
                x={xPosition - 7} 
                y="10" 
                width="15" 
                height="15" 
                rx="2" 
                stroke="url(#boxGradient)" 
                strokeWidth="1.5" 
                fill="none" 
                filter="url(#textGlow)"
                className="box-element"
              />
              <text 
                x={xPosition} 
                y="22" 
                fontSize="12" 
                fontWeight="bold" 
                fill="url(#textGradient)" 
                textAnchor="middle"
                filter="url(#textGlow)"
                className="text-element"
              >
                {char}
              </text>
            </g>
          );
        } else {
          return (
            <text 
              key={index}
              x={xPosition} 
              y="25" 
              fontSize="16" 
              fontWeight="bold" 
              fill="url(#textGradient)" 
              filter="url(#textGlow)"
              className="logo-element text-element"
            >
              {char}
            </text>
          );
        }
      })}
    </svg>
  );
};

export default AiAssistantSvg;
