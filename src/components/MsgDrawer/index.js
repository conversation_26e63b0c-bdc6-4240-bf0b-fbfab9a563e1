import React from 'react';
import { Drawer, Row, Col } from 'antd';
import styles from './index.less';

const NOOP = () => {};

const MsgDrawer = ({
  title = '',
  width = 360,
  visible = false,
  content = [],
  placement = 'right',
  onClose = NOOP,
}) => (
  <Drawer
    width={width}
    title={title}
    placement={placement}
    closable={false}
    onClose={onClose}
    visible={visible}
  >
    {content &&
      content.length > 0 &&
      content.map(item => (
        <Row gutter={24} key={`${item.label}_${item.value}`}>
          <Col span={24}>
            <div className={styles.box}>
              <div className={styles.term}>{item.label || ''}</div>
              <div>{item.value || '无'}</div>
            </div>
          </Col>
        </Row>
      ))}
  </Drawer>
);

export default MsgDrawer;
