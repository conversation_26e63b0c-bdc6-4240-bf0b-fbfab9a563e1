import React, { Component } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import mapValues from 'lodash/mapValues';

const DEFAULT_COL_SPAN = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};

function toCreateFormField(values) {
  return mapValues(values, value => Form.createFormField({ value }));
}

const FormItem = Form.Item;

class SubmitForm extends Component {
  constructor(props) {
    super(props);
    this.handleSearch = this.handleSearch.bind(this);
    this.getFormItemLayout = this.getFormItemLayout.bind(this);
  }

  getFormItemLayout = item => {
    // eslint-disable-line
    if (item.formItemLayout) {
      return item.formItemLayout;
    }
    return DEFAULT_COL_SPAN;
  };

  handleSearch(e) {
    e.preventDefault();
    const { form, handleSubmit } = this.props;
    form.validateFields((err, values) => {
      if (!err) {
        try {
          if (handleSubmit) {
            handleSubmit(values);
          }
        } catch (error) {
          throw error;
        }
      }
    });
  }

  render() {
    const { dataSource, submit, form } = this.props;
    const { getFieldDecorator } = form;

    const getFormItem = () => {
      if (dataSource && dataSource.length) {
        return dataSource.map(item => {
          if (item.isHidden) {
            return null;
          }
          return (
            <FormItem
              key={`submitform_${item.dataKey}`}
              label={item.title}
              {...this.getFormItemLayout(item)}
              extra={item.extra}
            >
              {getFieldDecorator(item.dataKey || '', item.option)(item.component)}
            </FormItem>
          );
        });
      }
      return [];
    };

    return (
      <Form key="FormItemEdit" onSubmit={this.handleSearch}>
        {getFormItem()}
        {submit && <FormItem>{submit.component}</FormItem>}
      </Form>
    );
  }
}

export default Form.create({
  mapPropsToFields: props => toCreateFormField(props.formData),
  onValuesChange: (props, values) => props.onValuesChange && props.onValuesChange(values),
  onFieldsChange: (props, fields) => props.onFieldsChange && props.onFieldsChange(fields),
})(SubmitForm);
