import React from 'react';
import { Select, Spin } from 'antd';
import debounce from 'lodash/debounce';
import get from 'lodash/get';

const { Option } = Select;

class SelectTasks extends React.Component {
  static getDerivedStateFromProps(nextProps) {
    // Should be a controlled component.
    if ('value' in nextProps) {
      return {
        ...(nextProps.value || {}),
      };
    }
    return null;
  }

  constructor(props) {
    super(props);
    this.lastFetchId = 0;
    this.fetchTask = debounce(this.fetchTask, 500);
    this.state = {
      data: [],
      fetching: false,
    };
  }

  fetchTask = value => {
    this.lastFetchId += 1;
    const fetchId = this.lastFetchId;
    this.setState({
      data: [],
      fetching: true,
    });
    fetch(`/api/crowd/algoModelTaskConfig/list?pageNum=1&pageSize=20&type=primary&name=${value}`)
      .then(res => res.json())
      .then(body => {
        if (fetchId !== this.lastFetchId) {
          return;
        }
        let data = get(body, 'data.rows', []);

        data = data.map(item => ({
          text: item.name,
          value: { taskId: item.id, name: item.name },
        }));

        this.setState({
          data,
          fetching: false,
        });
      });
  };

  handleChange = value => {
    this.setState({
      data: [],
      fetching: false,
    });

    this.triggerChange({ value });
  };

  triggerChange = ({ value }) => {
    let v;
    // Should provide an event to pass value to Form.
    const { onChange } = this.props;

    if (onChange) {
      v = value.key ? JSON.parse(value.key) : undefined;
      onChange(v);
    }
  };

  render() {
    const { fetching, data } = this.state;
    const { style = {}, value } = this.props;

    const newValue = value && {
      label: value.name,
      key: JSON.stringify(value),
    };
    return (
      <Select
        style={{ width: '100%', ...style }}
        labelInValue
        value={newValue}
        placeholder="请开始输入选品任务名称查找"
        notFoundContent={fetching ? <Spin size="small" /> : null}
        filterOption={false}
        showSearch
        onSearch={this.fetchTask}
        onChange={this.handleChange}
      >
        {data.map(d => (
          <Option key={d.value.taskId} value={JSON.stringify(d.value)}>
            {d.text}
          </Option>
        ))}
      </Select>
    );
  }
}

export default SelectTasks;
