/**
 * 人群放大弹窗
 * */
import React, { PureComponent } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, Row, Col, Button, Select, Input, DatePicker, Radio, Upload, message, Drawer } from 'antd';
import dayjs from 'dayjs';
import GetUser from '@/components/GetUser';
import { connect } from 'dva';

function beforeUpload(file) {
  const isTxt = file.type === 'text/plain';
  if (!isTxt) {
    message.error('你需要上传txt文件');
  }

  console.log('flile size:>>>', file.size)

  const isLt50M = file.size / 1024 / 1024 < 50;

  if (!isLt50M) {
    message.error('文件必须小于50M');
  }


  return isTxt && isLt50M;
}

const { Option } = Select;
const { TextArea } = Input;
const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const FORM_ITEM_LAYOUT = {
  labelCol: { span: 7 },
  wrapperCol: { span: 14 },
};
const noop = () => {};

@Form.create()
class FileForm extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      fileList: []
    };
    // 16.3之后ref的用法
    typeof this.props.onRef === 'function' && this.props.onRef(this)
  }

  componentWillReceiveProps(nextProps) {
    if (
      (!nextProps.editFormData || !nextProps.editFormData.crowdName) &&
      nextProps.editFormData !== this.props.editFormData
    ) {
      this.props.form.resetFields();
    }
  }

  handleFileChange = info => {
    if (info.file.status === 'done') {
      message.success(`${info.file.name} 上传成功`);
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} 上传失败`);
    }
    let fileList = [...info.fileList];

    fileList = fileList.slice(-1);

    // fileList = fileList.map(file => {
    //   // if (file.response) {
    //   //   file.url = file.response.url;
    //   // }
    //   return file;
    // });

    this.setState({ fileList });
  }

  handleSubmit = () => {
    const { form, onSubmit = noop, editFormData, user: { currentUser } } = this.props;

    form.validateFields((err, values) => {
      if (!err) {
        const { filePath = {}, expiredDate, isLoss } = values;
        const {
          fileList: [
            {
              response: {
                url,
                pathName,
              } = {}
            }
          ] = [{}]
        } = filePath;
        if(!pathName){
          message.error('你需要上传txt文件')
          return;
        }
        const payload = {
          ...values,
          // needUpdate: 0,
          expiredDate: expiredDate.valueOf(),
        };

        payload.crowdType = 'FILE_CROWD';
        payload.crowdTags = [];
        payload.profileType = editFormData.profileType || 'TAOBAO_USER'

        // 涉及资损
        if (isLoss === 1) {
          payload.crowdTags.push('LOSS_OF_ASSETS');
        }

        payload.extInfo = {
          UPLOAD_FILE_PATH: pathName,
          UPLOAD_FILE_NAME: url,
        };

        // 提交审核信息
        if (payload.applyApprovalReason && payload.qualityOwner) {
          payload.crowdApprovalInfo = {
            applyApprovalReason: payload.applyApprovalReason,
            applyOwner:{empId: currentUser.workId, nickName: currentUser.name},
            qualityOwner: payload.qualityOwner,
            needSubmit: true
          }
        }
        if (payload.applyApprovalReason) {
          delete payload.applyApprovalReason
        }
        if (payload.qualityOwner) {
          delete payload.qualityOwner
        }

        if (payload.filePath) {
          delete payload.filePath;
        }
        if (payload.isLoss) {
          delete payload.isLoss;
        }

        onSubmit(payload);
      }
    });
  };

  handleFilePath = (rule, value, callback) => {
    const {
      fileList = []
    } = value;
    if (fileList.length > 0
      && (fileList[0].pathName || fileList[0].name || (fileList[0].response && fileList[0].response.pathName))) {
      callback();
    } else {
      callback('请上传用户id文件！');
    }
  }

  render() {
    const {
      form: { getFieldDecorator, getFieldValue },
      editFormData,
    } = this.props;

    const disabledDate = current =>
      current < dayjs().startOf('day') || current > dayjs() + 90 * 24 * 60 * 60 * 1000;

    const fileList = this.state.fileList.length === 0 && editFormData.extInfo ? [
      {
        uid: '2',
        // url: editFormData.extInfo && editFormData.extInfo.UPLOAD_FILE_NAME,
        name: (editFormData.extInfo && editFormData.extInfo.UPLOAD_FILE_NAME || '').replace(/.*\//g, ''),
      }
    ] : this.state.fileList;

    return (
      <Form {...FORM_ITEM_LAYOUT} onSubmit={this.handleSubmit}>
        <FormItem label="人群(任务)名称">
          {getFieldDecorator('crowdName', {
            initialValue: editFormData.crowdName,
            rules: [{ required: true, message: '请输入人群名称' }],
          })(<Input placeholder="请输入人群名称" />)}
        </FormItem>
        <FormItem label="人群描述">
          {getFieldDecorator('crowdDescription', {
            initialValue: editFormData.crowdDescription,
            rules: [{ required: true, message: '请输入人群描述' }],
          })(<TextArea placeholder="请输入人群描述" />)}
        </FormItem>
        <FormItem label="管理员">
          {getFieldDecorator('operator', {
            initialValue: editFormData.operator,
            rules: [{ required: true, message: '请选择管理员' }],
          })(<GetUser />)}
        </FormItem>
        <FormItem label="涉及资损">
          {getFieldDecorator('isLoss', {
            initialValue:
              editFormData.crowdTags &&
              (editFormData.crowdTags.length > 0 &&
              editFormData.crowdTags.includes('LOSS_OF_ASSETS')
                ? 1
                : 0),
            rules: [{ required: true, message: '请选择是否涉及资损' }],
          })(
            <RadioGroup>
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </RadioGroup>
          )}
        </FormItem>

        <FormItem
          label="人群类型"
        >
          {editFormData.profileType === 'SCRM_USER' ? 'Scrm-Oneid人群包' : '淘宝id'}
        </FormItem>
        
        {
          getFieldValue('isLoss') === 1 && (<FormItem 
            label="详细描述"
            extra={
              <span style={{ color: 'red' }}>
                注：涉及资损的人群包将通过审批流获取额外测试保障，测试通过将后自动上线
              </span>
            }
          >
            {getFieldDecorator('applyApprovalReason', {
              initialValue: editFormData?.crowdApprovalInfo?.applyApprovalReason,
              rules: [{ required: true, message: '请输入详细描述' }],
            })(<Input.TextArea placeholder="请详细描述下可能发生资损的场景" allowClear />)}
          </FormItem>)
        }

        {
          getFieldValue('isLoss') === 1 && (
            <FormItem label="QA">
              {getFieldDecorator('qualityOwner', {
                initialValue: editFormData?.crowdApprovalInfo?.qualityOwner,
                rules: [{ required: true, message: '请输入业务对应的测试同学' }],
              })(<GetUser mode="default"/>)}
            </FormItem>
          )

        }

        <FormItem
          label="人群使用场景"
          colon={false}
          extra={
            <span style={{ color: 'red' }}>
              注：非PUSH场景请选中人群匹配，否则会导致无法判断人群
            </span>
          }
        >
          {getFieldDecorator('crowdApplyScene', {
            initialValue: editFormData.crowdApplyScene,
            rules: [{ required: true, message: '请选择人群使用场景' }],
          })(
            <Select placeholder="请选择人群使用场景" mode="multiple" showSearch>
              <Option key="PUSH">PUSH</Option>
              <Option key="MATCH">人群匹配</Option>
              {/* <Option key="ANALYSIS">人群分析</Option> */}
            </Select>
          )}
        </FormItem>
        <FormItem label="上传文件">
          {getFieldDecorator('filePath', {
            initialValue: {
              // 这么封装是为了和上面保持一致
              fileList: [
                {
                  response: {
                    url: editFormData && editFormData.extInfo && editFormData.extInfo.UPLOAD_FILE_NAME,
                    pathName: editFormData && editFormData.extInfo && editFormData.extInfo.UPLOAD_FILE_PATH,
                  }
                },
              ],
            },
            rules: [{ required: true, message: '请上传文件' }, { validator: this.handleFilePath }],
          })(
            <Upload
              name="file"
              action="/api/file/crowd"
              showUploadList={{ showRemoveIcon: false }}
              fileList={fileList}
              beforeUpload={beforeUpload}
              onChange={this.handleFileChange}
            >
              <Button>点击上传用户ID文件</Button>
              <p style={{ color: 'red' }}>注：最多50M,txt格式,UTF-8编码,一行一个</p>
            </Upload>)}
        </FormItem>
        <FormItem label="人群过期时间">
          {getFieldDecorator('expiredDate', {
            initialValue: editFormData.expiredDate ? dayjs(editFormData.expiredDate) : undefined,
            rules: [{ required: true, message: '请选择人群人群过期时间' }],
          })(
            <DatePicker
              disabledDate={disabledDate}
              format="YYYY-MM-DD HH:mm:ss"
              showTime={{
                defaultValue: dayjs('23:59:59', 'HH:mm:ss'),
              }}
            />
          )}
        </FormItem>

      </Form>
    );
  }
}
@connect(state => ({ user: state.user }))
export default class CreateFileCrowdModal extends PureComponent {

  onCancel = () => {
    const { onCancel = noop } = this.props;
    onCancel();
  };
  onSubmit = ()=>{
    if(this.ChildPage){
      this.ChildPage.handleSubmit();
    }
  }
  render() {
    const { 
      editModalVisible,
      editFormData,
      user: { currentUser, isSuperAdmin } 
    } = this.props;

    const isSubmit = () => {
      // 管理者和创建者及超级管理员能编辑
      const { creator = {}, operator = [] } = editFormData;
      const owners = [...operator, creator];

      const isGranted = owners.map(o => o.empId).includes(currentUser.workId);
      
      if(!editFormData.id || isSuperAdmin || isGranted) {
        return false
      }
      
      return true
    }

    return (
      <Drawer
        title='文件上传圈人'
        placement="right"
        visible={editModalVisible}
        destroyOnClose
        width="800"
        onClose={this.onCancel}
        footer={
          <div
            style={{
              textAlign: 'right',
            }}
          >
            <Button onClick={() => this.onCancel()} style={{ marginRight: 15 }}>
              取消
            </Button>
            <Button type="primary" onClick={this.onSubmit} disabled={isSubmit()}>
              保存
            </Button>
          </div>
        }
      >
        <FileForm onRef={c=>this.ChildPage=c} {...this.props} onCancel={this.onCancel} />
      </Drawer>
    );
  }
}
