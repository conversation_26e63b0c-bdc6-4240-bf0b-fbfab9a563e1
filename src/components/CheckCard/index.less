.checkcard{
  border: 1px solid #d9d9d9;
  margin-bottom: 16px;
  color: rgba(0,0,0,.85);
  font-size: 14px;
  line-height: 1.5715;
  vertical-align: top;
  background-color: #fff;
  border-radius: 2px;
  cursor: pointer;
  transition: all .2s;
  .checkcardContent{
    display: flex;
    padding: 12px 16px;
    flex-direction: column;
    min-height: 76px;
    justify-content: center;
    .checkcardTop{
      display: flex;
      align-items: center;
      margin-bottom: 4px;
      .checkcardName{
        overflow: hidden;
        color: rgba(0,0,0,.85);
        font-size: 16px;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .checkcardLabel{
        margin-left: 10px;
      }
    }
    .checkcardDesc{
      font-size: 12px;
      color: rgba(0,0,0,.45);
    }
  }
  &:hover{
    border-color: #1890ff;
  }
}

.checkcardOnlyTop{
  justify-content: center;
  .checkcardName{
    align-items: center;
    font-size: 14px !important;
  }
}

.checkcardChecked{
  background-color: #e6f7ff;
  border-color: #1890ff;
  position: relative;
  &::after{
    position: absolute;
    top: 2px;
    right: 2px;
    width: 0;
    height: 0;
    border-left: 6px solid #1890ff;
    border-bottom: 6px solid #1890ff;
    border-color: #1890ff #1890ff transparent transparent;
    border-style: solid;
    border-width: 6px;
    border-top-right-radius: 2px;
    content: "";
  }
}