import React, { useState, useEffect } from 'react';
import { Tag } from 'antd';
import styles from './index.less';

const CheckCard = (props) => {
  const { name, desc, isCommonUse ,disabled } = props;

  return (
    <div className={disabled ? `${styles.checkcard} ${styles.checkcardChecked}` : styles.checkcard}>
      <div className={styles.checkcardContent}>
        <div className={desc ? styles.checkcardTop : `${styles.checkcardTop} ${styles.checkcardOnlyTop}`}>
          <div className={styles.checkcardName}>{name}</div>
          {isCommonUse &&  <div className={styles.checkcardLabel}><Tag color="green">常用</Tag></div>}
        </div>
        {
          desc &&  <div className={styles.checkcardDesc}>{desc}</div>
        }
      </div>
    </div>
  );
};

export default CheckCard;
