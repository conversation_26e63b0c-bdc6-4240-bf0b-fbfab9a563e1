import React from 'react';
import { InputNumber } from 'antd';

class RangeInput extends React.Component {
  static getDerivedStateFromProps(nextProps) {
    // Should be a controlled component.
    if ('value' in nextProps) {
      const [start, end] = nextProps.value || [];
      return {
        start: start || undefined,
        end: end || undefined,
      };
    }
    return null;
  }

  constructor(props) {
    super(props);

    const value = props.value || [];
    const [start, end] = value;
    this.state = {
      start,
      end,
    };
  }

  handleStartChange = value => {
    const start = value;

    if (!('value' in this.props)) {
      this.setState({ start });
    }
    this.triggerChange({ start });
  };

  handleEndChange = value => {
    const end = value;

    if (!('value' in this.props)) {
      this.setState({ end });
    }
    this.triggerChange({ end });
  };

  triggerChange = changedValue => {
    // Should provide an event to pass value to Form.
    const { onChange } = this.props;
    const { start, end } = {
      ...this.state,
      ...changedValue,
    };
    if (onChange) {
      onChange([start, end]);
    }
  };

  render() {
    const { start, end } = this.state;
    return (
      <div>
        <InputNumber
          min={0}
          max={end}
          value={start}
          step={0.1}
          onChange={this.handleStartChange}
          style={{ marginRight: 4 }}
        />
        <span>~</span>
        <InputNumber
          min={start}
          value={end}
          step={0.1}
          onChange={this.handleEndChange}
          style={{ marginLeft: 4, marginRight: 4 }}
        />
      </div>
    );
  }
}

export default RangeInput;
