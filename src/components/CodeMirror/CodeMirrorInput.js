import React, {PureComponent} from 'react';
import 'codemirror/lib/codemirror.js'
import 'codemirror/lib/codemirror.css'
import 'codemirror/mode/sql/sql';
import 'codemirror/mode/groovy/groovy';
import 'codemirror/mode/javascript/javascript';
import 'codemirror/theme/yonce.css'

import 'codemirror/addon/selection/active-line';
import 'codemirror/addon/lint/lint';
import 'codemirror/addon/lint/lint.css';

import {UnControlled as CodeMirror} from 'react-codemirror2'

const sqlLinter = (text) => {
  if (!text || !text.trim()) {
    return [];
  }

  const errors = [];
  const lines = text.split('\n');
  const parenthesesStack = [];

  // 检查中文符号和括号匹配
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    // 检查中文括号
    const chineseParenMatch = line.match(/（|）/);
    if (chineseParenMatch) {
      const ch = line.indexOf(chineseParenMatch[0]);
      errors.push({
        message: 'SQL 语法错误，不支持中文 "（）" 符号',
        severity: 'error',
        from: { line: i, ch },
        to: { line: i, ch: ch + 1 }
      });
    }

    // 检查中文分号
    const chineseSemicolonMatch = line.match(/；/);
    if (chineseSemicolonMatch) {
      const ch = line.indexOf(chineseSemicolonMatch[0]);
      errors.push({
        message: 'SQL 语法错误，不支持中文 "；" 符号',
        severity: 'error',
        from: { line: i, ch },
        to: { line: i, ch: ch + 1 }
      });
    }

    // 检查中文引号
    const chineseQuoteMatch = line.match(/“|”/);
    if (chineseQuoteMatch) {
      const ch = line.indexOf(chineseQuoteMatch[0]);
      errors.push({
        message: 'SQL 语法错误，不支持中文引号',
        severity: 'error',
        from: { line: i, ch },
        to: { line: i, ch: ch + 1 }
      });
    }

    // ${date}、${bizdate} 不允许加引号
    const dateMatch = line.match(/(['"])(\$\{date\}|\$\{bizdate\})\1/);
    if (dateMatch) {
      errors.push({
        message: 'SQL 语法错误，${date}、${bizdate} 变量不允许加引号',
        severity: 'error',
      });
    }

    // 检测括号未匹配的情况
    for (let charIndex = 0; charIndex < line.length; charIndex++) {
      const char = line[charIndex];
      if (char === '(') {
        parenthesesStack.push({ line: i, ch: charIndex });
      } else if (char === ')') {
        if (parenthesesStack.length === 0) {
          // 多余的右括号
          errors.push({
            message: 'SQL 语法错误，存在多余的右括号 ")"',
            severity: 'error',
            from: { line: i, ch: charIndex },
            to: { line: i, ch: charIndex + 1 }
          });
        } else {
          parenthesesStack.pop();
        }
      }
    }
  }

  // 检查是否有未匹配的左括号
  parenthesesStack.forEach(bracket => {
    errors.push({
      message: 'SQL 语法错误，存在未匹配的左括号 "("',
      severity: 'error',
      from: { line: bracket.line, ch: bracket.ch },
      to: { line: bracket.line, ch: bracket.ch + 1 }
    });
  });

  return errors;
};

// 导出sqlLinter供其他组件使用
export { sqlLinter };

export default class CodeMirrorInput extends PureComponent {

  constructor(props) {
    super(props);
    this.state = {value: this.props.value};
  }

  triggerChange = (changedValue) => {
    // Should provide an event to pass value to Form.
    const onChange = this.props.onChange;
    const editorChange = this.props.editorChange;
    this.setState({changedValue});
    if (editorChange) {
      editorChange(changedValue);
      return
    }
    if (onChange) {
      onChange(changedValue);
    }
  };

  triggerBlur = () => {
    console.log('this.props--',this.props)
    const onBlurChange = this.props.onBlurChange;
    if (onBlurChange) {
      onBlurChange()
    }
  }

  componentWillReceiveProps(nextProps) {
    // Should be a controlled component.
    if ('value' in nextProps && this.state.changedValue !== nextProps.value ) {
      const value = nextProps.value;
      this.setState({value});
    }
  }

  componentDidMount() {
    this.setState({
      value: this.props.value
    })
  }

  render() {
    const {props} = this;
    const options = {
      lineNumbers: true,
      mode: this.props.mode || 'application/json',
      lineWrapping: true,
      styleActiveLine: true,
      autoCloseBrackets: true,
      theme:"yonce",
      autoCursor: false,
      readOnly: this.props.disabled || false,
      matchBrackets: true,
      smartIndent: true,  // 是否智能缩进
      foldGutter: true,
      gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter", "CodeMirror-lint-markers"],
      indentUnit: 4,  // 缩进单位，默认2
      lint: this.props.mode === 'text/x-sql' ? sqlLinter : true,
    };

    return (
      <div
        {...props}
      >
        <CodeMirror
          options={options}
          value={this.state.value}
          onChange={(editor, data, value) => {
            this.triggerChange(value);
          }}
          onBlur={(editor, data, value) => {
            this.triggerBlur()
          }}
        />
      </div>
    )
  }
}