
html,
body,
#root {
  height: 100%;
  margin: 0;
}

p {
  margin: 0;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.globalSpin {
  width: 100%;
  margin: 40px 0 !important;
}

ul,
ol {
  list-style: none;
}

.react-codemirror2{
  height: 100% !important;
}
.CodeMirror{
  height: 100% !important;
}

.antd-pro-components-top-nav-header-index-logo h1 {
  font-weight: bold !important;
  color: #1b98dc !important;
}


.ant-menu-submenu-arrow:after,.ant-menu-submenu-arrow:before {
  position: absolute;
  width: 6px;
  height: 1.5px;
  background-color: currentcolor;
  border-radius: 2px;
  top: 50%;
  margin-left: 8px;
  content: ""
}

.ant-menu-submenu-arrow:before {
  transform: rotate(-45deg) translateX(2.5px);
}

.ant-menu-submenu-arrow:after {
  transform: rotate(45deg) translateX(-2.5px);
}

.ant-menu-submenu-active {
  color: #fff !important;
}

.antd-pro-components-global-header-index-action:hover {
  background: transparent !important;
}

.ant-menu-vertical.ant-menu-sub, .ant-menu-vertical-left.ant-menu-sub, .ant-menu-vertical-right.ant-menu-sub {
  min-width: 120px !important;
}

.ant-menu.ant-menu-dark .ant-menu-item-selected,
.ant-menu-submenu-popup.ant-menu-dark .ant-menu-item-selected {
  background-color: transparent !important;
}

.title-icon {
  img {
    border-radius: 4px;
  }
}