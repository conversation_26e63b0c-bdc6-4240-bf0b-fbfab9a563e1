import { useState, useEffect } from 'react';
import { message } from 'antd';
import {
  queryThemeSuggestCategory,
  queryThemeSuggestCity,
  queryThemeSuggestNativePoi,
  queryThemeSuggestPoi,
  queryThemeSuggestPlayTag,
} from '@/services/strategy';
import { processTreeData } from '@/utils/tree';

const useSuggests = () => {
  const [categorySuggests, setCategorySuggests] = useState([]);
  const [citySuggests, setCitySuggests] = useState([]);
  const [nativePoiSuggests] = useState([]);
  const [poiSuggests] = useState([]);
  const [playTagSuggests, setPlayTagSuggests] = useState([]);
  const [allPoiSuggests, setAllPoiSuggests] = useState([]);

  const fetchCategorySuggests = params => {
    queryThemeSuggestCategory({ limit: 10, ...params }).then(res => {
      if (!res.success) {
        message.error(res.msg);
        return;
      }

      setCategorySuggests(res.data);
    });
  };

  const fetchCitySuggests = params =>
    queryThemeSuggestCity({ limit: 10, ...params }).then(res => {
      if (!res.success) {
        message.error(res.msg);
        return false;
      }

      setCitySuggests(res.data);

      console.log('运行');
      return res.data;
    });

  const fetchNativePoiSuggests = params =>
    queryThemeSuggestNativePoi({ limit: 10, ...params }).then(res => {
      if (!res.success) {
        message.error(res.msg);
        return false;
      }

      setAllPoiSuggests(res.data);

      return res.data;
    });

  const fetchPoiSuggests = params =>
    queryThemeSuggestPoi({ limit: 10, ...params }).then(res => {
      if (!res.success) {
        message.error(res.msg);
        return false;
      }

      setAllPoiSuggests(res.data);

      return res.data;
    });

  const fetchPlayTagSuggests = params =>
    queryThemeSuggestPlayTag({ limit: 10, ...params }).then(res => {
      if (!res.success) {
        message.error(res.msg);
        return false;
      }

      setPlayTagSuggests(res.data);
      return res.data;
    });

  const fetchAllPoi = (params = {}) => {
    const { cities = [], query } = params;
    Promise.all([
      queryThemeSuggestNativePoi({
        cities,
        query,
      }),
      queryThemeSuggestPoi({
        cities,
        query,
      }),
    ]).then(values => {
      const [res1, res2] = values;
      if (res1.success && res2.success) {
        const allPoi = (res1.data || [])
          .concat(res2.data || [])
          .filter((item, index, arr) => arr.findIndex(p => p.poiId === item.poiId) === index);
        setAllPoiSuggests(allPoi);
      }
    });
  };

  const restPoiPlaySuggests = () => {
    setPlayTagSuggests([]);
    setAllPoiSuggests([]);
  };

  useEffect(() => {
    queryThemeSuggestCategory().then(res => {
      if (!res.success) {
        message.error(res.msg);
        return;
      }

      setCategorySuggests(processTreeData(res.data));
    });
  }, []);

  return [
    {
      allPoiSuggests,
      categorySuggests,
      citySuggests,
      nativePoiSuggests,
      poiSuggests,
      playTagSuggests,
    },
    {
      fetchCategorySuggests,
      fetchCitySuggests,
      fetchNativePoiSuggests,
      fetchPoiSuggests,
      fetchPlayTagSuggests,
      fetchAllPoi,
    },
    restPoiPlaySuggests,
  ];
};

export default useSuggests;
