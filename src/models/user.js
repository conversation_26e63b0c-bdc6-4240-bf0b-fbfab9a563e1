import { message } from 'antd';
import WaterMark from '@alife/waterMark';
import { query as queryUsers, queryCurrent } from '@/services/user';
import {
  isSuperAdmin
} from '@/services/api';

export default {
  namespace: 'user',

  state: {
    list: [],
    currentUser: {},
    isSuperAdmin: false
  },

  effects: {
    *fetch(_, { call, put }) {
      const response = yield call(queryUsers, '人尤');
      yield put({
        type: 'save',
        payload: response,
      });
    },
    *fetchCurrent(_, { call, put }) {
      const response = yield call(queryCurrent);
      if (!response.data) {
        message.error('请求用户信息失败');
        return;
      }
      const { lastName, empId, nickNameCn } = response.data;

      yield put({
        type: 'saveCurrentUser',
        payload: {
          name: nickNameCn || lastName,
          nickNameCn,
          workId: empId,
          avatar: `https://work.alibaba-inc.com/photo/${empId}.220x220.jpg`,
        },
      });
    },

    *fetchSuperAdmin(_, { call, put }) {
      const response = yield call(isSuperAdmin);

      yield put({
        type: 'updateState',
        payload: {
          isSuperAdmin: response.data,
        }
      });
    },

  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        list: action.payload,
      };
    },
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    saveCurrentUser(state, action) {
      const currentUser = action.payload || {}
      // console.log('currentUser==========>', currentUser)
      if (!/localhost/.test(window.location.host)) {
        // 水印对 localhost 有处理，会认为是离线了
        // https://web.npm.alibaba-inc.com/package/@alife/waterMark
        // 备注: 本地访问会认为是离线，出现白屏
        try {
          const waterMark = new WaterMark({
            s: {
              disable: false,
              rightMenu: false,
              f12: false,
              hotKey: false,
              copy: false,
              cut: false,
              selectStart: false
            }
          });
          waterMark.a(`(${currentUser.name})(${currentUser.workId})`);
        } catch (e) {
          // eslint-disable-next-line no-console
          console.error(e.stack);
        }
      }

      return {
        ...state,
        currentUser,
      };
    },
    changeNotifyCount(state, action) {
      return {
        ...state,
        currentUser: {
          ...state.currentUser,
          notifyCount: action.payload.totalCount,
          unreadCount: action.payload.unreadCount,
        },
      };
    },
  },
};
