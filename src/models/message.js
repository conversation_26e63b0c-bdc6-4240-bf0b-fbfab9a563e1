import { message } from 'antd';
import { queryMessageList, queryUnreadMessageCount, readMessage, readAllMessage } from '@/services/api';

export default {
  namespace: 'message',

  state: {
    messageList: [],
    loading: false,
    tabCntMap: {
      ALL: 0,
      READ: 0,
      UNREAD: 0,
    },
    unReadCount: 0
  },

  effects: {
    *fetchData({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });
      const data = {
        ...payload,
        pageNo: payload.pageNo || 1,
        pageSize: payload.pageSize || 10,
      };

      const response = yield call(queryMessageList, data);
      if (!response.success) {
        message.error(response.msg);
      }
      yield put({
        type: 'updateState',
        payload: {
          messageList: response.data.messageList || [],
          tabCntMap: response.data.tabCntMap || {
            ALL: 0,
            READ: 0,
            UNREAD: 0,
          },
          loading: false,
        },
      });
    },
    *fetchUnreadCount(_, { call, put }) {
      const response = yield call(queryUnreadMessageCount);
      if (!response.success) {
        message.error(response.msg);
      }
      yield put({
        type: 'updateState',
        payload: {
          unReadCount: response.data || 0
        },
      });
    },
    *readMessage({ payload }, { call, put }) {
      const response = yield call(readMessage, payload.id);
      if (!response.success) {
        message.error(response.msg);
        return false
      }
       yield put({
        type: 'fetchData',
        payload: {
          pageSize: payload.pageSize,
        },
      });
      yield put({
        type: 'fetchUnreadCount',
      });
      return true
    },
    *readAllMessage({ payload }, { call, put }) {
      const response = yield call(readAllMessage);
      if (!response.success) {
        message.error(response.msg);
      }
      yield put({
        type: 'fetchData',
        payload: {
          ...payload
        },
      });
      yield put({
        type: 'fetchUnreadCount',
      });
      return true
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
