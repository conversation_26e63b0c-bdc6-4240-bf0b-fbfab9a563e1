export default [
  // app
  {
    path: '/',
    component: '../layouts/BasicLayout',
    routes: [
      { path: '/', redirect: '/crowd-stategy/gather-person' },
      {
        path: '/label-market',
        icon: 'tag',
        name: '标签市场',
        component: './LabelMarket',
      },
      {
        path: '/label-market/detail',
        isHidden: true,
        component: './LabelMarketDetail',
      },
      {
        path: '/crowd-stategy/crowd-circle',
        icon: 'check-circle',
        name: '旧人群圈选',
        isHidden: true,
        component: './GatherPerson',
      },
      {
        path: '/crowd-stategy/gather-person',
        icon: 'check-circle',
        name: '人群圈选',
        component: './CrowdCircle',
      },
      {
        path: '/crowd-stategy/crowd-operation',
        component: './CrowdCircle/CrowdOperation',
        isHidden: true,
      },
      {
        path: '/crowd-stategy/gather-person/detail',
        isHidden: true,
        component: './CrowdCircle/Detail',
      },
      {
        path: '/crowd-stategy',
        icon: 'heat-map',
        name: '圈人策略',
        isHidden: true,
        routes: [
          {
            path: '/crowd-stategy/crowd-circle/gather-person-by-tag',
            component: './GatherPerson/GpByTag',
            name: '标签圈人',
            isHidden: true,
          },
          {
            path: '/crowd-stategy/merchant-gather-person/merchant-crowd/:id',
            name: '单个商家圈人',
            component: './MerchantCrowd',
            isHidden: true,
          },
          {
            path: '/crowd-stategy/merchant-gather-person',
            icon: 'check-circle',
            name: '商家圈人',
            component: './MerchantGp',
            isHidden: true,
          },
          {
            path: '/crowd-stategy/operation-gather-person',
            name: '圈人运维',
            component: './GatherPerson/Operation',
            isHidden: true,
          },
        ],
      },
      {
        path: '/knowledge',
        icon: 'read',
        name: '知识管理',
        isHidden: true,
        routes: [
          {
            path: '/knowledge/enumValueDetail',
            component: './Knowledge/EnumValueDetail',
          },
          {
            path: '/knowledge/crowd-tag/:name',
            component: './Knowledge/CrowdTag',
          },
          {
            path: '/knowledge/create-crowd-tag/:name',
            component: './Knowledge/CreateCrowdTag',
          },
          {
            path: '/knowledge/knowledge-graph',
            name: '知识图谱',
            icon: 'gift',
            component: './KnowledgeGraph',
          },
        ],
      },
      {
        path: '/act-theme',
        icon: 'eye',
        name: '活动日历',
        isHidden: true,
        routes: [
          {
            path: '/act-theme/market-calendar',
            name: '营销日历',
            icon: 'calendar',
            component: './ActTheme/MarketCalendar',
          },
          {
            path: '/act-theme/act-admin',
            name: '活动管理',
            icon: 'calendar',
            component: './ActTheme/ActAdmin',
          },
        ],
      },
      // {
      //   path: '/heat-insight',
      //   icon: 'heat-map',
      //   name: '热度洞察',
      //   isHidden: true,
      //   routes: [
      //     {
      //       path: '/heat-insight/heat-overall',
      //       name: '总体热度洞察',
      //       icon: 'radar-chart',
      //       component: './HeatInsight/HeatOverall',
      //     },
      //     {
      //       path: '/heat-insight/heat-city',
      //       name: '城市热度洞察',
      //       icon: 'fund',
      //       component: './HeatInsight/HeatCity',
      //     },
      //     {
      //       path: '/heat-insight/theme-insight',
      //       name: '主题洞察',
      //       icon: 'bulb',
      //       component: './HeatInsight/ThemeInsight',
      //     },
      //     {
      //       path: '/heat-insight/theme-insight/detail',
      //       name: '主题洞察详情',
      //       component: './HeatInsight/ThemeInsightDetail',
      //       isHidden: true,
      //     },
      //   ],
      // },
      // {
      //   path: '/choose-product',
      //   icon: 'gift',
      //   name: '智能盘货',
      //   isHidden: true,
      //   routes: [
      //     {
      //       path: '/choose-product/choose-product',
      //       name: '算法盘货',
      //       icon: 'gift',
      //       component: './ChooseProduct',
      //       isHidden: true,
      //     },
      //     {
      //       path: '/choose-product/choose-portrait/:id',
      //       component: './ChoosePortrait',
      //     },
      //     {
      //       path: '/choose-product/play-product',
      //       name: '时空玩法盘货',
      //       icon: 'gift',
      //       component: './PlayProduct',
      //     },
      //     {
      //       path: '/choose-product/play-product/pool',
      //       component: './PlayProduct/PlayProductPool',
      //     },
      //     {
      //       name: '人货匹配素材',
      //       path: '/choose-product/enclosed-item',
      //       icon: 'gift',
      //       component: './EnclosedItem',
      //     },
      //   ],
      // },
      {
        path: '/portrait-analysis',
        icon: 'AreaChart',
        name: '用户洞察',
        routes: [
          // {
          //   path: '/portrait-analysis/insight-analysis',
          //   name: '洞察分析',
          //   component: './PortraitAnalysis/InsightAnalysis',
          //   isHidden: true,
          // },
          {
            path: '/portrait-analysis/new-insight-analysis',
            name: '洞察分析',
            component: './PortraitAnalysis/NewInsightAnalysis',
          },
          {
            path: '/portrait-analysis/new-insight-analysis/edit',
            name: '洞察分析编辑',
            component: './PortraitAnalysis/NewInsightAnalysis/Edit',
            isHidden: true,
          },
          {
            path: '/portrait-analysis/analysevorlage',
            name: '分析模版',
            component: './PortraitAnalysis/Analysevorlage',
          },
          // {
          //   path: '/portrait-analysis/crowd-insight',
          //   name: '画像分析',
          //   component: './PortraitAnalysis/CrowdInsight',
          //   isHidden: true,
          // },
          // {
          //   path: '/portrait-analysis/crowd-insight/edit-picture/:id',
          //   component: './PortraitAnalysis/CrowdInsight/components/editPicture.js',
          //   name: '编辑画像',
          //   isHidden: true,
          // },
        ],
      },
      {
        path: '/crowd-manage',
        icon: 'icon-new1',
        name: '人群管理',
        routes: [
          {
            icon: "icon-new1",
            path: '/crowd-manage/human-relations',
            name: '人群血缘关系',
            component: './HelpTool/HumanRelations',
          },
          {
            path: '/crowd-manage/match-result',
            name: '人群匹配校验',
            component: './HelpTool/MatchResult',
          },
          {
            path: '/crowd-manage/online-traffic-statistics',
            name: '人群流量分析',
            component: './HelpTool/OnlineTrafficStatistics',
          },
        ],
      },
      // {
      //   path: '/back-feature',
      //   icon: 'gold',
      //   name: '后台配置',
      //   isHidden: true,
      //   routes: [
      //     {
      //       path: '/back-feature/template',
      //       name: '模板管理',
      //       icon: 'unordered-list',
      //       component: './BackFeature/Template',
      //     },
      //     {
      //       path: '/back-feature/fbi-admin',
      //       name: 'FBI报表管理',
      //       icon: 'unordered-list',
      //       component: './BackFeature/FbiAdmin',
      //     },
      //     {
      //       path: '/back-feature/zg-data',
      //       name: '诸葛使用数据',
      //       icon: 'unordered-list',
      //       component: './BackFeature/ZfData',
      //     },
      //   ],
      // },
      {
        path: '/help-tool',
        icon: 'tool',
        name: '自助工具',
        routes: [
          {
            path: '/help-tool/tag-value-query',
            name: '标签匹配校验',
            component: './HelpTool/TagValueQuery',
          },
          {
            path: '/help-tool/odps-check',
            name: 'odps授权校验',
            component: './HelpTool/OdpsTableCheck',
          },
          {
            path: '/help-tool/account-conversion',
            name: '账号转化',
            component: './HelpTool/AccountConversion',
          },
        ],
      },
      {
        path: '/develop-tool',
        icon: 'Dashboard',
        name: '元数据管理',
        routes: [
          {
            path: '/develop-tool/label-management',
            name: '标签管理',
            component: './Knowledge/LabelManagement',
          },
          {
            path: '/develop-tool/label-management/detail',
            name: '标签详情',
            component: './Knowledge/LabelManagement/Detail',
            isHidden: true,
          },
          {
            path: '/develop-tool/crowd-tag/TAOBAO_USER',
            name: '旧标签管理',
            component: './Knowledge/CrowdTag',
            isHidden: true,
          },
          {
            path: '/develop-tool/create-crowd-tag/:name',
            component: './Knowledge/CreateCrowdTag',
          },
          {
            path: '/develop-tool/entity',
            name: '实体管理',
            component: './Knowledge/Entity',
            isHidden: true,
          },
          {
            path: '/develop-tool/enumValue',
            name: '枚举值管理',
            component: './Knowledge/EnumValue',
          },
        ],
      },
      {
        path: '/admin',
        icon: 'setting',
        name: '管理员',
        routes: [
          {
            path: '/admin/label-reconciliation',
            name: '标签对账',
            component: './Knowledge/LabelReconciliation',
          },
          {
            path: '/admin/portrait',
            name: '画像管理',
            component: './DevelopTool/Portrait',
          },
          {
            path: '/admin/register-table-management',
            name: '注册表管理',
            component: './DevelopTool/RegisterTableManagement',
          },
        ],
      },
      {
        name: 'AI助手',
        path: '/ai-assistant',
        component: './AiAssistant',
      },
      {
        component: '404',
      },
    ],
  },
];
