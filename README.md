<img src=https://gw.alipayobjects.com/zos/r/215m6q/20200306202646.png width=200>

## zhuge

> 诸葛后台(去node版)

- 项目类型: Ant Design 5 + Umi 4 + 飞猪中后台
- 创建者: 南麓 <<EMAIL>>
- 项目地址: <http://gitlab.alibaba-inc.com/trip/zhuge>
- 飞猪中后台使用文档可见 [开发手册](https://yuque.antfin-inc.com/zht/book)
- 备注：由于需要接入 DEF 发布，需要确保对应 group 下有 tbfed（前端研发平台）的用户为管理员

### 初始化

1. `<NAME_EMAIL>:one-sdk/ant-design-no-pro.git` 克隆仓库
2. `tnpm install` 安装依赖 `npm` 包

## 开发命令

```bash
clam newbranch // 新建分支
clam push // 提交代码
clam dev // 本地调试
clam prepub // 预发
clam publish // 正式发布
```

可以经常执行 npm run fix 来修复代码格式问题。

## Changelog

### 0.1.0 by 花名

- [+] 新增了 xxx 功能
- [!] 更改了 xxx 逻辑
- [-] 删除了 xxx 逻辑
